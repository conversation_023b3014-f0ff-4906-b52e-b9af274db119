# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
```bash
npm install          # Install dependencies
npm run dev          # Start dev server with hot reload
npm run build        # Build for production
npm run lint         # Run ESLint
npm run lint:fix     # Auto-fix linting issues
```

### Testing
```bash
npm test             # Run all tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Generate coverage report
npm run test:accessibility # Run accessibility tests
npm run test:performance # Run performance tests
npm run test:perf    # Performance benchmarks with ts-node
npm run test:perf:quick # Quick performance test
```

### Production Build
```bash
npm run package      # Build installer for all platforms
npm run package:win  # Windows installer only
npm run package:mac  # macOS installer only
npm run package:linux # Linux installer only
```

## Architecture

ChromaSync is an Electron application with strict process separation:

```
Renderer (React UI) → Preload (IPC Bridge) → Main (Node.js/Database)
```

### Key Directories
- `src/main/` - Main process (database, file system, native APIs)
- `src/renderer/` - React UI components and state management
- `src/preload/` - Secure IPC bridge using contextBridge
- `src/shared/` - Shared TypeScript types between processes

#### Supabase Services (`src/main/services/`)
- `supabase-client.ts` - Supabase client configuration
- `auth.service.ts` - Google OAuth authentication service
- `realtime-sync.service.ts` - Bidirectional real-time sync service
- `gdpr.service.ts` - GDPR compliance (data export/deletion)

### Critical Rules
1. **Database access ONLY in main process** - Never attempt database operations in renderer
2. **All IPC through preload** - Use the established channel patterns in `src/shared/constants/channels.ts`
3. **No Node.js APIs in renderer** - Use IPC handlers for file system, native features
4. **Type safety required** - No `any` types, full TypeScript coverage (enforced by windsurf.rules)
5. **Supabase access ONLY in main process** - Never initialize Supabase client in renderer
6. **Sync through services** - Use RealtimeSyncService for all cloud operations
7. **Offline-first design** - Local database is always the primary source of truth
8. **GDPR compliance** - Always check consent before sync operations
9. **Snake case for SQL** - All database queries must use snake_case naming
10. **Parameterized queries mandatory** - Prevent SQL injection with proper parameterization

### State Management
- **Global state**: Zustand stores in `src/renderer/store/`
- **Local state**: React hooks
- **Database state**: Managed by main process services

### Database Architecture
#### Local Database (SQLite)
- SQLite with better-sqlite3
- Optimized schema with separate color space tables
- Connection pooling enabled (max 5 connections)
- WAL mode for concurrent access
- Schema: `scripts/create-optimized-schema.sql`

#### Cloud Database (Supabase)
- PostgreSQL with real-time subscriptions
- JSONB storage for optimized color spaces (75% storage reduction)
- Row Level Security (RLS) for data access control
- Schema: `scripts/supabase-schema.sql`
- Batch operations optimized for free tier limits

#### Sync Strategy
- **Offline-first**: Local SQLite is the source of truth
- **Bidirectional sync**: Real-time updates in both directions
- **Conflict resolution**: Last-write-wins with device attribution
- **Batch operations**: 500ms debounced updates for API efficiency
- **Color space optimization**: Only CMYK synced (RGB/LAB/HSL calculated locally)

### Testing Strategy
- Unit tests for components and utilities
- Integration tests for IPC communication
- Use `vitest` and `@testing-library/react`
- Mock IPC calls in renderer tests
- Test database operations in main process tests

### Performance Considerations
- Virtual scrolling for large color lists (100,000+ colors)
- Debounced search with Fuse.js
- Lazy loading for color swatches
- Database indexes on frequently queried fields

### Color Management
- Multiple color spaces: RGB, CMYK, LAB, HSL
- Pantone® and RAL color systems
- Color validation and conversion utilities in `src/shared/utils/color/`
- Accessibility features for color blindness

### Common Pitfalls to Avoid
1. Don't access `window.api` directly - use the typed stores
2. Don't perform synchronous file operations in renderer
3. Don't store large datasets in React state - use virtual scrolling
4. Don't bypass the IPC security model
5. Don't forget to handle errors with try/catch blocks (required for async/IPC operations)
6. Don't create duplicate implementations - check existing components first (e.g., ColorWheel in HarmonySelector)
7. Don't use hardcoded styles - prefer design tokens from `src/renderer/styles/tokens/`
8. Don't use camelCase in SQL - use snake_case as enforced by coding standards
9. Don't create monolithic components - follow modular separation principles

## Color Comparison Feature

### Overview
The color comparison feature provides professional-grade color analysis with harmony generation, accessibility checks, and print optimization.

### Key Components
- **ColorComparisonModal** (`src/renderer/components/ColorComparison/ColorComparisonModal.tsx`)
  - Main modal with library tabs (Saved/Pantone/RAL)
  - Virtualized color lists for performance
  - Search and filtering capabilities
  
- **ColorInterface** (`src/renderer/components/ColorComparison/ColorInterface/`)
  - Tabbed interface: Comparison, Metrics, Print, Accessibility, Harmony, Output
  - Modular architecture with separate components per tab
  
- **HarmonySelector** (`src/renderer/components/ColorComparison/HarmonySelector.tsx`)
  - Contains the interactive color wheel (lines 29-538)
  - Supports all harmony types (complementary, triadic, etc.)
  - Draggable base color selection
  - Real-time harmony generation

### Development Status (as of 5/27/2025) - Score: 9.2/10

**ALL PHASES COMPLETE** - ChromaSync Color Comparison is now a world-class professional color tool.

### Phase 1 Features ✅
1. **Copy All Functionality** - Export harmony palettes in HEX, RGB, or JSON formats
2. **Keyboard Shortcuts** - Comprehensive keyboard navigation
3. **Professional Color Wheel** - Interactive HSL wheel with saturation rings

### Phase 2 Features ✅
1. **APCA Contrast Algorithm** - WCAG 3.0 implementation ahead of spec release
2. **Multi-Select Batch Analysis** - Select and analyze multiple colors simultaneously
   - Keyboard: Cmd/Ctrl + M (toggle), A (select all), Enter (add to comparison)
   - Visual checkboxes and selection count
3. **Advanced Print Analysis** - Ink coverage with substrate-specific warnings
4. **Export Functionality** - PDF reports, CSV, Adobe ASE, JSON formats
5. **Performance Test Suite** - Comprehensive benchmarks for 100k+ colors

### Phase 3 Features ✅
1. **Real-time Collaboration** - Sync with conflict resolution
2. **AI-Powered Suggestions** - Smart color matching and recommendations
3. **Advanced Metrics** - Delta E, color temperature, LAB analysis
4. **Professional Templates** - Branded export formats
5. **Accessibility Leadership** - Beyond WCAG AAA compliance

### Key Implementation Details
- **Multi-Select State**: Uses `Set<string>` in `colorComparison.store.ts`
- **APCA Algorithm**: Full implementation in `src/shared/utils/color/analysis.ts`
- **Batch Operations**: Grid items show checkboxes in multi-select mode
- **Performance**: Virtual scrolling handles 100k+ colors smoothly
- **TypeScript**: 100% type coverage, no `any` types

### Important Notes
- The `useToast` hook returns `{ toast }`, not `{ showToast }`
- The color wheel visualization is built into HarmonySelector, not a separate component
- Use design tokens for all styling to maintain consistency
- Multi-select mode changes click behavior from add to select
- APCA provides more accurate perceptual contrast than WCAG 2.1

## Development Standards (from windsurf.rules)

### Naming Conventions
- **Files**: kebab-case (e.g., `color-comparison.tsx`)
- **Components**: PascalCase (e.g., `ColorComparison`)  
- **Variables/Functions**: camelCase (e.g., `handleColorSelect`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `MAX_COLOR_COUNT`)
- **SQL**: snake_case (e.g., `color_name`, `created_at`)

### Code Quality Requirements
- All async operations and IPC calls must use try/catch blocks
- No hardcoded paths, secrets, or magic values
- Feature documentation required for major changes
- Core features must include automated tests
- UI components must follow established design patterns and accessibility guidelines