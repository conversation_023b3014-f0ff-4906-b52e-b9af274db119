import { SharedFolderFile } from '../../shared/types/shared-folder';

/**
 * Utility functions for working with the shared folder
 */

/**
 * Get the path to the shared folder
 */
export async function getSharedFolderPath(): Promise<string> {
  try {
    return await window.sharedFolder.getPath();
  } catch (error) {
    console.error('Error getting shared folder path:', error);
    throw error;
  }
}

/**
 * Ensure the shared folder exists
 */
export async function ensureSharedFolderExists(): Promise<boolean> {
  try {
    return await window.sharedFolder.ensureExists();
  } catch (error) {
    console.error('Error ensuring shared folder exists:', error);
    return false;
  }
}

/**
 * List all files in the shared folder
 */
export async function listSharedFolderFiles(): Promise<SharedFolderFile[]> {
  try {
    return await window.sharedFolder.listFiles();
  } catch (error) {
    console.error('Error listing files in shared folder:', error);
    return [];
  }
}

/**
 * Read a file from the shared folder
 */
export async function readSharedFile(fileName: string): Promise<string> {
  try {
    return await window.sharedFolder.readFile(fileName);
  } catch (error) {
    console.error(`Error reading file ${fileName} from shared folder:`, error);
    throw error;
  }
}

/**
 * Write content to a file in the shared folder
 */
export async function writeSharedFile(fileName: string, content: string): Promise<boolean> {
  try {
    return await window.sharedFolder.writeFile(fileName, content);
  } catch (error) {
    console.error(`Error writing file ${fileName} to shared folder:`, error);
    return false;
  }
}

/**
 * Save JSON data to a file in the shared folder
 */
export async function saveJsonToSharedFolder<T>(fileName: string, data: T): Promise<boolean> {
  try {
    const content = JSON.stringify(data, null, 2);
    return await writeSharedFile(fileName, content);
  } catch (error) {
    console.error(`Error saving JSON to ${fileName} in shared folder:`, error);
    return false;
  }
}

/**
 * Load JSON data from a file in the shared folder
 */
export async function loadJsonFromSharedFolder<T>(fileName: string): Promise<T | null> {
  try {
    const content = await readSharedFile(fileName);
    return JSON.parse(content) as T;
  } catch (error) {
    console.error(`Error loading JSON from ${fileName} in shared folder:`, error);
    return null;
  }
} 