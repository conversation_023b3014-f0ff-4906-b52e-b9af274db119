/**
 * Gradient Column Management
 * Handles gradient-related database columns and table setup
 */

/**
 * Gradient column manager for ensuring gradient support in database
 */
export class GradientColumnManager {
  private db: any;
  
  constructor(database: any) {
    this.db = database;
  }
  
  /**
   * Ensure gradient columns exist in colors table
   * This is a safe manual fix while the migration system is disabled
   */
  async ensureGradientColumns(): Promise<void> {
    if (!this.db) {
      console.warn('[GradientColumnManager] Database not initialized, skipping gradient columns check');
      return;
    }

    console.log('[GradientColumnManager] Checking for gradient columns...');

    try {
      // Check if is_gradient column exists
      const colorsColumns = this.db.prepare(`
        SELECT name FROM pragma_table_info('colors')
      `).all().map((row: any) => row.name);

      console.log('[GradientColumnManager] Colors table columns:', colorsColumns);

      // Add missing columns with existence checks
      await this.addColumnIfNotExists('colors', 'is_gradient', 'BOOLEAN NOT NULL DEFAULT FALSE');
      await this.addColumnIfNotExists('colors', 'is_metallic', 'BOOLEAN NOT NULL DEFAULT FALSE');
      await this.addColumnIfNotExists('colors', 'is_effect', 'BOOLEAN NOT NULL DEFAULT FALSE');

      // Create gradient_stops table if it doesn't exist
      await this.createGradientStopsTable();

      // Create index for gradient stops
      this.db.exec('CREATE INDEX IF NOT EXISTS idx_gradient_stops_gradient ON gradient_stops(gradient_id)');

      console.log('[GradientColumnManager] Gradient columns ensured successfully');
    } catch (error) {
      console.error('[GradientColumnManager] Error ensuring gradient columns:', error);
      // Don't throw - allow app to continue
    }
  }
  
  /**
   * Add column if it doesn't exist
   */
  private async addColumnIfNotExists(tableName: string, columnName: string, definition: string): Promise<void> {
    try {
      const columns = this.db.prepare(`
        SELECT name FROM pragma_table_info('${tableName}')
      `).all().map((row: any) => row.name);

      if (!columns.includes(columnName)) {
        console.log(`[GradientColumnManager] Adding ${columnName} column to ${tableName}...`);
        this.db.exec(`ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${definition}`);
      } else {
        console.log(`[GradientColumnManager] ${columnName} column already exists in ${tableName}`);
      }
    } catch (error) {
      console.error(`[GradientColumnManager] Error adding column ${columnName} to ${tableName}:`, error);
      throw error;
    }
  }
  
  /**
   * Create gradient_stops table if it doesn't exist
   */
  private async createGradientStopsTable(): Promise<void> {
    try {
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS gradient_stops (
          gradient_id INTEGER NOT NULL REFERENCES colors(id) ON DELETE CASCADE,
          stop_index INTEGER NOT NULL,
          position REAL NOT NULL CHECK (position >= 0 AND position <= 1),
          hex CHAR(7) NOT NULL,
          PRIMARY KEY (gradient_id, stop_index)
        ) WITHOUT ROWID
      `);
      
      console.log('[GradientColumnManager] gradient_stops table ensured');
    } catch (error) {
      console.error('[GradientColumnManager] Error creating gradient_stops table:', error);
      throw error;
    }
  }
  
  /**
   * Get gradient info for a color
   */
  getGradientInfo(colorId: number): any {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    
    const stops = this.db.prepare(`
      SELECT stop_index, position, hex
      FROM gradient_stops
      WHERE gradient_id = ?
      ORDER BY stop_index
    `).all(colorId) as Array<{stop_index: number, position: number, hex: string}>;

    if (stops.length === 0) {
      return undefined;
    }

    const gradientStops = stops.map(stop => ({
      color: stop.hex,
      position: stop.position,
      cmyk: undefined // Can be calculated if needed
    }));

    const colorStops = gradientStops
      .map(stop => `${stop.color} ${stop.position * 100}%`)
      .join(', ');

    return {
      gradientStops,
      gradientCSS: `linear-gradient(90deg, ${colorStops})`
    };
  }
  
  /**
   * Save gradient stops for a color
   */
  saveGradientStops(colorId: number, gradientStops: Array<{color: string, position: number}>): void {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    
    const transaction = this.db.transaction(() => {
      // Delete existing stops
      this.db.prepare('DELETE FROM gradient_stops WHERE gradient_id = ?').run(colorId);
      
      // Insert new stops
      if (gradientStops && gradientStops.length > 0) {
        gradientStops.forEach((stop, index) => {
          this.db.prepare(`
            INSERT INTO gradient_stops (gradient_id, stop_index, position, hex)
            VALUES (?, ?, ?, ?)
          `).run(colorId, index, stop.position, stop.color);
        });
      }
    });
    
    transaction();
  }
  
  /**
   * Delete gradient stops for a color
   */
  deleteGradientStops(colorId: number): void {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    
    this.db.prepare('DELETE FROM gradient_stops WHERE gradient_id = ?').run(colorId);
  }
  
  /**
   * Check if a color is a gradient
   */
  isGradientColor(colorId: number): boolean {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    
    const result = this.db.prepare(`
      SELECT is_gradient FROM colors WHERE id = ?
    `).get(colorId) as { is_gradient: number } | undefined;
    
    return result ? Boolean(result.is_gradient) : false;
  }
  
  /**
   * Set gradient flag for a color
   */
  setGradientFlag(colorId: number, isGradient: boolean): void {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    
    this.db.prepare(`
      UPDATE colors SET is_gradient = ? WHERE id = ?
    `).run(isGradient ? 1 : 0, colorId);
  }
  
  /**
   * Get all gradient colors
   */
  getAllGradientColors(): Array<any> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    
    return this.db.prepare(`
      SELECT id, external_id, code, display_name, hex
      FROM colors 
      WHERE is_gradient = 1 AND deleted_at IS NULL
      ORDER BY display_name
    `).all();
  }
  
  /**
   * Validate gradient stops data
   */
  validateGradientStops(gradientStops: Array<{color: string, position: number}>): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    
    if (!gradientStops || gradientStops.length === 0) {
      errors.push('Gradient must have at least one stop');
    } else {
      gradientStops.forEach((stop, index) => {
        if (!stop.color || typeof stop.color !== 'string') {
          errors.push(`Stop ${index + 1}: Invalid color`);
        }
        
        if (typeof stop.position !== 'number' || stop.position < 0 || stop.position > 1) {
          errors.push(`Stop ${index + 1}: Position must be between 0 and 1`);
        }
        
        if (stop.color && !stop.color.match(/^#[0-9A-Fa-f]{6}$/)) {
          errors.push(`Stop ${index + 1}: Color must be a valid hex color`);
        }
      });
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

/**
 * Legacy function for ensuring gradient columns
 */
export function ensureGradientColumns(db: any): void {
  const manager = new GradientColumnManager(db);
  manager.ensureGradientColumns().catch(error => {
    console.error('[Gradient Columns] Error in legacy function:', error);
  });
}
