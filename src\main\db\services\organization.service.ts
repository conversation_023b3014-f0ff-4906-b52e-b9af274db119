/**
 * @file organization.service.ts
 * @description Service for managing organizations and team members
 */

import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import { 
  Organization, 
  OrganizationMember
} from '../../../shared/types/organization.types';
import { getSupabaseClient } from '../../services/supabase-client';
import { zohoEmailService } from '../../services/email/zoho-email.service';

export class OrganizationService {
  
  constructor(private db: Database.Database) {
    // Email service is initialized separately in main process
  }

  /**
   * Create a new organization
   */
  async createOrganization(
    name: string, 
    ownerId: string
  ): Promise<Organization> {
    // Check for duplicate organization name for this user
    const existingOrg = this.db.prepare(`
      SELECT o.name 
      FROM organizations o
      JOIN organization_members om ON o.id = om.organization_id
      WHERE om.user_id = ? AND LOWER(o.name) = LOWER(?)
    `).get(ownerId, name);

    if (existingOrg) {
      throw new Error(`You already have an organization named "${name}"`);
    }

    const orgId = this.db.prepare(`
      SELECT COALESCE(MAX(id), 0) + 1 as next_id FROM organizations
    `).get().next_id;

    const externalId = uuidv4();
    const slug = this.generateSlug(name);

    this.db.transaction(() => {
      // Create organization
      this.db.prepare(`
        INSERT INTO organizations (id, external_id, name, slug, plan, settings)
        VALUES (?, ?, ?, ?, 'free', '{}')
      `).run(orgId, externalId, name, slug);

      // Add owner as first member
      this.db.prepare(`
        INSERT INTO organization_members (organization_id, user_id, role)
        VALUES (?, ?, 'owner')
      `).run(orgId, ownerId);
    })();

    const org = this.db.prepare(`
      SELECT * FROM organizations WHERE id = ?
    `).get(orgId);

    // Also sync to Supabase
    try {
      const supabase = getSupabaseClient();
      await supabase
        .from('organizations')
        .insert({
          id: externalId,
          name: name,
          slug: slug,
          plan: 'free',
          settings: {},
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      // Add owner as member in Supabase
      await supabase
        .from('organization_members')
        .insert({
          organization_id: externalId,
          user_id: ownerId,
          role: 'owner',
          joined_at: new Date().toISOString()
        });

      console.log('[Organization] Synced to Supabase:', externalId);
    } catch (error) {
      console.error('Failed to sync organization to Supabase:', error);
      // Continue even if Supabase sync fails - local-first approach
    }

    return this.mapToOrganization(org);
  }

  /**
   * Get organizations for a user
   */
  async getOrganizationsForUser(userId: string): Promise<Organization[]> {
    const rows = this.db.prepare(`
      SELECT o.*, om.role as user_role, COUNT(om2.user_id) as member_count
      FROM organizations o
      JOIN organization_members om ON o.id = om.organization_id
      LEFT JOIN organization_members om2 ON o.id = om2.organization_id
      WHERE om.user_id = ?
      GROUP BY o.id
    `).all(userId);

    return rows.map(row => ({
      ...this.mapToOrganization(row),
      userRole: row.user_role // Add the user's role to the organization object
    }));
  }

  /**
   * Get organization by ID
   */
  async getOrganization(orgId: string): Promise<Organization | null> {
    const row = this.db.prepare(`
      SELECT o.*, COUNT(om.user_id) as member_count
      FROM organizations o
      LEFT JOIN organization_members om ON o.id = om.organization_id
      WHERE o.external_id = ?
      GROUP BY o.id
    `).get(orgId);

    return row ? this.mapToOrganization(row) : null;
  }

  /**
   * Update organization
   */
  async updateOrganization(
    orgId: string,
    updates: Partial<Organization>
  ): Promise<Organization | null> {
    const allowedUpdates = ['name', 'plan', 'settings'];
    const updateFields = Object.keys(updates)
      .filter(key => allowedUpdates.includes(key))
      .map(key => `${key} = ?`);

    if (updateFields.length === 0) {
      return this.getOrganization(orgId);
    }

    const values = updateFields.map(field => {
      const key = field.split(' = ')[0];
      return key === 'settings' 
        ? JSON.stringify(updates[key]) 
        : updates[key];
    });

    this.db.prepare(`
      UPDATE organizations
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE external_id = ?
    `).run(...values, orgId);

    return this.getOrganization(orgId);
  }

  /**
   * Delete organization
   */
  async deleteOrganization(orgId: string, userId: string): Promise<boolean> {
    try {
      // Check if user has permission (must be owner)
      const userRole = await this.getUserRole(orgId, userId);
      if (userRole !== 'owner') {
        throw new Error('Only organization owners can delete organizations');
      }

      // Check if organization has any colors or products
      const hasData = this.db.prepare(`
        SELECT 
          (SELECT COUNT(*) FROM colors WHERE organization_id = (SELECT id FROM organizations WHERE external_id = ?)) +
          (SELECT COUNT(*) FROM products WHERE organization_id = (SELECT id FROM organizations WHERE external_id = ?)) as total
      `).get(orgId, orgId);

      if (hasData && hasData.total > 0) {
        throw new Error('Cannot delete organization with existing colors or products. Please delete all data first.');
      }

      // Get the internal organization ID
      const org = this.db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `).get(orgId);

      if (!org) {
        throw new Error('Organization not found');
      }

      // Delete in transaction
      this.db.transaction(() => {
        // Delete memberships
        this.db.prepare(`
          DELETE FROM organization_members WHERE organization_id = ?
        `).run(org.id);

        // Delete organization
        this.db.prepare(`
          DELETE FROM organizations WHERE id = ?
        `).run(org.id);
      })();

      // Also delete from Supabase
      try {
        const supabase = getSupabaseClient();
        await supabase
          .from('organizations')
          .delete()
          .eq('id', orgId);
      } catch (error) {
        console.error('Failed to delete organization from Supabase:', error);
        // Continue even if Supabase delete fails
      }

      return true;
    } catch (error) {
      console.error('Error deleting organization:', error);
      throw error;
    }
  }

  /**
   * Get organization members
   */
  async getMembers(orgId: string): Promise<OrganizationMember[]> {
    console.log('[OrganizationService] Getting members for organization:', orgId);
    
    const rows = this.db.prepare(`
      SELECT 
        om.*,
        u.email as user_email,
        u.name as user_name,
        u.display_name as user_display_name
      FROM organization_members om
      JOIN organizations o ON om.organization_id = o.id
      LEFT JOIN users u ON om.user_id = u.id
      WHERE o.external_id = ?
      ORDER BY om.joined_at DESC
    `).all(orgId);

    console.log(`[OrganizationService] Found ${rows.length} members in database`);
    
    // Get the current user ID from Supabase
    let currentUserId: string | null = null;
    try {
      const supabase = getSupabaseClient();
      const { data: { user } } = await supabase.auth.getUser();
      currentUserId = user?.id || null;
      console.log('[OrganizationService] Current user ID:', currentUserId);
    } catch (error) {
      console.error('Failed to get current user:', error);
    }

    // Map members and ensure user data is available
    const members = await Promise.all(rows.map(async (row) => {
      let memberData = this.mapToMember(row);
      
      // If user data is missing locally, try to fetch from Supabase
      if (!row.user_email && row.user_id) {
        console.log(`[OrganizationService] Missing user data for ${row.user_id}, fetching from Supabase...`);
        try {
          const userData = await this.fetchUserFromSupabase(row.user_id);
          if (userData) {
            memberData.user = userData;
            // Also update local database
            await this.syncUserToLocal(userData);
          }
        } catch (error) {
          console.error(`Failed to fetch user data for ${row.user_id}:`, error);
        }
      }
      
      return {
        ...memberData,
        isCurrentUser: row.user_id === currentUserId
      };
    }));

    return members;
  }

  /**
   * Add member to organization
   */
  async addMember(
    orgId: string,
    userId: string,
    role: 'admin' | 'member' = 'member',
    invitedBy?: string
  ): Promise<OrganizationMember> {
    const org = this.db.prepare(`
      SELECT id FROM organizations WHERE external_id = ?
    `).get(orgId);

    if (!org) {
      throw new Error('Organization not found');
    }

    // Check if user is already a member
    const existing = this.db.prepare(`
      SELECT 1 FROM organization_members 
      WHERE organization_id = ? AND user_id = ?
    `).get(org.id, userId);

    if (existing) {
      throw new Error('User is already a member');
    }

    this.db.prepare(`
      INSERT INTO organization_members (organization_id, user_id, role, invited_by)
      VALUES (?, ?, ?, ?)
    `).run(org.id, userId, role, invitedBy);

    return this.getMember(orgId, userId);
  }

  /**
   * Update member role
   */
  async updateMemberRole(
    orgId: string,
    userId: string,
    role: 'admin' | 'member'
  ): Promise<OrganizationMember | null> {
    const org = this.db.prepare(`
      SELECT id FROM organizations WHERE external_id = ?
    `).get(orgId);

    if (!org) {
      return null;
    }

    // Can't change owner role
    const member = this.db.prepare(`
      SELECT role FROM organization_members
      WHERE organization_id = ? AND user_id = ?
    `).get(org.id, userId);

    if (!member || member.role === 'owner') {
      return null;
    }

    this.db.prepare(`
      UPDATE organization_members
      SET role = ?
      WHERE organization_id = ? AND user_id = ?
    `).run(role, org.id, userId);

    return this.getMember(orgId, userId);
  }

  /**
   * Remove member from organization
   */
  async removeMember(orgId: string, userId: string): Promise<boolean> {
    const org = this.db.prepare(`
      SELECT id FROM organizations WHERE external_id = ?
    `).get(orgId);

    if (!org) {
      return false;
    }

    // Can't remove owner
    const member = this.db.prepare(`
      SELECT role FROM organization_members
      WHERE organization_id = ? AND user_id = ?
    `).get(org.id, userId);

    if (!member || member.role === 'owner') {
      return false;
    }

    const result = this.db.prepare(`
      DELETE FROM organization_members
      WHERE organization_id = ? AND user_id = ?
    `).run(org.id, userId);

    return result.changes > 0;
  }

  /**
   * Check if user is member of organization
   */
  async isMember(orgId: string, userId: string): Promise<boolean> {
    const result = this.db.prepare(`
      SELECT 1 FROM organization_members om
      JOIN organizations o ON om.organization_id = o.id
      WHERE o.external_id = ? AND om.user_id = ?
    `).get(orgId, userId);

    return !!result;
  }

  /**
   * Get member details
   */
  async getMember(orgId: string, userId: string): Promise<OrganizationMember | null> {
    const row = this.db.prepare(`
      SELECT 
        om.*,
        u.email as user_email,
        u.name as user_name,
        u.display_name as user_display_name
      FROM organization_members om
      JOIN organizations o ON om.organization_id = o.id
      LEFT JOIN users u ON om.user_id = u.id
      WHERE o.external_id = ? AND om.user_id = ?
    `).get(orgId, userId);

    return row ? this.mapToMember(row) : null;
  }

  /**
   * Get user's role in organization
   */
  async getUserRole(orgId: string, userId: string): Promise<string | null> {
    const result = this.db.prepare(`
      SELECT om.role 
      FROM organization_members om
      JOIN organizations o ON om.organization_id = o.id
      WHERE o.external_id = ? AND om.user_id = ?
    `).get(orgId, userId);

    return result?.role || null;
  }

  /**
   * Create an invitation to join an organization
   */
  async inviteMember(
    orgId: string,
    email: string,
    role: 'admin' | 'member',
    invitedById: string
  ): Promise<{ success: boolean; invitation?: any; error?: string }> {
    try {
      const org = this.db.prepare(`
        SELECT id, name FROM organizations WHERE external_id = ?
      `).get(orgId);

      console.log('[Organization] DEBUG - Organization lookup result:');
      console.log('  orgId (external_id):', orgId);
      console.log('  org found:', org);

      if (!org) {
        return { success: false, error: 'Organization not found' };
      }

      // Check if user has permission to invite (must be owner or admin)
      const inviterRole = await this.getUserRole(orgId, invitedById);
      if (!inviterRole || (inviterRole !== 'owner' && inviterRole !== 'admin')) {
        return { success: false, error: 'Insufficient permissions to invite members' };
      }

      // Check if email is already a member (skip this check since users table may not exist)
      // This will be caught when the user actually tries to join if they're already a member

      // Check if invitation already exists (including expired ones due to UNIQUE constraint)
      const existingInvitation = this.db.prepare(`
        SELECT id, email, accepted_at, expires_at FROM organization_invitations 
        WHERE organization_id = ? AND email = ?
      `).get(org.id, email);

      console.log('[Organization] DEBUG - Existing invitation check:');
      console.log('  existingInvitation:', existingInvitation);

      if (existingInvitation) {
        if (existingInvitation.accepted_at) {
          return { success: false, error: 'User has already accepted an invitation to this organization' };
        } else {
          // Delete the old invitation to allow a new one (in case it expired)
          console.log('[Organization] DEBUG - Deleting existing invitation to allow new one');
          this.db.prepare(`
            DELETE FROM organization_invitations 
            WHERE organization_id = ? AND email = ?
          `).run(org.id, email);
        }
      }

      // Create invitation
      const externalId = uuidv4();
      const token = uuidv4();
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7); // 7 days expiry

      // Debug logging before insert
      console.log('[Organization] DEBUG - About to insert invitation with values:');
      console.log('  externalId:', externalId);
      console.log('  org.id:', org.id, '(type:', typeof org.id, ')');
      console.log('  org.name:', org.name);
      console.log('  email:', email);
      console.log('  role:', role);
      console.log('  invitedById:', invitedById);
      console.log('  token:', token.substring(0, 8) + '...');
      console.log('  expiresAt:', expiresAt.toISOString());

      // First check if the organization_invitations table exists and get its schema
      const tableExists = this.db.prepare(`
        SELECT sql FROM sqlite_master 
        WHERE type='table' AND name='organization_invitations'
      `).get();
      
      console.log('[Organization] DEBUG - Table check:');
      console.log('  organization_invitations table exists:', !!tableExists);
      
      if (!tableExists) {
        throw new Error('organization_invitations table does not exist - migration may have failed');
      }
      
      console.log('[Organization] DEBUG - Table schema:');
      console.log('  SQL:', tableExists.sql);
      
      // Also check what foreign key constraints exist
      const foreignKeys = this.db.prepare(`
        PRAGMA foreign_key_list(organization_invitations)
      `).all();
      
      console.log('[Organization] DEBUG - Foreign key constraints:');
      console.log('  constraints:', foreignKeys);

      // Ensure the inviter exists in the users table (required for FK constraint)
      // First check if users table exists and get its schema
      const usersTableSchema = this.db.prepare(`
        SELECT sql FROM sqlite_master 
        WHERE type='table' AND name='users'
      `).get();
      
      console.log('[Organization] DEBUG - Users table check:');
      console.log('  users table exists:', !!usersTableSchema);
      console.log('  users table schema:', usersTableSchema?.sql || 'None');
      
      // Check if users table exists and what columns it has
      if (usersTableSchema) {
        try {
          const usersTableInfo = this.db.prepare('PRAGMA table_info(users)').all();
          console.log('[Organization] DEBUG - Users table columns:', usersTableInfo.map(col => col.name));
        } catch (pragmaError) {
          console.log('[Organization] DEBUG - Could not get table info:', pragmaError.message);
        }
      }
      
      let inviterExists = false;
      if (usersTableSchema) {
        try {
          inviterExists = !!this.db.prepare(`
            SELECT id FROM users WHERE id = ?
          `).get(invitedById);
        } catch (selectError) {
          console.log('[Organization] DEBUG - Error checking inviter:', selectError.message);
        }
      }
      
      console.log('[Organization] DEBUG - Inviter check:');
      console.log('  invitedById:', invitedById);
      console.log('  inviterExists:', inviterExists);
      
      if (!inviterExists && usersTableSchema) {
        console.log('[Organization] DEBUG - Creating user record for inviter');
        // Create a minimal user record for the inviter to satisfy FK constraint
        const userEmail = `user-${invitedById.substring(0, 8)}@local.app`;
        const userName = `User ${invitedById.substring(0, 8)}`;
        
        try {
          // Check what columns actually exist in the users table
          const tableInfo = this.db.prepare('PRAGMA table_info(users)').all();
          const columnNames = tableInfo.map(col => col.name);
          
          if (columnNames.includes('name')) {
            // Table has name column - use the expected schema
            this.db.prepare(`
              INSERT INTO users (id, email, name, created_at, updated_at)
              VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `).run(invitedById, userEmail, userName);
          } else {
            // Table doesn't have name column - use minimal schema
            const columns = ['id', 'email'];
            const values = [invitedById, userEmail];
            
            if (columnNames.includes('created_at')) {
              columns.push('created_at');
              values.push('CURRENT_TIMESTAMP');
            }
            if (columnNames.includes('updated_at')) {
              columns.push('updated_at'); 
              values.push('CURRENT_TIMESTAMP');
            }
            
            const placeholders = values.map(v => v === 'CURRENT_TIMESTAMP' ? 'CURRENT_TIMESTAMP' : '?');
            const actualValues = values.filter(v => v !== 'CURRENT_TIMESTAMP');
            
            this.db.prepare(`
              INSERT INTO users (${columns.join(', ')})
              VALUES (${placeholders.join(', ')})
            `).run(...actualValues);
          }
          
          console.log('[Organization] DEBUG - Created user record for inviter');
        } catch (insertError) {
          console.error('[Organization] DEBUG - Failed to create user record:', insertError.message);
          throw insertError;
        }
      }

      try {
        this.db.prepare(`
          INSERT INTO organization_invitations 
          (external_id, organization_id, email, role, invited_by, token, expires_at)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `).run(externalId, org.id, email, role, invitedById, token, expiresAt.toISOString());
      } catch (insertError) {
        console.error('[Organization] DEBUG - Insert error details:', {
          code: insertError.code,
          message: insertError.message,
          values: { externalId, orgId: org.id, email, role, invitedById, tokenPrefix: token.substring(0, 8) }
        });
        throw insertError;
      }

      // Send email notification directly (bypassing Edge Function)
      try {
        // Get inviter details with proper name
        const inviterDetails = this.db.prepare(`
          SELECT u.name, u.email, u.display_name
          FROM users u
          WHERE u.id = ?
        `).get(invitedById);
        
        // Determine the best name to use
        let inviterName = 'A team member';
        if (inviterDetails) {
          inviterName = inviterDetails.display_name || 
                       inviterDetails.name || 
                       inviterDetails.email?.split('@')[0] || 
                       'A team member';
        }

        // Send email directly via Zoho
        const emailSent = await this.sendInvitationEmailDirect(email, {
          organizationName: org.name,
          inviterName: inviterName,
          role: role,
          token: token,
          expiresAt: expiresAt
        });

        if (!emailSent) {
          console.error('[Organization] Failed to send invitation email to', email);
          // Don't fail the invitation creation if email fails
        } else {
          console.log('[Organization] Invitation email sent successfully to', email);
        }
      } catch (emailError) {
        console.error('[Organization] Email sending error:', emailError);
        // Continue anyway - invitation is created
      }

      return { 
        success: true, 
        invitation: {
          id: externalId,
          email,
          role,
          token,
          expiresAt: expiresAt.toISOString()
        }
      };
    } catch (error) {
      console.error('[Organization] Error inviting member:', error);
      return { success: false, error: 'Failed to create invitation' };
    }
  }

  /**
   * Accept an invitation to join an organization
   */
  async acceptInvitation(
    token: string,
    userId: string
  ): Promise<{ success: boolean; organization?: Organization; error?: string }> {
    try {
      // Find the invitation
      const invitation = this.db.prepare(`
        SELECT * FROM organization_invitations 
        WHERE token = ? AND accepted_at IS NULL
      `).get(token);

      if (!invitation) {
        return { success: false, error: 'Invalid or expired invitation' };
      }

      // Check if invitation has expired
      if (new Date(invitation.expires_at) < new Date()) {
        return { success: false, error: 'This invitation has expired' };
      }

      // Check if user email matches invitation email
      const user = this.db.prepare(`
        SELECT email FROM users WHERE id = ?
      `).get(userId);

      if (!user || user.email !== invitation.email) {
        return { success: false, error: 'This invitation is for a different email address' };
      }

      // Add user to organization
      this.db.transaction(() => {
        // Add member
        this.db.prepare(`
          INSERT INTO organization_members (organization_id, user_id, role, invited_by)
          VALUES (?, ?, ?, ?)
        `).run(invitation.organization_id, userId, invitation.role, invitation.invited_by);

        // Mark invitation as accepted
        this.db.prepare(`
          UPDATE organization_invitations 
          SET accepted_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).run(invitation.id);
      })();

      // Get the organization
      const org = this.db.prepare(`
        SELECT * FROM organizations WHERE id = ?
      `).get(invitation.organization_id);

      return { 
        success: true, 
        organization: org ? this.mapToOrganization(org) : undefined
      };
    } catch (error) {
      console.error('[Organization] Error accepting invitation:', error);
      return { success: false, error: 'Failed to accept invitation' };
    }
  }

  /**
   * Get pending invitations for an organization
   */
  async getPendingInvitations(orgId: string): Promise<any[]> {
    const org = this.db.prepare(`
      SELECT id FROM organizations WHERE external_id = ?
    `).get(orgId);

    if (!org) {
      return [];
    }

    const invitations = this.db.prepare(`
      SELECT 
        oi.*
      FROM organization_invitations oi
      WHERE oi.organization_id = ? 
      AND oi.accepted_at IS NULL 
      AND oi.expires_at > CURRENT_TIMESTAMP
      ORDER BY oi.created_at DESC
    `).all(org.id);

    return invitations.map(inv => ({
      id: inv.external_id,
      email: inv.email,
      role: inv.role,
      invitedBy: {
        id: inv.invited_by,
        name: `User ${inv.invited_by.substring(0, 8)}` // Fallback since users table may not exist
      },
      expiresAt: inv.expires_at,
      createdAt: inv.created_at
    }));
  }

  /**
   * Revoke an invitation
   */
  async revokeInvitation(orgId: string, invitationId: string): Promise<boolean> {
    const org = this.db.prepare(`
      SELECT id FROM organizations WHERE external_id = ?
    `).get(orgId);

    if (!org) {
      return false;
    }

    const result = this.db.prepare(`
      DELETE FROM organization_invitations 
      WHERE organization_id = ? AND external_id = ? AND accepted_at IS NULL
    `).run(org.id, invitationId);

    return result.changes > 0;
  }

  /**
   * Generate URL-safe slug from name
   */
  private generateSlug(name: string): string {
    const baseSlug = name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');

    // Check for uniqueness
    let slug = baseSlug;
    let counter = 1;
    
    while (this.db.prepare('SELECT 1 FROM organizations WHERE slug = ?').get(slug)) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    return slug;
  }

  /**
   * Map database row to Organization
   */
  private mapToOrganization(row: any): Organization {
    return {
      id: row.external_id,
      external_id: row.external_id,
      name: row.name,
      slug: row.slug,
      plan: row.plan,
      settings: typeof row.settings === 'string' 
        ? JSON.parse(row.settings) 
        : row.settings || {},
      created_at: row.created_at,
      updated_at: row.updated_at,
      memberCount: row.member_count || 1
    };
  }

  /**
   * Map database row to OrganizationMember
   */
  private mapToMember(row: any): OrganizationMember {
    // Determine the best name to display (prioritize display_name, then name, then email prefix)
    let displayName = 'User';
    if (row.user_display_name) {
      displayName = row.user_display_name;
    } else if (row.user_name) {
      displayName = row.user_name;
    } else if (row.user_email) {
      displayName = row.user_email.split('@')[0];
    } else {
      displayName = `user-${row.user_id.substring(0, 8)}`;
    }

    return {
      organization_id: row.organization_id,
      user_id: row.user_id,
      role: row.role,
      joined_at: row.joined_at,
      invited_by: row.invited_by,
      user: {
        id: row.user_id,
        email: row.user_email || `user-${row.user_id.substring(0, 8)}@local.app`,
        name: displayName
      }
    };
  }

  /**
   * Fetch user data from Supabase profiles table
   */
  private async fetchUserFromSupabase(userId: string): Promise<{ id: string; email: string; name?: string } | null> {
    try {
      const supabase = getSupabaseClient();
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('id, email, full_name')
        .eq('id', userId)
        .single();

      if (error) {
        console.error(`Failed to fetch user profile for ${userId}:`, error);
        return null;
      }

      if (!profile) {
        return null;
      }

      return {
        id: profile.id,
        email: profile.email || `user-${userId.substring(0, 8)}@local.app`,
        name: profile.full_name || profile.email?.split('@')[0] || 'User'
      };
    } catch (error) {
      console.error(`Error fetching user ${userId} from Supabase:`, error);
      return null;
    }
  }

  /**
   * Sync current user profile to local database (public method for OAuth)
   */
  async syncUserProfileToLocal(userId: string, email?: string, fullName?: string): Promise<void> {
    try {
      const userData = {
        id: userId,
        email: email || `user-${userId.substring(0, 8)}@local.app`,
        name: fullName || email?.split('@')[0] || 'User'
      };
      
      await this.syncUserToLocal(userData);
      console.log(`[OrganizationService] Successfully synced user profile: ${userData.email}`);
    } catch (error) {
      console.error(`[OrganizationService] Failed to sync user profile for ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Sync single user to local database
   */
  private async syncUserToLocal(userData: { id: string; email: string; name?: string }): Promise<void> {
    try {
      const existingUser = this.db.prepare(`
        SELECT id FROM users WHERE id = ?
      `).get(userData.id);

      if (!existingUser) {
        this.db.prepare(`
          INSERT INTO users (id, email, name)
          VALUES (?, ?, ?)
        `).run(
          userData.id,
          userData.email,
          userData.name || userData.email.split('@')[0] || 'User'
        );
        console.log(`[OrganizationService] Inserted user ${userData.id} into local database`);
      } else {
        this.db.prepare(`
          UPDATE users 
          SET email = ?, name = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).run(
          userData.email,
          userData.name || userData.email.split('@')[0] || 'User',
          userData.id
        );
        console.log(`[OrganizationService] Updated user ${userData.id} in local database`);
      }
    } catch (error) {
      console.error(`Failed to sync user ${userData.id} to local database:`, error);
    }
  }

  /**
   * Sync organization members from Supabase to local database
   */
  async syncMembersFromSupabase(orgId: string): Promise<void> {
    try {
      console.log(`[OrganizationService] Starting sync for organization: ${orgId}`);
      const supabase = getSupabaseClient();
      
      // Get organization internal ID
      const org = this.db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `).get(orgId) as { id: number } | undefined;
      
      if (!org) {
        console.error('Organization not found:', orgId);
        return;
      }
      
      console.log(`[OrganizationService] Found local organization ID: ${org.id}`);
      
      // Fetch members from Supabase
      const { data: members, error } = await supabase
        .from('organization_members')
        .select(`
          user_id,
          role,
          joined_at,
          invited_by,
          user:profiles (
            id,
            email,
            full_name
          )
        `)
        .eq('organization_id', orgId);

      if (error) {
        console.error('Failed to fetch members from Supabase:', error);
        return;
      }

      if (!members || members.length === 0) {
        console.log(`[OrganizationService] No members found in Supabase for organization: ${orgId}`);
        return;
      }

      console.log(`[OrganizationService] Found ${members.length} members in Supabase`);
      console.log('[OrganizationService] Members data:', members);

      // Sync each member to local database
      for (const member of members) {
        console.log(`[OrganizationService] Syncing member: ${member.user_id}`, member);
        
        // Ensure user exists in local database
        if (member.user) {
          // Handle the case where user might be an array (due to Supabase join)
          const userProfile = Array.isArray(member.user) ? member.user[0] : member.user;
          
          if (userProfile) {
            const existingUser = this.db.prepare(`
              SELECT id FROM users WHERE id = ?
            `).get(userProfile.id);

            if (!existingUser) {
              console.log(`[OrganizationService] Inserting new user: ${userProfile.id} (${userProfile.email})`);
              this.db.prepare(`
                INSERT INTO users (id, email, name)
                VALUES (?, ?, ?)
              `).run(
                userProfile.id,
                userProfile.email || '',
                userProfile.full_name || userProfile.email?.split('@')[0] || 'User'
              );
            } else {
              console.log(`[OrganizationService] Updating existing user: ${userProfile.id} (${userProfile.email})`);
              // Update user info
              this.db.prepare(`
                UPDATE users 
                SET email = ?, name = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
              `).run(
                userProfile.email || '',
                userProfile.full_name || userProfile.email?.split('@')[0] || 'User',
                userProfile.id
              );
            }
          }
        } else {
          console.warn(`[OrganizationService] No user profile data for member: ${member.user_id}`);
        }

        // Check if membership exists locally
        const existingMembership = this.db.prepare(`
          SELECT 1 FROM organization_members 
          WHERE organization_id = ? AND user_id = ?
        `).get(org.id, member.user_id);

        if (!existingMembership) {
          this.db.prepare(`
            INSERT INTO organization_members (organization_id, user_id, role, joined_at, invited_by)
            VALUES (?, ?, ?, ?, ?)
          `).run(org.id, member.user_id, member.role, member.joined_at, member.invited_by);
        } else {
          // Update role if changed
          this.db.prepare(`
            UPDATE organization_members 
            SET role = ?
            WHERE organization_id = ? AND user_id = ?
          `).run(member.role, org.id, member.user_id);
        }
      }
    } catch (error) {
      console.error('Error syncing members from Supabase:', error);
    }
  }

  /**
   * Sync organizations from Supabase to local database
   */
  async syncOrganizationsFromSupabase(userId: string): Promise<Organization[]> {
    try {
      const supabase = getSupabaseClient();
      
      // Fetch organizations and memberships from Supabase
      const { data: memberships, error } = await supabase
        .from('organization_members')
        .select(`
          role,
          organization:organizations (
            id,
            name,
            slug,
            plan,
            settings,
            created_at,
            updated_at
          )
        `)
        .eq('user_id', userId);

      if (error) {
        console.error('Failed to fetch organizations from Supabase:', error);
        return [];
      }

      if (!memberships || memberships.length === 0) {
        return [];
      }

      // Sync each organization to local database
      const organizations: Organization[] = [];
      const seenNames = new Set<string>(); // Track organization names to prevent duplicates
      
      for (const membership of memberships) {
        if (!membership.organization) {continue;}
        
        const org = membership.organization as any;
        
        // Skip if we've already seen this organization name
        const normalizedName = org.name.toLowerCase();
        if (seenNames.has(normalizedName)) {
          console.warn(`Skipping duplicate organization: ${org.name} (${org.id})`);
          continue;
        }
        seenNames.add(normalizedName);
        
        // Check if organization exists locally
        const existingOrg = this.db.prepare(`
          SELECT id FROM organizations WHERE external_id = ?
        `).get(org.id) as { id: number } | undefined;

        let localOrgId: number;

        if (!existingOrg) {
          // Create organization locally
          const result = this.db.prepare(`
            INSERT INTO organizations (external_id, name, slug, plan, settings, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
          `).run(
            org.id,
            org.name,
            org.slug,
            org.plan || 'free',
            JSON.stringify(org.settings || {}),
            org.created_at,
            org.updated_at
          );
          
          localOrgId = result.lastInsertRowid as number;
        } else {
          localOrgId = existingOrg.id;
          
          // Update organization
          this.db.prepare(`
            UPDATE organizations 
            SET name = ?, slug = ?, plan = ?, settings = ?, updated_at = ?
            WHERE id = ?
          `).run(
            org.name,
            org.slug,
            org.plan || 'free',
            JSON.stringify(org.settings || {}),
            org.updated_at,
            localOrgId
          );
        }

        // Sync membership
        const existingMembership = this.db.prepare(`
          SELECT 1 FROM organization_members 
          WHERE organization_id = ? AND user_id = ?
        `).get(localOrgId, userId);

        if (!existingMembership) {
          this.db.prepare(`
            INSERT INTO organization_members (organization_id, user_id, role, joined_at)
            VALUES (?, ?, ?, ?)
          `).run(localOrgId, userId, membership.role, new Date().toISOString());
        } else {
          // Update role if changed
          this.db.prepare(`
            UPDATE organization_members 
            SET role = ?
            WHERE organization_id = ? AND user_id = ?
          `).run(membership.role, localOrgId, userId);
        }

        // Add to return list
        const fullOrg = await this.getOrganization(org.id);
        if (fullOrg) {
          organizations.push(fullOrg);
        }
      }

      return organizations;
    } catch (error) {
      console.error('Error syncing organizations from Supabase:', error);
      return [];
    }
  }

  /**
   * Send invitation email using Zoho Mail API
   * Much more reliable than SMTP, with better error handling
   */
  private async sendInvitationEmailDirect(
    to: string,
    invitation: {
      organizationName: string;
      inviterName: string;
      role: string;
      token: string;
      expiresAt: Date;
    }
  ): Promise<boolean> {
    try {
      const result = await zohoEmailService.sendInvitationEmail(to, invitation);
      
      if (result) {
        console.log('[Organization] Email sent successfully via Zoho API');
        console.log('[Organization] Recipient:', to);
        console.log('[Organization] Organization:', invitation.organizationName);
      } else {
        console.error('[Organization] Failed to send email via Zoho API');
      }
      
      return result;
    } catch (error) {
      console.error('[Organization] Email error:', error);
      return false;
    }
  }
}
