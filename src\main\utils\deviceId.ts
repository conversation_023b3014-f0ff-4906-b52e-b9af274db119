/**
 * Device ID management utility for main process
 * Uses electron-store instead of localStorage
 */

import Store from 'electron-store';

const store = new Store();
const DEVICE_ID_KEY = 'pantone-tracker-device-id';

/**
 * Generate a new UUID v4
 */
function generateUUID(): string {
  // Simple UUID v4 generator
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 * Get or create the device ID
 */
export function getOrCreateDeviceId(): string {
  let deviceId = store.get(DEVICE_ID_KEY) as string;
  if (!deviceId) {
    deviceId = generateUUID();
    store.set(DEVICE_ID_KEY, deviceId);
  }
  return deviceId;
}

/**
 * Export the current device ID (for backup or migration)
 */
export function exportDeviceId(): string | null {
  return store.get(DEVICE_ID_KEY) as string || null;
}

/**
 * Import a device ID (restore identity)
 * @param deviceId The device ID to import
 */
export function importDeviceId(deviceId: string): void {
  if (!deviceId || typeof deviceId !== 'string') {
    throw new Error('Invalid device ID');
  }
  store.set(DEVICE_ID_KEY, deviceId);
}
