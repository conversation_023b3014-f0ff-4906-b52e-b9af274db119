/**
 * @file App.tsx
 * @description Root component for the Pantone Color Tracker app
 */

import { useEffect, useState, useRef, ReactNode, lazy, Suspense } from 'react';
import Header from './components/Header';
import SearchBar from './components/SearchBar';
import ViewTabs from './components/ViewTabs';
// Removed unused imports: ColorForm, GradientPickerModal
import { ColorCompareButton } from './components/ColorComparison';
import SplashScreen from './components/SplashScreen';
import { useStartupManager } from './hooks/useStartupManager';
import {
  ColorTableWithSuspense,
  ColorSwatchesWithSuspense,
  ProductsPanelWithSuspense
} from './utils/lazyComponents';
import { useSyncActions, useSyncStatus } from './store/sync.store';
import { SyncStatus } from '../shared/constants/sync-status';
import { RefreshCw, Settings } from 'lucide-react';
import { LicenseDialog } from './components/License/LicenseDialog';
import { SettingsModalWithSuspense } from './utils/lazyComponents';
import { useColorStore } from './store/color.store';
import { useOrganizationStore } from './store/organization.store';
import { TokenProvider } from './context/TokenProvider';
import { useTheme } from './context/ThemeContext';
import { ColorBlindnessProvider } from './context/ColorBlindnessContext';
import { useTokens } from './hooks/useTokens';
import useKeyboardShortcuts from './hooks/useKeyboardShortcuts';
import { useGlobalKeyboardNavigation } from './hooks/useGlobalKeyboardNavigation';
import { usePerformanceMonitoring } from './hooks/usePerformanceMonitoring';
import { errorLogger } from './utils/errorLogger';
import HelpButton from './components/common/HelpButton';
import SetupModal from './components/SetupModal';
import DebugMonitoringPanel from './components/DebugMonitoringPanel';
import { AppInitializer } from './components/AppInitializer';
import { showAcceptInvitationModal } from './components/organization/AcceptInvitation';
import './index.css';
import { getPantoneColors } from './utils/pantoneColors';
import { getRalColors } from './utils/ralColors';

// Define view mode types to match what's used in the app
type ViewMode = 'table' | 'swatches' | 'codes' | 'products';

interface AppContentProps {
  renderView: () => ReactNode;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  viewMode: ViewMode;
  setViewMode: (mode: ViewMode) => void;
}

type LicenseStatus = {
  isValid: boolean;
  inTrialMode: boolean;
  trialDaysRemaining: number;
};

export default function App() {
  // Check if this is a 3D color space window
  const urlParams = new URLSearchParams(window.location.search);
  const modeParam = urlParams.get('mode');
  const isColorSpace3DWindow = modeParam === 'color-space-3d';
  
  // Debug logging
  console.log('App.tsx: Full URL:', window.location.href);
  console.log('App.tsx: URL search params:', window.location.search);
  console.log('App.tsx: All URL params:', Array.from(urlParams.entries()));
  console.log('App.tsx: mode parameter:', modeParam);
  console.log('App.tsx: isColorSpace3DWindow:', isColorSpace3DWindow);
  
  // Force 3D window for testing - remove this line after debugging
  const forceTestMode = window.location.search.includes('color-space-3d') || window.location.search.includes('mode');
  console.log('App.tsx: forceTestMode:', forceTestMode);
  
  // If this is a 3D window, render the specialized component
  if (isColorSpace3DWindow || forceTestMode) {
    console.log('App.tsx: Rendering 3D Color Space Window');
    const ColorSpace3DWindow = lazy(() => import('./color-space-3d-entry').then(m => ({ default: m.ColorSpace3DWindow })));
    return (
      <TokenProvider>
        <ColorBlindnessProvider>
          <Suspense fallback={<div className="flex items-center justify-center h-screen bg-ui-background-primary">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary"></div>
              <span className="text-ui-foreground-secondary">Loading 3D visualization...</span>
            </div>
          </div>}>
            <ColorSpace3DWindow />
          </Suspense>
        </ColorBlindnessProvider>
      </TokenProvider>
    );
  }

  // Initialize startup manager
  const { stage, progress, isReady, getStartupTime } = useStartupManager();
  
  // Safe store access with fallbacks - MUST be before any conditional returns
  let storeData;
  try {
    storeData = useColorStore();
  } catch (error) {
    console.error('Error initializing color store:', error);
    // Provide fallback values
    storeData = {
      fetchColors: async () => console.log('fetchColors fallback'),
      searchQuery: '',
      setSearchQuery: () => {},
      viewMode: 'table' as ViewMode,
      setViewMode: () => {},
    };
  }
  
  const {
    fetchColors,
    searchQuery,
    setSearchQuery,
    viewMode,
    setViewMode,
  } = storeData;
  
  // Log startup time for performance monitoring
  useEffect(() => {
    if (isReady) {
      console.log(`[Startup] Application ready in ${getStartupTime()}ms`);
    }
  }, [isReady, getStartupTime]);
  
  const [showLicenseDialog, setShowLicenseDialog] = useState(false);
  const [licenseStatus, setLicenseStatus] = useState<LicenseStatus>({
    isValid: true,
    inTrialMode: false,
    trialDaysRemaining: 0
  });
  const [showDebugPanel, setShowDebugPanel] = useState(false);
  
  // Get organization state
  const { currentOrganization } = useOrganizationStore();

  // Load colors on component mount - only if organization is selected
  useEffect(() => {
    if (currentOrganization) {
      console.log('[App] Organization selected, fetching colors:', currentOrganization.name);
      fetchColors();
    } else {
      console.log('[App] No organization selected, skipping color fetch');
    }
  }, [fetchColors, currentOrganization]);

  // Initialize global keyboard navigation
  useGlobalKeyboardNavigation();

  // Initialize performance monitoring
  const _performanceStats = usePerformanceMonitoring('App');

  // Initialize error logging and health monitoring
  useEffect(() => {
    errorLogger.logInfo('Application started', {
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      viewport: `${window.innerWidth}x${window.innerHeight}`,
    });

    // Add keyboard shortcut for debug panel (Ctrl+Shift+D)
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'D' && process.env.NODE_ENV === 'development') {
        setShowDebugPanel(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Log color library statistics in development mode
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      try {
        const pantoneColors = getPantoneColors();
        const ralColors = getRalColors();

        if (pantoneColors && ralColors) {
          console.log('---------------------------------------');
          console.log('🎨 Color Library Statistics:');
          console.log(`📊 Pantone colors: ${pantoneColors.length}`);
          console.log(`📊 RAL colors: ${ralColors.length}`);
          console.log(`📊 Total colors: ${pantoneColors.length + ralColors.length}`);
          console.log('---------------------------------------');
        }
      } catch (error) {
        console.error('Error loading color libraries:', error);
      }
    }
  }, []);

  // Check license on startup
  useEffect(() => {
    const checkLicense = async () => {
      try {
        // TEMP: Skip license check for development
        if (process.env.NODE_ENV === 'development') {
          setLicenseStatus({
            isValid: true,
            inTrialMode: false,
            trialDaysRemaining: 0
          });
          return;
        }

        // Get license status
        const response = await window.licenseAPI.getStatus();

        if (response.licensed) {
          // Create license status from response
          const mergedStatus: LicenseStatus = {
            isValid: response.licensed,
            inTrialMode: false,
            trialDaysRemaining: 0
          };
          setLicenseStatus(mergedStatus);

          // If license is invalid or trial is expired, show license dialog
          if (!mergedStatus.isValid ||
              (mergedStatus.inTrialMode && mergedStatus.trialDaysRemaining <= 0)) {
            setShowLicenseDialog(true);
          }
        }

        // Set up license dialog listener
        const cleanup = window.licenseAPI.onShowDialog?.((data: any) => {
          setLicenseStatus({
            isValid: data.isValid ?? false,
            inTrialMode: data.inTrialMode ?? false,
            trialDaysRemaining: data.trialDaysRemaining ?? 0
          });
          setShowLicenseDialog(true);
        });

        return cleanup;
      } catch (error) {
        console.error('Error checking license:', error);
        // Fallback to valid license in case of error
        setLicenseStatus({
          isValid: true,
          inTrialMode: false,
          trialDaysRemaining: 0
        });
      }
    };

    checkLicense();
  }, []);

  // Handle invitation deep links
  useEffect(() => {
    // Listen for invitation tokens from deep links
    if (window.organizationAPI?.onInvitationReceived) {
      const cleanup = window.organizationAPI.onInvitationReceived((token: string) => {
        console.log('[App] Received invitation token:', token);
        showAcceptInvitationModal(token);
      });
      
      return cleanup;
    }
  }, []);

  // Render the appropriate view based on viewMode
  const renderView = () => {
    switch (viewMode) {
      case 'table':
        return <ColorTableWithSuspense view="details" />;
      case 'codes':
        return <ColorTableWithSuspense view="reference" />;
      case 'swatches':
        return <ColorSwatchesWithSuspense />;
      case 'products':
        return <ProductsPanelWithSuspense />;
      default:
        return <ColorTableWithSuspense view="details" />;
    }
  };

  // Show splash screen during startup - MUST be after all hooks
  if (!isReady) {
    return <SplashScreen stage={stage} progress={progress} />;
  }

  return (
    <TokenProvider>
      <ColorBlindnessProvider>
        <AppInitializer>
          <a href="#main-content" className="skip-to-main">Skip to main content</a>
          <SetupModal />

          {/* Blocking overlay if license is invalid */}
          {!licenseStatus.isValid ? (
            <div
              style={{
                position: "fixed",
                zIndex: 9999,
                top: 0,
                left: 0,
                width: "100vw",
                height: "100vh",
                background: "rgba(20,20,20,0.97)",
                color: "#fff",
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center"
              }}
            >
              <h1 style={{ fontSize: 32, marginBottom: 24 }}>Access Denied</h1>
              <p style={{ fontSize: 18, marginBottom: 16 }}>
                This device is not authorized to use ChromaSync.<br />
                Please contact your administrator to restore access.
              </p>
              <p style={{ fontSize: 14, opacity: 0.7 }}>
                Device ID: {typeof window.licenseAPI?.getDeviceId === "function" ? "(hidden for security)" : "Unavailable"}
              </p>
            </div>
          ) : (
            <>
              {/* License Dialog */}
              <LicenseDialog
                isOpen={showLicenseDialog}
                onClose={() => setShowLicenseDialog(false)}
                licenseStatus={licenseStatus}
              />

              <AppContent
                renderView={renderView}
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                viewMode={viewMode as ViewMode}
                setViewMode={setViewMode as (mode: ViewMode) => void}
              />

              {/* Debug Monitoring Panel */}
              {process.env.NODE_ENV === 'development' && (
                <DebugMonitoringPanel 
                  isOpen={showDebugPanel} 
                  onClose={() => setShowDebugPanel(false)} 
                />
              )}
            </>
          )}
        </AppInitializer>
      </ColorBlindnessProvider>
    </TokenProvider>
  );
}

// TableControls component for the top right buttons
function TableControls() {
  const tokens = useTokens();
  const { darkMode, toggleDarkMode, importColors, exportColors, clearColors } = useColorStore();
  const [menuOpen, setMenuOpen] = useState(false);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Sync logic
  const { syncData } = useSyncActions();
  const { status } = useSyncStatus(); // Get sync status from the store
  const [syncing, setSyncing] = useState(false);

  // Control button class using tokens
  const controlButtonClass = "p-[var(--spacing-2)] rounded-[var(--radius-md)] bg-ui-background-tertiary dark:bg-ui-background-tertiary text-ui-foreground-primary dark:text-ui-foreground-primary hover:bg-ui-background-secondary dark:hover:bg-ui-background-tertiary/80 transition-colors";

  // Handle sync
  const handleSync = async () => {
    if (syncing) {return;} // Prevent multiple sync attempts

    setSyncing(true);
    try {
      await syncData();
      // Note: We don't need to update the status here as it's managed by the sync store
      // and will be reflected in the UI through the useSyncStatus hook
    } catch (error) {
      console.error('Sync failed:', error);
    } finally {
      setSyncing(false);
    }
  };

  // Handle import
  const handleImport = async () => {
    try {
      const result = await importColors('merge');
      if (result.success) {
        alert(`${result.message || 'Colors imported successfully'}`);
      }
    } catch (error) {
      console.error('Import failed:', error);
      alert(`Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    setMenuOpen(false);
  };

  // Handle export
  const handleExport = async () => {
    try {
      const result = await exportColors();
      if (result.success) {
        alert(`${result.message || 'Colors exported successfully'}`);
      }
    } catch (error) {
      console.error('Export failed:', error);
      alert(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    setMenuOpen(false);
  };

  // Handle clear
  const handleClear = async () => {
    const confirmClear = window.confirm('Are you sure you want to clear all color data? This cannot be undone.');
    if (confirmClear) {
      try {
        await clearColors();
      } catch (error) {
        console.error('Clear all failed:', error);
      }
    }
    setMenuOpen(false);
  };

  // Close menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMenuOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="flex items-center gap-2">
      {/* Sync icon button with status dot */}
      <button
        onClick={handleSync}
        disabled={syncing}
        className={controlButtonClass + (syncing ? " opacity-50 cursor-not-allowed" : "")}
        aria-label="Sync data"
        title="Sync data with cloud"
        style={{ position: "relative" }}
      >
        <RefreshCw size={20} className={syncing ? "animate-spin" : ""} />

        {/* Status dot indicator */}
        <div
          className={`absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-ui-background-tertiary ${
            syncing ? 'bg-brand-primary animate-pulse' :
            status === SyncStatus.SUCCESS ? 'bg-[var(--color-feedback-success)]' :
            status === SyncStatus.ERROR ? 'bg-[var(--color-feedback-error)]' :
            status === SyncStatus.SYNCING ? 'bg-brand-primary animate-pulse' :
            'bg-[var(--color-ui-foreground-tertiary)]'
          }`}
          aria-hidden="true"
        />
      </button>

      {/* Dark mode toggle */}
      <button
        onClick={toggleDarkMode}
        className={controlButtonClass}
        aria-label={darkMode ? "Switch to light mode" : "Switch to dark mode"}
        data-testid="dark-mode-toggle"
      >
        {darkMode ? (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
          </svg>
        ) : (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
          </svg>
        )}
      </button>

      {/* Actions menu (hamburger) */}
      <div className="relative" ref={menuRef}>
        <button
          onClick={() => setMenuOpen(!menuOpen)}
          className={controlButtonClass}
          aria-label="Menu"
        >
          <div className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            </svg>
          </div>
        </button>

        {menuOpen && (
          <div className="absolute right-0 mt-1 w-48 bg-ui-background-primary dark:bg-ui-background-tertiary rounded-[var(--radius-lg)] shadow-[var(--shadow-lg)] z-10 border border-ui-border-light dark:border-ui-border-dark overflow-hidden"
               style={{
                 backgroundColor: darkMode ? 'var(--color-ui-background-tertiary)' : 'var(--color-ui-background-primary)',
                 transition: `opacity ${tokens?.transitions?.duration?.[200] || '200ms'} ${tokens?.transitions?.easing?.apple || 'ease'}, transform ${tokens?.transitions?.duration?.[200] || '200ms'} ${tokens?.transitions?.easing?.apple || 'ease'}`
               }}>
            <div className="py-1">
              <button
                className="w-full flex items-center px-4 py-2 text-sm text-ui-foreground-primary dark:text-ui-foreground-primary hover:bg-ui-background-secondary dark:hover:bg-ui-background-tertiary/80"
                onClick={handleImport}
                title="Import Data"
                aria-label="Import Data"
              >
                <svg className="w-5 h-5 text-ui-foreground-secondary dark:text-ui-foreground-secondary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                <span>Import</span>
              </button>

              <button
                className="w-full flex items-center px-4 py-2 text-sm text-ui-foreground-primary dark:text-ui-foreground-primary hover:bg-ui-background-secondary dark:hover:bg-ui-background-tertiary/80"
                onClick={handleExport}
                title="Export Data"
                aria-label="Export Data"
              >
                <svg className="w-5 h-5 text-ui-foreground-secondary dark:text-ui-foreground-secondary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                </svg>
                <span>Export</span>
              </button>

              <button
                className="w-full flex items-center px-4 py-2 text-sm text-ui-foreground-primary dark:text-ui-foreground-primary hover:bg-ui-background-secondary dark:hover:bg-ui-background-tertiary/80"
                onClick={handleClear}
                title="Clear All"
                aria-label="Clear All"
              >
                <svg className="w-5 h-5 text-ui-foreground-secondary dark:text-ui-foreground-secondary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                <span>Clear All</span>
              </button>

              <button
                className="w-full flex items-center px-4 py-2 text-sm text-ui-foreground-primary dark:text-ui-foreground-primary hover:bg-ui-background-secondary dark:hover:bg-ui-background-tertiary/80"
                onClick={() => {
                  setMenuOpen(false);
                  setSettingsOpen(true);
                }}
                title="Settings"
                aria-label="Settings"
              >
                <Settings size={20} className="text-ui-foreground-secondary dark:text-ui-foreground-secondary mr-2" />
                <span>Settings</span>
              </button>


            </div>
          </div>
        )}

        {/* Settings Modal */}
        <SettingsModalWithSuspense isOpen={settingsOpen} onClose={() => setSettingsOpen(false)} />
      </div>
    </div>
  );
}

// Separate component to access theme context
function AppContent({
  renderView,
  searchQuery,
  setSearchQuery,
  viewMode,
  setViewMode
}: AppContentProps) {
  const { effectiveTheme, isTransitioning } = useTheme();

  // Initialize keyboard shortcuts
  useKeyboardShortcuts();

  // Use the effectiveTheme as the class for the app container
  const themeClass = effectiveTheme;
  const transitionClass = isTransitioning ? 'theme-transition' : '';

  // Simple check for complex components
  const isComplex = viewMode === 'table' || viewMode === 'codes';

  return (
    <div className={`app-container ${themeClass} ${transitionClass} ${effectiveTheme === 'dark' ? 'overflow-hidden' : 'overflow-auto'}`}>
      <div className="dark-inner-container w-full h-full rounded-[12px] bg-ui-background-primary overflow-hidden flex flex-col">
          <Header />

          <main id="main-content" className="container mx-auto px-4 py-4 flex-1 overflow-hidden flex flex-col" role="main" aria-label="Main content">
            <div className="flex justify-between items-center mb-[var(--spacing-4)]">
              <HelpButton />
            </div>

            {/* Consolidated UI controls */}
            <div className="flex flex-wrap items-center gap-[var(--spacing-4)] mb-[var(--spacing-6)] bg-ui-background-secondary p-[var(--spacing-3)] rounded-[var(--radius-lg)]"
                 style={{
                   boxShadow: 'var(--shadow-sm)',
                   backgroundColor: 'var(--color-ui-background-secondary)',
                   transition: 'background-color var(--transition-duration-300) var(--transition-easing-apple), box-shadow var(--transition-duration-300) var(--transition-easing-apple)'
                 }}>
              <div className="flex-grow min-w-[200px]">
                <SearchBar
                  searchQuery={searchQuery}
                  onSearchChange={setSearchQuery}
                  viewMode={viewMode}
                />
              </div>

              <div className="flex-shrink-0">
                <ViewTabs
                  activeView={viewMode}
                  onViewChange={setViewMode}
                />
              </div>

              <div className="flex-shrink-0 ml-auto flex items-center gap-2">
                <ColorCompareButton
                  className="p-[var(--spacing-2)] rounded-[var(--radius-md)] bg-ui-background-tertiary text-ui-foreground-primary hover:bg-ui-background-secondary transition-colors flex items-center gap-1.5"
                />
                <TableControls />
              </div>
            </div>

            {/* Content area with token-based styling */}
            <div className="flex-1 overflow-auto">
              {/* Main content area */}
              <div className={`${isComplex ? 'complex-list' : ''} h-full`}>
                {renderView()}
              </div>
            </div>
          </main>
        </div>
      </div>
  );
}
