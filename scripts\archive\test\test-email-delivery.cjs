/**
 * Test email delivery with detailed logging
 */

require('dotenv').config();
const nodemailer = require('nodemailer');

console.log('🔍 Testing ChromaSync Email Delivery\n');

async function testEmailDelivery() {
  // Create transporter with debug logging
  const transporter = nodemailer.createTransport({
    host: 'smtp.zoho.eu',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: 'SKYiXmiehXjk'
    },
    logger: true,
    debug: true
  });

  // Test different recipient addresses
  const recipients = [
    '<EMAIL>',
    '<EMAIL>'
  ];

  for (const recipient of recipients) {
    console.log(`\n📧 Testing delivery to: ${recipient}`);
    
    try {
      // First verify connection
      await transporter.verify();
      console.log('✅ SMTP connection verified');

      // Try sending with different configurations
      const tests = [
        {
          name: 'Direct from michael@',
          from: '<EMAIL>'
        },
        {
          name: 'Support alias',
          from: '<EMAIL>'
        },
        {
          name: 'With display name',
          from: {
            name: 'ChromaSync Support',
            address: '<EMAIL>'
          }
        }
      ];

      for (const test of tests) {
        console.log(`\n  Testing: ${test.name}`);
        
        try {
          const info = await transporter.sendMail({
            from: test.from,
            to: recipient,
            subject: `Test - ${test.name} - ${new Date().toISOString()}`,
            text: `This is a test email from ChromaSync using ${test.name} configuration.\n\nIf you receive this, please confirm.`,
            html: `
              <div style="font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5;">
                <h2>ChromaSync Email Test</h2>
                <p>This test was sent using: <strong>${test.name}</strong></p>
                <p>Time: ${new Date().toISOString()}</p>
                <p>If you receive this email, the configuration is working!</p>
              </div>
            `,
            headers: {
              'X-Mailer': 'ChromaSync Test',
              'X-Test-ID': Date.now().toString()
            }
          });

          console.log(`  ✅ Sent! Message ID: ${info.messageId}`);
          console.log(`     Accepted: ${info.accepted.join(', ')}`);
          console.log(`     Response: ${info.response}`);
          
          // Wait a bit between tests
          await new Promise(resolve => setTimeout(resolve, 2000));
          
        } catch (error) {
          console.log(`  ❌ Failed: ${error.message}`);
        }
      }
    } catch (error) {
      console.error(`❌ Error testing ${recipient}:`, error.message);
    }
  }

  // Also check for common issues
  console.log('\n📋 Common Email Delivery Issues:');
  console.log('1. Check spam/junk folders');
  console.log('2. Verify recipient email addresses are correct');
  console.log('3. Check if Zoho requires SPF/DKIM records for chromasync.app domain');
  console.log('4. Verify no email filters are blocking chromasync.app');
  console.log('5. Check Zoho Mail sent folder to confirm emails were sent');
}

testEmailDelivery().catch(console.error);