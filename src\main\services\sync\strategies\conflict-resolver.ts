/**
 * @file conflict-resolver.ts
 * @description Conflict resolution system for sync operations
 * 
 * This module handles conflicts that arise when the same data is modified
 * in both local and remote systems simultaneously.
 */

import { getDatabase } from '../../../db/database';
import { getSupabaseClient } from '../../supabase-client';
import { 
  ConflictStrategy, 
  SyncError,
  SyncResult 
} from '../core/sync-types';

// ============================================================================
// CONFLICT TYPES
// ============================================================================

/**
 * Types of conflicts that can occur
 */
export type ConflictType = 'update' | 'delete' | 'create';

/**
 * Conflict information
 */
export interface SyncConflict {
  id: string;
  table: string;
  recordId: string;
  conflictType: ConflictType;
  localVersion: any;
  remoteVersion: any;
  localTimestamp: number;
  remoteTimestamp: number;
  fieldConflicts: string[];
}

/**
 * Conflict resolution result
 */
export interface ConflictResolution {
  resolved: boolean;
  strategy: ConflictStrategy;
  resolvedData: any;
  errors: SyncError[];
}

// ============================================================================
// CONFLICT RESOLVER
// ============================================================================

/**
 * Advanced conflict resolution system
 */
export class ConflictResolver {
  private defaultStrategy: ConflictStrategy;
  private userId: string;
  private organizationId: string;

  constructor(
    userId: string, 
    organizationId: string, 
    defaultStrategy: ConflictStrategy = ConflictStrategy.LAST_WRITE_WINS
  ) {
    this.userId = userId;
    this.organizationId = organizationId;
    this.defaultStrategy = defaultStrategy;
  }

  /**
   * Detect conflicts between local and remote data
   */
  async detectConflicts(
    table: string, 
    localData: any[], 
    remoteData: any[]
  ): Promise<SyncConflict[]> {
    const conflicts: SyncConflict[] = [];

    try {
      // Create maps for efficient lookup
      const localMap = new Map(localData.map(item => [item.external_id, item]));
      const remoteMap = new Map(remoteData.map(item => [item.external_id, item]));

      // Check for conflicts in items that exist in both local and remote
      for (const [id, localItem] of localMap.entries()) {
        const remoteItem = remoteMap.get(id);
        
        if (remoteItem) {
          const conflict = this.compareItems(table, id, localItem, remoteItem);
          if (conflict) {
            conflicts.push(conflict);
          }
        }
      }

      console.log(`[ConflictResolver] Detected ${conflicts.length} conflicts in ${table}`);
      return conflicts;

    } catch (error) {
      console.error('[ConflictResolver] Error detecting conflicts:', error);
      return [];
    }
  }

  /**
   * Resolve a single conflict using the specified strategy
   */
  async resolveConflict(
    conflict: SyncConflict, 
    strategy?: ConflictStrategy
  ): Promise<ConflictResolution> {
    const resolveStrategy = strategy || this.defaultStrategy;
    const errors: SyncError[] = [];

    try {
      console.log(`[ConflictResolver] Resolving conflict for ${conflict.table}:${conflict.recordId} using ${resolveStrategy}`);

      let resolvedData: any;

      switch (resolveStrategy) {
        case ConflictStrategy.LAST_WRITE_WINS:
          resolvedData = this.resolveLastWriteWins(conflict);
          break;

        case ConflictStrategy.LOCAL_WINS:
          resolvedData = conflict.localVersion;
          break;

        case ConflictStrategy.REMOTE_WINS:
          resolvedData = conflict.remoteVersion;
          break;

        case ConflictStrategy.FIELD_LEVEL_MERGE:
          resolvedData = this.resolveFieldLevelMerge(conflict);
          break;

        case ConflictStrategy.MANUAL_RESOLUTION:
          // For manual resolution, we store the conflict for user intervention
          await this.storeConflictForManualResolution(conflict);
          return {
            resolved: false,
            strategy: resolveStrategy,
            resolvedData: null,
            errors: []
          };

        default:
          throw new Error(`Unknown conflict resolution strategy: ${resolveStrategy}`);
      }

      return {
        resolved: true,
        strategy: resolveStrategy,
        resolvedData,
        errors
      };

    } catch (error) {
      console.error('[ConflictResolver] Error resolving conflict:', error);
      
      errors.push({
        id: `conflict-resolution-error-${Date.now()}`,
        timestamp: Date.now(),
        operation: `resolve conflict ${conflict.id}`,
        category: 'conflict',
        severity: 'high',
        message: `Conflict resolution failed: ${error.message}`,
        originalError: error,
        recoverable: true
      });

      return {
        resolved: false,
        strategy: resolveStrategy,
        resolvedData: null,
        errors
      };
    }
  }

  /**
   * Resolve multiple conflicts in batch
   */
  async resolveConflicts(
    conflicts: SyncConflict[], 
    strategy?: ConflictStrategy
  ): Promise<SyncResult> {
    const startTime = Date.now();
    const allErrors: SyncError[] = [];
    let resolved = 0;
    let failed = 0;

    console.log(`[ConflictResolver] Resolving ${conflicts.length} conflicts`);

    for (const conflict of conflicts) {
      try {
        const resolution = await this.resolveConflict(conflict, strategy);
        
        if (resolution.resolved) {
          resolved++;
          
          // Apply the resolved data
          await this.applyResolvedData(conflict.table, resolution.resolvedData);
        } else {
          failed++;
        }
        
        allErrors.push(...resolution.errors);

      } catch (error) {
        console.error(`[ConflictResolver] Error resolving conflict ${conflict.id}:`, error);
        failed++;
        
        allErrors.push({
          id: `conflict-batch-error-${Date.now()}-${conflict.id}`,
          timestamp: Date.now(),
          operation: `resolve conflict ${conflict.id}`,
          category: 'conflict',
          severity: 'high',
          message: `Conflict resolution failed: ${error.message}`,
          originalError: error,
          recoverable: true
        });
      }
    }

    return {
      success: failed === 0,
      itemsProcessed: conflicts.length,
      itemsSucceeded: resolved,
      itemsFailed: failed,
      errors: allErrors,
      duration: Date.now() - startTime
    };
  }

  /**
   * Get pending manual conflicts
   */
  async getPendingManualConflicts(): Promise<SyncConflict[]> {
    try {
      const db = await getDatabase();
      
      const conflicts = db.prepare(`
        SELECT * FROM sync_conflicts 
        WHERE organization_id = (
          SELECT id FROM organizations WHERE external_id = ?
        ) AND status = 'pending'
        ORDER BY created_at ASC
      `).all(this.organizationId);

      return conflicts.map(row => JSON.parse(row.conflict_data));

    } catch (error) {
      console.error('[ConflictResolver] Error getting pending conflicts:', error);
      return [];
    }
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Compare two items to detect conflicts
   */
  private compareItems(
    table: string, 
    id: string, 
    localItem: any, 
    remoteItem: any
  ): SyncConflict | null {
    // Get timestamps for comparison
    const localTimestamp = new Date(localItem.updated_at || localItem.created_at).getTime();
    const remoteTimestamp = new Date(remoteItem.updated_at || remoteItem.created_at).getTime();

    // Find field-level conflicts
    const fieldConflicts = this.findFieldConflicts(localItem, remoteItem);

    // Only consider it a conflict if there are actual field differences
    // and both items have been modified recently (within a reasonable window)
    if (fieldConflicts.length === 0) {
      return null; // No actual conflicts
    }

    const timeDifference = Math.abs(localTimestamp - remoteTimestamp);
    const conflictWindow = 5 * 60 * 1000; // 5 minutes

    if (timeDifference > conflictWindow) {
      return null; // Changes are far apart, not a conflict
    }

    return {
      id: `conflict-${table}-${id}-${Date.now()}`,
      table,
      recordId: id,
      conflictType: 'update',
      localVersion: localItem,
      remoteVersion: remoteItem,
      localTimestamp,
      remoteTimestamp,
      fieldConflicts
    };
  }

  /**
   * Find fields that differ between local and remote versions
   */
  private findFieldConflicts(localItem: any, remoteItem: any): string[] {
    const conflicts: string[] = [];
    const fieldsToCheck = ['name', 'description', 'hex', 'code', 'display_name', 'metadata'];

    for (const field of fieldsToCheck) {
      if (localItem[field] !== remoteItem[field]) {
        // Handle JSON fields specially
        if (field === 'metadata' && localItem[field] && remoteItem[field]) {
          try {
            const localMeta = typeof localItem[field] === 'string' 
              ? JSON.parse(localItem[field]) 
              : localItem[field];
            const remoteMeta = typeof remoteItem[field] === 'string' 
              ? JSON.parse(remoteItem[field]) 
              : remoteItem[field];
            
            if (JSON.stringify(localMeta) !== JSON.stringify(remoteMeta)) {
              conflicts.push(field);
            }
          } catch {
            conflicts.push(field);
          }
        } else {
          conflicts.push(field);
        }
      }
    }

    return conflicts;
  }

  /**
   * Resolve conflict using last-write-wins strategy
   */
  private resolveLastWriteWins(conflict: SyncConflict): any {
    if (conflict.remoteTimestamp > conflict.localTimestamp) {
      console.log(`[ConflictResolver] Remote version wins for ${conflict.recordId}`);
      return conflict.remoteVersion;
    } else {
      console.log(`[ConflictResolver] Local version wins for ${conflict.recordId}`);
      return conflict.localVersion;
    }
  }

  /**
   * Resolve conflict using field-level merge strategy
   */
  private resolveFieldLevelMerge(conflict: SyncConflict): any {
    const merged = { ...conflict.localVersion };

    // For each conflicting field, choose the newer value
    for (const field of conflict.fieldConflicts) {
      if (conflict.remoteTimestamp > conflict.localTimestamp) {
        merged[field] = conflict.remoteVersion[field];
      }
      // Local value is already in merged object
    }

    // Always use the newer timestamp
    merged.updated_at = conflict.remoteTimestamp > conflict.localTimestamp 
      ? conflict.remoteVersion.updated_at 
      : conflict.localVersion.updated_at;

    console.log(`[ConflictResolver] Field-level merge completed for ${conflict.recordId}`);
    return merged;
  }

  /**
   * Store conflict for manual resolution
   */
  private async storeConflictForManualResolution(conflict: SyncConflict): Promise<void> {
    try {
      const db = await getDatabase();
      
      const stmt = db.prepare(`
        INSERT OR REPLACE INTO sync_conflicts (
          conflict_id, table_name, record_id, conflict_data,
          organization_id, user_id, status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const orgId = db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `).get(this.organizationId)?.id;

      stmt.run(
        conflict.id,
        conflict.table,
        conflict.recordId,
        JSON.stringify(conflict),
        orgId,
        this.userId,
        'pending',
        new Date().toISOString()
      );

      console.log(`[ConflictResolver] Stored conflict for manual resolution: ${conflict.id}`);

    } catch (error) {
      console.error('[ConflictResolver] Error storing conflict:', error);
      throw error;
    }
  }

  /**
   * Apply resolved data to the appropriate storage
   */
  private async applyResolvedData(table: string, resolvedData: any): Promise<void> {
    try {
      // Apply to local database
      await this.applyToLocalDatabase(table, resolvedData);
      
      // Apply to remote database
      await this.applyToRemoteDatabase(table, resolvedData);

      console.log(`[ConflictResolver] Applied resolved data for ${table}:${resolvedData.external_id}`);

    } catch (error) {
      console.error('[ConflictResolver] Error applying resolved data:', error);
      throw error;
    }
  }

  /**
   * Apply resolved data to local database
   */
  private async applyToLocalDatabase(table: string, data: any): Promise<void> {
    const db = await getDatabase();
    
    // This is a simplified implementation - in practice, you'd want
    // table-specific update logic
    const fields = Object.keys(data).filter(key => key !== 'id');
    const placeholders = fields.map(() => '?').join(', ');
    const updates = fields.map(field => `${field} = ?`).join(', ');
    
    const stmt = db.prepare(`
      UPDATE ${table} SET ${updates} 
      WHERE external_id = ?
    `);
    
    const values = fields.map(field => data[field]);
    values.push(data.external_id);
    
    stmt.run(...values);
  }

  /**
   * Apply resolved data to remote database
   */
  private async applyToRemoteDatabase(table: string, data: any): Promise<void> {
    const supabase = getSupabaseClient();
    
    const { error } = await supabase
      .from(table)
      .upsert(data, { onConflict: 'external_id' });
    
    if (error) {
      throw new Error(`Failed to apply to remote: ${error.message}`);
    }
  }
}
