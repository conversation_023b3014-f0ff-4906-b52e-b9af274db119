-- Fix for R<PERSON> infinite recursion in organization_members table
-- This script creates security definer functions to avoid policy recursion

-- ========================================
-- 1. Create Security Definer Functions
-- ========================================

-- Function to check if user is member of organization (bypasses RLS)
CREATE OR REPLACE FUNCTION is_organization_member(
    p_user_id UUID,
    p_organization_id UUID
) RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM organization_members 
        WHERE user_id = p_user_id 
        AND organization_id = p_organization_id
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin or owner of organization
CREATE OR REPLACE FUNCTION is_organization_admin(
    p_user_id UUID,
    p_organization_id UUID
) RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM organization_members 
        WHERE user_id = p_user_id 
        AND organization_id = p_organization_id
        AND role IN ('owner', 'admin')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's organizations (bypasses RLS)
CREATE OR REPLACE FUNCTION get_user_organizations(p_user_id UUID)
RETURNS TABLE (organization_id UUID) AS $$
BEGIN
    RETURN QUERY
    SELECT om.organization_id 
    FROM organization_members om
    WHERE om.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's role in organization
CREATE OR REPLACE FUNCTION get_user_organization_role(
    p_user_id UUID,
    p_organization_id UUID
) RETURNS TEXT AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role
    FROM organization_members
    WHERE user_id = p_user_id 
    AND organization_id = p_organization_id;
    
    RETURN user_role;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ========================================
-- 2. Drop Existing Problematic Policies
-- ========================================

-- Drop all existing policies that cause recursion
DROP POLICY IF EXISTS "Users can view their organizations" ON organizations;
DROP POLICY IF EXISTS "Organization owners can update organization" ON organizations;
DROP POLICY IF EXISTS "Users can view organization members" ON organization_members;
DROP POLICY IF EXISTS "Admins and owners can manage members" ON organization_members;
DROP POLICY IF EXISTS "Organization admins can manage invitations" ON organization_invitations;

-- Drop product and color policies to recreate with functions
DROP POLICY IF EXISTS "Organization members can view products" ON products;
DROP POLICY IF EXISTS "Organization members can create products" ON products;
DROP POLICY IF EXISTS "Organization members can update products" ON products;
DROP POLICY IF EXISTS "Organization members can delete products" ON products;

DROP POLICY IF EXISTS "Organization members can view colors" ON colors;
DROP POLICY IF EXISTS "Organization members can create colors" ON colors;
DROP POLICY IF EXISTS "Organization members can update colors" ON colors;
DROP POLICY IF EXISTS "Organization members can delete colors" ON colors;

DROP POLICY IF EXISTS "Organization members can manage product colors" ON product_colors;
DROP POLICY IF EXISTS "Organization members can manage sync data" ON sync_metadata;

-- ========================================
-- 3. Create New Policies Using Security Definer Functions
-- ========================================

-- Organizations policies
CREATE POLICY "Users can view their organizations" ON organizations
    FOR SELECT TO authenticated
    USING (
        is_organization_member(auth.uid(), id)
    );

CREATE POLICY "Organization owners can update organization" ON organizations
    FOR UPDATE TO authenticated
    USING (
        get_user_organization_role(auth.uid(), id) = 'owner'
    );

-- Organization members policies (simplified to avoid recursion)
CREATE POLICY "Users can view members of their organizations" ON organization_members
    FOR SELECT TO authenticated
    USING (
        is_organization_member(auth.uid(), organization_id)
    );

CREATE POLICY "Admins can manage organization members" ON organization_members
    FOR INSERT TO authenticated
    WITH CHECK (
        is_organization_admin(auth.uid(), organization_id)
    );

CREATE POLICY "Admins can update organization members" ON organization_members
    FOR UPDATE TO authenticated
    USING (
        is_organization_admin(auth.uid(), organization_id)
        AND user_id != auth.uid() -- Cannot update own role
    );

CREATE POLICY "Admins can remove organization members" ON organization_members
    FOR DELETE TO authenticated
    USING (
        is_organization_admin(auth.uid(), organization_id)
        AND user_id != auth.uid() -- Cannot remove self
    );

-- Organization invitations policies
CREATE POLICY "Organization admins can view invitations" ON organization_invitations
    FOR SELECT TO authenticated
    USING (
        is_organization_admin(auth.uid(), organization_id)
    );

CREATE POLICY "Organization admins can create invitations" ON organization_invitations
    FOR INSERT TO authenticated
    WITH CHECK (
        is_organization_admin(auth.uid(), organization_id)
    );

CREATE POLICY "Organization admins can update invitations" ON organization_invitations
    FOR UPDATE TO authenticated
    USING (
        is_organization_admin(auth.uid(), organization_id)
    );

CREATE POLICY "Organization admins can delete invitations" ON organization_invitations
    FOR DELETE TO authenticated
    USING (
        is_organization_admin(auth.uid(), organization_id)
    );

-- Products policies
CREATE POLICY "Organization members can view products" ON products
    FOR SELECT TO authenticated
    USING (
        is_organization_member(auth.uid(), organization_id)
        OR (organization_id IS NULL AND user_id = auth.uid()) -- Legacy data
    );

CREATE POLICY "Organization members can create products" ON products
    FOR INSERT TO authenticated
    WITH CHECK (
        is_organization_member(auth.uid(), organization_id)
    );

CREATE POLICY "Organization members can update products" ON products
    FOR UPDATE TO authenticated
    USING (
        is_organization_member(auth.uid(), organization_id)
        OR (organization_id IS NULL AND user_id = auth.uid()) -- Legacy data
    );

CREATE POLICY "Organization members can delete products" ON products
    FOR DELETE TO authenticated
    USING (
        is_organization_member(auth.uid(), organization_id)
        OR (organization_id IS NULL AND user_id = auth.uid()) -- Legacy data
    );

-- Colors policies
CREATE POLICY "Organization members can view colors" ON colors
    FOR SELECT TO authenticated
    USING (
        is_organization_member(auth.uid(), organization_id)
        OR (organization_id IS NULL AND user_id = auth.uid()) -- Legacy data
    );

CREATE POLICY "Organization members can create colors" ON colors
    FOR INSERT TO authenticated
    WITH CHECK (
        is_organization_member(auth.uid(), organization_id)
    );

CREATE POLICY "Organization members can update colors" ON colors
    FOR UPDATE TO authenticated
    USING (
        is_organization_member(auth.uid(), organization_id)
        OR (organization_id IS NULL AND user_id = auth.uid()) -- Legacy data
    );

CREATE POLICY "Organization members can delete colors" ON colors
    FOR DELETE TO authenticated
    USING (
        is_organization_member(auth.uid(), organization_id)
        OR (organization_id IS NULL AND user_id = auth.uid()) -- Legacy data
    );

-- Product colors policies
CREATE POLICY "Organization members can view product colors" ON product_colors
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM products p 
            WHERE p.id = product_colors.product_id
            AND (
                is_organization_member(auth.uid(), p.organization_id)
                OR (p.organization_id IS NULL AND p.user_id = auth.uid())
            )
        )
    );

CREATE POLICY "Organization members can manage product colors" ON product_colors
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM products p 
            WHERE p.id = product_colors.product_id
            AND (
                is_organization_member(auth.uid(), p.organization_id)
                OR (p.organization_id IS NULL AND p.user_id = auth.uid())
            )
        )
    );

-- Sync metadata policies
CREATE POLICY "Organization members can view sync data" ON sync_metadata
    FOR SELECT TO authenticated
    USING (
        is_organization_member(auth.uid(), organization_id)
        OR (organization_id IS NULL AND user_id = auth.uid()) -- Legacy data
    );

CREATE POLICY "Organization members can manage sync data" ON sync_metadata
    FOR ALL TO authenticated
    USING (
        is_organization_member(auth.uid(), organization_id)
        OR (organization_id IS NULL AND user_id = auth.uid()) -- Legacy data
    );

-- ========================================
-- 4. Grant Execute Permissions
-- ========================================

GRANT EXECUTE ON FUNCTION is_organization_member(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION is_organization_admin(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_organizations(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_organization_role(UUID, UUID) TO authenticated;

-- ========================================
-- 5. Test Function
-- ========================================

-- Function to test if policies are working without recursion
CREATE OR REPLACE FUNCTION test_organization_access(p_user_id UUID)
RETURNS TABLE (
    test_name TEXT,
    test_result BOOLEAN,
    error_message TEXT
) AS $$
DECLARE
    test_org_id UUID;
BEGIN
    -- Get a test organization for the user
    SELECT organization_id INTO test_org_id
    FROM organization_members
    WHERE user_id = p_user_id
    LIMIT 1;
    
    -- Test 1: Can view organizations
    RETURN QUERY
    SELECT 
        'Can view organizations'::TEXT,
        EXISTS (
            SELECT 1 FROM organizations 
            WHERE is_organization_member(p_user_id, id)
        ),
        NULL::TEXT;
    
    -- Test 2: Can view organization members
    IF test_org_id IS NOT NULL THEN
        RETURN QUERY
        SELECT 
            'Can view organization members'::TEXT,
            EXISTS (
                SELECT 1 FROM organization_members 
                WHERE organization_id = test_org_id
            ),
            NULL::TEXT;
    END IF;
    
    RETURN;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 6. Add helpful comments
-- ========================================

COMMENT ON FUNCTION is_organization_member IS 'Security definer function to check organization membership without RLS recursion';
COMMENT ON FUNCTION is_organization_admin IS 'Security definer function to check admin/owner status without RLS recursion';
COMMENT ON FUNCTION get_user_organizations IS 'Security definer function to get user organizations without RLS recursion';
COMMENT ON FUNCTION get_user_organization_role IS 'Security definer function to get user role in organization without RLS recursion';

-- ========================================
-- Migration complete
-- ========================================

-- Verify the fix
-- Run this to check if policies are working:
-- SELECT * FROM test_organization_access(auth.uid());
