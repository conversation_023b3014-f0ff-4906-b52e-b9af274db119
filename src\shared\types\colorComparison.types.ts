/**
 * @file colorComparison.types.ts
 * @description Type definitions for color comparison feature
 */


export type HarmonyType = 
  | 'complementary'
  | 'analogous'
  | 'triadic'
  | 'tetradic'
  | 'splitComplementary'
  | 'monochromatic'
  | 'shades'
  | 'tints'
  | 'compound';

export interface HarmonyOptions {
  count?: number;
  angle?: number;
}

export interface RGB {
  r: number;
  g: number;
  b: number;
}

export interface HSL {
  h: number;
  s: number;
  l: number;
}

export interface HSV {
  h: number;
  s: number;
  v: number;
}

export interface CMYK {
  c: number;
  m: number;
  y: number;
  k: number;
}

export interface ColorComparisonResult {
  contrastRatio: number;
  wcagLevel: string;
  deltaE: number;
  harmonyScore: number;
} 