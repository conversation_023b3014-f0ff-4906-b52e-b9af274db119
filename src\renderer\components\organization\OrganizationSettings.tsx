                </div>

                <div className="pt-4">
                  <button
                    onClick={handleSaveGeneral}
                    disabled={!isDirty || isLoading}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                      !isDirty || isLoading
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : 'bg-blue-500 text-white hover:bg-blue-600'
                    }`}
                  >
                    {isLoading ? (
                      <span className="flex items-center">
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Saving...
                      </span>
                    ) : (
                      'Save Changes'
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        );

      case 'members':
        return <TeamSettings />;

      case 'security':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Security Settings</h3>
            
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Two-Factor Authentication</h4>
              <p className="text-sm text-gray-600 mb-3">
                Require all members to enable 2FA for enhanced security.
              </p>
              <button className="text-sm text-blue-600 hover:text-blue-700">
                Configure 2FA Requirements
              </button>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Allowed Email Domains</h4>
              <p className="text-sm text-gray-600 mb-3">
                Restrict membership to specific email domains.
              </p>
              <button className="text-sm text-blue-600 hover:text-blue-700">
                Manage Allowed Domains
              </button>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">API Access</h4>
              <p className="text-sm text-gray-600 mb-3">
                Manage API keys and access tokens for integrations.
              </p>
              <button className="text-sm text-blue-600 hover:text-blue-700">
                Manage API Keys
              </button>
            </div>
          </div>
        );

      case 'danger':
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-red-600 mb-4">Danger Zone</h3>
            
            <div className="border border-red-200 rounded-lg p-4 bg-red-50">
              <h4 className="font-medium text-red-900 mb-2">Delete Organization</h4>
              <p className="text-sm text-red-700 mb-4">
                Once you delete an organization, there is no going back. All data including colors, 
                products, and team member associations will be permanently deleted.
              </p>
              <button
                onClick={handleDeleteOrganization}
                disabled={isLoading}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  isLoading
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-red-600 text-white hover:bg-red-700'
                }`}
              >
                {isLoading ? (
                  <span className="flex items-center">
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Deleting...
                  </span>
                ) : (
                  'Delete This Organization'
                )}
              </button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="border-b px-6 py-4">
        <div className="flex items-center">
          <Building2 className="w-6 h-6 text-gray-700 mr-3" />
          <h1 className="text-2xl font-semibold text-gray-900">
            {currentOrganization.name} Settings
          </h1>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b">
        <div className="flex space-x-8 px-6">
          {tabs.map((tab) => (
            <button
              key={tab.value}
              onClick={() => setActiveTab(tab.value)}
              className={`py-3 px-1 border-b-2 flex items-center space-x-2 transition-colors ${
                activeTab === tab.value
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.icon}
              <span className="font-medium">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-6">
        {renderTabContent()}
      </div>
    </div>
  );
};
