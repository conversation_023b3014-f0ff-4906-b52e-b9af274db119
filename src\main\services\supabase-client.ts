/**
 * @file supabase-client.ts - Updated with PKCE and secure storage
 * @description Supabase client configuration for ChromaSync with PKCE flow and custom secure storage
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { safeStorage } from 'electron';
import Store from 'electron-store';

const store = new Store();

// Custom secure storage adapter using Electron's safeStorage
const customSecureStorage = {
  getItem: async (key: string): Promise<string | null> => {
    try {
      const encrypted = store.get(key) as string;
      if (!encrypted) {return null;}
      
      // Only decrypt if encryption is available
      if (safeStorage.isEncryptionAvailable()) {
        const decrypted = safeStorage.decryptString(Buffer.from(encrypted, 'base64'));
        return decrypted;
      }
      
      // Fallback to plain storage if encryption not available
      return encrypted;
    } catch (error) {
      console.error('[Storage] Failed to get item:', error);
      return null;
    }
  },
  
  setItem: async (key: string, value: string): Promise<void> => {
    try {
      // Only encrypt if encryption is available
      if (safeStorage.isEncryptionAvailable()) {
        const encrypted = safeStorage.encryptString(value);
        store.set(key, encrypted.toString('base64'));
      } else {
        // Fallback to plain storage
        store.set(key, value);
      }
    } catch (error) {
      console.error('[Storage] Failed to set item:', error);
    }
  },
  
  removeItem: async (key: string): Promise<void> => {
    store.delete(key);
  }
};

let supabaseClient: SupabaseClient | null = null;

export function getSupabaseClient(): SupabaseClient {
  if (!supabaseClient) {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
    
    console.log('[Supabase] Environment check:', {
      hasUrl: !!supabaseUrl,
      hasKey: !!supabaseAnonKey,
      url: supabaseUrl?.substring(0, 30) + '...',
      encryptionAvailable: safeStorage.isEncryptionAvailable()
    });
    
    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error('Supabase configuration missing. Please set SUPABASE_URL and SUPABASE_ANON_KEY in your .env file');
    }
    
    // Supabase client with PKCE and secure storage
    supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: false, // Let custom storage handle persistence
        detectSessionInUrl: false, // Manual handling for Electron
        flowType: 'pkce', // Enable PKCE flow
        storage: customSecureStorage, // Use our secure storage adapter
        storageKey: 'chromasync-auth-session' // Custom storage key
      }
    });
    
    console.log('[Supabase] Client created with PKCE flow and secure storage');
    
    // Set up auth state change listener
    supabaseClient.auth.onAuthStateChange((event, session) => {
      console.log('[Supabase] Auth state changed:', event, session?.user?.email);
    });
  }
  
  return supabaseClient;
}

export function closeSupabaseClient(): void {
  if (supabaseClient) {
    // Clean up any subscriptions
    supabaseClient.removeAllChannels();
    supabaseClient = null;
  }
}
