import { useEffect, useCallback } from 'react';

interface KeyboardShortcut {
  key: string;
  ctrl?: boolean;
  shift?: boolean;
  alt?: boolean;
  meta?: boolean;
  action: () => void;
  description: string;
}

export function useGlobalKeyboardNavigation() {

  // Define global keyboard shortcuts
  const shortcuts: KeyboardShortcut[] = [
    {
      key: '/',
      ctrl: true,
      action: () => {
        // Focus search bar
        const searchInput = document.querySelector('input[type="search"]') as HTMLInputElement;
        searchInput?.focus();
      },
      description: 'Focus search bar'
    },
    {
      key: 'n',
      ctrl: true,
      action: () => {
        // Trigger new color dialog
        const newColorButton = document.querySelector('[data-testid="new-color-button"]') as HTMLButtonElement;
        newColorButton?.click();
      },
      description: 'Create new color'
    },
    {
      key: 'p',
      ctrl: true,
      action: () => {
        // Switch to products tab
        const productsTab = document.querySelector('[data-testid="view-tab-products"]') as HTMLButtonElement;
        productsTab?.click();
      },
      description: 'Go to products'
    },
    {
      key: 'h',
      ctrl: true,
      action: () => {
        // Switch to table view (home)
        const tableTab = document.querySelector('[data-testid="view-tab-table"]') as HTMLButtonElement;
        tableTab?.click();
      },
      description: 'Go to home'
    },
    {
      key: 'Escape',
      action: () => {
        // Close any open modal
        const closeButton = document.querySelector('[data-testid="modal-close"]') as HTMLButtonElement;
        closeButton?.click();
      },
      description: 'Close modal/dialog'
    },
    {
      key: 'Tab',
      action: () => {
        // Default tab behavior - handled by browser
      },
      description: 'Navigate to next focusable element'
    },
    {
      key: 'Tab',
      shift: true,
      action: () => {
        // Default shift+tab behavior - handled by browser
      },
      description: 'Navigate to previous focusable element'
    },
    {
      key: 'Enter',
      action: () => {
        // Activate focused element - handled by browser
      },
      description: 'Activate focused element'
    },
    {
      key: ' ',
      action: () => {
        // Space to activate buttons/checkboxes - handled by browser
      },
      description: 'Activate button or toggle checkbox'
    },
    {
      key: 'ArrowUp',
      action: () => {
        // Navigate up in lists
        const focused = document.activeElement;
        if (focused?.getAttribute('role') === 'row') {
          const prev = focused.previousElementSibling as HTMLElement;
          prev?.focus();
        }
      },
      description: 'Navigate up in lists'
    },
    {
      key: 'ArrowDown',
      action: () => {
        // Navigate down in lists
        const focused = document.activeElement;
        if (focused?.getAttribute('role') === 'row') {
          const next = focused.nextElementSibling as HTMLElement;
          next?.focus();
        }
      },
      description: 'Navigate down in lists'
    },
    {
      key: '?',
      shift: true,
      action: () => {
        // Show keyboard shortcuts help
        const helpButton = document.querySelector('[data-testid="keyboard-help"]') as HTMLButtonElement;
        helpButton?.click();
      },
      description: 'Show keyboard shortcuts'
    }
  ];

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    // Find matching shortcut
    const shortcut = shortcuts.find(s => {
      const keyMatch = s.key.toLowerCase() === e.key.toLowerCase();
      const ctrlMatch = s.ctrl ? (e.ctrlKey || e.metaKey) : true;
      const shiftMatch = s.shift ? e.shiftKey : !e.shiftKey;
      const altMatch = s.alt ? e.altKey : !e.altKey;
      const metaMatch = s.meta ? e.metaKey : true;

      return keyMatch && ctrlMatch && shiftMatch && altMatch && metaMatch;
    });

    if (shortcut) {
      // Don't prevent default for Tab, Shift+Tab, Enter, Space, and arrow keys
      const allowDefaultKeys = ['Tab', 'Enter', ' ', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];
      if (!allowDefaultKeys.includes(e.key) || (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
      }
      
      shortcut.action();
    }

    // Enable keyboard navigation for tables
    const isInTable = document.activeElement?.closest('table[role="table"]');
    if (isInTable) {
      handleTableNavigation(e);
    }
  }, []);

  // Handle table navigation
  const handleTableNavigation = (e: KeyboardEvent) => {
    const focused = document.activeElement as HTMLElement;
    const row = focused.closest('tr[role="row"]') as HTMLElement;
    
    if (!row) {
      return;
    }

    switch (e.key) {
      case 'ArrowUp': {
        e.preventDefault();
        const prevRow = row.previousElementSibling as HTMLElement;
        if (prevRow) {
          const focusableElement = prevRow.querySelector('button, a, input, [tabindex="0"]') as HTMLElement;
          focusableElement?.focus();
        }
        break;
      }
        
      case 'ArrowDown': {
        e.preventDefault();
        const nextRow = row.nextElementSibling as HTMLElement;
        if (nextRow) {
          const focusableElement = nextRow.querySelector('button, a, input, [tabindex="0"]') as HTMLElement;
          focusableElement?.focus();
        }
        break;
      }
        
      case 'Home':
        if (e.ctrlKey) {
          e.preventDefault();
          const firstRow = row.parentElement?.firstElementChild as HTMLElement;
          const focusableElement = firstRow?.querySelector('button, a, input, [tabindex="0"]') as HTMLElement;
          focusableElement?.focus();
        }
        break;
        
      case 'End':
        if (e.ctrlKey) {
          e.preventDefault();
          const lastRow = row.parentElement?.lastElementChild as HTMLElement;
          const focusableElement = lastRow?.querySelector('button, a, input, [tabindex="0"]') as HTMLElement;
          focusableElement?.focus();
        }
        break;
    }
  };

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    
    // Add focus visible class for keyboard navigation
    document.body.classList.add('keyboard-navigation');
    
    // Set up focus trap for modals
    const handleFocusTrap = (e: KeyboardEvent) => {
      const modal = document.querySelector('[role="dialog"]') as HTMLElement;
      if (!modal) {
        return;
      }

      const focusableElements = modal.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      const firstElement = focusableElements[0] as HTMLElement;
      const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement?.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement?.focus();
          }
        }
      }
    };

    window.addEventListener('keydown', handleFocusTrap);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keydown', handleFocusTrap);
    };
  }, [handleKeyDown]);

  return { shortcuts };
}