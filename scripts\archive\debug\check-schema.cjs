const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Find database
function findDatabase() {
  const dbPath = path.join(
    process.env.HOME || process.env.USERPROFILE,
    'Library',
    'Application Support',
    'chroma-sync',
    'chromasync.db'
  );
  
  if (fs.existsSync(dbPath)) {
    return dbPath;
  }
  throw new Error('Database not found');
}

try {
  const dbPath = findDatabase();
  const db = new Database(dbPath, { readonly: true });
  
  // Get table info
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
  console.log('Tables:', tables.map(t => t.name).join(', '));
  
  // Get colors table schema
  console.log('\nColors table schema:');
  const colorSchema = db.prepare("PRAGMA table_info(colors)").all();
  colorSchema.forEach(col => {
    console.log(`  ${col.name} (${col.type})`);
  });
  
  // Get first few colors to see structure
  console.log('\nSample colors:');
  const sampleColors = db.prepare("SELECT * FROM colors LIMIT 3").all();
  console.log(sampleColors);
  
  db.close();
} catch (error) {
  console.error('Error:', error);
}