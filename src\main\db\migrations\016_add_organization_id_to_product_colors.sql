-- Add organization_id column to product_colors table
-- This ensures product-color relationships are properly scoped to organizations

BEGIN TRANSACTION;

-- Check if organization_id column already exists
PRAGMA table_info(product_colors);

-- Add organization_id column if it doesn't exist
-- Note: SQLite doesn't support ADD COLUMN with NOT NULL and REFERENCES in one step
-- So we need to recreate the table

-- Step 1: Create new table with the correct schema
CREATE TABLE IF NOT EXISTS product_colors_new (
  product_id INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  color_id INTEGER NOT NULL REFERENCES colors(id) ON DELETE CASCADE,
  display_order INTEGER NOT NULL DEFAULT 0,
  organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  added_at TEXT DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (product_id, color_id)
);

-- Step 2: Copy existing data (if any exists)
-- We need to determine the organization_id from the product
INSERT INTO product_colors_new (product_id, color_id, display_order, organization_id, added_at)
SELECT 
  pc.product_id, 
  pc.color_id, 
  pc.display_order,
  COALESCE(p.organization_id, 1) as organization_id, -- Use product's org_id, fallback to 1
  pc.added_at
FROM product_colors pc
LEFT JOIN products p ON pc.product_id = p.id
WHERE EXISTS (SELECT 1 FROM product_colors LIMIT 1); -- Only if old table has data

-- Step 3: Drop old table
DROP TABLE IF EXISTS product_colors;

-- Step 4: Rename new table
ALTER TABLE product_colors_new RENAME TO product_colors;

-- Step 5: Recreate indexes
CREATE INDEX IF NOT EXISTS idx_product_colors_product ON product_colors(product_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_color ON product_colors(color_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_org ON product_colors(organization_id);

COMMIT;