-- Fix product_colors table to ensure proper syncing
-- Run this in Supabase SQL Editor

-- First, check if the product_colors table exists and has the right structure
DO $$
BEGIN
    -- Check if product_colors table exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'product_colors') THEN
        RAISE NOTICE 'Creating product_colors table...';
        
        CREATE TABLE public.product_colors (
            product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
            color_id INTEGER REFERENCES colors(id) ON DELETE CASCADE,
            display_order INTEGER DEFAULT 0,
            PRIMARY KEY (product_id, color_id)
        );
        
        -- Create indexes for better performance
        CREATE INDEX idx_product_colors_product ON product_colors(product_id);
        CREATE INDEX idx_product_colors_color ON product_colors(color_id);
    ELSE
        RAISE NOTICE 'product_colors table already exists';
    END IF;
END $$;

-- Enable Row Level Security
ALTER TABLE product_colors ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if any
DROP POLICY IF EXISTS "Users can view their own product-color relationships" ON product_colors;
DROP POLICY IF EXISTS "Users can insert their own product-color relationships" ON product_colors;
DROP POLICY IF EXISTS "Users can update their own product-color relationships" ON product_colors;
DROP POLICY IF EXISTS "Users can delete their own product-color relationships" ON product_colors;

-- Create RLS policies for product_colors
-- Users can only see product-color relationships for their own products
CREATE POLICY "Users can view their own product-color relationships" ON product_colors
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM products p 
            WHERE p.id = product_colors.product_id 
            AND p.user_id = auth.uid()::text
        )
    );

-- Users can only insert product-color relationships for their own products
CREATE POLICY "Users can insert their own product-color relationships" ON product_colors
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM products p 
            WHERE p.id = product_colors.product_id 
            AND p.user_id = auth.uid()::text
        )
    );

-- Users can only update product-color relationships for their own products
CREATE POLICY "Users can update their own product-color relationships" ON product_colors
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM products p 
            WHERE p.id = product_colors.product_id 
            AND p.user_id = auth.uid()::text
        )
    );

-- Users can only delete product-color relationships for their own products
CREATE POLICY "Users can delete their own product-color relationships" ON product_colors
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM products p 
            WHERE p.id = product_colors.product_id 
            AND p.user_id = auth.uid()::text
        )
    );

-- Verify the table structure
SELECT 
    'product_colors table columns:' as info,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_name = 'product_colors'
ORDER BY ordinal_position;

-- Check if there are any existing relationships
SELECT 
    'Existing product_colors count:' as info,
    COUNT(*) as count
FROM product_colors;

-- Check a sample of data if any exists
SELECT 
    'Sample product_colors data:' as info,
    pc.*,
    p.name as product_name,
    c.display_name as color_name
FROM product_colors pc
JOIN products p ON pc.product_id = p.id
JOIN colors c ON pc.color_id = c.id
LIMIT 5;