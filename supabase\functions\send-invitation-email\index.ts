import { serve } from "https://deno.land/std@0.220.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('📧 ChromaSync Invitation Email Function Called')
    
    // Parse request body
    const { to, organizationName, inviterName, role, token, expiresAt } = await req.json()
    
    console.log('📨 Sending invitation email:', {
      to,
      organizationName,
      inviterName,
      role,
      tokenLength: token?.length
    })
    
    // Get Brevo API key from environment (or use default)
    const brevoApiKey = Deno.env.get('BREVO_API_KEY') || '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'
    
    // Create invitation URL
    const invitationUrl = `chromasync://invite/${token}`
    
    // Create professional HTML email
    const emailHtml = `<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChromaSync Team Invitation</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
            line-height: 1.6; 
            color: #374151; 
            margin: 0; 
            padding: 0; 
            background-color: #f9fafb; 
        }
        .container { 
            max-width: 600px; 
            margin: 20px auto; 
            background: #ffffff; 
            border-radius: 12px; 
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }
        .header { 
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white; 
            padding: 40px 40px 30px; 
            text-align: center; 
        }
        .logo { 
            font-size: 28px; 
            font-weight: bold; 
            margin-bottom: 8px; 
        }
        .title { 
            font-size: 20px; 
            margin: 0;
            opacity: 0.95;
        }
        .content {
            padding: 40px;
        }
        .invitation-box { 
            background: #f8fafc; 
            border: 1px solid #e2e8f0; 
            border-radius: 8px; 
            padding: 30px; 
            margin: 24px 0; 
            text-align: center;
        }
        .cta-button { 
            display: inline-block; 
            background: #3b82f6; 
            color: white; 
            padding: 14px 28px; 
            text-decoration: none; 
            border-radius: 8px; 
            font-weight: 600; 
            margin: 20px 0;
            font-size: 16px;
        }
        .details { 
            margin: 24px 0; 
            text-align: left;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f1f5f9;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: 600;
            color: #475569;
        }
        .detail-value {
            color: #1e293b;
        }
        .footer { 
            background: #f8fafc;
            padding: 30px 40px; 
            border-top: 1px solid #e2e8f0; 
            font-size: 14px; 
            color: #64748b; 
            text-align: center;
        }
        .url-box { 
            background: #1e293b; 
            color: #e2e8f0;
            padding: 16px; 
            border-radius: 6px; 
            font-family: monospace; 
            word-break: break-all; 
            margin: 16px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🎨 ChromaSync</div>
            <h1 class="title">Team Invitation</h1>
        </div>
        
        <div class="content">
            <p>Hello!</p>
            
            <p><strong>${inviterName}</strong> has invited you to join the <strong>${organizationName}</strong> team on ChromaSync as a <strong>${role}</strong>.</p>
            
            <div class="invitation-box">
                <h3 style="margin-top: 0; color: #1e293b;">🎯 Invitation Details</h3>
                
                <div class="details">
                    <div class="detail-row">
                        <span class="detail-label">Organization:</span>
                        <span class="detail-value">${organizationName}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Role:</span>
                        <span class="detail-value">${role.charAt(0).toUpperCase() + role.slice(1)}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Invited by:</span>
                        <span class="detail-value">${inviterName}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Expires:</span>
                        <span class="detail-value">${new Date(expiresAt).toLocaleDateString('en-US', { 
                            weekday: 'long', 
                            year: 'numeric', 
                            month: 'long', 
                            day: 'numeric' 
                        })}</span>
                    </div>
                </div>
                
                <p><strong>Click this link to accept the invitation:</strong></p>
                <div class="url-box">${invitationUrl}</div>
                
                <a href="${invitationUrl}" class="cta-button">🚀 Accept Invitation</a>
            </div>
            
            <p style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px; padding: 16px; font-size: 14px;">
                <strong>📱 Note:</strong> This link will open ChromaSync if you have it installed. 
                If you don't have ChromaSync yet, please download and install it first, then click the invitation link.
            </p>
        </div>
        
        <div class="footer">
            <p><strong>⏰ This invitation expires on ${new Date(expiresAt).toLocaleDateString()}</strong></p>
            <p>If you have any questions, please contact ${inviterName} or your team administrator.</p>
            <p>© ${new Date().getFullYear()} ChromaSync - Professional Color Management</p>
        </div>
    </div>
</body>
</html>`

    // Send email via Brevo REST API
    console.log('🔑 Using Brevo API to send email...')
    
    const emailResponse = await fetch('https://api.brevo.com/v3/smtp/email', {
      method: 'POST',
      headers: {
        'accept': 'application/json',
        'api-key': brevoApiKey,
        'content-type': 'application/json'
      },
      body: JSON.stringify({
        sender: {
          name: 'ChromaSync Team',
          email: '<EMAIL>'  // Using verified sender
        },
        to: [{
          email: to,
          name: to.split('@')[0]
        }],
        subject: `🎨 You're invited to join ${organizationName} on ChromaSync`,
        htmlContent: emailHtml
      })
    })

    const result = await emailResponse.json()
    console.log('📧 Brevo API Response:', result)

    if (!emailResponse.ok) {
      throw new Error(`Brevo API error: ${result.message || 'Unknown error'}`)
    }

    console.log('✅ Email sent successfully!')

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: `Invitation email sent successfully to ${to}`,
        details: {
          to,
          organizationName,
          inviterName,
          role,
          timestamp: new Date().toISOString(),
          messageId: result.messageId
        }
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('💥 Error sending invitation email:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false,
        error: `Failed to send email: ${error.message}`,
        timestamp: new Date().toISOString()
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})