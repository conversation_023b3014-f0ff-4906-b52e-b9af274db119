import { defineConfig, externalizeDepsPlugin } from 'electron-vite';
import { resolve } from 'path';

export default defineConfig({
  main: {
    plugins: [
      externalizeDepsPlugin({
        // Don't externalize better-sqlite3, let it be bundled
        exclude: []
      })
    ],
    resolve: {
      // In development, use the native module directly
      conditions: ['node'],
      browserField: false,
      alias: {
        '@': resolve(__dirname, 'src'),
        '@main': resolve(__dirname, 'src/main'),
        '@shared': resolve(__dirname, 'src/shared')
      }
    },
    build: {
      rollupOptions: {
        external: [
          'electron',
          'better-sqlite3'
        ]
      },
    },
  },
  preload: {
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@shared': resolve(__dirname, 'src/shared')
      }
    },
    build: {
      rollupOptions: {
        external: [
          'electron',
          'node-machine-id',
          'axios',
          'uuid',
          'crypto',
          'os'
        ],
        output: {
          format: 'cjs',
          entryFileNames: '[name].js',
          chunkFileNames: '[name].js'
        }
      },
    },
  },
  renderer: {
    // Configuration for renderer process
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@shared': resolve(__dirname, 'src/shared'),
        'shared': resolve(__dirname, 'shared'),
        '@renderer': resolve(__dirname, 'src/renderer'),
        '@components': resolve(__dirname, 'src/renderer/components'),
        '@hooks': resolve(__dirname, 'src/renderer/hooks'),
        '@styles': resolve(__dirname, 'src/renderer/styles'),
      }
    },
    build: {
      rollupOptions: {
        input: {
          index: resolve(__dirname, 'src/renderer/index.html'),
          'color-space-3d': resolve(__dirname, 'src/renderer/color-space-3d.html')
        }
      }
    }
  },
});
