-- ChromaSync Optimized Schema - Production Ready
-- Generated from chromasync-optimized-schema.md

-- Optimal SQLite configuration for production
PRAGMA foreign_keys = ON;
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA temp_store = MEMORY;
PRAGMA mmap_size = 30000000000;
PRAGMA cache_size = -64000;          -- 64MB cache
PRAGMA wal_autocheckpoint = 1000;    -- Checkpoint every 1000 pages

-- =================================================================
-- Core Tables
-- =================================================================

-- 1. Users Table
CREATE TABLE users (
    id TEXT PRIMARY KEY, -- UUID as TEXT in SQLite
    email TEXT NOT NULL UNIQUE,
    name TEXT,
    avatar_url TEXT,
    metadata TEXT DEFAULT '{}', -- J<PERSON><PERSON> stored as TEXT
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_users_email ON users(email);

-- Update trigger for updated_at
CREATE TRIGGER update_users_updated_at
AFTER UPDATE ON users
FOR EACH ROW
BEGIN
    UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 2. Products Table
CREATE TABLE products (
    id INTEGER PRIMARY KEY,
    external_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL COLLATE NOCASE,
    sku TEXT UNIQUE COLLATE NOCASE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    metadata JSON,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CHECK (length(trim(name)) > 0),
    CHECK (external_id GLOB '[0-9a-f]*-[0-9a-f]*-[0-9a-f]*-[0-9a-f]*-[0-9a-f]*')
);

-- Optimized indexes for products
CREATE INDEX idx_products_active_name ON products(is_active, name) WHERE is_active = TRUE;
CREATE INDEX idx_products_updated ON products(updated_at);

-- Auto-update timestamp trigger for products
CREATE TRIGGER products_update_timestamp 
AFTER UPDATE ON products
FOR EACH ROW
WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE products SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 2. Color Sources Lookup Table
CREATE TABLE color_sources (
    id INTEGER PRIMARY KEY,
    code TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    is_system BOOLEAN NOT NULL DEFAULT FALSE,
    properties JSON
);

-- Pre-populate with standard sources
INSERT INTO color_sources (id, code, name, is_system) VALUES
(1, 'user', 'User Created', FALSE),
(2, 'pantone', 'PANTONE®', TRUE),
(3, 'ral', 'RAL', TRUE),
(4, 'ncs', 'NCS', TRUE);

-- 3. Colors - Main Color Table
CREATE TABLE colors (
    id INTEGER PRIMARY KEY,
    external_id TEXT UNIQUE NOT NULL,
    source_id INTEGER NOT NULL REFERENCES color_sources(id),
    
    -- Identifiers
    code TEXT NOT NULL COLLATE NOCASE,
    display_name TEXT COLLATE NOCASE,
    
    -- Primary color value (always hex)
    hex CHAR(7) NOT NULL,
    
    -- Color characteristics
    is_gradient BOOLEAN NOT NULL DEFAULT FALSE,
    is_metallic BOOLEAN NOT NULL DEFAULT FALSE,
    is_effect BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Metadata
    properties JSON,
    search_terms TEXT,  -- For FTS optimization
    
    -- Audit
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP,
    
    -- Improved hex validation without REGEXP
    CHECK (
        length(hex) = 7 AND 
        substr(hex, 1, 1) = '#' AND
        hex GLOB '#[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]'
    ),
    CHECK (length(trim(code)) > 0),
    UNIQUE (source_id, code)
);

-- Generated column for active status
ALTER TABLE colors ADD COLUMN is_active BOOLEAN 
GENERATED ALWAYS AS (deleted_at IS NULL) STORED;

-- Covering index for common queries
CREATE INDEX idx_colors_active_complete ON colors(
    id, external_id, source_id, code, hex, is_gradient, is_active
) WHERE is_active = TRUE;

-- Specialized indexes
CREATE INDEX idx_colors_hex ON colors(hex) WHERE is_active = TRUE;
CREATE INDEX idx_colors_code_source ON colors(source_id, code COLLATE NOCASE) WHERE is_active = TRUE;
CREATE INDEX idx_colors_gradient ON colors(id, is_gradient) WHERE is_gradient = TRUE AND is_active = TRUE;

-- Update trigger for colors
CREATE TRIGGER colors_update_timestamp 
AFTER UPDATE ON colors
FOR EACH ROW
WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE colors SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 4. CMYK Color Space
CREATE TABLE color_cmyk (
    color_id INTEGER PRIMARY KEY REFERENCES colors(id) ON DELETE CASCADE,
    c INTEGER NOT NULL CHECK (c BETWEEN 0 AND 100),
    m INTEGER NOT NULL CHECK (m BETWEEN 0 AND 100),
    y INTEGER NOT NULL CHECK (y BETWEEN 0 AND 100),
    k INTEGER NOT NULL CHECK (k BETWEEN 0 AND 100)
) WITHOUT ROWID;

-- Optimized for color matching queries
CREATE INDEX idx_cmyk_values ON color_cmyk(c, m, y, k);

-- 5. RGB Color Space
CREATE TABLE color_rgb (
    color_id INTEGER PRIMARY KEY REFERENCES colors(id) ON DELETE CASCADE,
    r INTEGER NOT NULL CHECK (r BETWEEN 0 AND 255),
    g INTEGER NOT NULL CHECK (g BETWEEN 0 AND 255),
    b INTEGER NOT NULL CHECK (b BETWEEN 0 AND 255)
) WITHOUT ROWID;

CREATE INDEX idx_rgb_values ON color_rgb(r, g, b);

-- 6. LAB Color Space
CREATE TABLE color_lab (
    color_id INTEGER PRIMARY KEY REFERENCES colors(id) ON DELETE CASCADE,
    l REAL NOT NULL CHECK (l BETWEEN 0 AND 100),
    a REAL NOT NULL CHECK (a BETWEEN -128 AND 127),
    b REAL NOT NULL CHECK (b BETWEEN -128 AND 127),
    illuminant TEXT NOT NULL DEFAULT 'D65',
    observer TEXT NOT NULL DEFAULT '2'
) WITHOUT ROWID;

CREATE INDEX idx_lab_values ON color_lab(l, a, b);

-- 7. HSL Color Space
CREATE TABLE color_hsl (
    color_id INTEGER PRIMARY KEY REFERENCES colors(id) ON DELETE CASCADE,
    h INTEGER NOT NULL CHECK (h BETWEEN 0 AND 360),
    s INTEGER NOT NULL CHECK (s BETWEEN 0 AND 100),
    l INTEGER NOT NULL CHECK (l BETWEEN 0 AND 100)
) WITHOUT ROWID;

CREATE INDEX idx_hsl_values ON color_hsl(h, s, l);

-- 8. Product Colors - Optimized Junction Table
CREATE TABLE product_colors (
    product_id INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    color_id INTEGER NOT NULL REFERENCES colors(id) ON DELETE RESTRICT,
    display_order INTEGER NOT NULL DEFAULT 0,
    usage_type TEXT DEFAULT 'standard',
    quantity REAL,
    metadata JSON,
    added_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (product_id, color_id),
    CHECK (usage_type IN ('standard', 'primary', 'accent', 'variant'))
) WITHOUT ROWID;

-- Optimized indexes for common access patterns
CREATE INDEX idx_product_colors_color ON product_colors(color_id);
CREATE INDEX idx_product_colors_ordered ON product_colors(product_id, display_order);

-- 9. Gradient Stops
CREATE TABLE gradient_stops (
    gradient_id INTEGER NOT NULL REFERENCES colors(id) ON DELETE CASCADE,
    stop_index INTEGER NOT NULL,
    position REAL NOT NULL,
    hex CHAR(7) NOT NULL,
    
    PRIMARY KEY (gradient_id, stop_index),
    CHECK (position BETWEEN 0.0 AND 1.0),
    CHECK (
        length(hex) = 7 AND 
        substr(hex, 1, 1) = '#' AND
        hex GLOB '#[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]'
    ),
    CHECK (stop_index >= 0)
) WITHOUT ROWID;

-- Trigger to validate gradient colors only
CREATE TRIGGER validate_gradient_stops
BEFORE INSERT ON gradient_stops
FOR EACH ROW
WHEN (SELECT is_gradient FROM colors WHERE id = NEW.gradient_id) = FALSE
BEGIN
    SELECT RAISE(ABORT, 'Cannot add stops to non-gradient color');
END;

-- 10. Color Deltas - Cached Color Comparisons
CREATE TABLE color_deltas (
    color_a_id INTEGER NOT NULL,
    color_b_id INTEGER NOT NULL,
    delta_cie76 REAL,
    delta_cie94 REAL,
    delta_cie2000 REAL,
    perception TEXT GENERATED ALWAYS AS (
        CASE 
            WHEN delta_cie2000 < 1.0 THEN 'imperceptible'
            WHEN delta_cie2000 < 2.0 THEN 'barely_perceptible'
            WHEN delta_cie2000 < 3.5 THEN 'perceptible'
            WHEN delta_cie2000 < 5.0 THEN 'noticeable'
            ELSE 'different'
        END
    ) STORED,
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (color_a_id, color_b_id),
    FOREIGN KEY (color_a_id) REFERENCES colors(id) ON DELETE CASCADE,
    FOREIGN KEY (color_b_id) REFERENCES colors(id) ON DELETE CASCADE,
    CHECK (color_a_id < color_b_id)
) WITHOUT ROWID;

CREATE INDEX idx_deltas_perception ON color_deltas(perception, delta_cie2000);

-- 11. Audit Log - Unified Audit Trail
CREATE TABLE audit_log (
    id INTEGER PRIMARY KEY,
    table_name TEXT NOT NULL,
    record_id INTEGER NOT NULL,
    action TEXT NOT NULL,
    changes JSON,
    user_id TEXT,
    occurred_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CHECK (action IN ('INSERT', 'UPDATE', 'DELETE'))
);

CREATE INDEX idx_audit_lookup ON audit_log(table_name, record_id, occurred_at);
CREATE INDEX idx_audit_user_time ON audit_log(user_id, occurred_at) WHERE user_id IS NOT NULL;

-- =================================================================
-- Full-Text Search Support
-- =================================================================

-- FTS5 virtual table for color search
CREATE VIRTUAL TABLE colors_fts USING fts5(
    code, 
    display_name,
    search_terms,
    content=colors,
    content_rowid=id,
    tokenize='trigram'
);

-- Triggers to maintain FTS index
CREATE TRIGGER colors_fts_insert AFTER INSERT ON colors BEGIN
    INSERT INTO colors_fts(rowid, code, display_name, search_terms) 
    VALUES (NEW.id, NEW.code, NEW.display_name, NEW.search_terms);
END;

CREATE TRIGGER colors_fts_update AFTER UPDATE ON colors BEGIN
    UPDATE colors_fts 
    SET code = NEW.code, 
        display_name = NEW.display_name,
        search_terms = NEW.search_terms
    WHERE rowid = NEW.id;
END;

CREATE TRIGGER colors_fts_delete AFTER DELETE ON colors BEGIN
    DELETE FROM colors_fts WHERE rowid = OLD.id;
END;

-- =================================================================
-- Optimized Views
-- =================================================================

-- Complete Color Information with All Spaces
CREATE VIEW v_colors AS
WITH color_usage AS (
    SELECT 
        color_id,
        COUNT(*) as usage_count,
        GROUP_CONCAT(product_id) as product_ids
    FROM product_colors
    GROUP BY color_id
)
SELECT 
    c.id,
    c.external_id,
    cs.code as source,
    cs.is_system as is_library,
    c.code,
    c.display_name,
    c.hex,
    
    -- CMYK values
    cmyk.c as cyan,
    cmyk.m as magenta,
    cmyk.y as yellow,
    cmyk.k as black,
    
    -- RGB values
    rgb.r as red,
    rgb.g as green,
    rgb.b as blue,
    
    -- LAB values
    lab.l as lab_l,
    lab.a as lab_a,
    lab.b as lab_b,
    
    -- HSL values
    hsl.h as hue,
    hsl.s as saturation,
    hsl.l as lightness,
    
    -- Characteristics
    c.is_gradient,
    c.is_metallic,
    c.is_effect,
    c.is_active,
    
    -- Usage
    COALESCE(cu.usage_count, 0) as product_count,
    cu.product_ids,
    
    c.properties,
    c.created_at,
    c.updated_at
FROM colors c
INNER JOIN color_sources cs ON c.source_id = cs.id
LEFT JOIN color_cmyk cmyk ON c.id = cmyk.color_id
LEFT JOIN color_rgb rgb ON c.id = rgb.color_id
LEFT JOIN color_lab lab ON c.id = lab.color_id
LEFT JOIN color_hsl hsl ON c.id = hsl.color_id
LEFT JOIN color_usage cu ON c.id = cu.color_id
WHERE c.is_active = TRUE;

-- Orphaned User Colors for Cleanup
CREATE VIEW v_orphaned_user_colors AS
SELECT c.*
FROM colors c
WHERE c.source_id = 1  -- user colors
  AND c.is_active = TRUE
  AND NOT EXISTS (
      SELECT 1 FROM product_colors pc 
      WHERE pc.color_id = c.id
      LIMIT 1
  );

-- =================================================================
-- Optimized Triggers
-- =================================================================

-- Auto-cleanup Orphaned User Colors
CREATE TRIGGER cleanup_orphaned_user_colors
AFTER DELETE ON product_colors
FOR EACH ROW
WHEN (SELECT source_id FROM colors WHERE id = OLD.color_id) = 1
BEGIN
    UPDATE colors 
    SET deleted_at = CURRENT_TIMESTAMP
    WHERE id = OLD.color_id
      AND NOT EXISTS (
          SELECT 1 FROM product_colors 
          WHERE color_id = OLD.color_id
          LIMIT 1
      );
END;

-- Prevent System Color Deletion
CREATE TRIGGER protect_system_colors
BEFORE UPDATE OF deleted_at ON colors
FOR EACH ROW
WHEN NEW.deleted_at IS NOT NULL 
  AND (SELECT is_system FROM color_sources WHERE id = OLD.source_id) = TRUE
BEGIN
    SELECT RAISE(ABORT, 'System colors cannot be deleted');
END;

-- Comprehensive Audit Logging
CREATE TRIGGER audit_colors_change
AFTER INSERT ON colors
FOR EACH ROW
BEGIN
    INSERT INTO audit_log (table_name, record_id, action, changes)
    VALUES ('colors', NEW.id, 'INSERT', json_object(
        'code', NEW.code,
        'hex', NEW.hex,
        'source_id', NEW.source_id
    ));
END;

-- Run PRAGMA optimize on connection close (reminder)
-- PRAGMA optimize;
