#!/usr/bin/env node

// Test invitation email - CommonJS version
const axios = require('axios');
const path = require('path');
const fs = require('fs');
const os = require('os');
require('dotenv').config();

// Get token file location
const appName = 'ChromaSync';
const userData = process.platform === 'darwin' 
  ? path.join(os.homedir(), 'Library', 'Application Support', appName)
  : process.platform === 'win32'
  ? path.join(process.env.APPDATA, appName)
  : path.join(os.homedir(), '.config', appName);

const tokenFile = path.join(userData, 'zoho-tokens.json');

console.log('🧪 Testing ChromaSync Team Invitation Email...\n');

async function testInvitationEmail() {
  try {
    // Load saved tokens
    if (!fs.existsSync(tokenFile)) {
      console.error('❌ No token file found. Run refresh-zoho-token.cjs first.');
      process.exit(1);
    }
    
    const tokens = JSON.parse(fs.readFileSync(tokenFile, 'utf-8'));
    
    // Check if token is still valid
    if (Date.now() >= tokens.expiresAt) {
      console.error('❌ Token has expired. Run refresh-zoho-token.cjs first.');
      process.exit(1);
    }
    
    console.log('✅ Using existing access token\n');
    
    // Create test invitation data
    const testInvitation = {
      organizationName: 'Test Design Studio',
      inviterName: 'Michael (Test)',
      role: 'member',
      token: 'test-token-123456',
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
    };
    
    // Build email content
    const invitationUrl = `chromasync://invite/${testInvitation.token}`;
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ChromaSync Team Invitation</title>
  <style>
    body { 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
      line-height: 1.6; 
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f8fafc;
    }
    .container { 
      max-width: 600px;
      margin: 0 auto;
      background: #ffffff; 
      border-radius: 12px; 
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 40px 30px;
      text-align: center;
    }
    .logo {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    .content {
      padding: 40px 30px;
    }
    .invitation-card {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-left: 4px solid #3b82f6;
      border-radius: 8px;
      padding: 24px;
      margin: 24px 0;
    }
    .button { 
      display: inline-block; 
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: white; 
      padding: 12px 30px; 
      text-decoration: none; 
      border-radius: 8px;
      font-weight: 600;
      margin: 20px 0;
    }
    .footer {
      padding: 30px;
      background: #f8fafc;
      text-align: center;
      font-size: 14px;
      color: #64748b;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">🎨 ChromaSync</div>
      <p>Professional Color Management</p>
    </div>
    
    <div class="content">
      <h2>You're invited to join ${testInvitation.organizationName}!</h2>
      
      <div class="invitation-card">
        <p><strong>${testInvitation.inviterName}</strong> has invited you to join their ChromaSync team as a <strong>${testInvitation.role}</strong>.</p>
        <p>ChromaSync helps teams organize and sync their color palettes across projects.</p>
      </div>
      
      <p style="text-align: center;">
        <a href="${invitationUrl}" class="button">Accept Invitation</a>
      </p>
      
      <p style="text-align: center; color: #64748b; font-size: 14px;">
        <em>Having trouble? Copy and paste this link into ChromaSync:</em><br>
        <code style="background: #f1f5f9; padding: 8px; border-radius: 4px; font-size: 12px;">${invitationUrl}</code>
      </p>
    </div>
    
    <div class="footer">
      <p><strong>⏰ This invitation expires on ${testInvitation.expiresAt.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })}.</strong></p>
      <p>Questions? Contact us at <a href="mailto:<EMAIL>" style="color: #3b82f6;"><EMAIL></a></p>
      <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 24px 0;">
      <p>© 2024 ChromaSync. All rights reserved.</p>
    </div>
  </div>
</body>
</html>`;
    
    // Send email using Zoho API
    const emailData = {
      fromAddress: process.env.ZOHO_SUPPORT_ALIAS || '<EMAIL>',
      toAddress: '<EMAIL>', // Changed to requested email
      subject: `🎨 ${testInvitation.inviterName} invited you to join ${testInvitation.organizationName}`,
      content: htmlContent,
      mailFormat: 'html'
    };
    
    console.log('📧 Sending invitation email to:', emailData.toAddress);
    
    const zohoApiDomain = process.env.ZOHO_REGION === 'EU' ? 'mail.zoho.eu' : 'mail.zoho.com';
    const accountId = process.env.ZOHO_ACCOUNT_ID;
    
    const response = await axios.post(
      `https://${zohoApiDomain}/api/accounts/${accountId}/messages`,
      emailData,
      {
        headers: {
          'Authorization': `Zoho-oauthtoken ${tokens.accessToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 15000
      }
    );
    
    console.log('✅ Invitation email sent successfully!');
    console.log(`   Message ID: ${response.data.data?.messageId || 'N/A'}`);
    console.log(`   To: ${emailData.toAddress}`);
    console.log(`   From: ${emailData.fromAddress}`);
    console.log('\n📬 Check the <NAME_EMAIL>');
    
  } catch (error) {
    console.error('❌ Failed to send invitation email:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.error('\n⚠️  Token may be expired. Run refresh-zoho-token.cjs');
    } else if (error.response?.status === 400) {
      console.error('\n⚠️  Bad request - <NAME_EMAIL> alias is configured');
    }
    
    process.exit(1);
  }
}

// Run the test
testInvitationEmail();
