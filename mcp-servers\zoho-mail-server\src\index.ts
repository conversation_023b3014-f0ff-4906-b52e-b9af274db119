#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import axios from 'axios';
import * as dotenv from 'dotenv';
import { readFile, writeFile } from 'fs/promises';
import { join } from 'path';

dotenv.config();

interface ZohoConfig {
  clientId: string;
  clientSecret: string;
  refreshToken: string;
  accountId: string;
  accessToken?: string;
  tokenExpiry?: number;
}

interface EmailOptions {
  to: string | string[];
  subject: string;
  content: string;
  fromAddress?: string;
  cc?: string[];
  bcc?: string[];
  replyTo?: string;
  isHtml?: boolean;
}

class ZohoMailServer {
  private server: Server;
  private config: ZohoConfig;
  private configPath: string;

  constructor() {
    this.server = new Server(
      {
        name: 'zoho-mail-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.configPath = join(process.env.HOME || '', '.zoho-mcp-config.json');
    this.config = {
      clientId: process.env.ZOHO_CLIENT_ID || '',
      clientSecret: process.env.ZOHO_CLIENT_SECRET || '',
      refreshToken: process.env.ZOHO_REFRESH_TOKEN || '',
      accountId: process.env.ZOHO_ACCOUNT_ID || '',
    };

    this.setupHandlers();
  }

  private async loadConfig(): Promise<void> {
    try {
      const configData = await readFile(this.configPath, 'utf-8');
      this.config = { ...this.config, ...JSON.parse(configData) };
    } catch (error) {
      // Config file doesn't exist, use env vars
    }
  }

  private async saveConfig(): Promise<void> {
    await writeFile(this.configPath, JSON.stringify(this.config, null, 2));
  }

  private async refreshAccessToken(): Promise<string> {
    if (this.config.accessToken && this.config.tokenExpiry && Date.now() < this.config.tokenExpiry) {
      return this.config.accessToken;
    }

    try {
      const response = await axios.post(
        'https://accounts.zoho.com/oauth/v2/token',
        null,
        {
          params: {
            refresh_token: this.config.refreshToken,
            client_id: this.config.clientId,
            client_secret: this.config.clientSecret,
            grant_type: 'refresh_token',
          },
        }
      );

      this.config.accessToken = response.data.access_token;
      this.config.tokenExpiry = Date.now() + (response.data.expires_in * 1000) - 60000; // Refresh 1 min early
      await this.saveConfig();

      return response.data.access_token;
    } catch (error) {
      throw new Error(`Failed to refresh access token: ${error}`);
    }
  }

  private async sendEmail(options: EmailOptions): Promise<any> {
    const accessToken = await this.refreshAccessToken();
    
    const emailData: any = {
      fromAddress: options.fromAddress || '<EMAIL>',
      toAddress: Array.isArray(options.to) ? options.to.join(',') : options.to,
      subject: options.subject,
      content: options.content,
      mailFormat: options.isHtml ? 'html' : 'plaintext',
    };

    if (options.cc) emailData['ccAddress'] = options.cc.join(',');
    if (options.bcc) emailData['bccAddress'] = options.bcc.join(',');
    if (options.replyTo) emailData['replyTo'] = options.replyTo;

    try {
      const response = await axios.post(
        `https://mail.zoho.com/api/accounts/${this.config.accountId}/messages`,
        emailData,
        {
          headers: {
            'Authorization': `Zoho-oauthtoken ${accessToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      return response.data;
    } catch (error) {
      throw new Error(`Failed to send email: ${error}`);
    }
  }

  private setupHandlers(): void {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'send_email',
          description: 'Send an email via Zoho Mail',
          inputSchema: {
            type: 'object',
            properties: {
              to: {
                type: ['string', 'array'],
                description: 'Recipient email address(es)',
                items: { type: 'string' },
              },
              subject: {
                type: 'string',
                description: 'Email subject',
              },
              content: {
                type: 'string',
                description: 'Email content (plain text or HTML)',
              },
              fromAddress: {
                type: 'string',
                description: 'Sender email address (must be a verified alias)',
                default: '<EMAIL>',
              },
              isHtml: {
                type: 'boolean',
                description: 'Whether the content is HTML',
                default: false,
              },
              cc: {
                type: 'array',
                description: 'CC recipients',
                items: { type: 'string' },
              },
              bcc: {
                type: 'array',
                description: 'BCC recipients',
                items: { type: 'string' },
              },
              replyTo: {
                type: 'string',
                description: 'Reply-to email address',
              },
            },
            required: ['to', 'subject', 'content'],
          },
        },
        {
          name: 'send_invitation',
          description: 'Send a ChromaSync team invitation email',
          inputSchema: {
            type: 'object',
            properties: {
              to: {
                type: 'string',
                description: 'Recipient email address',
              },
              organizationName: {
                type: 'string',
                description: 'Name of the organization',
              },
              inviterName: {
                type: 'string',
                description: 'Name of the person inviting',
              },
              role: {
                type: 'string',
                description: 'Role in the organization',
              },
              invitationUrl: {
                type: 'string',
                description: 'Invitation URL',
              },
              expiresAt: {
                type: 'string',
                description: 'Expiration date',
              },
            },
            required: ['to', 'organizationName', 'inviterName', 'role', 'invitationUrl', 'expiresAt'],
          },
        },
        {
          name: 'configure_zoho',
          description: 'Configure Zoho OAuth credentials',
          inputSchema: {
            type: 'object',
            properties: {
              clientId: { type: 'string' },
              clientSecret: { type: 'string' },
              refreshToken: { type: 'string' },
              accountId: { type: 'string' },
            },
            required: ['clientId', 'clientSecret', 'refreshToken', 'accountId'],
          },
        },
      ],
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const args = request.params.arguments as Record<string, any>;
      
      switch (request.params.name) {
        case 'send_email':
          return await this.handleSendEmail(args as EmailOptions);

        case 'send_invitation':
          return await this.handleSendInvitation(args);

        case 'configure_zoho':
          return await this.handleConfigure(args);

        default:
          throw new McpError(
            ErrorCode.MethodNotFound,
            `Unknown tool: ${request.params.name}`
          );
      }
    });
  }

  private async handleSendEmail(args: EmailOptions): Promise<any> {
    try {
      const result = await this.sendEmail(args);
      return {
        content: [
          {
            type: 'text',
            text: `Email sent successfully! Message ID: ${result.data.messageId}`,
          },
        ],
      };
    } catch (error) {
      return {
        content: [
          {
            type: 'text',
            text: `Failed to send email: ${error}`,
          },
        ],
        isError: true,
      };
    }
  }

  private async handleSendInvitation(args: any): Promise<any> {
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ChromaSync Team Invitation</title>
  <style>
    body { 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
      line-height: 1.6; 
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f8fafc;
    }
    .container { 
      max-width: 600px;
      margin: 0 auto;
      background: #ffffff; 
      border-radius: 12px; 
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 40px 30px;
      text-align: center;
    }
    .content {
      padding: 40px 30px;
    }
    .button { 
      display: inline-block; 
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: white; 
      padding: 12px 30px; 
      text-decoration: none; 
      border-radius: 8px;
      font-weight: 600;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🎨 ChromaSync</h1>
      <p>Team Invitation</p>
    </div>
    <div class="content">
      <h2>You're invited to join ${args.organizationName}!</h2>
      <p>${args.inviterName} has invited you to join their ChromaSync team as a ${args.role}.</p>
      <a href="${args.invitationUrl}" class="button">Accept Invitation</a>
      <p><small>This invitation expires on ${args.expiresAt}.</small></p>
    </div>
  </div>
</body>
</html>`;

    return await this.handleSendEmail({
      to: args.to,
      subject: `🎨 ${args.inviterName} invited you to join ${args.organizationName}`,
      content: htmlContent,
      isHtml: true,
      fromAddress: '<EMAIL>',
    });
  }

  private async handleConfigure(args: any): Promise<any> {
    this.config = {
      ...this.config,
      ...args,
    };
    await this.saveConfig();

    return {
      content: [
        {
          type: 'text',
          text: 'Zoho configuration saved successfully!',
        },
      ],
    };
  }

  async run(): Promise<void> {
    await this.loadConfig();
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Zoho Mail MCP server running on stdio');
  }
}

const server = new ZohoMailServer();
server.run().catch(console.error);
