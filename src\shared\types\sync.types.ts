/**
 * @file sync.types.ts
 * @description Shared type definitions for the ChromaSync Sync System
 */

// Import the shared enums
import { SyncStatus } from '../constants/sync-status';
import { ConflictResolutionStrategy } from '../constants/conflict-resolution';

// Re-export the enums
export { SyncStatus, ConflictResolutionStrategy };

/**
 * Represents a timestamp with version information for sync operations
 */
export interface SyncTimestamp {
  timestamp: number; // Unix timestamp in milliseconds
  userId: string;    // Unique identifier for the user
  deviceId: string;  // Unique identifier for the device
  version: number;   // Version number, incremented with each sync
}

/**
 * Metadata for a database export
 */
export interface SyncMetadata {
  createdAt: SyncTimestamp;
  appVersion: string;
  dbVersion: string;
  recordCount: {
    colors: number;
    products: number;
    datasheets: number;
  };
  checksum: string; // SHA-256 hash of the database content
}

/**
 * Represents a database export for sync
 */
export interface DatabaseExport {
  metadata: SyncMetadata;
  data: string; // Base64 encoded SQLite database
}

// ConflictResolutionStrategy is now imported from '../constants/conflict-resolution'

/**
 * Configuration for sync operations
 */
export interface SyncConfig {
  autoSync: boolean;
  syncInterval: number; // In minutes
  conflictResolution: ConflictResolutionStrategy;
  maxStorageVersions: number; // Maximum number of versions to keep
}

/**
 * Represents a conflict between local and remote data
 */
export interface SyncConflict {
  table: string;
  recordId: string;
  localTimestamp: SyncTimestamp;
  remoteTimestamp: SyncTimestamp;
  localData: unknown;
  remoteData: unknown;
  resolved: boolean;
  resolution?: 'local' | 'remote' | 'merged';
}

/**
 * Response from a sync operation
 */
export interface SyncResponse {
  success: boolean;
  timestamp: SyncTimestamp;
  message?: string;
  conflicts?: SyncConflict[];
  newRecords?: number;
  updatedRecords?: number;
  deletedRecords?: number;
}

/**
 * Request for a sync operation
 */
export interface SyncRequest {
  lastSyncTimestamp?: SyncTimestamp;
  data?: DatabaseExport;
  deviceId?: string; // Device identifier for authentication
  action?: 'ping' | 'stats'; // Special actions for the sync server
  conflictResolutions?: Array<{
    conflictId: string;
    resolution: 'local' | 'remote' | 'merged';
    mergedData?: unknown;
  }>;
}

/**
 * Authentication state for sync
 */
export interface SyncAuthState {
  isAuthenticated: boolean;
  userId?: string;
  token?: string;
  tokenExpiry?: number;
}

/**
 * User profile for sync
 */
export interface SyncUserProfile {
  userId: string;
  email: string;
  displayName?: string;
  createdAt: number;
  lastActive: number;
  devices: Array<{
    deviceId: string;
    name: string;
    lastSync?: SyncTimestamp;
  }>;
}

/**
 * Sync event types for notifications
 */
export enum SyncEventType {
  SYNC_STARTED = 'sync-started',
  SYNC_COMPLETED = 'sync-completed',
  SYNC_ERROR = 'sync-error',
  CONFLICT_DETECTED = 'conflict-detected',
  NEW_DATA_AVAILABLE = 'new-data-available'
}

/**
 * Sync event for notifications
 */
export interface SyncEvent {
  type: SyncEventType;
  timestamp: number;
  message?: string;
  data?: unknown;
}
