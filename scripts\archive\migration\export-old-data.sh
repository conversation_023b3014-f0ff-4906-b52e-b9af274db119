#!/bin/bash

# ChromaSync Data Export Script
# Exports data from old database.sqlite to JSON format for import

DB_PATH="$HOME/Library/Application Support/ChromaSync/database.sqlite"
OUTPUT_PATH="$HOME/Library/Application Support/ChromaSync/chromasync-export-$(date +%s).json"

echo "ChromaSync Data Export Tool"
echo "=========================="
echo "Reading from: $DB_PATH"

# Check if database exists
if [ ! -f "$DB_PATH" ]; then
    echo "Error: database.sqlite not found!"
    exit 1
fi

# Export data using sqlite3 and create JSON
sqlite3 "$DB_PATH" <<EOF > "$OUTPUT_PATH.tmp"
.mode json
SELECT 
    uniqueId as name,
    colourCode as code,
    hex,
    cmyk,
    product,
    notes,
    gradient,
    CASE WHEN is_library = 1 THEN 'true' ELSE 'false' END as isLibrary
FROM colors
ORDER BY product, colourCode;
EOF

# Wrap the output in proper JSON structure
echo '{' > "$OUTPUT_PATH"
echo '  "version": "1.0",' >> "$OUTPUT_PATH"
echo '  "exportDate": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",' >> "$OUTPUT_PATH"
echo '  "colors": ' >> "$OUTPUT_PATH"
cat "$OUTPUT_PATH.tmp" >> "$OUTPUT_PATH"
echo '}' >> "$OUTPUT_PATH"
rm "$OUTPUT_PATH.tmp"

# Count colors
COLOR_COUNT=$(sqlite3 "$DB_PATH" "SELECT COUNT(*) FROM colors;")

echo ""
echo "Export completed successfully!"
echo "Exported $COLOR_COUNT colors"
echo "File saved to: $OUTPUT_PATH"
echo ""
echo "To import this data:"
echo "1. Open ChromaSync"
echo "2. Click the menu (three dots) in the header"
echo "3. Select 'Import Colors'"
echo "4. Choose 'Merge' mode to add to existing colors"
echo "5. Select the exported file"
