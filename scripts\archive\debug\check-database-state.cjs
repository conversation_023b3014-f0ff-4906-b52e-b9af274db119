const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Path to the database
const dbPath = '/Users/<USER>/Library/Application Support/chroma-sync/chromasync.db';

console.log('Opening database:', dbPath);
const db = new Database(dbPath);

// Enable foreign keys
db.pragma('foreign_keys = ON');

// Check current state
console.log('\n=== Current Database State ===');

const colorCount = db.prepare('SELECT COUNT(*) as count FROM colors WHERE deleted_at IS NULL').get();
console.log(`Total colors: ${colorCount.count}`);

const productCount = db.prepare('SELECT COUNT(*) as count FROM products WHERE is_active = 1').get();
console.log(`Total products: ${productCount.count}`);

const associationCount = db.prepare('SELECT COUNT(*) as count FROM product_colors').get();
console.log(`Product-color associations: ${associationCount.count}`);

// List products
console.log('\n=== Products ===');
const products = db.prepare('SELECT external_id, name, sku FROM products WHERE is_active = 1').all();
products.forEach(p => {
  console.log(`- ${p.name} (SKU: ${p.sku})`);
});

// List colors with their products
console.log('\n=== Colors ===');
const colors = db.prepare(`
  SELECT 
    c.external_id,
    c.code,
    c.display_name,
    c.hex,
    c.properties,
    GROUP_CONCAT(p.name) as products
  FROM colors c
  LEFT JOIN product_colors pc ON c.id = pc.color_id
  LEFT JOIN products p ON pc.product_id = p.id AND p.is_active = 1
  WHERE c.deleted_at IS NULL
  GROUP BY c.id
  LIMIT 10
`).all();

colors.forEach(c => {
  const props = c.properties ? JSON.parse(c.properties) : {};
  console.log(`- ${c.code} "${c.display_name}" ${c.hex} - Products: ${c.products || 'None'} - Props.product: ${props.product || 'None'}`);
});

// Check for orphaned colors (colors without product associations)
console.log('\n=== Orphaned Colors (no product associations) ===');
const orphanedColors = db.prepare(`
  SELECT COUNT(*) as count
  FROM colors c
  WHERE c.deleted_at IS NULL
  AND NOT EXISTS (
    SELECT 1 FROM product_colors pc WHERE pc.color_id = c.id
  )
`).get();
console.log(`Orphaned colors: ${orphanedColors.count}`);

// Close database
db.close();
console.log('\n✅ Database check complete');
