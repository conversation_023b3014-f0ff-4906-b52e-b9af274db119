const Database = require('better-sqlite3');

// Path to the database
const dbPath = '/Users/<USER>/Library/Application Support/chroma-sync/chromasync.db';

console.log('Opening database:', dbPath);
const db = new Database(dbPath);

console.log('\n=== Checking product_colors table schema ===');

try {
  // Get table schema
  const schema = db.prepare("PRAGMA table_info(product_colors)").all();
  console.log('product_colors table columns:');
  schema.forEach(col => {
    console.log(`  ${col.name} (${col.type}) - ${col.notnull ? 'NOT NULL' : 'NULL'} - ${col.pk ? 'PRIMARY KEY' : ''}`);
  });

  // Check if table has any data
  const count = db.prepare("SELECT COUNT(*) as count FROM product_colors").get();
  console.log(`\nTotal rows in product_colors: ${count.count}`);

  // Check products table structure
  console.log('\n=== Products table info ===');
  const productSchema = db.prepare("PRAGMA table_info(products)").all();
  console.log('products table columns:');
  productSchema.forEach(col => {
    console.log(`  ${col.name} (${col.type}) - ${col.notnull ? 'NOT NULL' : 'NULL'} - ${col.pk ? 'PRIMARY KEY' : ''}`);
  });

  // Check colors table structure
  console.log('\n=== Colors table info ===');
  const colorSchema = db.prepare("PRAGMA table_info(colors)").all();
  console.log('colors table columns:');
  colorSchema.forEach(col => {
    console.log(`  ${col.name} (${col.type}) - ${col.notnull ? 'NOT NULL' : 'NULL'} - ${col.pk ? 'PRIMARY KEY' : ''}`);
  });

  // Sample data from each table
  console.log('\n=== Sample data ===');
  
  const sampleProduct = db.prepare("SELECT * FROM products WHERE is_active = 1 LIMIT 1").get();
  console.log('Sample product:', sampleProduct);
  
  const sampleColor = db.prepare("SELECT * FROM colors WHERE deleted_at IS NULL LIMIT 1").get();
  console.log('Sample color:', sampleColor);

} catch (error) {
  console.error('Error:', error);
} finally {
  db.close();
  console.log('\n✅ Schema check complete');
}