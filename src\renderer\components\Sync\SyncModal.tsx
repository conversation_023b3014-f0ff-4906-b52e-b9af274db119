/**
 * @file SyncModal.tsx
 * @description Modal component for displaying the SyncPanel
 */

import React, { useRef, useEffect } from 'react';
import { SyncPanel } from './SyncPanel';
import { useTokens } from '../../hooks/useTokens';
import { useClickOutside } from '../../utils/useClickOutside';

interface SyncModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const SyncModal: React.FC<SyncModalProps> = ({ isOpen, onClose }) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const tokens = useTokens();

  // Use shared useClickOutside hook for modal
  useClickOutside(modalRef, onClose, isOpen);

  // Close modal when pressing Escape
  useEffect(() => {
    function handleEscapeKey(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        onClose();
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen, onClose]);

  if (!isOpen) {return null;}

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center"
      style={{
        transition: `opacity ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`,
        zIndex: 9999 // Ensure this is higher than any other z-index in the application
      }}
    >
      <div
        ref={modalRef}
        className="bg-ui-background-primary dark:bg-ui-background-tertiary rounded-[var(--radius-lg)] shadow-[var(--shadow-lg)] max-w-md w-full max-h-[90vh] overflow-auto relative"
        style={{
          transition: `transform ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`,
          zIndex: 10000 // Even higher than the overlay
        }}
      >
        <div className="flex justify-between items-center p-4 border-b border-ui-border-light dark:border-ui-border-dark">
          <h2 className="text-lg font-semibold text-ui-foreground-primary dark:text-ui-foreground-primary">
            Sync Settings
          </h2>
          <button
            onClick={onClose}
            className="text-ui-foreground-secondary dark:text-ui-foreground-secondary hover:text-ui-foreground-primary dark:hover:text-ui-foreground-primary transition-colors"
            aria-label="Close"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>

        <div className="p-4">
          <SyncPanel />
        </div>
      </div>
    </div>
  );
};
