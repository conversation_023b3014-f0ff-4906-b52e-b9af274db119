#!/bin/bash

echo "ChromaSync Database Cleanup"
echo "=========================="
echo ""
echo "This will remove duplicate/old databases, keeping only:"
echo "  - chromasync.db (main database)"
echo "  - backups/ folder (for safety)"
echo ""

# Show what will be removed
echo "Databases to be removed:"
echo "  - chroma-sync.db (old name)"
echo "  - chroma-sync-data.db (old name)"
echo "  - Backup files in main directory (moving to backups folder)"
echo "  - Electron directory databases"
echo ""

read -p "Continue with cleanup? (y/N): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Cleanup cancelled."
    exit 0
fi

# Create backups directory if it doesn't exist
mkdir -p ~/Library/Application\ Support/chroma-sync/backups

# Move any backup files from main directory to backups folder
echo "Moving backup files to backups folder..."
mv ~/Library/Application\ Support/chroma-sync/*backup*.db ~/Library/Application\ Support/chroma-sync/backups/ 2>/dev/null

# Remove old/duplicate databases
echo "Removing duplicate databases..."
rm -f ~/Library/Application\ Support/chroma-sync/chroma-sync.db
rm -f ~/Library/Application\ Support/chroma-sync/chroma-sync-data.db
rm -f ~/Library/Application\ Support/Electron/chroma-sync.db
rm -f ~/Library/Application\ Support/Electron/chroma-sync-data.db

echo ""
echo "Cleanup complete!"
echo ""
echo "Remaining database structure:"
echo "  Main: ~/Library/Application Support/chroma-sync/chromasync.db"
echo "  Backups: ~/Library/Application Support/chroma-sync/backups/"
echo ""
ls -la ~/Library/Application\ Support/chroma-sync/chromasync.db
