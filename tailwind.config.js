/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./src/renderer/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Brand colors
        brand: {
          primary: 'var(--color-brand-primary)',
          secondary: 'var(--color-brand-secondary)',
          accent: 'var(--color-brand-accent)',
        },
        // UI colors
        ui: {
          background: {
            primary: 'var(--color-ui-background-primary)',
            secondary: 'var(--color-ui-background-secondary)',
            tertiary: 'var(--color-ui-background-tertiary)',
          },
          foreground: {
            primary: 'var(--color-ui-foreground-primary)',
            secondary: 'var(--color-ui-foreground-secondary)',
            tertiary: 'var(--color-ui-foreground-tertiary)',
            inverse: 'var(--color-ui-foreground-inverse)',
          },
          border: {
            light: 'var(--color-ui-border-light)',
            medium: 'var(--color-ui-border-medium)',
            dark: 'var(--color-ui-border-dark)',
          },
          focus: 'var(--color-ui-focus)',
        },
        // Feedback colors
        feedback: {
          success: 'var(--color-feedback-success)',
          warning: 'var(--color-feedback-warning)',
          error: 'var(--color-feedback-error)',
          info: 'var(--color-feedback-info)',
        },
      },
      borderRadius: {
        none: 'var(--radius-none)',
        sm: 'var(--radius-sm)',
        DEFAULT: 'var(--radius-DEFAULT)',
        md: 'var(--radius-md)',
        lg: 'var(--radius-lg)',
        xl: 'var(--radius-xl)',
        '2xl': 'var(--radius-2xl)',
        full: 'var(--radius-full)',
      },
      spacing: {
        px: 'var(--spacing-px)',
        0: 'var(--spacing-0)',
        0.5: 'var(--spacing-0\\.5)',
        1: 'var(--spacing-1)',
        1.5: 'var(--spacing-1\\.5)',
        2: 'var(--spacing-2)',
        2.5: 'var(--spacing-2\\.5)',
        3: 'var(--spacing-3)',
        4: 'var(--spacing-4)',
        5: 'var(--spacing-5)',
        6: 'var(--spacing-6)',
        8: 'var(--spacing-8)',
        10: 'var(--spacing-10)',
        12: 'var(--spacing-12)',
        16: 'var(--spacing-16)',
        20: 'var(--spacing-20)',
        24: 'var(--spacing-24)',
      },
      fontFamily: {
        sans: 'var(--font-family-sans)',
        mono: 'var(--font-family-mono)',
      },
      fontSize: {
        xs: ['var(--font-size-xs)', { lineHeight: 'var(--line-height-tight)' }],
        sm: ['var(--font-size-sm)', { lineHeight: 'var(--line-height-snug)' }],
        base: ['var(--font-size-base)', { lineHeight: 'var(--line-height-normal)' }],
        lg: ['var(--font-size-lg)', { lineHeight: 'var(--line-height-normal)' }],
        xl: ['var(--font-size-xl)', { lineHeight: 'var(--line-height-snug)' }],
        '2xl': ['var(--font-size-2xl)', { lineHeight: 'var(--line-height-snug)' }],
        '3xl': ['var(--font-size-3xl)', { lineHeight: 'var(--line-height-tight)' }],
        '4xl': ['var(--font-size-4xl)', { lineHeight: 'var(--line-height-tight)' }],
      },
      fontWeight: {
        normal: 'var(--font-weight-normal)',
        medium: 'var(--font-weight-medium)',
        semibold: 'var(--font-weight-semibold)',
        bold: 'var(--font-weight-bold)',
      },
      lineHeight: {
        none: 'var(--line-height-none)',
        tight: 'var(--line-height-tight)',
        snug: 'var(--line-height-snug)',
        normal: 'var(--line-height-normal)',
        relaxed: 'var(--line-height-relaxed)',
        loose: 'var(--line-height-loose)',
      },
      boxShadow: {
        DEFAULT: 'var(--shadow)',
        sm: 'var(--shadow-sm)',
        md: 'var(--shadow-md)',
        lg: 'var(--shadow-lg)',
        xl: 'var(--shadow-xl)',
        none: 'none',
      },
      transitionDuration: {
        75: 'var(--transition-duration-75)',
        100: 'var(--transition-duration-100)',
        150: 'var(--transition-duration-150)',
        200: 'var(--transition-duration-200)',
        300: 'var(--transition-duration-300)',
        500: 'var(--transition-duration-500)',
        700: 'var(--transition-duration-700)',
        1000: 'var(--transition-duration-1000)',
      },
      transitionTimingFunction: {
        linear: 'var(--transition-easing-linear)',
        in: 'var(--transition-easing-in)',
        out: 'var(--transition-easing-out)',
        'in-out': 'var(--transition-easing-in-out)',
        apple: 'var(--transition-easing-apple)',
      },
      zIndex: {
        0: 'var(--z-0)',
        10: 'var(--z-10)',
        20: 'var(--z-20)',
        30: 'var(--z-30)',
        40: 'var(--z-40)',
        50: 'var(--z-50)',
        auto: 'var(--z-auto)',
        dropdown: 'var(--z-dropdown)',
        modal: 'var(--z-modal)',
        tooltip: 'var(--z-tooltip)',
      },
    },
  },
  plugins: [],
}