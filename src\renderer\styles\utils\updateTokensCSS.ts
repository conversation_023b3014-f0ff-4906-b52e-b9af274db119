/**
 * @file updateTokensCSS.ts
 * @description Script to generate and update the tokens.css file
 */

import fs from 'fs';
import path from 'path';
import { generateCssVariables } from './generateCssVariables';

// Path to tokens.css file
const tokensFilePath = path.resolve(__dirname, '../tokens.css');

// Generate the CSS variables
const cssVariables = generateCssVariables();

// Add header and utility classes
const cssContent = `/**
 * Apple-inspired design tokens CSS variables
 * This file is auto-generated - DO NOT EDIT DIRECTLY
 * Generated from src/renderer/styles/tokens/ directory
 */

${cssVariables}

/* Common utility classes */
.text-brand-primary {
  color: var(--color-brand-primary);
}

.text-brand-secondary {
  color: var(--color-brand-secondary);
}

.bg-brand-primary {
  background-color: var(--color-brand-primary);
}

.bg-brand-secondary {
  background-color: var(--color-brand-secondary);
}

.border-brand-primary {
  border-color: var(--color-brand-primary);
}

.border-brand-secondary {
  border-color: var(--color-brand-secondary);
}

/* Focus outline utility */
.focus-outline {
  outline: 2px solid var(--color-ui-focus);
  outline-offset: 2px;
}

/* Transition utility */
.transition-standard {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
  transition-duration: var(--transition-duration-200);
  transition-timing-function: var(--transition-easing-apple);
}

/* Apple-style utility classes */
.apple-card {
  background-color: var(--color-ui-background-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-ui-border-light);
}
`;

// Write to file
try {
  fs.writeFileSync(tokensFilePath, cssContent, 'utf8');
  console.log('Successfully updated tokens.css');
} catch (error) {
  console.error('Error updating tokens.css:', error);
}

export default function updateTokensCSS() {
  return cssContent;
} 