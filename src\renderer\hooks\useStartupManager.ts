/**
 * @file useStartupManager.ts
 * @description Hook to manage application startup sequence and performance
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { preloadCriticalComponents } from '../utils/lazyComponents';
import { useColorStore } from '../store/color.store';
import { useProductStore } from '../store/product.store';

export type StartupStage = 
  | 'initializing'
  | 'loading-database' 
  | 'loading-ui'
  | 'preloading-components'
  | 'ready';

interface StartupMetrics {
  startTime: number;
  stageTimings: Record<StartupStage, number>;
  totalTime: number;
}

export const useStartupManager = () => {
  const [stage, setStage] = useState<StartupStage>('initializing');
  const [progress, setProgress] = useState(0);
  const [isReady, setIsReady] = useState(false);
  const [metrics, setMetrics] = useState<StartupMetrics | null>(null);
  
  const startTimeRef = useRef<number>(Date.now());
  const stageTimingsRef = useRef<Record<StartupStage, number>>({} as any);
  const hasInitializedRef = useRef<boolean>(false);
  
  // Store actions
  const initializeColors = useColorStore(state => state.fetchColors);
  const initializeProducts = useProductStore(state => state.fetchProductsWithColors);
  
  const moveToStage = useCallback((newStage: StartupStage, progressValue: number) => {
    const now = Date.now();
    
    setStage(prevStage => {
      // Record timing for the previous stage
      stageTimingsRef.current[prevStage] = now - startTimeRef.current;
      return newStage;
    });
    
    setProgress(progressValue);
    
    console.log(`[Startup] Stage: ${newStage} (${progressValue}%)`);
  }, []);
  
  const initializeApp = useCallback(async () => {
    // Prevent multiple initializations
    if (hasInitializedRef.current) {
      console.log('[Startup] Already initialized, skipping...');
      return;
    }
    hasInitializedRef.current = true;
    
    try {
      // Stage 1: Initialize core systems (0-25%)
      moveToStage('initializing', 10);
      
      // Perform basic initialization checks
      await new Promise(resolve => setTimeout(resolve, 100)); // Simulate initialization
      
      // Stage 2: Load database (25-60%)
      moveToStage('loading-database', 25);
      
      // Initialize data stores with progressive loading
      const dataPromises = [
        initializeColors().catch(err => {
          console.warn('[Startup] Colors initialization failed:', err);
          return [];
        }),
        initializeProducts().catch(err => {
          console.warn('[Startup] Products initialization failed:', err);
          return [];
        })
      ];
      
      // Update progress as data loads
      setProgress(40);
      await Promise.allSettled(dataPromises);
      setProgress(60);
      
      // Stage 3: Load UI components (60-85%)
      moveToStage('loading-ui', 60);
      
      // Basic UI setup
      await new Promise(resolve => setTimeout(resolve, 50));
      setProgress(70);
      
      // Stage 4: Preload critical components (85-95%)
      moveToStage('preloading-components', 85);
      
      // Preload critical components in background
      await preloadCriticalComponents();
      setProgress(95);
      
      // Stage 5: Ready (95-100%)
      moveToStage('ready', 100);
      
      // Calculate final metrics
      const totalTime = Date.now() - startTimeRef.current;
      setMetrics({
        startTime: startTimeRef.current,
        stageTimings: { ...stageTimingsRef.current },
        totalTime
      });
      
      // Small delay to show completion
      await new Promise(resolve => setTimeout(resolve, 200));
      setIsReady(true);
      
      console.log(`[Startup] Application ready in ${totalTime}ms`);
      
    } catch (error) {
      console.error('[Startup] Initialization failed:', error);
      // Continue anyway with degraded functionality
      setIsReady(true);
    }
  }, [initializeColors, initializeProducts]);
  
  // Health check system
  const performHealthCheck = useCallback(async () => {
    try {
      const pingResult = await window.ipc?.invoke('ping') as { success?: boolean };
      if (!pingResult?.success) {
        console.warn('[Startup] Health check failed - IPC not responding');
        return false;
      }
      
      // Test database connectivity
      try {
        await window.ipc?.invoke('color:getAll');
        return true;
      } catch (err) {
        console.warn('[Startup] Database health check failed:', err);
        return false;
      }
    } catch (err) {
      console.warn('[Startup] Health check error:', err);
      return false;
    }
  }, []);
  
  // Background optimization
  const performBackgroundOptimization = useCallback(async () => {
    if (!isReady) {return;}
    
    // Optimize after startup is complete
    setTimeout(async () => {
      try {
        // Preload remaining components
        await Promise.allSettled([
          import('../components/Products/ProductsPanel'),
          import('../components/ColorComparison/ColorComparisonModal')
        ]);
        
        console.log('[Startup] Background optimization complete');
      } catch (err) {
        console.warn('[Startup] Background optimization failed:', err);
      }
    }, 2000); // Wait 2 seconds after app is ready
  }, [isReady]);
  
  // Initialize on mount
  useEffect(() => {
    initializeApp();
  }, [initializeApp]);
  
  // Run background optimization when ready
  useEffect(() => {
    performBackgroundOptimization();
  }, [performBackgroundOptimization]);
  
  return {
    stage,
    progress,
    isReady,
    metrics,
    performHealthCheck,
    // Utility functions for debugging
    getStartupTime: () => metrics?.totalTime || 0,
    getStageTimings: () => metrics?.stageTimings || {},
    retryInitialization: initializeApp
  };
};