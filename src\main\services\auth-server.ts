/**
 * @file auth-server.ts
 * @description Local server to handle OAuth callbacks for desktop app
 */

import { createServer, IncomingMessage, ServerResponse } from 'http';
import { parse } from 'url';
import { getSupabaseClient } from './supabase-client';

export class AuthServer {
  private server: any = null;
  private port = 54321;
  private authCallback: ((success: boolean, error?: string) => void) | null = null;

  async start(): Promise<string> {
    return new Promise((resolve, reject) => {
      this.server = createServer(async (req: IncomingMessage, res: ServerResponse) => {
        const url = parse(req.url || '', true);
        console.log(`[AuthServer] Received request: ${req.method} ${req.url}`);
        
        if (url.pathname === '/auth/callback') {
          // Extract the hash fragment (Supabase sends tokens in hash)
          const html = `
            <!DOCTYPE html>
            <html>
            <head>
              <title>ChromaSync - Authentication</title>
              <style>
                body {
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  height: 100vh;
                  margin: 0;
                  background: #f5f5f5;
                }
                .container {
                  text-align: center;
                  padding: 2rem;
                  background: white;
                  border-radius: 8px;
                  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .success { color: #4CAF50; }
                .error { color: #f44336; }
              </style>
            </head>
            <body>
              <div class="container">
                <h2>Processing authentication...</h2>
                <p>Please wait while we complete your sign-in.</p>
              </div>
              <script>
                // Send the URL fragment to the server
                if (window.location.hash) {
                  fetch('/auth/complete', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                      hash: window.location.hash,
                      search: window.location.search 
                    })
                  }).then(() => {
                    document.querySelector('.container').innerHTML = 
                      '<h2 class="success">✓ Authentication Successful!</h2>' +
                      '<p>You can close this window and return to ChromaSync.</p>';
                    setTimeout(() => window.close(), 2000);
                  }).catch(() => {
                    document.querySelector('.container').innerHTML = 
                      '<h2 class="error">Authentication Failed</h2>' +
                      '<p>Please close this window and try again.</p>';
                  });
                }
              </script>
            </body>
            </html>
          `;
          
          res.writeHead(200, { 'Content-Type': 'text/html' });
          res.end(html);
        } else if (url.pathname === '/auth/complete' && req.method === 'POST') {
          // Handle CORS
          res.setHeader('Access-Control-Allow-Origin', '*');
          res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
          res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
          
          // Handle the token exchange
          let body = '';
          req.on('data', chunk => body += chunk.toString());
          req.on('end', async () => {
            try {
              console.log('[AuthServer] Received token data from GitHub Pages');
              const data = JSON.parse(body);
              const hash = data.hash.substring(1); // Remove the #
              const params = new URLSearchParams(hash);
              
              const accessToken = params.get('access_token');
              const refreshToken = params.get('refresh_token');
              
              console.log('[AuthServer] Extracted tokens:', { 
                hasAccessToken: !!accessToken, 
                hasRefreshToken: !!refreshToken 
              });
              
              if (accessToken && refreshToken) {
                // Set the session in Supabase
                const supabase = getSupabaseClient();
                console.log('[AuthServer] Setting session in Supabase...');
                const { data: sessionData, error } = await supabase.auth.setSession({
                  access_token: accessToken,
                  refresh_token: refreshToken
                });
                
                if (!error && sessionData.session) {
                  console.log('[AuthServer] Session set successfully via local server');
                  console.log('[AuthServer] User email:', sessionData.session.user?.email);
                  if (this.authCallback) {
                    this.authCallback(true);
                  }
                } else {
                  console.error('[AuthServer] Failed to set session:', error);
                  if (this.authCallback) {
                    this.authCallback(false, error?.message);
                  }
                }
              } else {
                console.error('[AuthServer] No tokens found in callback');
                if (this.authCallback) {
                  this.authCallback(false, 'No tokens found');
                }
              }
              
              res.writeHead(200, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({ success: true }));
            } catch (err) {
              console.error('[AuthServer] Error processing callback:', err);
              res.writeHead(500, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({ error: err.message }));
            }
          });
        } else if (url.pathname === '/auth/complete' && req.method === 'OPTIONS') {
          // Handle CORS preflight
          res.setHeader('Access-Control-Allow-Origin', '*');
          res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
          res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
          res.writeHead(200);
          res.end();
        } else {
          res.writeHead(404);
          res.end();
        }
      });

      this.server.listen(this.port, 'localhost', () => {
        console.log(`[Auth] Local server started on http://localhost:${this.port}`);
        resolve(`http://localhost:${this.port}/auth/callback`);
      });

      this.server.on('error', (err: any) => {
        if (err.code === 'EADDRINUSE') {
          this.port++;
          this.server.close();
          this.start().then(resolve).catch(reject);
        } else {
          reject(err);
        }
      });
    });
  }

  stop() {
    if (this.server) {
      this.server.close();
      this.server = null;
      console.log('[Auth] Local server stopped');
    }
  }

  onAuthComplete(callback: (success: boolean, error?: string) => void) {
    this.authCallback = callback;
  }
}