-- <PERSON>ript to remove duplicate color assignments to products
-- Keep only one instance of each unique color per product

-- Create a temporary table with the records we want to keep (one per product+color combination)
CREATE TEMPORARY TABLE product_colors_clean AS
SELECT 
    pc.product_id,
    pc.color_id,
    MIN(pc.rowid) as keep_rowid,  -- Keep the first occurrence
    pc.display_order,
    pc.added_at
FROM product_colors pc
GROUP BY pc.product_id, pc.color_id;

-- Count duplicates before cleanup
SELECT 'BEFORE CLEANUP:' as status;
SELECT 
    p.name as product_name,
    COUNT(*) as total_colors,
    COUNT(DISTINCT c.hex) as unique_colors,
    COUNT(*) - COUNT(DISTINCT c.hex) as duplicates
FROM product_colors pc 
JOIN products p ON pc.product_id = p.id 
JOIN colors c ON pc.color_id = c.id 
GROUP BY p.id, p.name
HAVING duplicates > 0
ORDER BY duplicates DESC;

-- Delete all records from product_colors
DELETE FROM product_colors;

-- Insert back only the unique records
INSERT INTO product_colors (product_id, color_id, display_order, added_at)
SELECT 
    pcc.product_id,
    pcc.color_id,
    pcc.display_order,
    pcc.added_at
FROM product_colors_clean pcc;

-- Count after cleanup
SELECT 'AFTER CLEANUP:' as status;
SELECT 
    p.name as product_name,
    COUNT(*) as total_colors,
    COUNT(DISTINCT c.hex) as unique_colors,
    COUNT(*) - COUNT(DISTINCT c.hex) as duplicates
FROM product_colors pc 
JOIN products p ON pc.product_id = p.id 
JOIN colors c ON pc.color_id = c.id 
WHERE p.name IN ('IVG BAR', '2400 BARS', 'IVG 12k Switch')
GROUP BY p.id, p.name
ORDER BY total_colors DESC;

-- Show summary
SELECT 'SUMMARY:' as status;
SELECT COUNT(*) as total_relationships FROM product_colors;