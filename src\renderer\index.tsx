/**
 * @file index.tsx
 * @description Entry point for the renderer process
 */

import React, { useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
// ErrorBoundary is defined below
import './index.css'; // This imports Tailwind
import './test.css';  // This imports our test CSS

// Add error boundary to catch initialization errors
window.addEventListener('error', (event) => {
  console.error('Global error caught:', event.error);
  console.error('Stack:', event.error?.stack);
});

// Clear potentially corrupted localStorage in production
if (process.env.NODE_ENV === 'production') {
  try {
    const colorStorage = localStorage.getItem('color-storage');
    if (colorStorage) {
      const parsed = JSON.parse(colorStorage);
      // Check for corrupted state
      if (parsed.state && (!Array.isArray(parsed.state.colors) || 
          !Array.isArray(parsed.state.pantoneColors) || 
          !Array.isArray(parsed.state.ralColors))) {
        console.warn('Clearing corrupted color storage');
        localStorage.removeItem('color-storage');
      }
    }
  } catch (e) {
    console.error('Error checking localStorage:', e);
    localStorage.removeItem('color-storage');
  }
}

/**
 * Toggle Token System Component
 * This component adds a keyboard shortcut to toggle the token system in development
 */
const DevToolsKeyboardShortcuts: React.FC = () => {
  useEffect(() => {
    // Toggle token system with Alt+Shift+T
    const handleKeyDown = (e: KeyboardEvent) => {
      // Alt+Shift+T to toggle token system
      if (e.altKey && e.shiftKey && e.key === 'T') {
        // Get token system state from localStorage
        const useNewTokenSystem = localStorage.getItem('useNewTokenSystem') === 'true';
        // Toggle state
        localStorage.setItem('useNewTokenSystem', (!useNewTokenSystem).toString());
        console.log(`Token system ${!useNewTokenSystem ? 'enabled' : 'disabled'}`);
        // Reload to apply changes
        window.location.reload();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  return null;
};

// Mount React app
const container = document.getElementById('root');
if (!container) {
  throw new Error('Failed to find the root element');
}

const root = ReactDOM.createRoot(container);

// Only render the DevTools component in development
const isDev = process.env.NODE_ENV === 'development';

// Error boundary component
class ErrorBoundary extends React.Component<{children: React.ReactNode}, {hasError: boolean, error: Error | null}> {
  constructor(props: {children: React.ReactNode}) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('React Error Boundary caught:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '20px', color: 'red' }}>
          <h1>Something went wrong</h1>
          <pre>{this.state.error?.message}</pre>
          <pre>{this.state.error?.stack}</pre>
          <button onClick={() => {
            localStorage.clear();
            window.location.reload();
          }}>Clear Storage and Reload</button>
        </div>
      );
    }

    return this.props.children;
  }
}

root.render(
  <React.StrictMode>
    <ErrorBoundary>
      <App />
      {isDev && <DevToolsKeyboardShortcuts />}
    </ErrorBoundary>
  </React.StrictMode>
); 