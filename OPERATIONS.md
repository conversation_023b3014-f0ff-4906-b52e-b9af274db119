# ChromaSync Operations Guide

Complete guide for deploying and operating ChromaSync in production environments with cloud sync capabilities.

## Table of Contents

1. [Production Deployment](#production-deployment)
2. [Environment Configuration](#environment-configuration)
3. [Email Service Setup](#email-service-setup)
4. [Monitoring & Health Checks](#monitoring--health-checks)
5. [Backup & Recovery](#backup--recovery)
6. [Performance Optimization](#performance-optimization)
7. [Troubleshooting](#troubleshooting)
8. [Emergency Procedures](#emergency-procedures)
9. [Production Readiness Checklist](#production-readiness-checklist)

---

## Production Deployment

### Prerequisites
- Node.js 18+ and npm 9+
- Platform-specific build tools:
  - **Windows**: Windows Build Tools
  - **macOS**: Xcode Command Line Tools
  - **Linux**: build-essential package

### Production Build Process

```bash
# Install dependencies
npm ci --production=false

# Run tests and quality checks
npm run test
npm run lint
npm run typecheck

# Build application
npm run build

# Create platform packages
npm run package:win   # Windows installer
npm run package:mac   # macOS DMG  
npm run package:linux # Linux AppImage
npm run package       # All platforms
```

### Code Signing

#### Windows Code Signing
```bash
# Set environment variables for certificate
export CSC_LINK="path/to/certificate.pfx"
export CSC_KEY_PASSWORD="certificate_password"

# Build with signing
npm run package:win
```

#### macOS Code Signing & Notarization
```bash
# Requires Apple Developer certificate
# Set in environment or electron-builder config
export APPLE_ID="<EMAIL>"
export APPLE_APP_SPECIFIC_PASSWORD="app-specific-password"
export APPLE_TEAM_ID="team-id"

# Build with signing and notarization
npm run package:mac
```

### Release Process

1. **Update Version**
   ```bash
   npm version patch|minor|major
   ```

2. **Build All Platforms**
   ```bash
   npm run package
   ```

3. **Create GitHub Release**
   - Tag version in Git
   - Upload installers to GitHub Releases
   - Include generated release notes

### Auto-Update Configuration

Configure in `electron-builder.json`:
```json
{
  "publish": {
    "provider": "github",
    "owner": "your-org",
    "repo": "chromasync"
  }
}
```

---

## Environment Configuration

### Supabase Cloud Setup

1. **Create Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create new project with strong database password
   - Choose region closest to users

2. **Apply Database Schema**
   ```bash
   psql -h db.xxx.supabase.co -p 5432 -d postgres -U postgres -f scripts/supabase-schema.sql
   ```

   **Critical Tables:**
   - `colors` - Color data with JSONB optimization
   - `products` - Product information
   - `organizations` - Multi-tenant support
   - `organization_invitations` - Team invitations
   - `user_profiles` - Extended user data

3. **Configure Row Level Security (RLS)**
   ```sql
   -- Enable RLS on all tables
   ALTER TABLE colors ENABLE ROW LEVEL SECURITY;
   ALTER TABLE products ENABLE ROW LEVEL SECURITY;
   ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
   
   -- Create organization access policies
   CREATE POLICY "organization_access" ON colors
     FOR ALL USING (
       EXISTS (
         SELECT 1 FROM user_profiles 
         WHERE user_profiles.user_id = auth.uid() 
         AND user_profiles.organization_id = colors.organization_id
       )
     );
   ```

### Authentication Setup

1. **Google OAuth Configuration**
   - Create OAuth 2.0 credentials in Google Cloud Console
   - Enable Google+ API
   - Configure authorized domains and redirect URIs:
     ```
     Authorized JavaScript origins:
     - https://your-project.supabase.co
     - https://your-domain.com
     
     Authorized redirect URIs:
     - https://your-project.supabase.co/auth/v1/callback
     - https://auth.your-domain.com/auth/callback
     ```

2. **Environment Variables**
   ```bash
   # Supabase Configuration
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_ANON_KEY=your-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   
   # Environment
   NODE_ENV=production
   ```

### PKCE Flow Configuration

The app uses PKCE (Proof Key for Code Exchange) for secure OAuth:

```typescript
// Already configured in src/main/services/supabase-client.ts
const supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    flowType: 'pkce', // PKCE flow enabled
    storage: customSecureStorage, // Encrypted token storage
    persistSession: false,
    detectSessionInUrl: false
  }
});
```

---

## Email Service Setup

### Environment-Specific Configuration

#### Development Mode (`npm run dev`)
- Uses `.env` file directly via `dotenv`
- Email credentials loaded from environment variables

#### Local Build (`npm start`)
- Uses `.env` file directly via `dotenv`
- Works because it runs from source directory

#### Production Build (`npm run package`)
- Uses `app-config.json` created at build time
- Credentials embedded in the build (in `/out` directory)
- Config loaded via `config-loader.ts`

### Configuration Variables

```bash
# Email Service Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
```

### Build Process for Email

1. `npm run build` includes `node scripts/build-config.cjs`
2. Creates `/out/app-config.json` with credentials from `.env`
3. Config file is included in packaged app
4. Runtime loads credentials from:
   - Environment variables (if available)
   - `/out/app-config.json` (fallback)

### Security Considerations

⚠️ **WARNING**: Production builds embed credentials in the distributed app!

**Recommended alternatives for production:**
1. **User-provided credentials**: Let users enter their own email API keys
2. **Server proxy**: Route emails through your server instead
3. **Encrypted storage**: Store credentials encrypted with user-specific key

### Testing Email Service

```bash
# Test email functionality
npm run test:email

# Test production build email
npm run build
npm start
# Test invitation sending through UI
```

---

## Monitoring & Health Checks

### Built-in Health Endpoints

```typescript
GET /health                    // Application health
GET /api/database/health      // Database connectivity  
GET /api/sync/status          // Sync service status
```

### Key Performance Metrics

Monitor these critical metrics:
- **Database Performance**: <20ms local queries, <200ms cloud queries
- **Sync Performance**: <500ms batch updates, real-time notifications
- **Memory Usage**: <200MB typical usage
- **Color Processing**: 100,000+ colors supported
- **Startup Time**: <3 seconds typical
- **Authentication Success**: >99% success rate

### Performance Benchmarks

```bash
# Quick performance test (10k colors)
npm run test:perf:quick  # Should complete <2 seconds

# Full performance test (100k colors)  
npm run test:perf:full   # Should complete <10 seconds

# Comprehensive performance testing
npm run test:perf       # Performance benchmarks with ts-node
```

### Logging Configuration

```typescript
// Production logging setup
const logger = {
  level: process.env.NODE_ENV === 'production' ? 'error' : 'debug',
  transports: [
    new winston.transports.File({ filename: 'chromasync.log' }),
    new winston.transports.Console()
  ]
};
```

### Sync Service Health

Critical sync service checks:
- Organization exists in local database
- All required tables present
- Supabase connection active
- User authentication valid
- Sync version compatibility

---

## Backup & Recovery

### Database Backup Strategies

#### Supabase Cloud Backup
```bash
# Manual backup via Supabase CLI
supabase db dump --file backup_$(date +%Y%m%d).sql

# Automated backups
# Configure in Supabase Dashboard > Settings > Database
```

#### Local SQLite Backup
```bash
# Manual local database backup
cp ~/Library/Application\ Support/chroma-sync/chromasync.db backup_$(date +%Y%m%d).db

# Verify backup integrity
sqlite3 backup_$(date +%Y%m%d).db ".schema"
```

### Data Export for Users

```typescript
// Built-in GDPR-compliant data export
window.syncAPI.exportData()    // Complete JSON export
// Includes: colors, products, organizations, metadata
```

### Recovery Procedures

1. **Local Database Corruption**
   - Stop application
   - Restore from backup
   - Restart and verify sync

2. **Cloud Sync Issues**
   - Reset sync state: Settings > Sync > Reset Sync Data
   - Re-authenticate if necessary
   - Trigger manual sync

3. **Complete Data Loss**
   - Restore from latest backup
   - Verify data integrity
   - Re-establish cloud sync connection

---

## Performance Optimization

### Database Performance

```sql
-- Monitor Supabase table sizes
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Regular maintenance
VACUUM ANALYZE colors;
VACUUM ANALYZE products;
```

### Application Performance

- **Virtual Scrolling**: Automatically enabled for lists >100 items
- **Batch Operations**: 500ms debounced sync updates for API efficiency
- **Memory Management**: Automatic cleanup of subscriptions and listeners
- **Query Optimization**: Prepared statements and proper indexing

### Sync Performance Optimization

```typescript
// Retry logic for sync reliability
async performInitialSyncWithRetry(maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      await this.performInitialSync();
      return; // Success
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 2000 * (i + 1)));
    }
  }
}
```

---

## Troubleshooting

### Common Production Issues

#### Sync Service Failures

**Symptoms**: Users unable to sync data, authentication loops

**Diagnosis**:
```bash
# Check authentication state
# Settings > Sync > View Sync Status

# Test Supabase connectivity
curl -H "apikey: YOUR_ANON_KEY" \
     "https://your-project.supabase.co/rest/v1/colors?limit=1"
```

**Resolution**:
1. Implement retry logic in sync service
2. Validate first-run setup (organization, tables, auth)
3. Add sync status indicators to UI

#### Authentication Issues

**Symptoms**: Login loops, session expiration

**Resolution**:
```bash
# Clear authentication state
rm -rf ~/Library/Application\ Support/chroma-sync/
# Or via app: Settings > Reset Application Data
```

#### Database Connectivity Problems

**Local Database**:
```bash
# Test local database access
sqlite3 ~/Library/Application\ Support/chroma-sync/chromasync.db ".tables"
```

**Cloud Database**:
```bash
# Test Supabase connection
psql -h db.xxx.supabase.co -p 5432 -d postgres -U postgres -c "SELECT version();"
```

#### Email Delivery Issues

**Diagnosis**:
```bash
# Test email service directly
npm run test:email-direct

# Verify SMTP credentials
npm run test-smtp-connection
```

**Common causes**:
- Incorrect SMTP credentials
- Firewall blocking SMTP ports
- Email service rate limiting

### Debug Mode

```bash
# Start with comprehensive logging
npm start -- --enable-logging --verbose

# Access Electron DevTools
# View > Toggle Developer Tools

# Check specific log files
tail -f ~/Library/Application\ Support/chroma-sync/logs/main.log
```

### Log File Locations

- **Application Logs**: `~/Library/Application Support/chroma-sync/logs/`
- **Database File**: `~/Library/Application Support/chroma-sync/chromasync.db`
- **Configuration**: `~/Library/Application Support/chroma-sync/config.json`
- **Crash Reports**: `~/Library/Application Support/chroma-sync/crashes/`

---

## Emergency Procedures

### Critical Security Incident

1. **Immediate Response** (< 1 hour)
   - Disable affected user accounts in Supabase Dashboard
   - Rotate compromised API keys in environment configuration
   - Document incident details and timeline

2. **Investigation** (< 4 hours)
   - Review Supabase audit logs
   - Assess data exposure scope
   - Identify root cause and attack vector

3. **Recovery** (< 24 hours)
   - Deploy security patches
   - Restore service with enhanced security measures
   - Notify affected users with transparent communication

### Service Outage Response

1. **Detection**: Monitor health endpoints and user reports
2. **Triage**: Identify affected components (local vs cloud services)
3. **Communication**: Update status page and notify users via email
4. **Resolution**: Deploy fixes and verify functionality restoration
5. **Post-mortem**: Document lessons learned and improve processes

### Data Corruption Emergency

1. **Stop sync service** immediately to prevent propagation
2. **Assess corruption scope** (local database vs cloud)
3. **Restore from most recent clean backup**
4. **Verify data integrity** before resuming operations
5. **Implement additional safeguards** to prevent recurrence

### Contact Information

- **Operations Team**: <EMAIL> (24-hour response)
- **Security Team**: <EMAIL> (emergency response)
- **Development Team**: <EMAIL> (technical issues)

---

## Production Readiness Checklist

### Pre-Deployment Validation

#### Technical Requirements
- [ ] All tests passing (`npm test`)
- [ ] Code quality checks passed (`npm run lint`)
- [ ] TypeScript compilation clean (`npm run typecheck`)
- [ ] Performance benchmarks met (`npm run test:perf`)
- [ ] Security audit clean (`npm audit`)

#### Infrastructure Setup
- [ ] Supabase project created and configured
- [ ] Database schema applied with RLS enabled
- [ ] Google OAuth configured and tested
- [ ] Email service configured and tested
- [ ] Environment variables properly set
- [ ] Code signing certificates configured and valid

#### Sync Service Reliability
- [ ] Retry logic implemented for sync operations
- [ ] Schema compatibility handling for Supabase/SQLite differences
- [ ] First-run validation (organization, tables, auth)
- [ ] Sync status indicators added to UI
- [ ] Error recovery mechanisms tested

#### Testing Validation
- [ ] New user flow tested (account → organization → sync → data)
- [ ] Error recovery tested (network interruption → app restart → sync completion)
- [ ] Large dataset tested (1000+ colors, 50+ products)
- [ ] Cross-platform compatibility verified

#### Production Environment
- [ ] Auto-update mechanism tested and functional
- [ ] Backup strategy implemented and tested
- [ ] Monitoring and health checks enabled
- [ ] Performance metrics collection active
- [ ] Error reporting and logging configured

#### Security & Compliance
- [ ] GDPR compliance features tested (data export, account deletion)
- [ ] Security headers and policies configured
- [ ] Authentication flow security validated
- [ ] Data encryption verified (at rest and in transit)

#### Documentation & Support
- [ ] User documentation updated
- [ ] API documentation current
- [ ] Troubleshooting guides available
- [ ] Support channels established

### Post-Deployment Monitoring

Monitor these metrics for the first 48 hours:
- User registration and authentication success rates
- Sync operation success rates and latency
- Error rates by operation type
- Performance metrics vs. benchmarks
- User feedback and support requests

### Rollback Plan

If critical issues are discovered:
1. **Immediate**: Disable auto-updates to prevent further distribution
2. **Short-term**: Communicate issue to users via in-app notification
3. **Resolution**: Deploy hotfix or rollback to previous stable version
4. **Follow-up**: Post-mortem analysis and process improvements

---

*This operations guide consolidates production deployment, monitoring, and emergency procedures to ensure reliable ChromaSync operations in enterprise environments.*