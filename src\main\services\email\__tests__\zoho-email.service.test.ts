/**
 * @file zoho-email.service.test.ts
 * @description Comprehensive tests for ZohoEmailService with exponential backoff and circuit breaker
 */

import { describe, it, expect, beforeEach, afterEach, vi, MockedFunction } from 'vitest';
import axios from 'axios';
import fs from 'fs/promises';
import { ZohoEmailService } from '../zoho-email.service';
import { getConfig } from '../../../utils/config-loader';

// Mock dependencies
vi.mock('axios');
vi.mock('fs/promises');
vi.mock('../../../utils/config-loader');
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn().mockReturnValue('/test/app/data')
  }
}));
vi.mock('../email-retry-queue', () => ({
  emailRetryQueue: {
    initialize: vi.fn().mockResolvedValue(undefined),
    queueEmail: vi.fn().mockResolvedValue('test-queue-id'),
    getQueueStats: vi.fn().mockReturnValue({
      totalQueued: 0,
      readyToProcess: 0,
      processed: 0,
      failed: 0
    })
  }
}));

const mockedAxios = axios as jest.Mocked<typeof axios>;
const mockedFs = fs as jest.Mocked<typeof fs>;
const mockedGetConfig = getConfig as MockedFunction<typeof getConfig>;

describe('ZohoEmailService', () => {
  let service: ZohoEmailService;

  beforeEach(async () => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Mock config values
    mockedGetConfig.mockImplementation((key: string) => {
      const config: Record<string, string> = {
        'ZOHO_CLIENT_ID': 'test-client-id',
        'ZOHO_CLIENT_SECRET': 'test-client-secret',
        'ZOHO_ACCOUNT_ID': 'test-account-id',
        'ZOHO_REFRESH_TOKEN': 'test-refresh-token',
        'ZOHO_SUPPORT_ALIAS': '<EMAIL>'
      };
      return config[key] || '';
    });
    
    // Mock fs operations
    mockedFs.readFile.mockRejectedValue(new Error('File not found'));
    mockedFs.writeFile.mockResolvedValue();
    
    // Create service instance
    service = new ZohoEmailService();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize successfully with valid configuration', async () => {
      await expect(service.initialize()).resolves.not.toThrow();
    });

    it('should throw error with missing configuration', async () => {
      mockedGetConfig.mockReturnValue('');
      service = new ZohoEmailService();
      
      await expect(service.initialize()).rejects.toThrow('Zoho Email Service not configured');
    });

    it('should validate ZOHO_REGION configuration', async () => {
      mockedGetConfig.mockImplementation((key: string) => {
        if (key === 'ZOHO_REGION') return 'INVALID';
        return 'test-value';
      });
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      service = new ZohoEmailService();
      
      await service.initialize();
      
      expect(consoleSpy).toHaveBeenCalledWith('[ZohoEmail] Invalid ZOHO_REGION: INVALID');
      consoleSpy.mockRestore();
    });

    it('should accept valid EU region', async () => {
      mockedGetConfig.mockImplementation((key: string) => {
        if (key === 'ZOHO_REGION') return 'EU';
        return 'test-value';
      });
      
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      service = new ZohoEmailService();
      
      await service.initialize();
      
      expect(consoleSpy).toHaveBeenCalledWith('[ZohoEmail] Using EU region (accounts.zoho.eu)');
      consoleSpy.mockRestore();
    });
  });

  describe('Exponential Backoff', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should retry with exponential backoff on rate limit error', async () => {
      const rateLimitError = {
        response: {
          data: {
            error: 'Access Denied',
            error_description: 'too many requests'
          }
        }
      };

      // Mock axios to fail with rate limit error, then succeed
      mockedAxios.post
        .mockRejectedValueOnce(rateLimitError)
        .mockRejectedValueOnce(rateLimitError)
        .mockResolvedValueOnce({
          data: {
            access_token: 'new-token',
            expires_in: 3600
          }
        });

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      // This should trigger the refresh token logic
      await expect(service.sendEmail({
        to: '<EMAIL>',
        subject: 'Test',
        content: 'Test content'
      })).resolves.toBe(true);

      expect(mockedAxios.post).toHaveBeenCalledTimes(3); // 2 failures + 1 success
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Rate limit exceeded, retrying in')
      );
      
      consoleSpy.mockRestore();
    });

    it('should fail after max retries reached', async () => {
      const rateLimitError = {
        response: {
          data: {
            error: 'Access Denied',
            error_description: 'too many requests'
          }
        }
      };

      // Mock axios to always fail with rate limit error
      mockedAxios.post.mockRejectedValue(rateLimitError);

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      await expect(service.sendEmail({
        to: '<EMAIL>',
        subject: 'Test',
        content: 'Test content'
      })).resolves.toBe(true); // Should return true because it gets queued

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Max retries')
      );
      
      consoleSpy.mockRestore();
    });

    it('should retry on network errors', async () => {
      const networkError = new Error('ENOTFOUND');
      (networkError as any).code = 'ENOTFOUND';

      // Mock to fail with network error, then succeed
      mockedAxios.post
        .mockRejectedValueOnce(networkError)
        .mockResolvedValueOnce({
          data: {
            access_token: 'new-token',
            expires_in: 3600
          }
        });

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      await expect(service.sendEmail({
        to: '<EMAIL>',
        subject: 'Test',
        content: 'Test content'
      })).resolves.toBe(true);

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Retryable error, retrying in')
      );
      
      consoleSpy.mockRestore();
    });

    it('should not retry on non-retryable errors', async () => {
      const badRequestError = {
        response: {
          status: 400,
          data: { error: 'Bad Request' }
        }
      };

      mockedAxios.post.mockRejectedValue(badRequestError);

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      await expect(service.sendEmail({
        to: '<EMAIL>',
        subject: 'Test',
        content: 'Test content'
      })).resolves.toBe(true); // Gets queued for retry

      // Should only try once for non-retryable errors
      expect(mockedAxios.post).toHaveBeenCalledTimes(1);
      
      consoleSpy.mockRestore();
    });
  });

  describe('Circuit Breaker', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should open circuit breaker after max failures', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { error: 'Internal Server Error' }
        }
      };

      mockedAxios.post.mockRejectedValue(serverError);
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      // Try to send multiple emails to trigger circuit breaker
      for (let i = 0; i < 4; i++) {
        await service.sendEmail({
          to: '<EMAIL>',
          subject: 'Test',
          content: 'Test content'
        });
      }

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Circuit breaker opening after 3 failures')
      );
      
      consoleSpy.mockRestore();
    });

    it('should block operations when circuit breaker is open', async () => {
      // First, open the circuit breaker by causing failures
      const serverError = {
        response: {
          status: 500,
          data: { error: 'Internal Server Error' }
        }
      };

      mockedAxios.post.mockRejectedValue(serverError);
      
      // Cause enough failures to open the circuit
      for (let i = 0; i < 3; i++) {
        await service.sendEmail({
          to: '<EMAIL>',
          subject: 'Test',
          content: 'Test content'
        });
      }

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      // Now try to send another email - should be blocked
      const result = await service.sendEmail({
        to: '<EMAIL>',
        subject: 'Test',
        content: 'Test content'
      });

      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith(
        '[ZohoEmail] Circuit breaker is OPEN, cannot send email'
      );
      
      consoleSpy.mockRestore();
    });

    it('should transition to half-open after timeout', async () => {
      // This test would require mocking timers to properly test the timeout behavior
      // For now, we'll test the logic directly
      const service_: any = service;
      
      // Manually set circuit breaker state
      service_.circuitBreakerState = 'OPEN';
      service_.lastFailureTime = Date.now() - 70000; // 70 seconds ago
      service_.circuitBreakerTimeout = 60000; // 1 minute
      
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      
      // Call the private method directly
      const canProceed = service_.checkCircuitBreaker();
      
      expect(canProceed).toBe(true);
      expect(consoleSpy).toHaveBeenCalledWith(
        '[ZohoEmail] Circuit breaker moving to HALF_OPEN state'
      );
      
      consoleSpy.mockRestore();
    });
  });

  describe('Email Sending', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should send email successfully with valid configuration', async () => {
      // Mock successful token refresh
      mockedAxios.post
        .mockResolvedValueOnce({
          data: {
            access_token: 'test-token',
            expires_in: 3600
          }
        })
        .mockResolvedValueOnce({
          data: {
            data: {
              messageId: 'test-message-id'
            }
          }
        });

      const result = await service.sendEmail({
        to: '<EMAIL>',
        subject: 'Test Subject',
        content: 'Test content',
        isHtml: false
      });

      expect(result).toBe(true);
      expect(mockedAxios.post).toHaveBeenCalledTimes(2); // Token refresh + send email
    });

    it('should use correct domain for EU region', async () => {
      mockedGetConfig.mockImplementation((key: string) => {
        if (key === 'ZOHO_REGION') return 'EU';
        return 'test-value';
      });
      
      // Reinitialize with EU region
      service = new ZohoEmailService();
      await service.initialize();

      // Mock successful responses
      mockedAxios.post
        .mockResolvedValueOnce({
          data: {
            access_token: 'test-token',
            expires_in: 3600
          }
        })
        .mockResolvedValueOnce({
          data: {
            data: { messageId: 'test-message-id' }
          }
        });

      await service.sendEmail({
        to: '<EMAIL>',
        subject: 'Test',
        content: 'Test content'
      });

      // Check that EU domains were used
      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://accounts.zoho.eu/oauth/v2/token',
        null,
        expect.any(Object)
      );
      
      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://mail.zoho.eu/api/accounts/test-account-id/messages',
        expect.any(Object),
        expect.any(Object)
      );
    });

    it('should queue email for retry on failure', async () => {
      const serverError = {
        response: {
          status: 500,
          data: { error: 'Server Error' }
        }
      };

      mockedAxios.post.mockRejectedValue(serverError);

      const result = await service.sendEmail({
        to: '<EMAIL>',
        subject: 'Test',
        content: 'Test content'
      });

      // Should return true because email gets queued for retry
      expect(result).toBe(true);
    });
  });

  describe('Invitation Emails', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should send invitation email with high priority', async () => {
      // Mock successful responses
      mockedAxios.post
        .mockResolvedValueOnce({
          data: {
            access_token: 'test-token',
            expires_in: 3600
          }
        })
        .mockResolvedValueOnce({
          data: {
            data: { messageId: 'test-message-id' }
          }
        });

      const result = await service.sendInvitationEmail('<EMAIL>', {
        organizationName: 'Test Org',
        inviterName: 'Test User',
        role: 'member',
        token: 'test-token-123',
        expiresAt: new Date('2024-12-31')
      });

      expect(result).toBe(true);
      
      // Check that the email content includes the invitation token
      const emailCall = mockedAxios.post.mock.calls.find(call => 
        call[0].includes('mail.zoho.com/api/accounts')
      );
      expect(emailCall).toBeDefined();
      expect(emailCall![1].content).toContain('test-token-123');
    });

    it('should include enhanced manual instructions in invitation', async () => {
      mockedAxios.post
        .mockResolvedValueOnce({
          data: {
            access_token: 'test-token',
            expires_in: 3600
          }
        })
        .mockResolvedValueOnce({
          data: {
            data: { messageId: 'test-message-id' }
          }
        });

      await service.sendInvitationEmail('<EMAIL>', {
        organizationName: 'Test Org',
        inviterName: 'Test User',
        role: 'member',
        token: 'test-token-123',
        expiresAt: new Date('2024-12-31')
      });

      const emailCall = mockedAxios.post.mock.calls.find(call => 
        call[0].includes('mail.zoho.com/api/accounts')
      );
      
      const content = emailCall![1].content;
      expect(content).toContain('📋 How to Accept Your Invitation:');
      expect(content).toContain('🚨 Troubleshooting:');
      expect(content).toContain('Copy This Code');
    });
  });

  describe('Region Detection', () => {
    it('should validate valid regions', () => {
      const service_: any = service;
      
      expect(service_.isValidZohoRegion('EU')).toBe(true);
      expect(service_.isValidZohoRegion('US')).toBe(true);
      expect(service_.isValidZohoRegion('eu')).toBe(true); // Case insensitive
      expect(service_.isValidZohoRegion('INVALID')).toBe(false);
    });

    it('should return correct domains for regions', () => {
      const service_: any = service;
      
      const euDomains = service_.getZohoDomain('EU');
      expect(euDomains.auth).toBe('accounts.zoho.eu');
      expect(euDomains.api).toBe('mail.zoho.eu');
      
      const usDomains = service_.getZohoDomain('US');
      expect(usDomains.auth).toBe('accounts.zoho.com');
      expect(usDomains.api).toBe('mail.zoho.com');
      
      const defaultDomains = service_.getZohoDomain();
      expect(defaultDomains.auth).toBe('accounts.zoho.com');
      expect(defaultDomains.api).toBe('mail.zoho.com');
    });
  });

  describe('Queue Statistics', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should return queue statistics', () => {
      const stats = service.getRetryQueueStats();
      
      expect(stats).toHaveProperty('totalQueued');
      expect(stats).toHaveProperty('readyToProcess');
      expect(stats).toHaveProperty('processed');
      expect(stats).toHaveProperty('failed');
    });
  });

  describe('Error Handling', () => {
    beforeEach(async () => {
      await service.initialize();
    });

    it('should handle token refresh failures gracefully', async () => {
      const authError = {
        response: {
          status: 401,
          data: { error: 'Invalid refresh token' }
        }
      };

      mockedAxios.post.mockRejectedValue(authError);

      const result = await service.sendEmail({
        to: '<EMAIL>',
        subject: 'Test',
        content: 'Test content'
      });

      // Should handle gracefully and queue for retry
      expect(result).toBe(true);
    });

    it('should handle network connectivity issues', async () => {
      const networkError = new Error('Network Error');
      (networkError as any).code = 'ECONNRESET';

      mockedAxios.post.mockRejectedValue(networkError);

      const result = await service.sendEmail({
        to: '<EMAIL>',
        subject: 'Test',
        content: 'Test content'
      });

      expect(result).toBe(true); // Queued for retry
    });
  });
});