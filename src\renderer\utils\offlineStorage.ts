/**
 * Offline Storage using IndexedDB
 * Provides enterprise-level offline capabilities for ChromaSync
 */

interface OfflineRecord {
  id: string;
  type: 'color' | 'product' | 'datasheet' | 'product_color';
  action: 'create' | 'update' | 'delete';
  data: any;
  timestamp: number;
  synced: boolean;
  attempts: number;
  lastAttempt?: number;
  error?: string;
}

interface StoredData {
  colors: any[];
  products: any[];
  datasheets: any[];
  productColors: any[];
  lastSync: number;
  version: number;
}

export class OfflineStorage {
  private static instance: OfflineStorage;
  private db: IDBDatabase | null = null;
  private readonly dbName = 'ChromaSyncOffline';
  private readonly version = 1;
  private isInitialized = false;

  private constructor() {}

  static getInstance(): OfflineStorage {
    if (!OfflineStorage.instance) {
      OfflineStorage.instance = new OfflineStorage();
    }
    return OfflineStorage.instance;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        console.error('[OfflineStorage] Failed to open IndexedDB:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        this.isInitialized = true;
        console.log('[OfflineStorage] IndexedDB initialized successfully');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // Create object stores
        this.createObjectStores(db);
        console.log('[OfflineStorage] IndexedDB schema created');
      };
    });
  }

  private createObjectStores(db: IDBDatabase): void {
    // Offline queue for pending sync operations
    if (!db.objectStoreNames.contains('offlineQueue')) {
      const queueStore = db.createObjectStore('offlineQueue', { keyPath: 'id' });
      queueStore.createIndex('type', 'type', { unique: false });
      queueStore.createIndex('action', 'action', { unique: false });
      queueStore.createIndex('timestamp', 'timestamp', { unique: false });
      queueStore.createIndex('synced', 'synced', { unique: false });
    }

    // Cached data stores
    if (!db.objectStoreNames.contains('colors')) {
      const colorsStore = db.createObjectStore('colors', { keyPath: 'external_id' });
      colorsStore.createIndex('hex', 'hex', { unique: false });
      colorsStore.createIndex('name', 'name', { unique: false });
      colorsStore.createIndex('code', 'code', { unique: false });
      colorsStore.createIndex('updated_at', 'updated_at', { unique: false });
    }

    if (!db.objectStoreNames.contains('products')) {
      const productsStore = db.createObjectStore('products', { keyPath: 'external_id' });
      productsStore.createIndex('name', 'name', { unique: false });
      productsStore.createIndex('updated_at', 'updated_at', { unique: false });
    }

    if (!db.objectStoreNames.contains('datasheets')) {
      const datasheetsStore = db.createObjectStore('datasheets', { keyPath: 'external_id' });
      datasheetsStore.createIndex('product_id', 'product_id', { unique: false });
      datasheetsStore.createIndex('updated_at', 'updated_at', { unique: false });
    }

    if (!db.objectStoreNames.contains('productColors')) {
      const productColorsStore = db.createObjectStore('productColors', { keyPath: 'id' });
      productColorsStore.createIndex('product_id', 'product_id', { unique: false });
      productColorsStore.createIndex('color_id', 'color_id', { unique: false });
    }

    // Metadata store
    if (!db.objectStoreNames.contains('metadata')) {
      db.createObjectStore('metadata', { keyPath: 'key' });
    }
  }

  // =============================================================================
  // Offline Queue Management
  // =============================================================================

  async addToOfflineQueue(record: Omit<OfflineRecord, 'id' | 'timestamp' | 'synced' | 'attempts'>): Promise<string> {
    if (!this.db) {throw new Error('OfflineStorage not initialized');}

    const offlineRecord: OfflineRecord = {
      ...record,
      id: `offline_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
      timestamp: Date.now(),
      synced: false,
      attempts: 0
    };

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['offlineQueue'], 'readwrite');
      const store = transaction.objectStore('offlineQueue');
      const request = store.add(offlineRecord);

      request.onsuccess = () => {
        console.log('[OfflineStorage] Added to offline queue:', offlineRecord.id);
        resolve(offlineRecord.id);
      };

      request.onerror = () => {
        console.error('[OfflineStorage] Failed to add to offline queue:', request.error);
        reject(request.error);
      };
    });
  }

  async getOfflineQueue(): Promise<OfflineRecord[]> {
    if (!this.db) {throw new Error('OfflineStorage not initialized');}

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['offlineQueue'], 'readonly');
      const store = transaction.objectStore('offlineQueue');
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = () => {
        console.error('[OfflineStorage] Failed to get offline queue:', request.error);
        reject(request.error);
      };
    });
  }

  async getPendingSync(): Promise<OfflineRecord[]> {
    if (!this.db) {throw new Error('OfflineStorage not initialized');}

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['offlineQueue'], 'readonly');
      const store = transaction.objectStore('offlineQueue');
      const index = store.index('synced');
      const request = index.getAll(false);

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = () => {
        console.error('[OfflineStorage] Failed to get pending sync items:', request.error);
        reject(request.error);
      };
    });
  }

  async markAsSynced(recordId: string): Promise<void> {
    if (!this.db) {throw new Error('OfflineStorage not initialized');}

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['offlineQueue'], 'readwrite');
      const store = transaction.objectStore('offlineQueue');
      const getRequest = store.get(recordId);

      getRequest.onsuccess = () => {
        const record = getRequest.result;
        if (record) {
          record.synced = true;
          const putRequest = store.put(record);
          
          putRequest.onsuccess = () => {
            console.log('[OfflineStorage] Marked as synced:', recordId);
            resolve();
          };
          
          putRequest.onerror = () => {
            console.error('[OfflineStorage] Failed to mark as synced:', putRequest.error);
            reject(putRequest.error);
          };
        } else {
          reject(new Error('Record not found'));
        }
      };

      getRequest.onerror = () => {
        console.error('[OfflineStorage] Failed to get record for sync marking:', getRequest.error);
        reject(getRequest.error);
      };
    });
  }

  async updateSyncAttempt(recordId: string, error?: string): Promise<void> {
    if (!this.db) {throw new Error('OfflineStorage not initialized');}

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['offlineQueue'], 'readwrite');
      const store = transaction.objectStore('offlineQueue');
      const getRequest = store.get(recordId);

      getRequest.onsuccess = () => {
        const record = getRequest.result;
        if (record) {
          record.attempts++;
          record.lastAttempt = Date.now();
          if (error) {
            record.error = error;
          }
          
          const putRequest = store.put(record);
          
          putRequest.onsuccess = () => {
            resolve();
          };
          
          putRequest.onerror = () => {
            reject(putRequest.error);
          };
        } else {
          reject(new Error('Record not found'));
        }
      };

      getRequest.onerror = () => {
        reject(getRequest.error);
      };
    });
  }

  // =============================================================================
  // Data Caching
  // =============================================================================

  async cacheColors(colors: any[]): Promise<void> {
    if (!this.db) {throw new Error('OfflineStorage not initialized');}

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['colors'], 'readwrite');
      const store = transaction.objectStore('colors');

      // Clear existing data
      const clearRequest = store.clear();
      
      clearRequest.onsuccess = () => {
        // Add new data
        let completed = 0;
        let hasError = false;

        if (colors.length === 0) {
          resolve();
          return;
        }

        colors.forEach(color => {
          const addRequest = store.add(color);
          
          addRequest.onsuccess = () => {
            completed++;
            if (completed === colors.length && !hasError) {
              console.log(`[OfflineStorage] Cached ${colors.length} colors`);
              resolve();
            }
          };
          
          addRequest.onerror = () => {
            if (!hasError) {
              hasError = true;
              console.error('[OfflineStorage] Failed to cache colors:', addRequest.error);
              reject(addRequest.error);
            }
          };
        });
      };

      clearRequest.onerror = () => {
        console.error('[OfflineStorage] Failed to clear colors cache:', clearRequest.error);
        reject(clearRequest.error);
      };
    });
  }

  async getCachedColors(): Promise<any[]> {
    if (!this.db) {throw new Error('OfflineStorage not initialized');}

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['colors'], 'readonly');
      const store = transaction.objectStore('colors');
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = () => {
        console.error('[OfflineStorage] Failed to get cached colors:', request.error);
        reject(request.error);
      };
    });
  }

  async cacheProducts(products: any[]): Promise<void> {
    if (!this.db) {throw new Error('OfflineStorage not initialized');}

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['products'], 'readwrite');
      const store = transaction.objectStore('products');

      const clearRequest = store.clear();
      
      clearRequest.onsuccess = () => {
        let completed = 0;
        let hasError = false;

        if (products.length === 0) {
          resolve();
          return;
        }

        products.forEach(product => {
          const addRequest = store.add(product);
          
          addRequest.onsuccess = () => {
            completed++;
            if (completed === products.length && !hasError) {
              console.log(`[OfflineStorage] Cached ${products.length} products`);
              resolve();
            }
          };
          
          addRequest.onerror = () => {
            if (!hasError) {
              hasError = true;
              console.error('[OfflineStorage] Failed to cache products:', addRequest.error);
              reject(addRequest.error);
            }
          };
        });
      };

      clearRequest.onerror = () => {
        console.error('[OfflineStorage] Failed to clear products cache:', clearRequest.error);
        reject(clearRequest.error);
      };
    });
  }

  async getCachedProducts(): Promise<any[]> {
    if (!this.db) {throw new Error('OfflineStorage not initialized');}

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['products'], 'readonly');
      const store = transaction.objectStore('products');
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result);
      };

      request.onerror = () => {
        console.error('[OfflineStorage] Failed to get cached products:', request.error);
        reject(request.error);
      };
    });
  }

  // =============================================================================
  // Metadata Management
  // =============================================================================

  async setLastSyncTime(timestamp: number): Promise<void> {
    if (!this.db) {throw new Error('OfflineStorage not initialized');}

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['metadata'], 'readwrite');
      const store = transaction.objectStore('metadata');
      const request = store.put({ key: 'lastSync', value: timestamp });

      request.onsuccess = () => {
        resolve();
      };

      request.onerror = () => {
        console.error('[OfflineStorage] Failed to set last sync time:', request.error);
        reject(request.error);
      };
    });
  }

  async getLastSyncTime(): Promise<number> {
    if (!this.db) {throw new Error('OfflineStorage not initialized');}

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['metadata'], 'readonly');
      const store = transaction.objectStore('metadata');
      const request = store.get('lastSync');

      request.onsuccess = () => {
        resolve(request.result?.value || 0);
      };

      request.onerror = () => {
        console.error('[OfflineStorage] Failed to get last sync time:', request.error);
        reject(request.error);
      };
    });
  }

  // =============================================================================
  // Connection Status
  // =============================================================================

  isOnline(): boolean {
    return navigator.onLine;
  }

  onConnectionChange(callback: (isOnline: boolean) => void): () => void {
    const handleOnline = () => callback(true);
    const handleOffline = () => callback(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }

  // =============================================================================
  // Utilities
  // =============================================================================

  async getStorageStats(): Promise<{
    offlineQueue: number;
    pendingSync: number;
    cachedColors: number;
    cachedProducts: number;
    lastSync: Date | null;
    isOnline: boolean;
  }> {
    const [offlineQueue, pendingSync, cachedColors, cachedProducts, lastSyncTime] = await Promise.all([
      this.getOfflineQueue(),
      this.getPendingSync(),
      this.getCachedColors(),
      this.getCachedProducts(),
      this.getLastSyncTime()
    ]);

    return {
      offlineQueue: offlineQueue.length,
      pendingSync: pendingSync.length,
      cachedColors: cachedColors.length,
      cachedProducts: cachedProducts.length,
      lastSync: lastSyncTime > 0 ? new Date(lastSyncTime) : null,
      isOnline: this.isOnline()
    };
  }

  async clearAllData(): Promise<void> {
    if (!this.db) {throw new Error('OfflineStorage not initialized');}

    const storeNames = ['offlineQueue', 'colors', 'products', 'datasheets', 'productColors', 'metadata'];
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(storeNames, 'readwrite');
      let completed = 0;
      let hasError = false;

      storeNames.forEach(storeName => {
        const store = transaction.objectStore(storeName);
        const request = store.clear();

        request.onsuccess = () => {
          completed++;
          if (completed === storeNames.length && !hasError) {
            console.log('[OfflineStorage] All data cleared');
            resolve();
          }
        };

        request.onerror = () => {
          if (!hasError) {
            hasError = true;
            console.error(`[OfflineStorage] Failed to clear ${storeName}:`, request.error);
            reject(request.error);
          }
        };
      });
    });
  }
}

// Export singleton instance
export const offlineStorage = OfflineStorage.getInstance();