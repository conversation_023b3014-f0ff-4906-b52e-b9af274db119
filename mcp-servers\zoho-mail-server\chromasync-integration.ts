// Integration example for ChromaSync
// This shows how to replace SMTP with the Zoho MCP server

import { spawn } from 'child_process';
import { join } from 'path';

export class ZohoMCPEmailService {
  private mcpServerPath: string;

  constructor() {
    this.mcpServerPath = join(__dirname, '../../mcp-servers/zoho-mail-server/build/index.js');
  }

  async sendEmail(options: {
    to: string | string[];
    subject: string;
    content: string;
    isHtml?: boolean;
    fromAddress?: string;
  }): Promise<boolean> {
    return new Promise((resolve) => {
      const child = spawn('node', [this.mcpServerPath], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
        },
      });

      let output = '';
      let error = '';

      child.stdout.on('data', (data) => {
        output += data.toString();
      });

      child.stderr.on('data', (data) => {
        error += data.toString();
      });

      // Send the email request
      const request = {
        jsonrpc: '2.0',
        method: 'tools/call',
        params: {
          name: 'send_email',
          arguments: options,
        },
        id: 1,
      };

      child.stdin.write(JSON.stringify(request) + '\n');
      child.stdin.end();

      child.on('close', (code) => {
        if (code === 0 && output.includes('successfully')) {
          resolve(true);
        } else {
          console.error('Email send failed:', error || output);
          resolve(false);
        }
      });
    });
  }

  async sendInvitation(invitation: {
    to: string;
    organizationName: string;
    inviterName: string;
    role: string;
    invitationUrl: string;
    expiresAt: Date;
  }): Promise<boolean> {
    return new Promise((resolve) => {
      const child = spawn('node', [this.mcpServerPath], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: process.env,
      });

      let output = '';

      child.stdout.on('data', (data) => {
        output += data.toString();
      });

      const request = {
        jsonrpc: '2.0',
        method: 'tools/call',
        params: {
          name: 'send_invitation',
          arguments: {
            ...invitation,
            expiresAt: invitation.expiresAt.toISOString(),
          },
        },
        id: 1,
      };

      child.stdin.write(JSON.stringify(request) + '\n');
      child.stdin.end();

      child.on('close', (code) => {
        resolve(code === 0 && output.includes('successfully'));
      });
    });
  }
}

// Usage in organization.service.ts:
/*
import { ZohoMCPEmailService } from './zoho-mcp-email.service';

export class OrganizationService {
  private emailService: ZohoMCPEmailService;

  constructor(private db: Database) {
    this.emailService = new ZohoMCPEmailService();
  }

  private async sendInvitationEmail(to: string, invitation: any): Promise<boolean> {
    return await this.emailService.sendInvitation({
      to,
      ...invitation,
    });
  }
}
*/
