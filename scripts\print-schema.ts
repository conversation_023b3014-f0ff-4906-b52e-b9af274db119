import Database from 'better-sqlite3';
import path from 'path';

const DB_PATH = path.resolve(
  process.env.LOCAL_DB_PATH ||
    process.env.DB_PATH ||
    process.env.HOME + '/Library/Application Support/chroma-sync/chroma-sync-data.db'
);

const db = new Database(DB_PATH);

['colors', 'selections', 'selection_colors'].forEach(table => {
  const info = db.prepare(`PRAGMA table_info(${table})`).all();
  console.log(`\nSchema for table "${table}":`);
  console.table(info);
});

db.close();
