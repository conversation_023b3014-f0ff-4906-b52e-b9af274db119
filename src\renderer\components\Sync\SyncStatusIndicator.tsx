/**
 * @file SyncStatusIndicator.tsx
 * @description A small indicator showing the sync status
 */

import React, { useState, useEffect } from 'react';
// Import directly from the shared constants file instead of sync.types
import { SyncStatus as SyncStatusEnum } from '../../../shared/constants/sync-status';

// Define the type for the status update listener
interface StatusUpdate {
  status: SyncStatusEnum;
  timestamp?: number;
  error?: string;
  message?: string;
}

// Define the type for the sync state response
interface SyncStateResponse {
  success: boolean;
  data: {
    status?: SyncStatusEnum;
    message?: string;
    timestamp?: number;
    lastSyncTime?: number;
    autoSyncEnabled?: boolean;
  };
  error?: string;
}

export const SyncStatusIndicator: React.FC = () => {
  const [status, setStatus] = useState<SyncStatusEnum>(SyncStatusEnum.IDLE);
  const [lastSyncTime, setLastSyncTime] = useState<number | null>(null);
  const [hasUnsyncedChanges, setHasUnsyncedChanges] = useState<boolean>(false);

  // Helper to check for unsynced local changes
  const checkUnsyncedChanges = async () => {
    if (window.syncAPI && typeof window.syncAPI.hasUnsyncedLocalChanges === 'function') {
      try {
        const result = await window.syncAPI.hasUnsyncedLocalChanges();
        setHasUnsyncedChanges(!!result);
      } catch {
        setHasUnsyncedChanges(false);
      }
    } else {
      setHasUnsyncedChanges(false);
    }
  };

  // Initialize sync status
  useEffect(() => {
    const initSync = async () => {
      try {
        // Get last sync time from window.syncAPI
        if (!window.syncAPI || typeof window.syncAPI.getState !== 'function') {
          setStatus(SyncStatusEnum.ERROR);
          return;
        }
        const response = await window.syncAPI.getState() as SyncStateResponse;

        if (response.success) {
          // Set the last sync time if available
          if (response.data.lastSyncTime) {
            setLastSyncTime(response.data.lastSyncTime);
          }

          // Set the status based on the actual status from the main process
          if (response.data.status) {
            setStatus(response.data.status);
          } else {
            // If no status is provided, use IDLE as the default
            setStatus(SyncStatusEnum.IDLE);
          }
        }

        // Check for unsynced changes on mount
        await checkUnsyncedChanges();

        // Set up status update listener
        const statusListener = (data: unknown) => {
          const update = data as StatusUpdate;
          setStatus(update.status);

          // If we get a success status with a timestamp, update the last sync time
          if (update.status === SyncStatusEnum.SUCCESS && update.timestamp) {
            setLastSyncTime(update.timestamp);
          }

          // If we get an error status, log it and show it to the user
          if (update.status === SyncStatusEnum.ERROR) {
            console.log('Received error status:', update.error || 'Unknown error');
          }

          // Check for unsynced changes on every status update
          checkUnsyncedChanges();
        };

        // Register the listener
        if (typeof window.syncAPI.onStatusUpdate === 'function') {
          window.syncAPI.onStatusUpdate(statusListener);
        }

        // Return cleanup function
        return () => {
          if (window.syncAPI && typeof window.syncAPI.offStatusUpdate === 'function') {
            window.syncAPI.offStatusUpdate(statusListener);
          }
        };
      } catch (error) {
        console.error('Error initializing sync:', error);
        setStatus(SyncStatusEnum.ERROR);
      }
    };

    initSync();
  }, []);

  // Format timestamp
  const formatTimestamp = (timestamp: number | null): string | null => {
    if (!timestamp) {return null;}

    const date = new Date(timestamp);
    if (isNaN(date.getTime())) {return null;}

    return date.toLocaleString();
  };

  // Status indicator component
  const StatusIndicator = ({
    status,
    hasUnsyncedChanges
  }: {
    status: SyncStatusEnum;
    hasUnsyncedChanges: boolean;
  }) => {
    const getStatusColor = () => {
      if (status === SyncStatusEnum.IDLE && hasUnsyncedChanges) {
        return 'bg-yellow-400'; // Use a yellow/orange color for unsynced changes
      }
      switch (status) {
        case SyncStatusEnum.SUCCESS:
          return 'bg-[var(--color-feedback-success)]';
        case SyncStatusEnum.ERROR:
          return 'bg-[var(--color-feedback-error)]';
        case SyncStatusEnum.SYNCING:
          return 'bg-[var(--color-brand-primary)] animate-pulse';
        default:
          return 'bg-[var(--color-ui-foreground-tertiary)]';
      }
    };

    const getStatusLabel = () => {
      if (status === SyncStatusEnum.IDLE && hasUnsyncedChanges) {
        return 'Unsynced changes';
      }
      switch (status) {
        case SyncStatusEnum.SYNCING:
          return 'Syncing...';
        case SyncStatusEnum.SUCCESS:
          return 'Synced';
        case SyncStatusEnum.ERROR:
          return 'Sync failed';
        default:
          return 'Ready to sync';
      }
    };

    return (
      <div
        className={`w-3 h-3 rounded-full ${getStatusColor()}`}
        title={getStatusLabel()}
        aria-label={getStatusLabel()}
      ></div>
    );
  };

  // Get formatted timestamp
  const formattedTime = formatTimestamp(lastSyncTime);

  // Get status label for display
  const getStatusText = () => {
    if (status === SyncStatusEnum.IDLE && hasUnsyncedChanges) {
      return 'Unsynced changes';
    }
    switch (status) {
      case SyncStatusEnum.SYNCING:
        return 'Syncing...';
      case SyncStatusEnum.SUCCESS:
        return 'Synced';
      case SyncStatusEnum.ERROR:
        return 'Sync failed';
      default:
        return 'Ready to sync';
    }
  };

  return (
    <div className="w-full px-[var(--spacing-2)] py-[var(--spacing-2)] text-[var(--font-size-xs)] text-[var(--color-ui-foreground-secondary)] border-t border-[var(--color-ui-border-light)] dark:border-[var(--color-ui-border-dark)]">
      {/* Status indicator with dot */}
      <div className="flex flex-col items-center justify-center mb-[var(--spacing-1)]">
        <StatusIndicator status={status} hasUnsyncedChanges={hasUnsyncedChanges} />
        <span className="mt-[var(--spacing-1)] text-center">
          {getStatusText()}
        </span>
      </div>

      {/* Last sync time */}
      {formattedTime && (
        <div className="text-center text-[10px] opacity-70 mb-[var(--spacing-1)]">
          Last: {formattedTime}
        </div>
      )}
    </div>
  );
};
