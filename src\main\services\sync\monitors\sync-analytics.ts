/**
 * @file sync-analytics.ts
 * @description Analytics collection and analysis for sync operations
 * 
 * This module provides comprehensive analytics for sync operations,
 * including usage patterns, performance trends, and insights.
 */

import { 
  SyncAnalytics, 
  SyncResult, 
  SyncOperation,
  SyncEventHandler 
} from '../core/sync-types';

// ============================================================================
// ANALYTICS DATA STRUCTURES
// ============================================================================

interface SyncEvent {
  timestamp: number;
  operation: SyncOperation;
  duration: number;
  itemsProcessed: number;
  success: boolean;
  errorCategory?: string;
  networkQuality?: string;
  batchSize?: number;
}

interface UsagePattern {
  hour: number;
  dayOfWeek: number;
  syncCount: number;
  averageDuration: number;
  successRate: number;
}

interface PerformanceTrend {
  date: string;
  averageDuration: number;
  successRate: number;
  totalSyncs: number;
  dataVolume: number;
}

// ============================================================================
// SYNC ANALYTICS MANAGER
// ============================================================================

/**
 * Comprehensive analytics system for sync operations
 */
export class SyncAnalyticsManager {
  private syncEvents: SyncEvent[] = [];
  private usagePatterns = new Map<string, UsagePattern>();
  private performanceTrends: PerformanceTrend[] = [];
  private eventHandlers = new Map<string, SyncEventHandler[]>();
  
  private readonly MAX_EVENTS = 10000;
  private readonly MAX_TRENDS = 90; // 90 days
  private readonly ANALYTICS_INTERVAL = 60000; // 1 minute

  private analyticsInterval: ReturnType<typeof setInterval> | null = null;
  private isEnabled = true;

  constructor(enabled: boolean = true) {
    this.isEnabled = enabled;
    
    if (this.isEnabled) {
      this.startAnalyticsProcessing();
    }
  }

  /**
   * Record a sync operation for analytics
   */
  recordSyncOperation(
    operation: SyncOperation,
    result: SyncResult,
    networkQuality?: string,
    batchSize?: number
  ): void {
    if (!this.isEnabled) return;

    const syncEvent: SyncEvent = {
      timestamp: Date.now(),
      operation,
      duration: result.duration,
      itemsProcessed: result.itemsProcessed,
      success: result.success,
      errorCategory: result.errors.length > 0 ? result.errors[0].category : undefined,
      networkQuality,
      batchSize
    };

    this.syncEvents.push(syncEvent);

    // Maintain events size
    if (this.syncEvents.length > this.MAX_EVENTS) {
      this.syncEvents.shift();
    }

    // Update usage patterns
    this.updateUsagePatterns(syncEvent);

    // Emit analytics event
    this.emitEvent('sync-recorded', syncEvent);
  }

  /**
   * Get comprehensive analytics data
   */
  getAnalytics(): SyncAnalytics {
    if (!this.isEnabled || this.syncEvents.length === 0) {
      return {
        syncFrequency: 0,
        dataVolume: 0,
        errorRate: 0,
        peakUsageHours: [],
        averageItemsPerSync: 0
      };
    }

    const totalSyncs = this.syncEvents.length;
    const totalItems = this.syncEvents.reduce((sum, event) => sum + event.itemsProcessed, 0);
    const totalErrors = this.syncEvents.filter(event => !event.success).length;
    
    // Calculate time span for frequency calculation
    const timeSpan = this.syncEvents.length > 1 
      ? (this.syncEvents[this.syncEvents.length - 1].timestamp - this.syncEvents[0].timestamp) / (1000 * 60 * 60) // hours
      : 1;

    return {
      syncFrequency: totalSyncs / Math.max(timeSpan, 1), // syncs per hour
      dataVolume: totalItems,
      errorRate: (totalErrors / totalSyncs) * 100,
      peakUsageHours: this.getPeakUsageHours(),
      averageItemsPerSync: totalItems / totalSyncs
    };
  }

  /**
   * Get detailed usage patterns
   */
  getUsagePatterns(): {
    hourlyPatterns: Array<{ hour: number; syncCount: number; successRate: number }>;
    dailyPatterns: Array<{ dayOfWeek: number; syncCount: number; successRate: number }>;
    operationBreakdown: Array<{ operation: SyncOperation; count: number; percentage: number }>;
  } {
    const hourlyPatterns = Array.from({ length: 24 }, (_, hour) => {
      const pattern = this.usagePatterns.get(`hour-${hour}`);
      return {
        hour,
        syncCount: pattern?.syncCount || 0,
        successRate: pattern?.successRate || 0
      };
    });

    const dailyPatterns = Array.from({ length: 7 }, (_, day) => {
      const pattern = this.usagePatterns.get(`day-${day}`);
      return {
        dayOfWeek: day,
        syncCount: pattern?.syncCount || 0,
        successRate: pattern?.successRate || 0
      };
    });

    // Calculate operation breakdown
    const operationCounts = new Map<SyncOperation, number>();
    for (const event of this.syncEvents) {
      operationCounts.set(event.operation, (operationCounts.get(event.operation) || 0) + 1);
    }

    const totalOperations = this.syncEvents.length;
    const operationBreakdown = Array.from(operationCounts.entries()).map(([operation, count]) => ({
      operation,
      count,
      percentage: (count / totalOperations) * 100
    }));

    return {
      hourlyPatterns,
      dailyPatterns,
      operationBreakdown
    };
  }

  /**
   * Get performance trends over time
   */
  getPerformanceTrends(): PerformanceTrend[] {
    return [...this.performanceTrends];
  }

  /**
   * Get sync insights and recommendations
   */
  getInsights(): {
    insights: string[];
    recommendations: string[];
    optimizations: string[];
  } {
    const insights: string[] = [];
    const recommendations: string[] = [];
    const optimizations: string[] = [];

    if (this.syncEvents.length === 0) {
      return { insights, recommendations, optimizations };
    }

    const analytics = this.getAnalytics();
    const patterns = this.getUsagePatterns();

    // Generate insights
    if (analytics.errorRate > 10) {
      insights.push(`High error rate detected: ${analytics.errorRate.toFixed(1)}%`);
      recommendations.push('Review error logs and improve error handling');
    }

    if (analytics.syncFrequency > 10) {
      insights.push(`High sync frequency: ${analytics.syncFrequency.toFixed(1)} syncs per hour`);
      optimizations.push('Consider batching operations to reduce sync frequency');
    }

    // Analyze peak usage
    const peakHours = this.getPeakUsageHours();
    if (peakHours.length > 0) {
      insights.push(`Peak usage hours: ${peakHours.join(', ')}`);
      optimizations.push('Consider pre-loading data during off-peak hours');
    }

    // Analyze operation patterns
    const manualSyncs = patterns.operationBreakdown.find(op => op.operation === 'manual');
    if (manualSyncs && manualSyncs.percentage > 50) {
      insights.push('High manual sync usage detected');
      recommendations.push('Consider improving automatic sync triggers');
    }

    // Performance analysis
    const avgDuration = this.syncEvents.reduce((sum, event) => sum + event.duration, 0) / this.syncEvents.length;
    if (avgDuration > 5000) {
      insights.push(`Average sync duration is high: ${(avgDuration / 1000).toFixed(1)}s`);
      optimizations.push('Consider reducing batch sizes or optimizing queries');
    }

    return { insights, recommendations, optimizations };
  }

  /**
   * Export analytics data for external analysis
   */
  exportAnalyticsData(): {
    summary: SyncAnalytics;
    events: SyncEvent[];
    patterns: UsagePattern[];
    trends: PerformanceTrend[];
    exportTimestamp: number;
  } {
    return {
      summary: this.getAnalytics(),
      events: [...this.syncEvents],
      patterns: Array.from(this.usagePatterns.values()),
      trends: [...this.performanceTrends],
      exportTimestamp: Date.now()
    };
  }

  /**
   * Clear analytics data
   */
  clearAnalytics(): void {
    this.syncEvents = [];
    this.usagePatterns.clear();
    this.performanceTrends = [];
    
    console.log('[SyncAnalytics] Analytics data cleared');
    this.emitEvent('analytics-cleared', {});
  }

  /**
   * Enable or disable analytics collection
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    
    if (enabled && !this.analyticsInterval) {
      this.startAnalyticsProcessing();
    } else if (!enabled && this.analyticsInterval) {
      this.stopAnalyticsProcessing();
    }
    
    console.log(`[SyncAnalytics] Analytics ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Subscribe to analytics events
   */
  on(event: string, handler: SyncEventHandler): () => void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    
    this.eventHandlers.get(event)!.push(handler);

    // Return unsubscribe function
    return () => {
      const handlers = this.eventHandlers.get(event);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    };
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stopAnalyticsProcessing();
    this.eventHandlers.clear();
    console.log('[SyncAnalytics] Analytics manager destroyed');
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Start analytics processing
   */
  private startAnalyticsProcessing(): void {
    this.analyticsInterval = setInterval(() => {
      this.processAnalytics();
    }, this.ANALYTICS_INTERVAL);
    
    console.log('[SyncAnalytics] Started analytics processing');
  }

  /**
   * Stop analytics processing
   */
  private stopAnalyticsProcessing(): void {
    if (this.analyticsInterval) {
      clearInterval(this.analyticsInterval);
      this.analyticsInterval = null;
    }
  }

  /**
   * Process analytics data periodically
   */
  private processAnalytics(): void {
    this.updatePerformanceTrends();
    this.emitEvent('analytics-processed', this.getAnalytics());
  }

  /**
   * Update usage patterns based on sync event
   */
  private updateUsagePatterns(event: SyncEvent): void {
    const date = new Date(event.timestamp);
    const hour = date.getHours();
    const dayOfWeek = date.getDay();

    // Update hourly pattern
    const hourKey = `hour-${hour}`;
    const hourPattern = this.usagePatterns.get(hourKey) || {
      hour,
      dayOfWeek: 0,
      syncCount: 0,
      averageDuration: 0,
      successRate: 0
    };

    hourPattern.syncCount++;
    hourPattern.averageDuration = 
      ((hourPattern.averageDuration * (hourPattern.syncCount - 1)) + event.duration) / hourPattern.syncCount;
    
    const successCount = hourPattern.syncCount * (hourPattern.successRate / 100);
    const newSuccessCount = successCount + (event.success ? 1 : 0);
    hourPattern.successRate = (newSuccessCount / hourPattern.syncCount) * 100;

    this.usagePatterns.set(hourKey, hourPattern);

    // Update daily pattern
    const dayKey = `day-${dayOfWeek}`;
    const dayPattern = this.usagePatterns.get(dayKey) || {
      hour: 0,
      dayOfWeek,
      syncCount: 0,
      averageDuration: 0,
      successRate: 0
    };

    dayPattern.syncCount++;
    dayPattern.averageDuration = 
      ((dayPattern.averageDuration * (dayPattern.syncCount - 1)) + event.duration) / dayPattern.syncCount;
    
    const daySuccessCount = dayPattern.syncCount * (dayPattern.successRate / 100);
    const newDaySuccessCount = daySuccessCount + (event.success ? 1 : 0);
    dayPattern.successRate = (newDaySuccessCount / dayPattern.syncCount) * 100;

    this.usagePatterns.set(dayKey, dayPattern);
  }

  /**
   * Update performance trends
   */
  private updatePerformanceTrends(): void {
    const today = new Date().toISOString().split('T')[0];
    
    // Get today's events
    const todayStart = new Date(today).getTime();
    const todayEnd = todayStart + (24 * 60 * 60 * 1000);
    
    const todayEvents = this.syncEvents.filter(event => 
      event.timestamp >= todayStart && event.timestamp < todayEnd
    );

    if (todayEvents.length === 0) return;

    // Calculate today's metrics
    const totalDuration = todayEvents.reduce((sum, event) => sum + event.duration, 0);
    const successfulSyncs = todayEvents.filter(event => event.success).length;
    const totalItems = todayEvents.reduce((sum, event) => sum + event.itemsProcessed, 0);

    const trend: PerformanceTrend = {
      date: today,
      averageDuration: totalDuration / todayEvents.length,
      successRate: (successfulSyncs / todayEvents.length) * 100,
      totalSyncs: todayEvents.length,
      dataVolume: totalItems
    };

    // Update or add trend
    const existingIndex = this.performanceTrends.findIndex(t => t.date === today);
    if (existingIndex >= 0) {
      this.performanceTrends[existingIndex] = trend;
    } else {
      this.performanceTrends.push(trend);
    }

    // Maintain trends size
    if (this.performanceTrends.length > this.MAX_TRENDS) {
      this.performanceTrends.shift();
    }
  }

  /**
   * Get peak usage hours
   */
  private getPeakUsageHours(): number[] {
    const hourlyData = Array.from({ length: 24 }, (_, hour) => {
      const pattern = this.usagePatterns.get(`hour-${hour}`);
      return { hour, count: pattern?.syncCount || 0 };
    });

    // Find hours with above-average usage
    const averageUsage = hourlyData.reduce((sum, data) => sum + data.count, 0) / 24;
    
    return hourlyData
      .filter(data => data.count > averageUsage * 1.5) // 50% above average
      .sort((a, b) => b.count - a.count)
      .slice(0, 3) // Top 3 peak hours
      .map(data => data.hour);
  }

  /**
   * Emit event to all registered handlers
   */
  private emitEvent(event: string, data: any): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`[SyncAnalytics] Error in event handler for ${event}:`, error);
        }
      });
    }
  }
}
