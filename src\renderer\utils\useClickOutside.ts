import { useEffect, RefObject } from 'react';

/**
 * useClickOutside
 * Attaches a mousedown event listener to the document and calls the provided handler
 * if a click occurs outside the referenced element.
 *
 * @param ref - React ref to the element to detect outside clicks for
 * @param handler - Callback to invoke on outside click
 * @param enabled - Optional boolean to enable/disable the listener (default: true)
 */
export function useClickOutside<T extends HTMLElement>(
  ref: RefObject<T>,
  handler: (event: MouseEvent) => void,
  enabled: boolean = true
) {
  useEffect(() => {
    if (!enabled) {return;}

    function handleClickOutside(event: MouseEvent) {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        handler(event);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, handler, enabled]);
}
