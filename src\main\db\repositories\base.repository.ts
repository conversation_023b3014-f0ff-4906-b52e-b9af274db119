/**
 * Base repository class with common database operations
 */

import { executeWithPool } from '../core/connection';

export abstract class BaseRepository {
  protected tableName: string;
  
  constructor(tableName: string) {
    this.tableName = tableName;
  }
  
  /**
   * Execute query with connection pooling
   */
  protected async executeQuery<T>(operation: (db: any) => T): Promise<T> {
    return executeWithPool(operation);
  }
  
  /**
   * Check if a record exists by external ID
   */
  async exists(externalId: string): Promise<boolean> {
    return this.executeQuery(db => {
      const result = db.prepare(`
        SELECT COUNT(*) as count 
        FROM ${this.tableName} 
        WHERE external_id = ?
      `).get(externalId) as { count: number };
      
      return result.count > 0;
    });
  }
  
  /**
   * Get record by external ID
   */
  async findByExternalId(externalId: string): Promise<any | null> {
    return this.executeQuery(db => {
      return db.prepare(`
        SELECT * FROM ${this.tableName} 
        WHERE external_id = ?
      `).get(externalId) || null;
    });
  }
  
  /**
   * Get all active records
   */
  async findAll(): Promise<any[]> {
    return this.executeQuery(db => {
      return db.prepare(`
        SELECT * FROM ${this.tableName} 
        WHERE deleted_at IS NULL OR is_active = TRUE
        ORDER BY created_at DESC
      `).all();
    });
  }
  
  /**
   * Delete by external ID (soft delete)
   */
  async softDelete(externalId: string): Promise<boolean> {
    return this.executeQuery(db => {
      const result = db.prepare(`
        UPDATE ${this.tableName} 
        SET deleted_at = CURRENT_TIMESTAMP 
        WHERE external_id = ?
      `).run(externalId);
      
      return result.changes > 0;
    });
  }
  
  /**
   * Count records
   */
  async count(): Promise<number> {
    return this.executeQuery(db => {
      const result = db.prepare(`
        SELECT COUNT(*) as count 
        FROM ${this.tableName} 
        WHERE deleted_at IS NULL OR is_active = TRUE
      `).get() as { count: number };
      
      return result.count;
    });
  }
  
  /**
   * Check if organization context is required
   */
  protected requiresOrganizationContext(): boolean {
    return true; // Override in repositories that don't need org context
  }
  
  /**
   * Get records by organization ID
   */
  async findByOrganization(organizationId: number): Promise<any[]> {
    if (!this.requiresOrganizationContext()) {
      throw new Error(`${this.tableName} repository does not support organization filtering`);
    }
    
    return this.executeQuery(db => {
      return db.prepare(`
        SELECT * FROM ${this.tableName} 
        WHERE organization_id = ? 
          AND (deleted_at IS NULL OR is_active = TRUE)
        ORDER BY created_at DESC
      `).all(organizationId);
    });
  }
}