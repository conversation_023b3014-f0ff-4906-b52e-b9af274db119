/**
 * @file index.ts
 * @description Main exports for the design token system
 */

import { colors } from './colors';
import { typography } from './typography';
import { spacing } from './spacing';
import { borderRadius } from './borderRadius';
import { shadows } from './shadows';
import { transitions } from './transitions';
import { zIndex } from './zIndex';
import { breakpoints } from './breakpoints';

// Import types
import { 
  DesignTokens,
  ColorTokens, 
  TypographyTokens,
  SpacingTokens,
  BorderRadiusTokens,
  ShadowTokens,
  TransitionTokens,
  ZIndexTokens,
  BreakpointTokens
} from './types';

// Export types for consumers
export type {
  DesignTokens,
  ColorTokens, 
  TypographyTokens,
  SpacingTokens,
  BorderRadiusTokens,
  ShadowTokens,
  TransitionTokens,
  ZIndexTokens,
  BreakpointTokens
};

// Create and export the complete token system
const tokens: DesignTokens = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  transitions,
  zIndex,
  breakpoints,
};

export default tokens;

// Export individual categories for direct imports
export { 
  colors, 
  typography, 
  spacing, 
  borderRadius, 
  shadows, 
  transitions, 
  zIndex, 
  breakpoints 
}; 