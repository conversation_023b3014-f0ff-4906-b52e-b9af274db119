/**
 * @file colorHandlers.ts
 * @description IPC handlers for color operations
 */

import { ipcMain, dialog, app, shell } from 'electron';
import Database from 'better-sqlite3';
import { ColorService } from '../db/services/color.service';
// import { SelectionService } from '../db/services/selection.service'; // DISABLED
import { ColorChannels, NewColorEntry, UpdateColorEntry } from '../../shared/types/color.types';
// import { registerSelectionHandlers } from './selection.ipc'; // DISABLED
import fs from 'node:fs';
import path from 'path';
import { canRegisterHandler } from '../utils/ipcRegistry';
import { SharedFolderManager } from '../shared-folder'; // Import SharedFolderManager
import { getErrorMessage } from '../utils/error-utils'; // Import shared error handler

export function registerIpcHandlers(db: Database.Database) {
  // Create service instances
  const colorService = new ColorService(db);
  // const selectionService = new SelectionService(db); // DISABLED

  // DISABLED: Selection handlers are disabled until the app is refactored
  // console.log('Registering selection handlers...');
  // registerSelectionHandlers(selectionService, colorService, db);
  // console.log('Selection handlers registered');

  // Get all colors
  if (canRegisterHandler(ColorChannels.GET_ALL)) {
    ipcMain.handle(ColorChannels.GET_ALL, async () => {
      try {
        return await colorService.getAll();
      } catch (error) {
        console.error('Error fetching colors:', error);
        throw new Error(getErrorMessage(error));
      }
    });
  }

  // Get color by ID
  if (canRegisterHandler(ColorChannels.GET_BY_ID)) {
    ipcMain.handle(ColorChannels.GET_BY_ID, async (_, id: string) => {
      try {
        return await colorService.getById(id);
      } catch (error) {
        console.error(`Error fetching color with ID ${id}:`, error);
        throw new Error(getErrorMessage(error));
      }
    });
  }

  // Add new color
  if (canRegisterHandler(ColorChannels.ADD)) {
    ipcMain.handle(ColorChannels.ADD, async (_, color: NewColorEntry) => {
      try {
        return await colorService.add(color);
      } catch (error) {
        console.error('Error adding color:', error);
        throw new Error(getErrorMessage(error));
      }
    });
  }

  // Update color
  if (canRegisterHandler(ColorChannels.UPDATE)) {
    ipcMain.handle(ColorChannels.UPDATE, async (_, id: string, updates: UpdateColorEntry) => {
      try {
        const updatedColor = await colorService.update(id, updates);
        if (!updatedColor) {
          throw new Error(`Color with ID ${id} not found`);
        }
        return updatedColor;
      } catch (error) {
        console.error(`Error updating color with ID ${id}:`, error);
        throw new Error(getErrorMessage(error));
      }
    });
  }

  // Delete color
  if (canRegisterHandler(ColorChannels.DELETE)) {
    ipcMain.handle(ColorChannels.DELETE, async (_, id: string) => {
      try {
        return await colorService.delete(id);
      } catch (error) {
        console.error(`Error deleting color with ID ${id}:`, error);
        throw new Error(getErrorMessage(error));
      }
    });
  }

  // Clear all colors
  if (canRegisterHandler(ColorChannels.CLEAR_ALL)) {
    ipcMain.handle(ColorChannels.CLEAR_ALL, async () => {
      try {
        colorService.clearAll();
        return true;
      } catch (error) {
        console.error('Error clearing colors:', error);
        throw new Error(getErrorMessage(error));
      }
    });
  }

  // Import colors from JSON file
  if (canRegisterHandler(ColorChannels.IMPORT)) {
    ipcMain.handle(ColorChannels.IMPORT, async (_, mergeMode: 'replace' | 'merge' = 'replace', customFilePath?: string, format: 'json' | 'csv' = 'json') => {
      console.log(`Import handler called in main process (mode: ${mergeMode}, format: ${format})`);
      try {
        let filePath: string;

        // If a path was provided by the renderer, use it
        if (customFilePath) {
          filePath = customFilePath;
          console.log(`Using provided file path: ${filePath}`);
        } else {
          // Show open dialog to let user choose a file to import
          const { canceled, filePaths } = await dialog.showOpenDialog({
            title: 'Import Pantone Colors',
            defaultPath: app.getPath('documents'),
            filters: [
              { name: 'All Supported Files', extensions: ['json', 'csv'] },
              { name: 'JSON Files', extensions: ['json'] },
              { name: 'CSV Files', extensions: ['csv'] }
            ],
            properties: ['openFile']
          });

          // If user canceled the dialog, return early
          if (canceled || filePaths.length === 0) {
            console.log('Import canceled by user');
            return {
              imported: false,
              message: 'Import canceled by user'
            };
          }

          filePath = filePaths[0];
          console.log(`User selected file path: ${filePath}`);

          // Determine format based on file extension if not explicitly specified
          if (!format) {
            if (filePath.toLowerCase().endsWith('.csv')) {
              format = 'csv';
            } else {
              format = 'json';
            }
          }
        }

        // Check if the file exists
        if (!fs.existsSync(filePath)) {
          console.log(`Import file not found: ${filePath}`);
          return {
            imported: false,
            message: `File not found: ${filePath}`
          };
        }

        console.log(`Reading file from: ${filePath} as ${format}`);

        // Read file in a try/catch to handle file read errors specifically
        let fileContent;
        try {
          fileContent = fs.readFileSync(filePath, 'utf8');
          console.log(`Successfully read file (${fileContent.length} bytes)`);
        } catch (readError) {
          console.error('Error reading file:', readError);
          return { imported: false, message: getErrorMessage(readError) };
        }

        // Parse the file content based on format
        let colorsData;
        try {
          if (format === 'csv') {
            // Basic CSV parsing logic
            const lines = fileContent.split('\n').filter(line => line.trim());
            const headers = lines[0].split(',').map(h => h.trim().toLowerCase());

            // Ensure all required fields are present
            const requiredFields = ['name', 'pantone', 'hex'];
            const missingFields = requiredFields.filter(field => !headers.includes(field));

            if (missingFields.length > 0) {
              return {
                imported: false,
                message: `CSV is missing required columns: ${missingFields.join(', ')}`
              };
            }

            // Parse each line into a color entry
            colorsData = [];
            for (let i = 1; i < lines.length; i++) {
              const values = lines[i].split(',').map(val => val.trim());
              if (values.length !== headers.length) continue; // Skip malformed lines

              const entry: any = {};
              headers.forEach((header, index) => {
                entry[header] = values[index];
              });

              colorsData.push(entry);
            }

            console.log(`Successfully parsed CSV data (${colorsData.length} colors)`);
          } else {
            // JSON parsing
            colorsData = JSON.parse(fileContent) as NewColorEntry[];
            console.log(`Successfully parsed JSON data (${colorsData.length} colors)`);
          }
        } catch (parseError) {
          console.error(`Error parsing ${format.toUpperCase()}:`, parseError);
          return { imported: false, message: getErrorMessage(parseError) };
        }

        console.log(`Importing colors to database with mode: ${mergeMode}...`);
        const importedColors = await colorService.importColors(colorsData, mergeMode);
        console.log(`Successfully imported ${importedColors.length} colors`);

        return {
          imported: true,
          count: importedColors.length,
          message: `Successfully imported ${importedColors.length} colors`
        };
      } catch (error) {
        console.error('Error importing colors:', error);
        return {
          imported: false,
          message: getErrorMessage(error)
        };
      }
    });
  }

  // Export colors to file
  if (canRegisterHandler(ColorChannels.EXPORT)) {
    ipcMain.handle(ColorChannels.EXPORT, async (_, customFilePath?: string, format: 'json' | 'csv' = 'json') => {
      console.log(`Export handler called in main process (format: ${format})`);
      try {
        let filePath: string;

        // If a path was provided by the renderer, use it
        if (customFilePath) {
          filePath = customFilePath;
          console.log(`Using provided file path: ${filePath}`);
        } else {
          // Show save dialog to let user choose where to save the file
          const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
          const defaultFilename = `pantone-colors-${timestamp}.${format === 'json' ? 'json' : 'csv'}`;

          const { canceled, filePath: selectedPath } = await dialog.showSaveDialog({
            title: `Export Pantone Colors as ${format.toUpperCase()}`,
            defaultPath: path.join(app.getPath('documents'), defaultFilename),
            filters: format === 'json'
              ? [{ name: 'JSON Files', extensions: ['json'] }]
              : [{ name: 'CSV Files', extensions: ['csv'] }],
            properties: ['createDirectory']
          });

          // If user canceled the save dialog, return early
          if (canceled || !selectedPath) {
            console.log('Export canceled by user');
            return {
              exported: false,
              message: 'Export canceled by user'
            };
          }

          filePath = selectedPath;
          console.log(`User selected file path: ${filePath}`);
        }

        // Call the service method to export
        const result = await colorService.exportColors(filePath, format);
        console.log('Export result:', result);

        return result;
      } catch (error) {
        console.error('Error exporting colors:', error);
        return {
          exported: false,
          message: getErrorMessage(error)
        };
      }
    });
  }
}

/**
 * Open a product datasheet
 * @param event IPC event
 * @param product Product name
 * @param sharedFolderManager Instance of SharedFolderManager
 * @returns Promise<{ success: boolean; error?: string }> Success status and optional error message
 */
export const handleOpenProductDatasheet = async (
  event: Electron.IpcMainInvokeEvent,
  product: string,
  sharedFolderManager: SharedFolderManager | null // Add manager as parameter
): Promise<{ success: boolean; error?: string }> => { // Return object for better error handling
  try {
    console.log(`Attempting to open datasheet for product: "${product}"`);

    // Product data folder path
    const productDataFolder = path.join(app.getPath('downloads'), 'Pantone tracker dev v3', 'Product data');
    console.log(`Product data folder: ${productDataFolder}`);

    if (!fs.existsSync(productDataFolder)) {
      console.warn(`Product data folder does not exist: ${productDataFolder}`);
    } else {
      console.log('Product data folder exists, checking contents...');
      try {
        const files = fs.readdirSync(productDataFolder);
        console.log('Files in product data folder:', files);
      } catch (error) {
        console.error('Error reading product data folder:', error);
      }
    }

    // Define mapping of product names to their data sheet filenames
    const productDataMap: Record<string, string[]> = {
      'IVG MOSMO 2+10+10 DTL': ['IVG DTL 20K (2+10 ml +10 ml)  Product Brief.docx'],
      'BEYOND CLK 6000': ['Beyond CLK 6000 Product Brief.docx'],
      'IVG 2400 RECHARGEABLE': ['IVG 2400 Rechargeable Product Brief.docx'],
      'IVG AIR 4 IN 1': ['IVG Air 4 in 1 product brief.docx', 'IVG Air 4 in 1 Product Brief.docx'],
      'IVG AIR 4IN1': ['IVG Air 4 in 1 product brief.docx', 'IVG Air 4 in 1 Product Brief.docx'],
      'IVG 4 IN 1': ['IVG Air 4 in 1 product brief.docx', 'IVG Air 4 in 1 Product Brief.docx'],
      'IVG 4IN1': ['IVG Air 4 in 1 product brief.docx', 'IVG Air 4 in 1 Product Brief.docx'],
      'IVG AIR 2 IN 1': ['IVG Air 2 in 1 product brief.docx'],
      'IVG AIR 2IN1': ['IVG Air 2 in 1 product brief.docx'],
      'IVG 2 IN 1': ['IVG Air 2 in 1 product brief.docx'],
      'IVG 2IN1': ['IVG Air 2 in 1 product brief.docx'],
      'IVG PRO 12': ['IVG Pro 12 Product Brief.docx'],
      'IVG SAVR 3000': ['IVG SAVR 3000 Product Brief.docx'],
      'IVG SMART 5500': ['IVG Smart 5500 Product Brief.docx'],
      'IVG SMART MAX': ['IVG Smart MAX Product Brief.docx']
    };

    // Normalize product name for case-insensitive comparison
    const normalizedProductName = product.toUpperCase().trim();
    console.log(`Normalized product name: "${normalizedProductName}"`);

    // Function to try finding a match with a more flexible approach
    const findMatch = () => {
      // First, direct match in the map
      if (productDataMap[normalizedProductName]) {
        return productDataMap[normalizedProductName];
      }

      // Try partial matches as a fallback
      for (const [key, filenames] of Object.entries(productDataMap)) {
        if (normalizedProductName.includes(key) || key.includes(normalizedProductName)) {
          console.log(`Found partial match: "${key}" for "${normalizedProductName}"`);
          return filenames;
        }
      }

      return null;
    };

    const matchingFilenames = findMatch();
    if (matchingFilenames) {
      console.log(`Found potential file matches: ${matchingFilenames.join(', ')}`);

      for (const filename of matchingFilenames) {
        const productDataPath = path.join(productDataFolder, filename);
        console.log(`Checking if file exists: ${productDataPath}`);

        // Check if the file exists
        if (fs.existsSync(productDataPath)) {
          console.log(`File exists, opening: ${productDataPath}`);
          await shell.openPath(productDataPath);
          console.log(`Opened product data sheet for ${product} at ${productDataPath}`);
          return { success: true };
        } else {
          console.log(`File does not exist: ${productDataPath}`);
        }
      }
    } else {
      console.log(`No matching product found for "${normalizedProductName}" in the product map`);
    }

    // Use the passed sharedFolderManager instance
    if (!sharedFolderManager) {
      console.error('SharedFolderManager is not available.');
      return { success: false, error: 'Shared Folder Manager not initialized' };
    }
    const sharedFolderPath = sharedFolderManager.getPath();

    // Standardize the product name for filename - replace spaces with underscores and remove special chars
    const formattedProductName = product.replace(/\s+/g, '_').replace(/[^\w_-]/g, '');

    // Check for multiple potential filename formats
    const possibleFilenames = [
      `${formattedProductName}.pdf`,              // Product_Name.pdf
      `${product.replace(/\s+/g, '_')}.pdf`,      // Product_Name.pdf (with some special chars)
      `${product.replace(/\s+/g, '')}.pdf`,       // ProductName.pdf (no spaces)
      `${product.toLowerCase().replace(/\s+/g, '_')}.pdf`, // product_name.pdf (lowercase)
    ];

    // Construct the path to the product datasheet
    const datasheetDir = path.join(sharedFolderPath, 'datasheets');

    // Create datasheets directory if it doesn't exist
    if (!fs.existsSync(datasheetDir)) {
      fs.mkdirSync(datasheetDir, { recursive: true });
      console.log(`Created datasheet directory: ${datasheetDir}`);
    }

    // Try each possible filename until we find one that exists
    let datasheetPath: string | null = null;
    for (const filename of possibleFilenames) {
      const testPath = path.join(datasheetDir, filename);
      if (fs.existsSync(testPath)) {
        datasheetPath = testPath;
        console.log(`Datasheet found: ${datasheetPath}`); // Added log
        break;
      }
    }

    // If no datasheet is found with any of the variants
    if (!datasheetPath) {
      console.warn(`Datasheet not found for product: ${product} in any of the expected formats`);

      // Show a notification to the user
      if (event.sender) {
        dialog.showMessageBox({
          type: 'info',
          title: 'Datasheet Not Found',
          message: `No datasheet available for ${product}`,
          detail: 'The product datasheet file could not be found. Please contact your administrator to add this document.',
          buttons: ['OK']
        });
      }

      return { success: false, error: 'Datasheet not found' };
    }

    // Open the datasheet with the default PDF viewer
    await shell.openPath(datasheetPath);
    console.log(`Opened datasheet for product: ${product} at ${datasheetPath}`);

    return { success: true };
  } catch (error) {
    console.error(`Error opening datasheet for product ${product}:`, error);
    return { success: false, error: getErrorMessage(error) };
  }
};