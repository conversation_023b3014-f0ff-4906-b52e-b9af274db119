#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const preloadPath = path.join(__dirname, '../out/preload');
const mjsFile = path.join(preloadPath, 'index.mjs');
const jsFile = path.join(preloadPath, 'index.js');

// Read the .mjs file
const content = fs.readFileSync(mjsFile, 'utf8');

// Convert ES module imports to CommonJS
const convertedContent = content
  .replace(/^import\s+{([^}]+)}\s+from\s+["']([^"']+)["'];?$/gm, (match, imports, module) => {
    return `const {${imports}} = require("${module}");`;
  })
  .replace(/^import\s+(\w+)\s+from\s+["']([^"']+)["'];?$/gm, (match, name, module) => {
    return `const ${name} = require("${module}");`;
  });

// Write the converted content to .js file
fs.writeFileSync(jsFile, convertedContent, 'utf8');

console.log('Preload script converted successfully!');
console.log(`Created: ${jsFile}`);