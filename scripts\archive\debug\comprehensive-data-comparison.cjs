const fs = require('fs');
const path = require('path');

// Read the summaries
const originalData = JSON.parse(fs.readFileSync(path.join(__dirname, 'original-data-summary.json'), 'utf8'));
const databaseData = JSON.parse(fs.readFileSync(path.join(__dirname, 'database-summary.json'), 'utf8'));

console.log('╔══════════════════════════════════════════════════════════════════════════════╗');
console.log('║                       COMPREHENSIVE DATA COMPARISON REPORT                    ║');
console.log('╚══════════════════════════════════════════════════════════════════════════════╝');
console.log();

// Overall summary
console.log('┌─────────────────────────────────────────────────────────────────────────────┐');
console.log('│                               OVERALL SUMMARY                                │');
console.log('├─────────────────────────────────────────────────────────────────────────────┤');
console.log(`│ Original JSON entries:        ${originalData.totalEntries.toString().padStart(6)}                                        │`);
console.log(`│ Database unique colors:       ${databaseData.totalUniqueColors.toString().padStart(6)} (${databaseData.totalUniqueColors === originalData.totalEntries ? '✅ MATCH' : '❌ MISMATCH'})                           │`);
console.log(`│ Database product-color maps:  ${databaseData.totalMappings.toString().padStart(6)} (${(databaseData.totalMappings - originalData.totalEntries).toString().padStart(4)} extra mappings)            │`);
console.log(`│ Orphaned colors in DB:        ${databaseData.orphanedColors.toString().padStart(6)}                                        │`);
console.log('└─────────────────────────────────────────────────────────────────────────────┘');
console.log();

// Detailed product comparison
console.log('┌─────────────────────────────────────────────────────────────────────────────┐');
console.log('│                          PRODUCT-BY-PRODUCT COMPARISON                       │');
console.log('├─────────────────────────────────────────────────────────────────────────────┤');
console.log('│ Product Name                           │ Original │ Database │ Diff │ Status │');
console.log('├────────────────────────────────────────┼──────────┼──────────┼──────┼────────┤');

const allProducts = new Set([...Object.keys(originalData.productCounts), ...Object.keys(databaseData.productCounts)]);
const productComparison = [];
let totalOriginal = 0;
let totalDatabase = 0;
let perfectMatches = 0;
let mismatches = 0;

allProducts.forEach(product => {
  const original = originalData.productCounts[product] || 0;
  const database = databaseData.productCounts[product] || 0;
  const diff = database - original;
  const status = diff === 0 ? '✅' : diff > 0 ? '⬆️' : '⬇️';
  
  totalOriginal += original;
  totalDatabase += database;
  
  if (diff === 0 && original > 0) {
    perfectMatches++;
  } else if (diff !== 0) {
    mismatches++;
  }
  
  productComparison.push({
    product,
    original,
    database,
    diff,
    status
  });
  
  console.log(`│ ${product.padEnd(38)} │ ${original.toString().padStart(8)} │ ${database.toString().padStart(8)} │ ${diff.toString().padStart(4)} │   ${status}    │`);
});

console.log('├────────────────────────────────────────┼──────────┼──────────┼──────┼────────┤');
console.log(`│ ${'TOTAL'.padEnd(38)} │ ${totalOriginal.toString().padStart(8)} │ ${totalDatabase.toString().padStart(8)} │ ${(totalDatabase - totalOriginal).toString().padStart(4)} │        │`);
console.log('└────────────────────────────────────────┴──────────┴──────────┴──────┴────────┘');
console.log();

// Analysis of discrepancies
console.log('┌─────────────────────────────────────────────────────────────────────────────┐');
console.log('│                              DISCREPANCY ANALYSIS                            │');
console.log('├─────────────────────────────────────────────────────────────────────────────┤');

const overCount = productComparison.filter(p => p.diff > 0);
const underCount = productComparison.filter(p => p.diff < 0);
const missingFromDB = productComparison.filter(p => p.database === 0 && p.original > 0);
const extraInDB = productComparison.filter(p => p.original === 0 && p.database > 0);

console.log(`│ Perfect matches:           ${perfectMatches.toString().padStart(3)} products                               │`);
console.log(`│ Mismatches:                ${mismatches.toString().padStart(3)} products                               │`);
console.log(`│                                                                              │`);
console.log(`│ Products with MORE colors: ${overCount.length.toString().padStart(3)} products (+${overCount.reduce((sum, p) => sum + p.diff, 0)} colors total)         │`);
overCount.forEach(p => {
  console.log(`│   • ${p.product.padEnd(35)} (+${p.diff} colors)                  │`);
});

if (underCount.length > 0) {
  console.log(`│                                                                              │`);
  console.log(`│ Products with FEWER colors: ${underCount.length.toString().padStart(2)} products (${underCount.reduce((sum, p) => sum + p.diff, 0)} colors total)          │`);
  underCount.forEach(p => {
    console.log(`│   • ${p.product.padEnd(35)} (${p.diff} colors)                   │`);
  });
}

if (missingFromDB.length > 0) {
  console.log(`│                                                                              │`);
  console.log(`│ Products MISSING from database: ${missingFromDB.length}                                    │`);
  missingFromDB.forEach(p => {
    console.log(`│   • ${p.product.padEnd(35)} (${p.original} colors missing)         │`);
  });
}

if (extraInDB.length > 0) {
  console.log(`│                                                                              │`);
  console.log(`│ Products ONLY in database: ${extraInDB.length}                                         │`);
  extraInDB.forEach(p => {
    console.log(`│   • ${p.product.padEnd(35)} (${p.database} colors)                │`);
  });
}

console.log('└─────────────────────────────────────────────────────────────────────────────┘');
console.log();

// Likely causes analysis
console.log('┌─────────────────────────────────────────────────────────────────────────────┐');
console.log('│                           LIKELY CAUSES OF DISCREPANCIES                     │');
console.log('├─────────────────────────────────────────────────────────────────────────────┤');

// Check for shared colors between products
const sharedColorProducts = ['5500 SMART', 'Smart 5500', 'IVG Beyond CLK 6000', 'IVG Beyond Cybr 6000'];
const hasSharedColorIssues = sharedColorProducts.some(p => {
  const comp = productComparison.find(pc => pc.product === p);
  return comp && comp.diff > 0;
});

if (hasSharedColorIssues) {
  console.log('│ 🔄 SHARED COLORS BETWEEN PRODUCTS:                                          │');
  console.log('│    Several products appear to share colors, causing higher counts:          │');
  console.log('│    • 5500 SMART & Smart 5500 (likely the same product)                     │');
  console.log('│    • IVG Beyond CLK 6000 & IVG Beyond Cybr 6000 (share many colors)        │');
  console.log('│                                                                              │');
}

const totalExtraMappings = databaseData.totalMappings - originalData.totalEntries;
console.log('│ 📊 COLOR REUSE ANALYSIS:                                                    │');
console.log(`│    • Total unique colors: ${databaseData.totalUniqueColors}                                         │`);
console.log(`│    • Total product-color mappings: ${databaseData.totalMappings}                                 │`);
console.log(`│    • Extra mappings: ${totalExtraMappings} (colors used by multiple products)             │`);
console.log(`│    • Average reuse: ${(databaseData.totalMappings / databaseData.totalUniqueColors).toFixed(2)} products per color                            │`);

if (databaseData.orphanedColors > 0) {
  console.log('│                                                                              │');
  console.log(`│ ⚠️  ORPHANED COLORS: ${databaseData.orphanedColors} colors exist without product assignment       │`);
}

console.log('└─────────────────────────────────────────────────────────────────────────────┘');
console.log();

// Recommendations
console.log('┌─────────────────────────────────────────────────────────────────────────────┐');
console.log('│                              RECOMMENDATIONS                                 │');
console.log('├─────────────────────────────────────────────────────────────────────────────┤');
console.log('│ 1. MERGE DUPLICATE PRODUCTS:                                                │');
console.log('│    • Consider merging "5500 SMART" and "Smart 5500"                        │');
console.log('│                                                                              │');
console.log('│ 2. REVIEW SHARED COLORS:                                                    │');
console.log('│    • Verify if IVG Beyond CLK/Cybr 6000 should share colors                │');
console.log('│    • Check if color sharing is intentional or data duplication             │');
console.log('│                                                                              │');
console.log('│ 3. INVESTIGATE ORPHANED COLORS:                                             │');
console.log(`│    • Review ${databaseData.orphanedColors} orphaned colors and assign to products or remove      │`);
console.log('│                                                                              │');
console.log('│ 4. VALIDATE SINGLE-FLAVOR PRODUCTS:                                         │');
console.log('│    • Products like "Blue Raspberry Ice", "Classic Mint" show multiple      │');
console.log('│      colors but single flavor - verify data integrity                      │');
console.log('└─────────────────────────────────────────────────────────────────────────────┘');

// Save detailed report
const detailedReport = {
  summary: {
    originalEntries: originalData.totalEntries,
    databaseUniqueColors: databaseData.totalUniqueColors,
    databaseMappings: databaseData.totalMappings,
    orphanedColors: databaseData.orphanedColors,
    perfectMatches,
    mismatches
  },
  productComparison,
  discrepancies: {
    overCount: overCount.map(p => ({ product: p.product, extraColors: p.diff })),
    underCount: underCount.map(p => ({ product: p.product, missingColors: -p.diff })),
    missingFromDB: missingFromDB.map(p => ({ product: p.product, colorsInOriginal: p.original })),
    extraInDB: extraInDB.map(p => ({ product: p.product, colorsInDB: p.database }))
  },
  timestamp: new Date().toISOString()
};

fs.writeFileSync(
  path.join(__dirname, 'comprehensive-comparison-report.json'),
  JSON.stringify(detailedReport, null, 2)
);

console.log('\n📄 Detailed report saved to: comprehensive-comparison-report.json');