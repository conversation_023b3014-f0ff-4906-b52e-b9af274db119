/**
 * Native module loader for better-sqlite3
 * Handles the dynamic loading of the native module in Electron
 */

import { app } from 'electron';
import path from 'path';
import fs from 'fs';

/**
 * Load better-sqlite3 with proper path resolution
 */
export function loadBetterSqlite3(): any {
  try {
    // Try standard import first
    const Database = require('better-sqlite3');
    return Database;
  } catch (error) {
    console.error('[DB] Failed to load better-sqlite3 directly:', error);
    
    // Try alternative paths
    const possiblePaths = [
      // Development paths
      path.join(process.cwd(), 'node_modules', 'better-sqlite3'),
      path.join(__dirname, '..', '..', '..', 'node_modules', 'better-sqlite3'),
      
      // Production paths
      path.join(app.getAppPath(), 'node_modules', 'better-sqlite3'),
      path.join(app.getPath('userData'), '..', '..', 'node_modules', 'better-sqlite3'),
    ];
    
    for (const modulePath of possiblePaths) {
      try {
        if (fs.existsSync(modulePath)) {
          console.log(`[DB] Trying to load better-sqlite3 from: ${modulePath}`);
          const Database = require(modulePath);
          console.log('[DB] Successfully loaded better-sqlite3');
          return Database;
        }
      } catch (_err) {
        // Continue to next path
      }
    }
    
    throw new Error('Could not load better-sqlite3 from any known location');
  }
}
