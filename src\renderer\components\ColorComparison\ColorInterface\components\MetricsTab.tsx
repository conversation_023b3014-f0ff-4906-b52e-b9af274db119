/**
 * Metrics Tab Component
 * Displays comprehensive color metrics and values
 */

import React, { memo } from 'react';
import { BarChart3 } from 'lucide-react';
import type { MetricsTabProps } from '../types';

export const MetricsTab = memo<MetricsTabProps>(({ selectedColor, metrics }) => {
  if (!selectedColor || !metrics) {
    return (
      <div className="p-4 text-center text-ui-text-muted">
        <BarChart3 className="mx-auto mb-2 h-8 w-8 opacity-50" />
        <p>Select a color to view metrics</p>
      </div>
    );
  }

  const { rgb, hsl, cmyk, lab } = metrics;

  return (
    <div className="p-4">
      <div className="bg-ui-background-tertiary dark:bg-zinc-800 rounded-md p-3 mb-3">
        <h4 className="text-sm font-medium mb-3">Color Values</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* RGB Values */}
          <div className="space-y-3">
            <div>
              <div className="text-xs font-medium text-ui-foreground-tertiary mb-1">RGB</div>
              <div className="grid grid-cols-3 gap-2">
                <div className="flex flex-col items-center">
                  <div 
                    className="h-4 w-full rounded" 
                    style={{ backgroundColor: `rgb(${rgb.r},0,0)` }}
                  />
                  <div className="text-xs mt-1 font-medium">{Math.round(rgb.r)}</div>
                </div>
                <div className="flex flex-col items-center">
                  <div 
                    className="h-4 w-full rounded" 
                    style={{ backgroundColor: `rgb(0,${rgb.g},0)` }}
                  />
                  <div className="text-xs mt-1 font-medium">{Math.round(rgb.g)}</div>
                </div>
                <div className="flex flex-col items-center">
                  <div 
                    className="h-4 w-full rounded" 
                    style={{ backgroundColor: `rgb(0,0,${rgb.b})` }}
                  />
                  <div className="text-xs mt-1 font-medium">{Math.round(rgb.b)}</div>
                </div>
              </div>
            </div>

            {/* HSL Values */}
            <div>
              <div className="text-xs font-medium text-ui-foreground-tertiary mb-1">HSL</div>
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span>Hue</span>
                  <span className="font-mono">{hsl.h}°</span>
                </div>
                <div className="w-full h-2 rounded bg-gradient-to-r from-red-500 via-yellow-500 via-green-500 via-cyan-500 via-blue-500 via-violet-500 to-red-500" />
                
                <div className="flex justify-between text-xs mt-2">
                  <span>Saturation</span>
                  <span className="font-mono">{hsl.s}%</span>
                </div>
                <div className="w-full h-2 rounded bg-gradient-to-r from-gray-400 to-[var(--selected-color)]"
                     style={{ '--selected-color': selectedColor.hex } as React.CSSProperties} />
                
                <div className="flex justify-between text-xs mt-2">
                  <span>Lightness</span>
                  <span className="font-mono">{hsl.l}%</span>
                </div>
                <div className="w-full h-2 rounded bg-gradient-to-r from-black via-[var(--selected-color)] to-white"
                     style={{ '--selected-color': selectedColor.hex } as React.CSSProperties} />
              </div>
            </div>
          </div>

          {/* CMYK Values */}
          <div className="space-y-3">
            <div>
              <div className="text-xs font-medium text-ui-foreground-tertiary mb-1">CMYK</div>
              <div className="space-y-2">
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <span>Cyan</span>
                    <span className="font-mono">{cmyk.c}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                    <div 
                      className="h-1.5 rounded-full transition-all duration-300"
                      style={{ 
                        width: `${cmyk.c}%`,
                        backgroundColor: '#00bcd4'
                      }}
                    />
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <span>Magenta</span>
                    <span className="font-mono">{cmyk.m}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                    <div 
                      className="h-1.5 rounded-full transition-all duration-300"
                      style={{ 
                        width: `${cmyk.m}%`,
                        backgroundColor: '#e91e63'
                      }}
                    />
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <span>Yellow</span>
                    <span className="font-mono">{cmyk.y}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                    <div 
                      className="h-1.5 rounded-full transition-all duration-300"
                      style={{ 
                        width: `${cmyk.y}%`,
                        backgroundColor: '#ffeb3b'
                      }}
                    />
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <span>Black</span>
                    <span className="font-mono">{cmyk.k}%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                    <div 
                      className="h-1.5 rounded-full transition-all duration-300"
                      style={{ 
                        width: `${cmyk.k}%`,
                        backgroundColor: '#424242'
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* LAB Values */}
          <div className="space-y-3">
            {lab && (
              <div>
                <div className="text-xs font-medium text-ui-foreground-tertiary mb-1">LAB</div>
                <div className="space-y-2">
                  <div className="flex justify-between text-xs">
                    <span>Lightness (L*)</span>
                    <span className="font-mono">{lab.l.toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span>Green-Red (a*)</span>
                    <span className="font-mono">{lab.a.toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span>Blue-Yellow (b*)</span>
                    <span className="font-mono">{lab.b.toFixed(1)}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Additional Info */}
            <div className="mt-4 pt-4 border-t border-ui-border">
              <div className="text-xs space-y-1">
                <div className="flex justify-between">
                  <span className="text-ui-foreground-tertiary">Hex</span>
                  <span className="font-mono">{selectedColor.hex}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-ui-foreground-tertiary">Pantone</span>
                  <span className="font-medium">{selectedColor.pantone}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

MetricsTab.displayName = 'MetricsTab';