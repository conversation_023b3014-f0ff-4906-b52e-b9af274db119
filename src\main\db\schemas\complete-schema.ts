/**
 * Complete database schema including organization support
 * This ensures all required tables are created on first run
 */

export const COMPLETE_SCHEMA = `
-- Enable foreign keys
PRAGMA foreign_keys = ON;

-- Color sources table
CREATE TABLE IF NOT EXISTS color_sources (
  id INTEGER PRIMARY KEY,
  code TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  is_system BOOLEAN NOT NULL DEFAULT FALSE,
  properties JSON
);

-- Pre-populate with standard sources
INSERT OR IGNORE INTO color_sources (id, code, name, is_system) VALUES
(1, 'user', 'User Created', FALSE),
(2, 'pantone', 'PANTONE®', TRUE),
(3, 'ral', 'RAL', TRUE),
(4, 'ncs', 'NCS', TRUE);

-- Products table
CREATE TABLE IF NOT EXISTS products (
  id INTEGER PRIMARY KEY,
  external_id TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  is_master BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  -- Organization support
  organization_id INTEGER,
  created_by TEXT,
  user_id TEXT,
  metadata JSON DEFAULT '{}',
  is_active BOOLEAN NOT NULL DEFAULT TRUE
);

CREATE INDEX IF NOT EXISTS idx_products_external ON products(external_id);
CREATE INDEX IF NOT EXISTS idx_products_master ON products(is_master);
CREATE INDEX IF NOT EXISTS idx_products_org ON products(organization_id);

-- Colors table
CREATE TABLE IF NOT EXISTS colors (
  id INTEGER PRIMARY KEY,
  external_id TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  display_name TEXT,
  code TEXT,
  hex TEXT NOT NULL,
  source_id INTEGER NOT NULL DEFAULT 1 REFERENCES color_sources(id),
  properties JSON DEFAULT '{}',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  version INTEGER NOT NULL DEFAULT 1,
  -- Organization support
  organization_id INTEGER,
  created_by TEXT,
  user_id TEXT,
  deleted_at TEXT,
  device_id TEXT
);

CREATE INDEX IF NOT EXISTS idx_colors_external ON colors(external_id);
CREATE INDEX IF NOT EXISTS idx_colors_hex ON colors(hex);
CREATE INDEX IF NOT EXISTS idx_colors_source ON colors(source_id);
CREATE INDEX IF NOT EXISTS idx_colors_org ON colors(organization_id);

-- Product colors junction table
CREATE TABLE IF NOT EXISTS product_colors (
  product_id INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  color_id INTEGER NOT NULL REFERENCES colors(id) ON DELETE CASCADE,
  display_order INTEGER NOT NULL DEFAULT 0,
  organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  added_at TEXT DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (product_id, color_id)
);

CREATE INDEX IF NOT EXISTS idx_product_colors_product ON product_colors(product_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_color ON product_colors(color_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_org ON product_colors(organization_id);

-- Color spaces tables
CREATE TABLE IF NOT EXISTS color_rgb (
  color_id INTEGER PRIMARY KEY REFERENCES colors(id) ON DELETE CASCADE,
  r INTEGER NOT NULL,
  g INTEGER NOT NULL,
  b INTEGER NOT NULL,
  UNIQUE(r, g, b)
);

CREATE TABLE IF NOT EXISTS color_cmyk (
  color_id INTEGER PRIMARY KEY REFERENCES colors(id) ON DELETE CASCADE,
  c REAL NOT NULL,
  m REAL NOT NULL,
  y REAL NOT NULL,
  k REAL NOT NULL
);

CREATE TABLE IF NOT EXISTS color_lab (
  color_id INTEGER PRIMARY KEY REFERENCES colors(id) ON DELETE CASCADE,
  l REAL NOT NULL,
  a REAL NOT NULL,
  b REAL NOT NULL
);

CREATE TABLE IF NOT EXISTS color_hsl (
  color_id INTEGER PRIMARY KEY REFERENCES colors(id) ON DELETE CASCADE,
  h REAL NOT NULL,
  s REAL NOT NULL,
  l REAL NOT NULL
);

-- Organizations table
CREATE TABLE IF NOT EXISTS organizations (
  id INTEGER PRIMARY KEY,
  external_id TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  plan TEXT DEFAULT 'free' CHECK (plan IN ('free', 'team', 'enterprise')),
  settings JSON DEFAULT '{}',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Organization members table
CREATE TABLE IF NOT EXISTS organization_members (
  organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
  joined_at TEXT DEFAULT CURRENT_TIMESTAMP,
  invited_by TEXT,
  PRIMARY KEY (organization_id, user_id)
);

-- Organization invitations table
CREATE TABLE IF NOT EXISTS organization_invitations (
  id INTEGER PRIMARY KEY,
  organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('admin', 'member')),
  invited_by TEXT NOT NULL,
  invited_at TEXT DEFAULT CURRENT_TIMESTAMP,
  expires_at TEXT NOT NULL,
  accepted_at TEXT,
  token TEXT UNIQUE NOT NULL,
  UNIQUE(organization_id, email)
);

-- Users table (local cache of user data)
CREATE TABLE IF NOT EXISTS users (
  id TEXT PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  name TEXT,
  display_name TEXT,
  avatar_url TEXT,
  preferences JSON DEFAULT '{}',
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Color libraries table (Pantone, RAL, NCS, etc.)
CREATE TABLE IF NOT EXISTS color_libraries (
  id INTEGER PRIMARY KEY,
  code TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  is_system BOOLEAN NOT NULL DEFAULT TRUE,
  version TEXT,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- Library colors table (individual colors within libraries)
CREATE TABLE IF NOT EXISTS library_colors (
  id INTEGER PRIMARY KEY,
  external_id TEXT UNIQUE NOT NULL,
  library_id INTEGER NOT NULL REFERENCES color_libraries(id) ON DELETE CASCADE,
  code TEXT NOT NULL,
  name TEXT NOT NULL,
  hex CHAR(7) NOT NULL,
  cmyk TEXT,
  rgb TEXT,
  lab TEXT,
  hsl TEXT,
  notes TEXT,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  sort_order INTEGER DEFAULT 0,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,

  -- Constraints
  CHECK (
    length(hex) = 7 AND
    substr(hex, 1, 1) = '#' AND
    hex GLOB '#[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]'
  ),
  CHECK (length(trim(code)) > 0),
  CHECK (length(trim(name)) > 0),
  UNIQUE (library_id, code)
);

-- Color library metadata (for additional properties)
CREATE TABLE IF NOT EXISTS library_color_metadata (
  color_id INTEGER PRIMARY KEY REFERENCES library_colors(id) ON DELETE CASCADE,
  properties JSON,
  tags TEXT,
  search_terms TEXT,
  popularity_score INTEGER DEFAULT 0,
  usage_count INTEGER DEFAULT 0
) WITHOUT ROWID;

-- Indexes for color library performance
CREATE INDEX IF NOT EXISTS idx_library_colors_library ON library_colors(library_id);
CREATE INDEX IF NOT EXISTS idx_library_colors_code ON library_colors(library_id, code);
CREATE INDEX IF NOT EXISTS idx_library_colors_hex ON library_colors(hex);
CREATE INDEX IF NOT EXISTS idx_library_colors_active ON library_colors(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_library_colors_search ON library_colors(name, code);

-- Insert default color libraries
INSERT OR IGNORE INTO color_libraries (id, code, name, description, version) VALUES
(1, 'PANTONE', 'PANTONE®', 'PANTONE Color Matching System', '2024'),
(2, 'RAL', 'RAL Classic', 'RAL Classic Color Collection', '2024'),
(3, 'NCS', 'Natural Color System', 'NCS Natural Color System', '2024'),
(4, 'USER', 'User Colors', 'User-created custom colors', '1.0');

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_org_members_user ON organization_members(user_id);
CREATE INDEX IF NOT EXISTS idx_org_slug ON organizations(slug);
CREATE INDEX IF NOT EXISTS idx_organizations_external ON organizations(external_id);
CREATE INDEX IF NOT EXISTS idx_invitations_email ON organization_invitations(email);
CREATE INDEX IF NOT EXISTS idx_invitations_token ON organization_invitations(token);
CREATE INDEX IF NOT EXISTS idx_invitations_org ON organization_invitations(organization_id);

-- Schema migrations tracking
CREATE TABLE IF NOT EXISTS schema_migrations (
  version INTEGER PRIMARY KEY,
  name TEXT NOT NULL,
  applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Mark initial schema as applied
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (1, 'initial_schema');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (2, 'add_organizations');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (3, 'add_organization_invitations');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (4, 'add_users_table');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (7, 'add_code_column');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (8, 'fix_product_colors_schema');
INSERT OR IGNORE INTO schema_migrations (version, name) VALUES (16, 'add_organization_id_to_product_colors');

-- Triggers for updated_at timestamps
CREATE TRIGGER IF NOT EXISTS update_products_timestamp 
AFTER UPDATE ON products
BEGIN
  UPDATE products SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_colors_timestamp 
AFTER UPDATE ON colors
BEGIN
  UPDATE colors SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_organizations_timestamp 
AFTER UPDATE ON organizations
BEGIN
  UPDATE organizations SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_users_timestamp 
AFTER UPDATE ON users
BEGIN
  UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
`;