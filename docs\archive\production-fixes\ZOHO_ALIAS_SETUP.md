# Zoho Email Alias Setup Guide

## 📧 <NAME_EMAIL> alias

Since you're using Zoho EU server, follow these steps:

### 1. Login to Zoho Mail
- Go to https://mail.zoho.eu/
- Login with micha<PERSON>@chromasync.app

### 2. Add Email Alias
1. Click on the **Settings** icon (⚙️) in the top right
2. Navigate to **Mail Accounts** → **Email <PERSON>as**
3. Click **Add Alias**
4. Enter:
   - Alias Name: ChromaSync Support
   - Alias Email: <EMAIL>
5. Click **Add**
6. Verify the alias if required

### 3. Set as Default (Optional)
- In the alias settings, you <NAME_EMAIL> as the default "From" address
- This ensures all outgoing emails appear to come from support@

### 4. Test the Configuration
Run the test script to verify everything works:

```bash
node test-email-zoho.js
```

### ✅ Expected Result
- Test email sent successfully
- <PERSON>ail appears in your inbox with "From: <EMAIL>"

### 🚨 Troubleshooting
If you get a 400 error when sending emails:
- Ensure the alias is verified in Zoho
- Check that the domain chromasync.app is properly configured
- Verify SPF/DKIM records if needed

### 📝 Note
The alias must be configured in Zoho before it can be used as a "fromAddress" in the API.
