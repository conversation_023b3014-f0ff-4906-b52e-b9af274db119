/**
 * @file SettingsModal.tsx
 * @description Settings modal component with tabbed interface
 */

import React, { useState } from 'react';
import Modal from '../ui/Modal';
import {} from '../../hooks/useTokens';
import { useTheme } from '../../context/ThemeContext';
import { useSettingsStore } from '../../store/settings.store';
import { useSyncActions, useSyncStatus } from '../../store/sync.store';
import { useOrganizationStoreWithAliases } from '../../store/organization.store';
import { TeamSettings } from '../organization/TeamSettings';
import { SyncStatus } from '../../../shared/constants/sync-status';
import { Settings, Database, AlertTriangle, RefreshCw, Check, Clock, Info, Upload, Trash2, FileText, Users } from 'lucide-react';
import placeholderImage from '../../assets/placeholder-image.svg';

type SettingsTab = 'general' | 'advanced' | 'team';

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}


interface ThemeOptionProps {
  name: string;
  selected: boolean;
  onClick: () => void;
  className: string;
  textColor: string;
}
  // @ts-ignore - Intentionally unused

const _ThemeOption: React.FC<ThemeOptionProps> = ({ name, selected, onClick, className, textColor }) => {
  return (
    <button
      onClick={onClick}
      className={`relative h-20 rounded-[var(--radius-md)] border ${className} transition-all ${selected ? 'ring-2 ring-brand-primary ring-offset-2' : 'hover:border-ui-border-medium dark:hover:border-zinc-600'}`}
    >
      <div className="absolute inset-0 flex items-center justify-center">
        <span className={`font-medium ${textColor}`}>{name}</span>
      </div>
      {selected && (
        <div className="absolute top-2 right-2 h-4 w-4 rounded-full bg-brand-primary flex items-center justify-center">
          <Check size={12} className="text-white" />
        </div>
      )}
    </button>
  );
};

const SettingsModal: React.FC<SettingsModalProps> = ({ isOpen, onClose }) => {
  const theme = useTheme();
  const settings = useSettingsStore();
  const syncStatus = useSyncStatus();
  const syncActions = useSyncActions();
  const { currentOrg, loadCurrentOrganization } = useOrganizationStoreWithAliases();
  const [activeTab, setActiveTab] = useState<SettingsTab>('general');
  
  // Debug logging
  console.log('[SettingsModal] Current organization:', currentOrg);
  
  // Load organization when modal opens
  React.useEffect(() => {
    if (isOpen && !currentOrg) {
      console.log('[SettingsModal] Modal opened but no org, loading...');
      loadCurrentOrganization();
    }
  }, [isOpen, currentOrg, loadCurrentOrganization]);

  // Tab button styling
  const getTabButtonClass = (tab: SettingsTab) => {
    return `flex items-center px-4 py-2 text-[var(--font-size-sm)] font-[var(--font-weight-medium)] rounded-[var(--radius-md)] transition-colors ${
      activeTab === tab
        ? 'bg-ui-background-tertiary dark:bg-zinc-700 text-ui-foreground-primary dark:text-white'
        : 'text-ui-foreground-secondary dark:text-gray-400 hover:text-ui-foreground-primary dark:hover:text-white'
    }`;
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Settings" maxWidth="max-w-4xl">
      <div className="flex h-[70vh] max-h-[600px]">
        {/* Sidebar */}
        <div className="w-48 border-r border-ui-border-light dark:border-zinc-700 p-4">
          <nav className="space-y-1">
            <button
              className={getTabButtonClass('general')}
              onClick={() => setActiveTab('general')}
            >
              <Settings size={16} className="mr-2" />
              <span>General</span>
            </button>
            {currentOrg && (
              <button
                className={getTabButtonClass('team')}
                onClick={() => setActiveTab('team')}
              >
                <Users size={16} className="mr-2" />
                <span>Team</span>
              </button>
            )}
            <button
            >
              <Database size={16} className="mr-2" />
            </button>
            <button
              className={getTabButtonClass('advanced')}
              onClick={() => setActiveTab('advanced')}
            >
              <AlertTriangle size={16} className="mr-2" />
              <span>Advanced</span>
            </button>
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          {activeTab === 'general' ? (
            <div className="space-y-8">
              {/* Theme Selection */}
              <section>
                <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-4">
                  Theme Preferences
                </h3>
                <div className="space-y-6">
                  {/* Preferred Light Theme */}
                  <div>
                    <label className="block text-ui-foreground-primary dark:text-white mb-2">
                      Preferred Light Theme
                    </label>
                    <div className="flex items-center space-x-4">
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          className="form-radio h-4 w-4 text-brand-primary"
                          checked={settings.preferredLightTheme === 'light'}
                          onChange={() => {
                            settings.setPreferredLightTheme('light');
                            if (theme.mode === 'light') {
                              // Apply the change immediately if we're in light mode
                              theme.setMode('light');
                            }
                          }}
                        />
                        <span className="ml-2 text-ui-foreground-primary dark:text-gray-300">Standard Light</span>
                      </label>
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          className="form-radio h-4 w-4 text-brand-primary"
                          checked={settings.preferredLightTheme === 'system-light'}
                          onChange={() => {
                            settings.setPreferredLightTheme('system-light');
                            if (theme.mode === 'light') {
                              // Apply the change immediately if we're in light mode
                              theme.setMode('light');
                            }
                          }}
                        />
                        <span className="ml-2 text-ui-foreground-primary dark:text-gray-300">System Light</span>
                      </label>
                    </div>
                  </div>
                  {/* Preferred Dark Theme */}
                  <div>
                    <label className="block text-ui-foreground-primary dark:text-white mb-2">
                      Preferred Dark Theme
                    </label>
                    <div className="flex items-center space-x-4">
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          className="form-radio h-4 w-4 text-brand-primary"
                          checked={settings.preferredDarkTheme === 'dark'}
                          onChange={() => {
                            settings.setPreferredDarkTheme('dark');
                            if (theme.mode === 'dark') {
                              // Apply the change immediately if we're in dark mode
                              theme.setMode('dark');
                            }
                          }}
                        />
                        <span className="ml-2 text-ui-foreground-primary dark:text-gray-300">Standard Dark</span>
                      </label>
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          className="form-radio h-4 w-4 text-brand-primary"
                          checked={settings.preferredDarkTheme === 'new-york'}
                          onChange={() => {
                            settings.setPreferredDarkTheme('new-york');
                            if (theme.mode === 'dark') {
                              // Apply the change immediately if we're in dark mode
                              theme.setMode('dark');
                            }
                          }}
                        />
                        <span className="ml-2 text-ui-foreground-primary dark:text-gray-300">New York</span>
                      </label>
                    </div>
                  </div>
                </div>
              </section>

              {/* Logo Management */}
              <section>
                <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-4">
                  Logo
                </h3>
                <div className="bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4 mb-4">
                  {settings.logoPath ? (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-16 h-16 bg-ui-background-tertiary dark:bg-zinc-700 rounded-[var(--radius-md)] flex items-center justify-center overflow-hidden">
                          <img
                            src={settings.logoPath}
                            alt="Logo"
                            className="max-w-full max-h-full object-contain"
                            onError={(e) => {
                              // If image fails to load, show a placeholder
                              (e.target as HTMLImageElement).src = placeholderImage;
                            }}
                          />
                        </div>
                        <span className="ml-4 text-ui-foreground-primary dark:text-white truncate max-w-xs">
                          {settings.logoPath.split('/').pop()}
                        </span>
                      </div>
                      <button
                        onClick={() => settings.setLogoPath(null)}
                        className="text-ui-foreground-tertiary dark:text-gray-400 hover:text-feedback-error dark:hover:text-red-400 transition-colors"
                        aria-label="Remove logo"
                      >
                        <Trash2 size={20} />
                      </button>
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <p className="text-ui-foreground-secondary dark:text-gray-400 mb-4">
                        No logo selected. Upload a logo to display in the app header.
                      </p>
                      <button
                        onClick={() => {
                          // This would use electron's dialog API via IPC
                          // For now, we'll just set a placeholder path
                          settings.setLogoPath('../../../assets/IVG Logo normal.svg');
                        }}
                        className="inline-flex items-center px-4 py-2 bg-ui-background-tertiary dark:bg-zinc-700 text-ui-foreground-primary dark:text-white rounded-[var(--radius-md)] hover:bg-ui-background-tertiary/80 dark:hover:bg-zinc-600 transition-colors"
                      >
                        <Upload size={16} className="mr-2" />
                        Select Logo
                      </button>
                    </div>
                  )}
                </div>
              </section>

              {/* Import/Export Preferences */}
              <section>
                <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-4">
                  Import/Export Preferences
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-ui-foreground-primary dark:text-white mb-2">
                      Default Import Format
                    </label>
                    <div className="flex items-center space-x-4">
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          className="form-radio h-4 w-4 text-brand-primary"
                          checked={settings.defaultImportFormat === 'json'}
                          onChange={() => settings.setDefaultImportFormat('json')}
                        />
                        <span className="ml-2 text-ui-foreground-primary dark:text-gray-300">JSON</span>
                      </label>
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          className="form-radio h-4 w-4 text-brand-primary"
                          checked={settings.defaultImportFormat === 'csv'}
                          onChange={() => settings.setDefaultImportFormat('csv')}
                        />
                        <span className="ml-2 text-ui-foreground-primary dark:text-gray-300">CSV</span>
                      </label>
                    </div>
                  </div>

                  <div>
                    <label className="block text-ui-foreground-primary dark:text-white mb-2">
                      Default Export Format
                    </label>
                    <div className="flex items-center space-x-4">
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          className="form-radio h-4 w-4 text-brand-primary"
                          checked={settings.defaultExportFormat === 'json'}
                          onChange={() => settings.setDefaultExportFormat('json')}
                        />
                        <span className="ml-2 text-ui-foreground-primary dark:text-gray-300">JSON</span>
                      </label>
                      <label className="flex items-center cursor-pointer">
                        <input
                          type="radio"
                          className="form-radio h-4 w-4 text-brand-primary"
                          checked={settings.defaultExportFormat === 'csv'}
                          onChange={() => settings.setDefaultExportFormat('csv')}
                        />
                        <span className="ml-2 text-ui-foreground-primary dark:text-gray-300">CSV</span>
                      </label>
                    </div>
                  </div>
                </div>
              </section>

              {/* Accessibility Settings */}
              <section>
                <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-4">
                  Accessibility
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-ui-foreground-primary dark:text-white mb-2">
                      Color Blindness Mode
                    </label>
                    <div className="bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4">
                      <div className="space-y-3">
                        <label className="flex items-center justify-between cursor-pointer">
                          <span className="text-ui-foreground-primary dark:text-gray-300">Enable Color Blindness Mode</span>
                          <input
                            type="checkbox"
                            className="form-checkbox h-5 w-5 text-brand-primary rounded"
                            checked={false}
                            onChange={() => {}}
                          />
                        </label>
                        <div className="mt-3">
                          <select
                            className="w-full px-3 py-2 bg-ui-background-tertiary dark:bg-zinc-700 border border-ui-border-light dark:border-zinc-600 rounded-[var(--radius-md)] text-ui-foreground-primary dark:text-white"
                            disabled={true}
                          >
                            <option value="normal">Normal Vision</option>
                            <option value="protanopia">Protanopia (Red-blind)</option>
                            <option value="deuteranopia">Deuteranopia (Green-blind)</option>
                            <option value="tritanopia">Tritanopia (Blue-blind)</option>
                            <option value="protanomaly">Protanomaly (Red-weak)</option>
                            <option value="deuteranomaly">Deuteranomaly (Green-weak)</option>
                            <option value="tritanomaly">Tritanomaly (Blue-weak)</option>
                            <option value="achromatopsia">Achromatopsia (No color)</option>
                          </select>
                        </div>
                        <div className="text-sm text-ui-foreground-secondary dark:text-gray-400 mt-2">
                          <Info size={14} className="inline mr-1" />
                          Simulates how colors appear to people with different types of color vision deficiencies.
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label className="flex items-center justify-between cursor-pointer bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4">
                      <div>
                        <span className="text-ui-foreground-primary dark:text-gray-300 block">High Contrast Mode</span>
                        <span className="text-sm text-ui-foreground-secondary dark:text-gray-400">Increases contrast for better visibility</span>
                      </div>
                      <input
                        type="checkbox"
                        className="form-checkbox h-5 w-5 text-brand-primary rounded"
                        checked={false}
                        onChange={() => {}}
                      />
                    </label>
                  </div>

                  <div>
                    <label className="flex items-center justify-between cursor-pointer bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4">
                      <div>
                        <span className="text-ui-foreground-primary dark:text-gray-300 block">Reduce Motion</span>
                        <span className="text-sm text-ui-foreground-secondary dark:text-gray-400">Minimizes animations and transitions</span>
                      </div>
                      <input
                        type="checkbox"
                        className="form-checkbox h-5 w-5 text-brand-primary rounded"
                        checked={false}
                        onChange={() => {}}
                      />
                    </label>
                  </div>
                </div>
              </section>

              {/* License Information */}
              <section>
                <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-4">
                  About ChromaSync
                </h3>
                <div className="bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-ui-foreground-secondary dark:text-gray-400">Version</span>
                      <span className="text-ui-foreground-primary dark:text-white">1.0.0</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-ui-foreground-secondary dark:text-gray-400">Device ID</span>
                      <span className="text-ui-foreground-primary dark:text-white font-mono text-sm">
                        Not available
                      </span>
                    </div>
                    <div className="pt-2">
                      <div className="flex items-start">
                        <Info size={16} className="text-ui-foreground-secondary dark:text-gray-400 mt-1 mr-2 flex-shrink-0" />
                        <p className="text-sm text-ui-foreground-secondary dark:text-gray-400">
                          Your device information for licensing and support purposes.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </section>

              {/* Reset to Default */}
              <section>
                <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-4">
                  Reset Settings
                </h3>
                <button
                  onClick={() => {
                    if (window.confirm('Are you sure you want to reset all settings to their default values?')) {
                      settings.resetSettings();
                    }
                  }}
                  className="px-4 py-2 bg-feedback-error text-white rounded-[var(--radius-md)] hover:bg-red-600 transition-colors"
                >
                  Reset to Default
                </button>
              </section>

              {/* Sync Status */}
              <section>
                <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-4">
                  Sync Status
                </h3>
                <div className="bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-ui-foreground-secondary dark:text-gray-400">Status</span>
                      <div className="flex items-center">
                        {syncStatus.status === SyncStatus.SUCCESS && (
                          <span className="flex items-center text-feedback-success">
                            <Check size={16} className="mr-1" />
                            Synced
                          </span>
                        )}
                        {syncStatus.status === SyncStatus.ERROR && (
                          <span className="flex items-center text-feedback-error">
                            <AlertTriangle size={16} className="mr-1" />
                            Error
                          </span>
                        )}
                        {syncStatus.status === SyncStatus.SYNCING && (
                          <span className="flex items-center text-brand-primary">
                            <RefreshCw size={16} className="mr-1 animate-spin" />
                            Syncing
                          </span>
                        )}
                        {syncStatus.status === SyncStatus.IDLE && (
                          <span className="flex items-center text-ui-foreground-tertiary dark:text-gray-400">
                            <Clock size={16} className="mr-1" />
                            Idle
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-ui-foreground-secondary dark:text-gray-400">Last Sync</span>
                      <span className="text-ui-foreground-primary dark:text-white">
                        {syncStatus.lastSyncTime ? new Date(syncStatus.lastSyncTime).toLocaleString() : 'Never'}
                      </span>
                    </div>
                    <div className="pt-2">
                      <button
                        onClick={() => {
                          syncActions.syncData();
                        }}
                        className="w-full px-4 py-2 bg-brand-primary text-white rounded-[var(--radius-md)] hover:bg-brand-primary/90 transition-colors flex items-center justify-center"
                      >
                        <RefreshCw size={16} className="mr-2" />
                        Sync Now
                      </button>
                    </div>
                  </div>
                </div>
              </section>

              {/* Sync Settings */}
              <section>
                <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-4">
                  Sync Settings
                </h3>
                <div className="space-y-4">
                  {/* Auto-Sync Interval */}
                  <div>
                    <label className="block text-ui-foreground-primary dark:text-white mb-2">
                      Auto-Sync Interval
                    </label>
                    <select
                      value={settings.autoSyncInterval}
                      onChange={(e) => settings.setAutoSyncInterval(Number(e.target.value))}
                      className="w-full px-3 py-2 bg-ui-background-primary dark:bg-zinc-700 border border-ui-border-light dark:border-zinc-600 rounded-[var(--radius-md)] text-ui-foreground-primary dark:text-white focus:outline-none focus:ring-2 focus:ring-brand-primary"
                    >
                      <option value="0">Disabled</option>
                      <option value="5">Every 5 minutes</option>
                      <option value="15">Every 15 minutes</option>
                      <option value="30">Every 30 minutes</option>
                      <option value="60">Every hour</option>
                      <option value="360">Every 6 hours</option>
                      <option value="720">Every 12 hours</option>
                      <option value="1440">Every 24 hours</option>
                    </select>
                  </div>

                  {/* Auto-Sync on Startup */}
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="autoSyncOnStartup"
                      checked={settings.autoSyncOnStartup}
                      onChange={(e) => settings.setAutoSyncOnStartup(e.target.checked)}
                      className="h-4 w-4 text-brand-primary rounded border-ui-border-medium focus:ring-brand-primary"
                    />
                    <label htmlFor="autoSyncOnStartup" className="ml-2 text-ui-foreground-primary dark:text-white">
                      Auto-Sync on Startup
                    </label>
                  </div>

                  {/* Conflict Resolution Strategy */}
                  <div>
                    <label className="block text-ui-foreground-primary dark:text-white mb-2">
                      Conflict Resolution Strategy
                    </label>
                    <select
                      value={settings.syncConflictStrategy}
                      onChange={(e) => settings.setSyncConflictStrategy(e.target.value as 'local' | 'remote' | 'newest')}
                      className="w-full px-3 py-2 bg-ui-background-primary dark:bg-zinc-700 border border-ui-border-light dark:border-zinc-600 rounded-[var(--radius-md)] text-ui-foreground-primary dark:text-white focus:outline-none focus:ring-2 focus:ring-brand-primary"
                    >
                      <option value="newest">Use Newest Version</option>
                      <option value="local">Prefer Local Changes</option>
                      <option value="remote">Prefer Remote Changes</option>
                    </select>
                    <p className="mt-1 text-sm text-ui-foreground-tertiary dark:text-gray-400">
                      {settings.syncConflictStrategy === 'newest' && 'When conflicts occur, the most recently updated version will be used.'}
                      {settings.syncConflictStrategy === 'local' && 'When conflicts occur, your local changes will always be preserved.'}
                      {settings.syncConflictStrategy === 'remote' && 'When conflicts occur, remote changes will override your local changes.'}
                    </p>
                  </div>

                  {/* Data Retention */}
                  <div>
                    <label className="block text-ui-foreground-primary dark:text-white mb-2">
                      Local Data Retention
                    </label>
                    <select
                      value={settings.dataRetentionDays}
                      onChange={(e) => settings.setDataRetentionDays(Number(e.target.value))}
                      className="w-full px-3 py-2 bg-ui-background-primary dark:bg-zinc-700 border border-ui-border-light dark:border-zinc-600 rounded-[var(--radius-md)] text-ui-foreground-primary dark:text-white focus:outline-none focus:ring-2 focus:ring-brand-primary"
                    >
                      <option value="0">Keep Forever</option>
                      <option value="7">7 Days</option>
                      <option value="30">30 Days</option>
                      <option value="90">90 Days</option>
                      <option value="180">180 Days</option>
                      <option value="365">365 Days</option>
                    </select>
                    <p className="mt-1 text-sm text-ui-foreground-tertiary dark:text-gray-400">
                      {settings.dataRetentionDays === 0
                        ? 'Local data will be kept indefinitely.'
                        : `Local data older than ${settings.dataRetentionDays} days will be automatically purged.`}
                    </p>
                  </div>

                  {/* Realtime Sync */}
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="enableRealtimeSync"
                      checked={settings.enableRealtimeSync}
                      onChange={(e) => settings.setEnableRealtimeSync(e.target.checked)}
                      className="h-4 w-4 text-brand-primary rounded border-ui-border-medium focus:ring-brand-primary"
                    />
                    <label htmlFor="enableRealtimeSync" className="ml-2 text-ui-foreground-primary dark:text-white">
                      Enable Realtime Sync (Experimental)
                    </label>
                  </div>
                </div>
              </section>

              <section>
                <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-4">
                </h3>
                <div className="space-y-4">
                  <div className="bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] p-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-ui-foreground-primary dark:text-white font-mono text-sm truncate max-w-xs">
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-ui-foreground-secondary dark:text-gray-400">Device ID</span>
                        <span className="text-ui-foreground-primary dark:text-white font-mono text-sm truncate max-w-xs">
                          device-id-placeholder
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Test Connection */}
                  <div>
                    <button
                      onClick={() => {
                        // This would call an actual test connection function
                        alert('Connection test successful!');
                      }}
                      className="w-full px-4 py-2 bg-ui-background-tertiary dark:bg-zinc-700 text-ui-foreground-primary dark:text-white rounded-[var(--radius-md)] hover:bg-ui-background-tertiary/80 dark:hover:bg-zinc-600 transition-colors flex items-center justify-center"
                    >
                      <RefreshCw size={16} className="mr-2" />
                      Test Connection
                    </button>
                  </div>
                </div>
              </section>
            </div>
          ) : activeTab === 'advanced' ? (
            <div className="space-y-8">
              {/* Warning Banner */}
              <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded-[var(--radius-lg)] p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="h-5 w-5 text-yellow-400" aria-hidden="true" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                      Advanced Settings
                    </h3>
                    <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                      <p>
                        These settings are intended for troubleshooting and debugging purposes.
                        Changes in this section may affect application stability.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Error Logs */}
              <section>
                <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-4">
                  Error Logs
                </h3>
                <div className="space-y-4">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => {
                        // This would use an IPC call to fetch logs from the main process
                        alert('Logs would be displayed here.');
                      }}
                      className="px-4 py-2 bg-ui-background-tertiary dark:bg-zinc-700 text-ui-foreground-primary dark:text-white rounded-[var(--radius-md)] hover:bg-ui-background-tertiary/80 dark:hover:bg-zinc-600 transition-colors flex items-center"
                    >
                      <FileText size={16} className="mr-2" />
                      View Logs
                    </button>

                    <button
                      onClick={() => {
                        if (window.confirm('Are you sure you want to clear all error logs? This cannot be undone.')) {
                          // This would use an IPC call to clear logs in the main process
                          alert('Logs cleared successfully.');
                        }
                      }}
                      className="px-4 py-2 bg-feedback-error text-white rounded-[var(--radius-md)] hover:bg-red-600 transition-colors flex items-center"
                    >
                      <Trash2 size={16} className="mr-2" />
                      Clear Logs
                    </button>
                  </div>
                </div>
              </section>

              {/* Developer Tools */}
              <section>
                <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-4">
                  Developer Tools
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="devTools"
                      className="h-4 w-4 text-brand-primary rounded border-ui-border-medium focus:ring-brand-primary"
                      checked={settings.showDevTools}
                      onChange={(e) => settings.setShowDevTools(e.target.checked)}
                    />
                    <label htmlFor="devTools" className="ml-2 text-ui-foreground-primary dark:text-white">
                      Show Developer Tools in Production
                    </label>
                  </div>
                  <p className="text-sm text-ui-foreground-tertiary dark:text-gray-400">
                    When enabled, developer tools will be available in production mode. This setting requires an application restart to take effect.
                  </p>
                </div>
              </section>

              {/* Database Maintenance */}
              <section>
                <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-4">
                  Database Maintenance
                </h3>
                <div className="space-y-4">
                  <button
                    onClick={() => {
                      if (window.confirm('Are you sure you want to optimize the database? The application will be unresponsive during this process.')) {
                        // This would trigger an IPC call to optimize the database
                        alert('Database optimization complete.');
                      }
                    }}
                    className="px-4 py-2 bg-ui-background-tertiary dark:bg-zinc-700 text-ui-foreground-primary dark:text-white rounded-[var(--radius-md)] hover:bg-ui-background-tertiary/80 dark:hover:bg-zinc-600 transition-colors"
                  >
                    Optimize Database
                  </button>
                  <p className="text-sm text-ui-foreground-tertiary dark:text-gray-400">
                    Optimizes the local SQLite database to improve performance. This process may take several minutes depending on the size of your database.
                  </p>
                </div>
              </section>
            </div>
          ) : activeTab === 'team' ? (
            <div className="h-full">
              <TeamSettings />
            </div>
          ) : null}
        </div>
      </div>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-ui-border-light dark:border-zinc-700 flex justify-end">
        <button
          onClick={onClose}
          className="px-4 py-2 bg-brand-primary text-white rounded-[var(--radius-md)] hover:bg-brand-primary/90 transition-colors"
        >
          Close
        </button>
      </div>
    </Modal>
  );
};

export default SettingsModal;
