-- Simple test data insertion script
-- Insert test product
INSERT INTO products (id, external_id, name, metadata, created_at, updated_at)
VALUES 
  ('test-product-001', 'test-product-001', 'Test Product with Colors', 
   '{"description": "Test product with flat and gradient colors", "testData": true}',
   datetime('now'), datetime('now'));

-- Insert flat color
INSERT INTO colors (id, external_id, source_id, code, display_name, hex, 
                   red, green, blue, cyan, magenta, yellow, black, 
                   is_gradient, created_at, updated_at)
VALUES 
  ('test-color-flat-001', 'test-color-flat-001', 1, 'TEST-BLUE', 'Test Blue', '#2563EB',
   37, 99, 235, 85, 60, 0, 8,
   0, datetime('now'), datetime('now'));

-- Insert gradient color  
INSERT INTO colors (id, external_id, source_id, code, display_name, hex,
                   red, green, blue, cyan, magenta, yellow, black,
                   is_gradient, properties, created_at, updated_at)
VALUES 
  ('test-color-gradient-001', 'test-color-gradient-001', 1, 'TEST-GRADIENT', 'Test Gradient (Blue to Purple)', '#2563EB',
   37, 99, 235, 85, 60, 0, 8,
   1, '{"gradient": {"type": "linear", "angle": 45, "stops": [{"position": 0, "color": "#2563EB"}, {"position": 0.5, "color": "#7C3AED"}, {"position": 1, "color": "#DC2626"}]}}',
   datetime('now'), datetime('now'));

-- Associate colors with product
INSERT INTO product_colors (product_id, color_id, display_order)
VALUES 
  ('test-product-001', 'test-color-flat-001', 1),
  ('test-product-001', 'test-color-gradient-001', 2);

-- Show what was created
SELECT 
  p.name as product_name,
  c.code as color_code,
  c.display_name as color_name,
  c.hex,
  CASE WHEN c.is_gradient = 1 THEN 'Gradient' ELSE 'Flat' END as color_type
FROM products p
JOIN product_colors pc ON p.id = pc.product_id
JOIN colors c ON pc.color_id = c.id
WHERE p.id = 'test-product-001'
ORDER BY pc.display_order;