/**
 * @file HarmonyDisplay.tsx
 * @description Component for displaying harmony color results with world-class aesthetics
 */

import React, { useState } from 'react';
import { useColorComparisonStore } from '../../store/colorComparison.store';
import { useTokens } from '../../hooks/useTokens';
import { Check, Info, ClipboardCopy, EyeOff, Eye, Copy, FileText, Code } from 'lucide-react';
import { getContrastRatio, getWCAGLevel } from './utils';
import Tooltip from '../Tooltip';
import { formatCMYKForDisplay, parseCMYK } from '../../../shared/utils/color';
import { useToast } from '../../hooks/useToast';
import { useClickOutside } from '../../utils/useClickOutside';

// Function removed as it's not used

const HarmonyDisplay: React.FC = () => {
  const tokens = useTokens();
  const { harmonyResults: harmonyColors, selectedHarmonyType: harmonyType } = useColorComparisonStore();
  const [copiedColor, setCopiedColor] = useState<string | null>(null);
  const [activeColor, setActiveColor] = useState<number | null>(null);
  const [showDetails, setShowDetails] = useState<boolean>(true);
  const [showCopyMenu, setShowCopyMenu] = useState<boolean>(false);
  const { toast } = useToast();
  const copyMenuRef = React.useRef<HTMLDivElement>(null);
  
  useClickOutside(copyMenuRef, () => setShowCopyMenu(false));

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopiedColor(text);
    setTimeout(() => setCopiedColor(null), 1500);
  };

  const copyAllColors = (format: 'hex' | 'rgb' | 'json') => {
    if (!harmonyColors || harmonyColors.length === 0) {return;}

    let copyText = '';
    
    switch (format) {
      case 'hex':
        copyText = harmonyColors.map(c => c.hex).join(', ');
        break;
      case 'rgb':
        copyText = harmonyColors.map(c => c.rgb ? `rgb(${c.rgb.r}, ${c.rgb.g}, ${c.rgb.b})` : c.hex).join(', ');
        break;
      case 'json':
        copyText = JSON.stringify(harmonyColors.map(c => ({
          hex: c.hex,
          rgb: c.rgb,
          name: c.name || c.code || undefined,
          cmyk: c.cmyk || undefined
        })), null, 2);
        break;
    }

    navigator.clipboard.writeText(copyText);
    toast({ 
      title: `Copied ${harmonyColors.length} colors`,
      description: `Colors copied as ${format.toUpperCase()} format`,
      type: 'success'
    });
    setShowCopyMenu(false);
  };

  if (!harmonyColors || harmonyColors.length === 0) {
    return (
      <div className="text-center p-[var(--spacing-4)] text-ui-text-muted flex flex-col items-center justify-center h-48 bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-lg)] border border-ui-border dark:border-zinc-700 border-dashed">
        <div className="mb-[var(--spacing-2)]">
          <Info size={20} className="text-ui-text-muted opacity-40 dark:text-gray-400" />
        </div>
        <p className="text-ui-text-secondary font-medium text-[0.6rem] dark:text-gray-300">No harmony colors generated yet</p>
        <p className="text-[0.55rem] text-ui-text-muted mt-[var(--spacing-1)] dark:text-gray-400">
          Select a base color and harmony type, then click the refresh button
        </p>
      </div>
    );
  }

  return (
    <div className="harmony-display">
      <div className="flex items-center justify-between mb-[var(--spacing-3)]">
        <h3 className="text-xs font-medium flex items-center text-ui-text-primary dark:text-white">
          {harmonyType.charAt(0).toUpperCase() + harmonyType.slice(1)} Colour Harmony
          <Tooltip content="Color harmonies are combinations of colors that create visual balance and are appealing to the eye.">
            <button className="ml-[var(--spacing-1)]">
              <Info size={12} className="text-ui-text-muted dark:text-gray-400" />
            </button>
          </Tooltip>
        </h3>
        <div className="flex items-center gap-[var(--spacing-2)]">
          <div className="relative" ref={copyMenuRef}>
            <button
              onClick={() => setShowCopyMenu(!showCopyMenu)}
              className="flex items-center gap-[var(--spacing-1)] text-[0.5rem] text-ui-text-muted dark:text-gray-400 hover:text-ui-text-primary dark:hover:text-white transition-colors"
            >
              <Copy size={10} />
              <span>Copy all</span>
            </button>
            {showCopyMenu && (
              <div className="absolute top-full right-0 mt-1 bg-ui-background-primary dark:bg-zinc-800 border border-ui-border dark:border-zinc-700 rounded-[var(--radius-md)] shadow-lg py-1 z-10 min-w-[120px]">
                <button
                  onClick={() => copyAllColors('hex')}
                  className="flex items-center gap-2 w-full px-3 py-1.5 text-[0.55rem] text-ui-text-primary dark:text-gray-200 hover:bg-ui-background-secondary dark:hover:bg-zinc-700 transition-colors"
                >
                  <Code size={12} />
                  HEX values
                </button>
                <button
                  onClick={() => copyAllColors('rgb')}
                  className="flex items-center gap-2 w-full px-3 py-1.5 text-[0.55rem] text-ui-text-primary dark:text-gray-200 hover:bg-ui-background-secondary dark:hover:bg-zinc-700 transition-colors"
                >
                  <FileText size={12} />
                  RGB values
                </button>
                <button
                  onClick={() => copyAllColors('json')}
                  className="flex items-center gap-2 w-full px-3 py-1.5 text-[0.55rem] text-ui-text-primary dark:text-gray-200 hover:bg-ui-background-secondary dark:hover:bg-zinc-700 transition-colors"
                >
                  <Code size={12} />
                  JSON format
                </button>
              </div>
            )}
          </div>
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center gap-[var(--spacing-1)] text-[0.5rem] text-ui-text-muted dark:text-gray-400 hover:text-ui-text-primary dark:hover:text-white transition-colors"
          >
            {showDetails ? (
              <>
                <EyeOff size={10} />
                <span>Hide details</span>
              </>
            ) : (
              <>
                <Eye size={10} />
                <span>Show details</span>
              </>
            )}
          </button>
        </div>
      </div>

      <div className="harmony-colors grid grid-cols-5 gap-[var(--spacing-3)]">
        {harmonyColors.map((color, index) => {
          const blackContrast = getContrastRatio(color.hex, '#000000');
          const whiteContrast = getContrastRatio(color.hex, '#ffffff');
          // Removed unused variable: bestTextColor
          // const bestTextColor = whiteContrast > blackContrast ? '#ffffff' : '#000000';
          const blackLevel = getWCAGLevel(blackContrast);
          const whiteLevel = getWCAGLevel(whiteContrast);

          const isActive = activeColor === index;

          return (
            <div
              key={index}
              className={`harmony-color relative rounded-[var(--radius-lg)] overflow-hidden group ${isActive ? 'ring-2 ring-brand-primary' : ''}`}
              style={{
                boxShadow: isActive ? tokens.shadows.lg : tokens.shadows.md,
                transition: `all ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`
              }}
              onMouseEnter={() => setActiveColor(index)}
              onMouseLeave={() => setActiveColor(null)}
            >
              <div
                className="color-preview aspect-square flex flex-col items-center justify-center cursor-pointer relative overflow-hidden"
                style={{ backgroundColor: color.hex }}
                onClick={() => copyToClipboard(color.hex)}
              >
                {/* Pantone Code and Hex Display */}
                <div className="absolute inset-0 flex flex-col items-center justify-center p-2">
                  {color.code && (
                    <div className="text-white text-[0.55rem] font-semibold mb-1" 
                         style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }}>
                      {color.code}
                    </div>
                  )}
                  <div className="text-white text-[0.5rem] font-mono font-bold" 
                       style={{ textShadow: '1px 1px 2px rgba(0,0,0,0.8)' }}>
                    {color.hex}
                  </div>
                </div>

                {/* Hover overlay */}
                <div
                  className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                  style={{
                    backgroundColor: 'rgba(0,0,0,0.4)'
                  }}
                >
                  <ClipboardCopy className="text-white" size={16} />
                </div>

                {/* Overlay for focus state */}
                <div
                  className={`absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 ${copiedColor === color.hex ? 'bg-opacity-20' : ''}`}
                ></div>

                {/* Copy feedback */}
                <div
                  className={`absolute inset-0 flex items-center justify-center transition-opacity duration-300 ${copiedColor === color.hex ? 'opacity-100' : 'opacity-0'}`}
                  style={{ backgroundColor: 'rgba(0,0,0,0.2)' }}
                >
                  <div
                    className="bg-ui-background-primary dark:bg-zinc-700 rounded-full p-1.5 shadow-[var(--shadow-lg)]"
                    style={{ transform: 'scale(1.2)' }}
                  >
                    <Check className="text-brand-primary" size={14} />
                  </div>
                </div>

              </div>

              {showDetails && (
                <div className="color-info p-[var(--spacing-1.5)] bg-ui-background-primary dark:bg-zinc-700 border-t border-ui-border dark:border-zinc-600">
                  {/* RGB Values */}
                  {color.rgb && (
                    <div className="text-[0.45rem] text-ui-text-secondary dark:text-gray-300 text-center mb-[var(--spacing-1)]">
                      RGB: {color.rgb.r}, {color.rgb.g}, {color.rgb.b}
                    </div>
                  )}

                  <div className="grid grid-cols-2 gap-[var(--spacing-1)]">
                    <div
                      className="flex items-center justify-center text-[0.45rem] bg-ui-background-secondary dark:bg-zinc-800 py-[var(--spacing-0.5)] px-[var(--spacing-0.5)] rounded-[var(--radius-sm)]"
                    >
                      <span className="w-1.5 h-1.5 rounded-full bg-white border border-ui-border dark:border-zinc-600 mr-[var(--spacing-0.5)] flex-shrink-0"></span>
                      <span className={`${whiteLevel === 'AAA' ? 'text-green-500' : whiteLevel === 'AA' ? 'text-yellow-500' : 'text-red-500'}`}>
                        {whiteLevel}
                      </span>
                    </div>

                    <div
                      className="flex items-center justify-center text-[0.45rem] bg-ui-background-secondary dark:bg-zinc-800 py-[var(--spacing-0.5)] px-[var(--spacing-0.5)] rounded-[var(--radius-sm)]"
                    >
                      <span className="w-1.5 h-1.5 rounded-full bg-black mr-[var(--spacing-0.5)] flex-shrink-0"></span>
                      <span className={`${blackLevel === 'AAA' ? 'text-green-500' : blackLevel === 'AA' ? 'text-yellow-500' : 'text-red-500'}`}>
                        {blackLevel}
                      </span>
                    </div>

                    {color.cmyk && (
                      <div
                        className="col-span-2 text-[0.45rem] text-ui-text-muted dark:text-gray-400 mt-[var(--spacing-0.5)] text-center truncate"
                        title={formatCMYKForDisplay(parseCMYK(color.cmyk))}
                      >
                        {formatCMYKForDisplay(parseCMYK(color.cmyk))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Color harmony explanation */}
      <div className="mt-[var(--spacing-4)] p-[var(--spacing-2)] bg-ui-background-secondary dark:bg-zinc-800 rounded-[var(--radius-md)] border border-ui-border dark:border-zinc-700 text-[0.6rem] text-ui-text-secondary dark:text-gray-300">
        <p className="mb-[var(--spacing-1)]">
          <strong className="font-medium text-ui-text-primary dark:text-white">{harmonyType.charAt(0).toUpperCase() + harmonyType.slice(1)} harmony</strong> is a colour scheme that {getHarmonyDescription(harmonyType)}
        </p>
        <p className="text-[0.5rem] text-ui-text-muted dark:text-gray-400">
          Click on any color to copy its hex value to your clipboard
        </p>
      </div>
    </div>
  );
};

// Helper function to get descriptions based on harmony type
const getHarmonyDescription = (harmonyType: string): string => {
  switch (harmonyType) {
    case 'complementary':
      return 'uses colors opposite each other on the color wheel, creating high contrast and visual impact.';
    case 'analogous':
      return 'uses colors adjacent to each other on the color wheel, creating a cohesive and harmonious feel.';
    case 'triadic':
      return 'uses three colors evenly spaced around the color wheel, offering vibrant contrast while maintaining balance.';
    case 'tetradic':
      return 'uses four colors arranged in two complementary pairs, offering rich color possibilities with balanced contrast.';
    case 'splitComplementary':
      return 'uses a base color and two colors adjacent to its complement, creating visual interest with less tension than complementary schemes.';
    case 'monochromatic':
      return 'uses different shades, tones and tints of a single color, creating a cohesive look with visual depth.';
    case 'shades':
      return 'uses variations of a base color mixed with black, creating a sophisticated and cohesive palette.';
    case 'tints':
      return 'uses variations of a base color mixed with white, creating a light and airy palette with visual cohesion.';
    case 'compound':
      return 'combines analogous and complementary colors for a rich palette with both harmony and contrast.';
    default:
      return 'creates a balanced and visually appealing combination of colors based on color theory principles.';
  }
};

export default HarmonyDisplay;
