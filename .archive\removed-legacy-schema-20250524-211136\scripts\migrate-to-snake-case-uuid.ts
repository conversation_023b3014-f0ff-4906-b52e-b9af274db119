// migrate-to-snake-case-uuid.ts
// Migration script to convert legacy camelCase/txt-id schema to snake_case/uuid schema for local SQLite
// Run this script ONCE after deploying the new schema. It will:
//   - Map all legacy IDs to UUIDs and update foreign keys
//   - Convert all camelCase columns to snake_case
//   - Preserve legacy IDs in a new column (legacy_id) for reference

import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs';

// Update this path to your local DB location
const DB_PATH = path.resolve(
  process.env.LOCAL_DB_PATH ||
    process.env.DB_PATH ||
    process.env.HOME + '/Library/Application Support/chroma-sync/chroma-sync-data.db'
);

if (!fs.existsSync(DB_PATH)) {
  throw new Error(`Database file not found: ${DB_PATH}`);
}

console.log(`[MIGRATION] Connecting to ${DB_PATH}`);
const db = new Database(DB_PATH);

db.pragma('foreign_keys=OFF');
db.transaction(() => {
  // 1. Add legacy_id columns where needed
  db.exec(`ALTER TABLE colors ADD COLUMN legacy_id TEXT`);
  db.exec(`ALTER TABLE selections ADD COLUMN legacy_id TEXT`);
  db.exec(`ALTER TABLE selection_colors ADD COLUMN legacy_id TEXT`);

  // 2. Map old IDs to new UUIDs
  const idMap = new Map();
  // Explicit types for rows
interface IdRow { id: string; }
interface SelectionColorRow { selection_id: string; color_id: string; }

// Update colors PKs to UUIDs
for (const row of db.prepare('SELECT id FROM colors').all() as IdRow[]) {
    const uuid = uuidv4();
    idMap.set(row.id, uuid);
    db.prepare('UPDATE colors SET legacy_id = id, id = ? WHERE id = ?').run(uuid, row.id);
}
// Update selections PKs to UUIDs
for (const row of db.prepare('SELECT id FROM selections').all() as IdRow[]) {
    const uuid = uuidv4();
    idMap.set(row.id, uuid);
    db.prepare('UPDATE selections SET legacy_id = id, id = ? WHERE id = ?').run(uuid, row.id);
}
// Update FKs in selection_colors
for (const row of db.prepare('SELECT selection_id, color_id FROM selection_colors').all() as SelectionColorRow[]) {
    const newSelId = idMap.get(row.selection_id) || row.selection_id;
    const newColId = idMap.get(row.color_id) || row.color_id;
    db.prepare('UPDATE selection_colors SET selection_id = ?, color_id = ? WHERE selection_id = ? AND color_id = ?').run(newSelId, newColId, row.selection_id, row.color_id);
}

  // --- Safe column rename helper ---
  function safeRenameColumn(table: string, from: string, to: string) {
    const cols = db.prepare(`PRAGMA table_info(${table})`).all();
    if (!cols.some((col: any) => col.name === to)) {
      try {
        db.exec(`ALTER TABLE ${table} RENAME COLUMN ${from} TO ${to}`);
        console.log(`[MIGRATION] Renamed ${table}.${from} -> ${to}`);
      } catch (err: any) {
        if (String(err.message).includes('no such column')) {
          // Column does not exist, ignore
        } else {
          throw err;
        }
      }
    } else {
      // Target column already exists, skip
    }
  }

  // 4. Convert camelCase columns to snake_case (rename and copy)
  // Example for colors
  safeRenameColumn('colors', 'createdAt', 'created_at');
  safeRenameColumn('colors', 'updatedAt', 'updated_at');
  safeRenameColumn('colors', 'createdBy', 'created_by');
  safeRenameColumn('colors', 'updatedBy', 'updated_by');
  safeRenameColumn('colors', 'isLibrary', 'is_library');

  safeRenameColumn('selections', 'createdAt', 'created_at');
  safeRenameColumn('selections', 'updatedAt', 'updated_at');
  safeRenameColumn('selections', 'createdBy', 'created_by');
  safeRenameColumn('selections', 'updatedBy', 'updated_by');
  safeRenameColumn('selections', 'productId', 'product_id');

  safeRenameColumn('selection_colors', 'createdAt', 'created_at');
  safeRenameColumn('selection_colors', 'createdBy', 'created_by');
  safeRenameColumn('selection_colors', 'updatedAt', 'updated_at');
  safeRenameColumn('selection_colors', 'updatedBy', 'updated_by');
  safeRenameColumn('selection_colors', 'selectionId', 'selection_id');
  safeRenameColumn('selection_colors', 'colorId', 'color_id');
  safeRenameColumn('selection_colors', 'addedAt', 'added_at');

  // 5. Repeat for other tables as needed (datasheets, etc.)
  safeRenameColumn('datasheets', 'createdAt', 'created_at');
  safeRenameColumn('datasheets', 'updatedAt', 'updated_at');
  safeRenameColumn('datasheets', 'createdBy', 'created_by');
  safeRenameColumn('datasheets', 'updatedBy', 'updated_by');
  safeRenameColumn('datasheets', 'selectionId', 'selection_id');
  safeRenameColumn('datasheets', 'fileType', 'file_type');
  safeRenameColumn('datasheets', 'dateAdded', 'date_added');

  // 6. Done!
  console.log('[MIGRATION] Migration completed successfully.');
})();
db.pragma('foreign_keys=ON');
db.close();
