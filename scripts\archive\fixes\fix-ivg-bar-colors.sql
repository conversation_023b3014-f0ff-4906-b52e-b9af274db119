-- Fix IVG BAR by removing colors that belong to other products

-- Step 1: Show current state
SELECT 'BEFORE FIX - IVG BAR color count:' as status, COUNT(*) as count
FROM product_colors pc 
JOIN products p ON pc.product_id = p.id 
WHERE p.name = 'IVG BAR';

-- Step 2: Count colors that should be removed (belong to other products)
SELECT 'Colors to remove from IVG BAR:' as status, COUNT(*) as count
FROM product_colors pc 
JOIN products p ON pc.product_id = p.id 
JOIN colors c ON pc.color_id = c.id
WHERE p.name = 'IVG BAR' 
  AND c.properties LIKE '%"product":%'
  AND c.properties NOT LIKE '%"product":"IVG BAR"%';

-- Step 3: Remove the incorrect associations
DELETE FROM product_colors 
WHERE rowid IN (
    SELECT pc.rowid
    FROM product_colors pc 
    JOIN products p ON pc.product_id = p.id 
    JOIN colors c ON pc.color_id = c.id
    WHERE p.name = 'IVG BAR' 
      AND c.properties LIKE '%"product":%'
      AND c.properties NOT LIKE '%"product":"IVG BAR"%'
);

-- Step 4: Show result
SELECT 'AFTER FIX - IVG BAR color count:' as status, COUNT(*) as count
FROM product_colors pc 
JOIN products p ON pc.product_id = p.id 
WHERE p.name = 'IVG BAR';

-- Step 5: Check a few other products too
SELECT 'ALL PRODUCT COUNTS AFTER FIX:' as status;
SELECT 
    p.name as product_name,
    COUNT(*) as color_count
FROM product_colors pc 
JOIN products p ON pc.product_id = p.id 
GROUP BY p.id, p.name
ORDER BY color_count DESC;