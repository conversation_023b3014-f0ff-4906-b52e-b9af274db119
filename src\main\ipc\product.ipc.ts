/**
 * @file product.ipc.ts
 * @description IPC handlers for product operations using the optimized schema
 */

import { ipcMain } from 'electron';
import Database from 'better-sqlite3';
import { ProductService } from '../db/services/product.service';
import { ColorService } from '../db/services/color.service';
import { getCurrentOrganizationId } from './organization.ipc';
import { standardizeHex } from '../../shared/utils/color';

export function registerProductHandlers(
  _db: Database.Database,
  productService: ProductService,
  colorService: ColorService
): void {
  console.log('[ProductIPC] Registering product IPC handlers...');

  // Get all products
  ipcMain.removeHandler('product:getAll');
  ipcMain.handle('product:getAll', async () => {
    try {
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      const products = productService.getAll(organizationId);
      return products;
    } catch (error) {
      console.error('[ProductIPC] Error getting all products:', error);
      throw error;
    }
  });

  // Get product by ID
  ipcMain.removeHandler('product:getById');
  ipcMain.handle('product:getById', async (_, id: string) => {
    try {
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      const product = productService.getById(id, organizationId);
      return product;
    } catch (error) {
      console.error(`[ProductIPC] Error getting product ${id}:`, error);
      throw error;
    }
  });

  // Get product with colors
  ipcMain.removeHandler('product:getWithColors');
  ipcMain.handle('product:getWithColors', async (_, id: string) => {
    try {
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      const product = productService.getProductWithColors(id, organizationId);
      return product;
    } catch (error) {
      console.error(`[ProductIPC] Error getting product with colors ${id}:`, error);
      throw error;
    }
  });

  // Get all products with colors
  ipcMain.removeHandler('product:getAllWithColors');
  ipcMain.handle('product:getAllWithColors', async () => {
    try {
      console.log('[ProductIPC] Getting all products with colors...');
      const organizationId = getCurrentOrganizationId();
      console.log('[ProductIPC] Current organization ID from getCurrentOrganizationId():', organizationId);
      
      if (!organizationId) {
        console.error('[ProductIPC] No organization selected - this is the problem!');
        throw new Error('No organization selected');
      }
      const products = productService.getAllProductsWithColors(organizationId);
      console.log('[ProductIPC] Found products:', JSON.stringify(products, null, 2));
      return products;
    } catch (error) {
      console.error('[ProductIPC] Error getting all products with colors:', error);
      throw error;
    }
  });

  // Create product
  ipcMain.removeHandler('product:create');
  ipcMain.handle('product:create', async (_, name: string, metadata?: any) => {
    try {
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      const product = productService.create({ name, metadata }, organizationId);
      return product;
    } catch (error) {
      console.error('[ProductIPC] Error creating product:', error);
      throw error;
    }
  });

  // Create product (legacy handler name)
  ipcMain.removeHandler('product:add');
  ipcMain.handle('product:add', async (_, productData: { name: string, metadata?: any }) => {
    try {
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      const product = productService.create({ name: productData.name, metadata: productData.metadata }, organizationId);
      return product;
    } catch (error) {
      console.error('[ProductIPC] Error adding product:', error);
      throw error;
    }
  });

  // Update product
  ipcMain.removeHandler('product:update');
  ipcMain.handle('product:update', async (_, id: string, updates: any) => {
    try {
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      const product = productService.update(id, updates, organizationId);
      return product;
    } catch (error) {
      console.error(`[ProductIPC] Error updating product ${id}:`, error);
      throw error;
    }
  });

  // Delete product
  ipcMain.removeHandler('product:delete');
  ipcMain.handle('product:delete', async (_, id: string) => {
    try {
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      const success = productService.delete(id, organizationId);
      return success;
    } catch (error) {
      console.error(`[ProductIPC] Error deleting product ${id}:`, error);
      throw error;
    }
  });

  // Add color to product
  ipcMain.removeHandler('product:addColor');
  ipcMain.handle('product:addColor', async (_, productId: string, colorId: string) => {
    try {
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      const success = productService.addColor(productId, colorId, organizationId);
      return success;
    } catch (error) {
      console.error(`[ProductIPC] Error adding color to product ${productId}:`, error);
      throw error;
    }
  });

  // Remove color from product
  ipcMain.removeHandler('product:removeColor');
  ipcMain.handle('product:removeColor', async (_, productId: string, colorId: string) => {
    try {
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      const success = productService.removeColor(productId, colorId, organizationId);
      return success;
    } catch (error) {
      console.error(`[ProductIPC] Error removing color from product ${productId}:`, error);
      throw error;
    }
  });

  // Get colors for product
  ipcMain.removeHandler('product:getColors');
  ipcMain.handle('product:getColors', async (_, productId: string) => {
    try {
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      const colors = productService.getProductColors(productId, organizationId);
      return colors;
    } catch (error) {
      console.error(`[ProductIPC] Error getting colors for product ${productId}:`, error);
      throw error;
    }
  });

  // Add library color to product (creates a new color entry)
  ipcMain.removeHandler('product:addLibraryColor');
  ipcMain.handle('product:addLibraryColor', async (_, productId: string, libraryColor: any, customName?: string) => {
    try {
      console.log('[ProductIPC] Adding library color to product:', { productId, libraryColor, customName });
      
      // Create a new color entry based on the library color
      const newColor = {
        product: '', // Will be set by association
        name: customName || libraryColor.name || libraryColor.code,
        code: libraryColor.code,
        hex: standardizeHex(libraryColor.hex), // Ensure hex is 6-digit format
        cmyk: libraryColor.cmyk,
        notes: `Added from ${libraryColor.isLibrary ? 'library' : 'saved colors'}`,
        gradient: libraryColor.gradient,
        isLibrary: false // This will be a user color
      };
      
      // Create the color
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      const createdColor = colorService.add(newColor, organizationId);
      
      if (createdColor) {
        // Associate with product
        const success = productService.addColorToProduct(productId, createdColor.id, organizationId);
        if (success) {
          return createdColor;
        } else {
          // If association failed, delete the color
          colorService.delete(createdColor.id, organizationId);
          throw new Error('Failed to associate color with product');
        }
      }
      
      throw new Error('Failed to create color');
    } catch (error) {
      console.error(`[ProductIPC] Error adding library color to product ${productId}:`, error);
      throw error;
    }
  });

  console.log('[ProductIPC] Product IPC handlers registered.');
}