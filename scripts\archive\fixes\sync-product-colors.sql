-- Sync product-color relationships from Supabase data
-- This script should be run after products have been synced

-- First, let's verify we have products
SELECT COUNT(*) as product_count FROM products WHERE organization_id = (SELECT id FROM organizations WHERE external_id = '4047153f-7be8-490b-9cb2-a1e3ed04b92b');

-- Get a more efficient dump of all product-color relationships from Supabase
-- and insert them into the local database

-- Clear existing product-color relationships for these products to avoid duplicates
DELETE FROM product_colors 
WHERE product_id IN (
    SELECT id FROM products 
    WHERE organization_id = (SELECT id FROM organizations WHERE external_id = '4047153f-7be8-490b-9cb2-a1e3ed04b92b')
);

-- Now we need to run individual queries to get all the product-color mappings
-- Since we can't directly query Supabase from SQLite, you'll need to run the 
-- following Supabase query to get all mappings:

/*
Run this in Supabase SQL Editor to get all product-color mappings:

SELECT 
  'INSERT INTO product_colors (product_id, color_id, display_order) ' ||
  'SELECT p.id, c.id, ' || pc.display_order || 
  ' FROM products p, colors c ' ||
  'WHERE p.external_id = ''' || p.external_id || ''' ' ||
  'AND c.external_id = ''' || c.external_id || ''';' as insert_sql
FROM product_colors pc
JOIN products p ON pc.product_id = p.id
JOIN colors c ON pc.color_id = c.id
WHERE p.organization_id = '4047153f-7be8-490b-9cb2-a1e3ed04b92b'
ORDER BY p.name, pc.display_order;

Then copy the results and run them in this SQLite database.
*/

-- For now, let's verify the structure is ready
SELECT 'Ready to insert product-color relationships' as status;

-- After running the inserts, verify with:
-- SELECT p.name, COUNT(pc.color_id) as color_count 
-- FROM products p 
-- LEFT JOIN product_colors pc ON p.id = pc.product_id 
-- WHERE p.organization_id = (SELECT id FROM organizations WHERE external_id = '4047153f-7be8-490b-9cb2-a1e3ed04b92b')
-- GROUP BY p.name 
-- ORDER BY p.name;
