/**
 * @file index.ts
 * @description Main sync system exports
 * 
 * This module provides the complete sync system interface, making it easy
 * to import and use all sync functionality from a single entry point.
 */

// ============================================================================
// CORE SYSTEM
// ============================================================================

// Import for internal use
import {
  SyncEngine,
  SyncQueueManager,
  SyncConfigManager,
  getSyncConfig,
  resetSyncConfig
} from './core';

// Re-export for external use
export {
  SyncEngine,
  SyncQueueManager,
  SyncConfigManager,
  getSyncConfig,
  resetSyncConfig
} from './core';

// All types and interfaces
export * from './core/sync-types';

// Default configurations
export {
  DEFAULT_CONFIG,
  DEFAULT_BATCH_SIZES,
  DEFAULT_TIMEOUTS,
  DEFAULT_RETRY
} from './core/sync-config';

// ============================================================================
// SYNC STRATEGIES
// ============================================================================

// Import for internal use
import {
  ColorSyncStrategy,
  ProductSyncStrategy,
  OrganizationSyncStrategy,
  ConflictResolver
} from './strategies';

// Re-export for external use
export {
  ColorSyncStrategy,
  ProductSyncStrategy,
  OrganizationSyncStrategy,
  ConflictResolver,
  type SyncConflict,
  type ConflictResolution,
  type ConflictType
} from './strategies';

// ============================================================================
// MONITORING & ANALYTICS
// ============================================================================

// Import for internal use
import {
  SyncMonitor,
  ErrorRecoveryManager,
  SyncAnalyticsManager
} from './monitors';

// Re-export for external use
export {
  SyncMonitor,
  ErrorRecoveryManager,
  SyncAnalyticsManager
} from './monitors';

// ============================================================================
// UTILITIES
// ============================================================================

// Import for internal use
import {
  BatchProcessor,
  createBatchProcessor,
  processSingleBatch,
  NetworkMonitor,
  createNetworkMonitor,
  checkNetworkConnectivity,
  getNetworkQuality,
  CircuitBreaker,
  createCircuitBreaker,
  createSyncCircuitBreaker
} from './utils';

// Re-export for external use
export {
  BatchProcessor,
  createBatchProcessor,
  processSingleBatch,
  NetworkMonitor,
  createNetworkMonitor,
  checkNetworkConnectivity,
  getNetworkQuality,
  CircuitBreaker,
  createCircuitBreaker,
  createSyncCircuitBreaker,
  type CircuitBreakerState
} from './utils';

// ============================================================================
// FACTORY FUNCTIONS
// ============================================================================

/**
 * Create a complete sync system with all components
 */
export function createSyncSystem(config?: Partial<import('./core/sync-types').SyncConfig>) {
  // Initialize configuration
  const configManager = getSyncConfig(config);
  
  // Create core engine
  const syncEngine = new SyncEngine();
  
  // Create monitoring components
  const syncMonitor = new SyncMonitor();
  const errorRecovery = new ErrorRecoveryManager();
  const analytics = new SyncAnalyticsManager(configManager.getConfig().enableAnalytics);
  
  // Create utilities
  const networkMonitor = createNetworkMonitor();
  const circuitBreaker = createSyncCircuitBreaker();
  const batchProcessor = createBatchProcessor();
  
  return {
    // Core components
    engine: syncEngine,
    config: configManager,
    
    // Monitoring
    monitor: syncMonitor,
    errorRecovery,
    analytics,
    
    // Utilities
    networkMonitor,
    circuitBreaker,
    batchProcessor,
    
    // Convenience methods
    async initialize(userId: string, organizationId: string) {
      // Register strategies
      syncEngine.registerStrategy(new ColorSyncStrategy(userId, organizationId));
      syncEngine.registerStrategy(new ProductSyncStrategy(userId, organizationId));
      syncEngine.registerStrategy(new OrganizationSyncStrategy(userId, organizationId));
      
      // Initialize engine
      await syncEngine.initialize(userId, organizationId);
      
      console.log('[SyncSystem] Complete sync system initialized');
    },
    
    async cleanup() {
      await syncEngine.cleanup();
      syncMonitor.destroy();
      errorRecovery.destroy();
      analytics.destroy();
      networkMonitor.destroy();
      
      console.log('[SyncSystem] Complete sync system cleaned up');
    }
  };
}

/**
 * Create a lightweight sync system for testing
 */
export function createTestSyncSystem() {
  const syncEngine = new SyncEngine();
  const configManager = getSyncConfig();
  
  return {
    engine: syncEngine,
    config: configManager,
    
    async initialize(userId: string, organizationId: string) {
      // Register minimal strategies for testing
      syncEngine.registerStrategy(new ColorSyncStrategy(userId, organizationId));
      await syncEngine.initialize(userId, organizationId);
    },
    
    async cleanup() {
      await syncEngine.cleanup();
    }
  };
}
