#!/usr/bin/env node

/**
 * Transform ChromaSync V2 archive data to current ChromaSync format
 * 
 * Archive format:
 * {
 *   id: string,
 *   name: string (product name),
 *   flavor: string (color name),
 *   pantoneCode: string,
 *   colorHex: string,
 *   cmyk: string,
 *   notes: string,
 *   timestamp: string
 * }
 * 
 * Target format (ColorEntry):
 * {
 *   id: string (UUID),
 *   product: string,
 *   name: string (color name),
 *   code: string (pantone code),
 *   hex: string,
 *   cmyk: string,
 *   notes?: string,
 *   gradient?: GradientInfo,
 *   isLibrary?: boolean,
 *   createdAt: string,
 *   updatedAt: string
 * }
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Generate a UUID v4
function generateUUID() {
  return ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g, c =>
    (c ^ crypto.randomBytes(1)[0] & 15 >> c / 4).toString(16)
  );
}

// Normalize CMYK values
function normalizeCMYK(cmykString) {
  if (!cmykString || cmykString === '') {
    return '0,0,0,0';
  }
  
  // Handle various CMYK formats
  // "C:85 M:81 Y:0 K:0" -> "85,81,0,0"
  // "C70, M95, Y0, K0" -> "70,95,0,0"
  // "0,17,80,0" -> "0,17,80,0" (already correct)
  
  // Remove all non-numeric characters except commas
  let normalized = cmykString.replace(/[^0-9,]/g, ' ').trim();
  
  // Split by spaces or commas and filter empty strings
  let values = normalized.split(/[\s,]+/).filter(v => v !== '');
  
  // Ensure we have exactly 4 values
  if (values.length === 4) {
    return values.join(',');
  } else if (values.length > 4) {
    // Take first 4 values
    return values.slice(0, 4).join(',');
  } else {
    // Pad with zeros if less than 4 values
    while (values.length < 4) {
      values.push('0');
    }
    return values.join(',');
  }
}

// Validate and normalize hex color
function normalizeHex(hexString) {
  if (!hexString) return '#000000';
  
  // Remove any whitespace
  let hex = hexString.trim();
  
  // Add # if missing
  if (!hex.startsWith('#')) {
    hex = '#' + hex;
  }
  
  // Validate hex format
  if (!/^#[0-9A-Fa-f]{6}$/.test(hex)) {
    console.warn(`Invalid hex color: ${hexString}, using default #CCCCCC`);
    return '#CCCCCC';
  }
  
  return hex.toUpperCase();
}

// Transform a single color entry
function transformColorEntry(archiveEntry) {
  const now = new Date().toISOString();
  
  return {
    id: generateUUID(),
    product: archiveEntry.name || 'Unknown Product',
    name: archiveEntry.flavor || archiveEntry.pantoneCode || 'Unnamed Color',
    code: archiveEntry.pantoneCode || archiveEntry.id || generateUUID().substring(0, 8),
    hex: normalizeHex(archiveEntry.colorHex),
    cmyk: normalizeCMYK(archiveEntry.cmyk),
    notes: archiveEntry.notes || '',
    isLibrary: false, // All imported colors are user colors
    createdAt: archiveEntry.timestamp || now,
    updatedAt: archiveEntry.timestamp || now
  };
}

// Main transformation function
async function transformArchiveData(inputPath, outputPath) {
  try {
    // Read the archive data
    console.log(`Reading archive data from: ${inputPath}`);
    const rawData = fs.readFileSync(inputPath, 'utf-8');
    const archiveData = JSON.parse(rawData);
    
    console.log(`Found ${archiveData.length} color entries to transform`);
    
    // Transform each entry
    const transformedData = [];
    const stats = {
      total: archiveData.length,
      successful: 0,
      failed: 0,
      duplicates: new Set(),
      products: new Set()
    };
    
    for (const entry of archiveData) {
      try {
        const transformed = transformColorEntry(entry);
        transformedData.push(transformed);
        stats.successful++;
        stats.products.add(transformed.product);
        
        // Check for duplicate codes
        const existingIndex = transformedData.findIndex(
          (e, i) => i < transformedData.length - 1 && 
                    e.product === transformed.product && 
                    e.code === transformed.code
        );
        
        if (existingIndex !== -1) {
          stats.duplicates.add(`${transformed.product}-${transformed.code}`);
          // Append a unique suffix to the code
          transformed.code = `${transformed.code}-${generateUUID().substring(0, 8).toUpperCase()}`;
        }
      } catch (error) {
        console.error(`Failed to transform entry:`, entry, error);
        stats.failed++;
      }
    }
    
    // Sort by product and then by name
    transformedData.sort((a, b) => {
      if (a.product !== b.product) {
        return a.product.localeCompare(b.product);
      }
      return a.name.localeCompare(b.name);
    });
    
    // Write the transformed data
    console.log(`Writing transformed data to: ${outputPath}`);
    fs.writeFileSync(outputPath, JSON.stringify(transformedData, null, 2));
    
    // Print statistics
    console.log('\n=== Transformation Statistics ===');
    console.log(`Total entries: ${stats.total}`);
    console.log(`Successfully transformed: ${stats.successful}`);
    console.log(`Failed: ${stats.failed}`);
    console.log(`Unique products: ${stats.products.size}`);
    console.log(`Duplicate codes resolved: ${stats.duplicates.size}`);
    console.log('\nProducts found:');
    Array.from(stats.products).sort().forEach(p => console.log(`  - ${p}`));
    
    // Create a summary report
    const summaryPath = outputPath.replace('.json', '-summary.txt');
    const summary = `ChromaSync Data Transformation Summary
=====================================
Date: ${new Date().toLocaleString()}
Source: ${inputPath}
Output: ${outputPath}

Statistics:
-----------
Total entries: ${stats.total}
Successfully transformed: ${stats.successful}
Failed: ${stats.failed}
Unique products: ${stats.products.size}
Duplicate codes resolved: ${stats.duplicates.size}

Products:
---------
${Array.from(stats.products).sort().map(p => `- ${p}`).join('\n')}

Duplicates Resolved:
-------------------
${Array.from(stats.duplicates).sort().map(d => `- ${d}`).join('\n') || 'None'}
`;
    
    fs.writeFileSync(summaryPath, summary);
    console.log(`\nSummary written to: ${summaryPath}`);
    
    return transformedData;
  } catch (error) {
    console.error('Error transforming data:', error);
    throw error;
  }
}

// CLI usage
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length < 1) {
    console.log('Usage: node transform-archive-data.js <input-file> [output-file]');
    console.log('Example: node transform-archive-data.js load.data.json chromasync-import.json');
    process.exit(1);
  }
  
  const inputFile = args[0];
  const outputFile = args[1] || 'chromasync-import-ready.json';
  
  const inputPath = path.resolve(inputFile);
  const outputPath = path.resolve(outputFile);
  
  if (!fs.existsSync(inputPath)) {
    console.error(`Input file not found: ${inputPath}`);
    process.exit(1);
  }
  
  transformArchiveData(inputPath, outputPath)
    .then(() => {
      console.log('\nTransformation completed successfully!');
      console.log(`You can now import ${outputPath} into ChromaSync`);
    })
    .catch(error => {
      console.error('\nTransformation failed:', error);
      process.exit(1);
    });
}

module.exports = { transformArchiveData, transformColorEntry };
