/**
 * @file browser-opener.ts
 * @description Utility functions for opening URLs in the default browser
 */

import { BrowserWindow, shell } from 'electron';
import * as childProcess from 'child_process';

/**
 * Checks if a URL is a SharePoint or OneDrive URL
 * @param url The URL to check
 * @returns True if the URL is a SharePoint or OneDrive URL
 */
export function isSharePointOrOneDriveUrl(url: string): boolean {
  return url.includes('sharepoint.com') || url.includes('onedrive.com');
}

/**
 * Opens a URL in the default browser using a more reliable method for SharePoint URLs
 * @param url The URL to open
 * @returns Promise resolving to true if successful, false otherwise
 */
export async function openSharePointUrl(url: string): Promise<boolean> {
  return new Promise<boolean>((resolve) => {
    try {
      console.log(`Opening SharePoint URL: ${url}`);

      // Get the current focused window to restore focus later
      const focusedWindow = BrowserWindow.getFocusedWindow();
      const windowId = focusedWindow?.id;

      // Use the platform-specific approach for better reliability
      const platform = process.platform;
      let command: string;
      let args: string[] = [];

      if (platform === 'win32') {
        // Windows - use start command which is more reliable for SharePoint URLs
        command = 'cmd.exe';
        args = ['/c', 'start', '', url];
      } else if (platform === 'darwin') {
        // macOS
        command = 'open';
        args = [url];
      } else {
        // Linux and others - fall back to xdg-open
        command = 'xdg-open';
        args = [url];
      }

      // Execute the command
      const childProc = childProcess.spawn(command, args, {
        detached: true,
        stdio: 'ignore'
      });

      childProc.on('error', (err) => {
        console.error(`Error opening SharePoint URL with ${command}:`, err);

        // Fall back to shell.openExternal if the command fails
        shell.openExternal(url, { activate: true })
          .then(() => {
            console.log('Opened SharePoint URL with shell.openExternal as fallback');
            resolve(true);
          })
          .catch((shellError) => {
            console.error('Error opening SharePoint URL with fallback method:', shellError);
            resolve(false);
          });
      });

      // Unref the process to allow the Node.js process to exit
      childProc.unref();

      // Log success
      console.log(`Launched browser for SharePoint URL using ${command}`);

      // Restore focus to the app window after a delay
      setTimeout(() => {
        try {
          if (windowId !== undefined) {
            const win = BrowserWindow.fromId(windowId);
            if (win && !win.isDestroyed()) {
              if (win.isMinimized()) {
                win.restore();
              }
              win.focus();
              console.log('Restored focus to application window after opening SharePoint URL');
            }
          }
        } catch (focusError) {
          console.error('Error restoring focus after opening SharePoint URL:', focusError);
        }
      }, 2000); // 2 second delay for SharePoint URLs

      // Assume success if no error occurs
      resolve(true);
    } catch (error) {
      console.error('Error opening SharePoint URL:', error);

      // Try the fallback method
      try {
        shell.openExternal(url, { activate: true })
          .then(() => {
            console.log('Opened SharePoint URL with shell.openExternal after error');
            resolve(true);
          })
          .catch((shellError) => {
            console.error('Error opening SharePoint URL with all methods:', shellError);
            resolve(false);
          });
      } catch (finalError) {
        console.error('Final error opening SharePoint URL:', finalError);
        resolve(false);
      }
    }
  });
}

/**
 * Opens a URL in the default browser using platform-specific commands
 * @param url The URL to open
 * @returns Promise resolving to true if successful, false otherwise
 */
export async function openUrlInDefaultBrowser(url: string): Promise<boolean> {
  // For SharePoint and OneDrive URLs, use the specialized method
  if (isSharePointOrOneDriveUrl(url)) {
    return openSharePointUrl(url);
  }

  return new Promise((resolve) => {
    try {
      const platform = process.platform;
      let command: string;
      let args: string[] = [];

      // Determine the command based on the platform
      if (platform === 'win32') {
        // Windows
        command = 'explorer.exe';
        args = [url];
      } else if (platform === 'darwin') {
        // macOS
        command = 'open';
        args = [url];
      } else if (platform === 'linux') {
        // Linux
        command = 'xdg-open';
        args = [url];
      } else {
        console.error(`Unsupported platform: ${platform}`);
        resolve(false);
        return;
      }

      // Get the current focused window to restore focus later
      const focusedWindow = BrowserWindow.getFocusedWindow();
      const windowId = focusedWindow?.id;

      // Execute the command
      const childProc = childProcess.spawn(command, args, {
        detached: true,
        stdio: 'ignore'
      });

      childProc.on('error', (err) => {
        console.error(`Error opening URL with ${command}:`, err);
        resolve(false);
      });

      // Unref the process to allow the Node.js process to exit
      childProc.unref();

      // Restore focus to the app window after a delay
      setTimeout(() => {
        if (windowId !== undefined) {
          const win = BrowserWindow.fromId(windowId);
          if (win && !win.isDestroyed()) {
            if (win.isMinimized()) {
              win.restore();
            }
            win.focus();
            console.log('Restored focus to application window');
          }
        }
      }, 1500); // 1.5 second delay

      // Assume success if no error occurs
      resolve(true);
    } catch (error) {
      console.error('Error opening URL in default browser:', error);
      resolve(false);
    }
  });
}
