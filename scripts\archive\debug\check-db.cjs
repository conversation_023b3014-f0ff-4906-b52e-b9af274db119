// Quick debug script to check which database is being used
const Database = require('better-sqlite3');
const path = require('path');
const os = require('os');

const userDataPath = path.join(os.homedir(), 'Library/Application Support/ChromaSync');

console.log('ChromaSync Database Check');
console.log('========================\n');

// Check chromasync.db
try {
  const newDb = new Database(path.join(userDataPath, 'chromasync.db'), { readonly: true });
  const tables = newDb.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
  console.log('chromasync.db tables:', tables.map(t => t.name).join(', ') || 'NONE');
  
  if (tables.length > 0) {
    const colorCount = newDb.prepare("SELECT COUNT(*) as count FROM colors").get();
    console.log('chromasync.db colors:', colorCount ? colorCount.count : 0);
  }
  newDb.close();
} catch (e) {
  console.log('chromasync.db error:', e.message);
}

console.log('');

// Check database.sqlite
try {
  const oldDb = new Database(path.join(userDataPath, 'database.sqlite'), { readonly: true });
  const colorCount = oldDb.prepare("SELECT COUNT(*) as count FROM colors").get();
  console.log('database.sqlite colors:', colorCount.count);
  oldDb.close();
} catch (e) {
  console.log('database.sqlite error:', e.message);
}

console.log('\nIf you see colors in the app and chromasync.db is empty,');
console.log('then the data is likely coming from Supabase!');
