/**
 * @file index.ts
 * @description Main process entry point for Electron application
 */

import dotenv from 'dotenv';
// Load environment variables from .env file
const dotenvResult = dotenv.config();
console.log('[DEBUG] process.cwd():', process.cwd());
console.log('[DEBUG] dotenv.config() result:', dotenvResult);

import { app, BrowserWindow, ipcMain, dialog, Menu, nativeTheme } from 'electron';
import path from 'path';
import fs from 'fs';
import { initDatabase, getDatabase } from './db/database';
import { registerIpcHandlers } from './ipc/colorHandlers';
import { registerColorHandlers } from './ipc/color.ipc';
import { registerDatasheetHandlers } from './ipc/datasheet.ipc';
import { DatasheetService } from './db/services/datasheet.service';
import { ColorService } from './db/services/color.service';
import { ProductService } from './db/services/product.service';
import { registerLicenseHandlers } from './ipc/license-handlers';
import { createDebugWindow } from './windows/debug-window';
import { setupSharedFolderIPC } from './ipc/shared-folder-ipc';
import { SharedFolderManager } from './shared-folder';
import { setupAutoUpdater } from './auto-updater';
import { registerProductHandlers } from './ipc/product.ipc';
import { registerTestDataHandlers } from './ipc/test-data.ipc';
import { registerSyncHandlers } from './ipc/sync-handlers';
import { registerSampleDataHandlers } from './ipc/sample-data.ipc';
import { canRegisterHandler } from './utils/ipcRegistry';
import { ColorChannels } from '../shared/types/color.types';

// Ensure better-sqlite3 is properly configured for all platforms
// Create necessary directories for native modules
const setupNativeModulePaths = () => {
  try {
    // Create build directory in the app root
    const buildDir = path.join(process.cwd(), 'build');
    if (!fs.existsSync(buildDir)) {
      fs.mkdirSync(buildDir, { recursive: true });
      console.log(`[DB] Created build directory at: ${buildDir}`);
    }

    // Log the app paths for debugging
    console.log(`[DB] App paths:`);
    console.log(`- process.cwd(): ${process.cwd()}`);
    console.log(`- app.getAppPath(): ${app.getAppPath()}`);
    console.log(`- app.getPath('userData'): ${app.getPath('userData')}`);

    // Log the better-sqlite3 module location
    const possiblePaths = [
      path.join(process.cwd(), 'node_modules', 'better-sqlite3', 'build', 'Release', 'better_sqlite3.node'),
      path.join(app.getAppPath(), 'node_modules', 'better-sqlite3', 'build', 'Release', 'better_sqlite3.node')
    ];

    console.log('[DB] Checking better-sqlite3.node locations:');
    possiblePaths.forEach(p => {
      const exists = fs.existsSync(p);
      console.log(`- ${p} (exists: ${exists})`);
    });
  } catch (error) {
    console.error('[DB] Error setting up native module paths:', error);
  }
};

// Run the setup function
setupNativeModulePaths();

// Set app name
app.name = 'ChromaSync';

// Determine if we're in development mode
const isDevelopment = process.env.NODE_ENV === 'development';

// Keep a global reference of the window object to avoid garbage collection
let mainWindow: BrowserWindow | null = null;

// Zoom constants
const MIN_ZOOM = 0.5;
const MAX_ZOOM = 2.0;
const DEFAULT_ZOOM = 1.0;
const ZOOM_INCREMENT = 0.1;

// Register window control handlers immediately to ensure they're available
// IMPORTANT: These should be registered before database initialization
function setupWindowControlHandlers() {
  console.log('Registering window control handlers...');

  // Register window control handlers
  ipcMain.removeHandler('window:minimize');
  ipcMain.removeHandler('window:maximize');
  ipcMain.removeHandler('window:unmaximize');
  ipcMain.removeHandler('window:close');
  ipcMain.removeHandler('window:isMaximized');

  ipcMain.on('window:minimize', () => {
    if (mainWindow) mainWindow.minimize();
  });

  ipcMain.on('window:maximize', () => {
    if (mainWindow && !mainWindow.isMaximized()) mainWindow.maximize();
  });

  ipcMain.on('window:unmaximize', () => {
    if (mainWindow && mainWindow.isMaximized()) mainWindow.unmaximize();
  });

  ipcMain.on('window:close', () => {
    if (mainWindow) mainWindow.close();
  });

  ipcMain.handle('window:isMaximized', () => {
    return mainWindow ? mainWindow.isMaximized() : false;
  });

  // Add DevTools toggle handler
  ipcMain.handle('window:toggleDevTools', () => {
    if (mainWindow) {
      if (mainWindow.webContents.isDevToolsOpened()) {
        mainWindow.webContents.closeDevTools();
        return false;
      } else {
        mainWindow.webContents.openDevTools();
        return true;
      }
    }
    return false;
  });

  console.log('Window control handlers registered successfully');
}

// Register zoom handlers
function setupZoomHandlers() {
  console.log('Registering zoom handlers...');

  // Register zoom handlers
  ipcMain.removeHandler('zoom:in');
  ipcMain.removeHandler('zoom:out');
  ipcMain.removeHandler('zoom:reset');
  ipcMain.removeHandler('zoom:get-factor');
  ipcMain.removeHandler('app:get-zoom-factor');

  ipcMain.handle('zoom:in', () => {
    zoomIn();
    return mainWindow ? mainWindow.webContents.getZoomFactor() : DEFAULT_ZOOM;
  });

  ipcMain.handle('zoom:out', () => {
    zoomOut();
    return mainWindow ? mainWindow.webContents.getZoomFactor() : DEFAULT_ZOOM;
  });

  ipcMain.handle('zoom:reset', () => {
    resetZoom();
    return DEFAULT_ZOOM;
  });

  ipcMain.handle('zoom:get-factor', () => {
    return mainWindow ? mainWindow.webContents.getZoomFactor() : DEFAULT_ZOOM;
  });

  ipcMain.handle('app:get-zoom-factor', () => {
    return mainWindow ? mainWindow.webContents.getZoomFactor() : DEFAULT_ZOOM;
  });

  console.log('Zoom handlers registered successfully');
}

/**
 * Zoom in the main window
 */
function zoomIn() {
  if (!mainWindow) return;

  const currentZoom = mainWindow.webContents.getZoomFactor();
  if (currentZoom < MAX_ZOOM) {
    const newZoom = Math.min(currentZoom + ZOOM_INCREMENT, MAX_ZOOM);
    mainWindow.webContents.setZoomFactor(newZoom);
  }
}

/**
 * Zoom out the main window
 */
function zoomOut() {
  if (!mainWindow) return;

  const currentZoom = mainWindow.webContents.getZoomFactor();
  if (currentZoom > MIN_ZOOM) {
    const newZoom = Math.max(currentZoom - ZOOM_INCREMENT, MIN_ZOOM);
    mainWindow.webContents.setZoomFactor(newZoom);
  }
}

/**
 * Reset zoom to default level
 */
function resetZoom() {
  if (!mainWindow) return;
  mainWindow.webContents.setZoomFactor(DEFAULT_ZOOM);
}

/**
 * Create the main application window
 */
async function createWindow() {
  console.log('[TRACE] Entered createWindow');

  // Set up window control handlers
  setupWindowControlHandlers();
  console.log('[TRACE] Window control handlers set up');

  // Set up zoom handlers
  setupZoomHandlers();
  console.log('[TRACE] Zoom handlers set up');

  // Register product datasheet handler
  ipcMain.removeHandler('product:open-datasheet');
  ipcMain.handle('product:open-datasheet', async (_, product) => {
    console.log(`Opening product datasheet for: ${product}`);
    return { success: true, message: `Would open datasheet for ${product}` };
  });

  // Register initial setup handlers
  ipcMain.removeHandler('get-initial-config-status');
  ipcMain.handle('get-initial-config-status', async () => {
    return { configured: false };
  });

  // Handler for selecting shared folder
  ipcMain.removeHandler('select-shared-folder');
  ipcMain.handle('select-shared-folder', async () => {
    if (!mainWindow) return null;

    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openDirectory'],
      title: 'Select Shared Folder',
      buttonLabel: 'Select Folder'
    });

    if (result.canceled) {
      return null;
    }

    const selectedPath = result.filePaths[0];
    console.log(`Selected shared folder: ${selectedPath}`);
    return selectedPath;
  });

  // Handler for saving storage config
  ipcMain.removeHandler('save-storage-config');
  ipcMain.handle('save-storage-config', async (_, config) => {
    console.log(`Saving storage config: ${JSON.stringify(config)}`);
    return { success: true };
  });

  // Handler for selecting shared folder path
  ipcMain.removeHandler('setup:getSharedFolderPath');
  ipcMain.handle('setup:getSharedFolderPath', async () => {
      if (!mainWindow) return null;

      const result = await dialog.showOpenDialog(mainWindow, {
        properties: ['openDirectory'],
        title: 'Select Shared Folder',
        buttonLabel: 'Select Folder'
      });

      if (result.canceled) {
        return null;
      }

      const selectedPath = result.filePaths[0];
      console.log(`Selected shared folder: ${selectedPath}`);
      return selectedPath;
  });

  // Handler for letting the renderer signal setup is complete
  ipcMain.removeHandler('setup-complete');
  ipcMain.on('setup-complete', () => {
    console.log('[IPC] Received setup-complete signal from renderer.');
  });

  // Register renderer error tracking handler
  ipcMain.removeHandler('track-renderer-error');
  ipcMain.handle('track-renderer-error', async (_, errorInfo) => {
    console.error('Renderer error:', errorInfo);
    return { success: true };
  });

  // Register test handler for debugging
  ipcMain.removeHandler('test-ipc-available');
  ipcMain.handle('test-ipc-available', () => {
    return { success: true, message: 'IPC is working correctly' };
  });

  try {
    console.log('[TRACE] Creating main window...');

    // Create the browser window - frameless with no menu
    mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      frame: false, // Remove default window frame
      titleBarStyle: 'hidden', // Hide default title bar
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../preload/index.js'),
        sandbox: true, // Enable sandbox for renderer
        // Improve security with CSP
        webSecurity: true,
        // Restrict navigation
        allowRunningInsecureContent: false,
        // Disable experimental features
        experimentalFeatures: false
      },
      transparent: true, // Enable transparency
      backgroundColor: '#00000000', // Fully transparent background
      hasShadow: false, // Disable window shadow to prevent edge artifacts
      roundedCorners: true, // Enable rounded corners on Windows
      // Additional window settings for better corners
      autoHideMenuBar: true, // Hide menu bar
      // Additional fix for Windows 10/11 native rounded corners
      resizable: true,
      show: false, // Hide window initially to set up properly
    });

    // Windows-specific fix for rounded corners
    if (process.platform === 'win32') {
      try {
        // Use CSS-based rounded corners instead of native API
        console.log('[TRACE] Using CSS-based rounded corners for Windows');

        // Make sure opacity and transparency are set correctly
        mainWindow.setBackgroundColor('#00000000');
        mainWindow.setOpacity(1.0);

        // Note: We're not using the native Windows API methods as they're not consistently
        // available across all Electron versions and can cause compatibility issues
      } catch (error) {
        console.log('[TRACE] Windows corner customization setup error:', error);
      }
    }

    // Set up auto-updater with window reference
    setupAutoUpdater(mainWindow);

    // Show window after all setup is complete
    mainWindow.once('ready-to-show', () => {
      if (mainWindow) {
        mainWindow.show();
        console.log('[TRACE] Main window shown.');
      }
    });

    // Configure vibrancy on macOS if available (electron 8.0+)
    try {
      if (process.platform === 'darwin') {
        // Add vibrancy to the window (macOS only)
        mainWindow.setVibrancy('window');
        console.log('[TRACE] Vibrancy set for macOS.');
      }
    } catch (error) {
      console.log('[TRACE] Vibrancy not supported on this platform, continuing without it...');
    }

    // Remove the application menu
    Menu.setApplicationMenu(null);
    console.log('[TRACE] Application menu removed.');

    // Set up content security policy
    mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          'Content-Security-Policy': [
            "default-src 'self'",
            "script-src 'self'",
            "style-src 'self' 'unsafe-inline'",
            "img-src 'self' data:",
            "font-src 'self' data:",
            "connect-src 'self'",
            "worker-src 'self'"
          ].join('; ')
        }
      });
    });
    console.log('[TRACE] Content security policy set.');

    // Load the app
    if (isDevelopment) {
      // In development, load from the dev server
      // Try both ports in case one is busy
      try {
        await mainWindow.loadURL('http://localhost:5173');
        console.log('[TRACE] Loaded app from dev server on port 5173');
      } catch (error) {
        console.log('[TRACE] Port 5173 failed, trying port 5174');
        await mainWindow.loadURL('http://localhost:5174');
        console.log('[TRACE] Loaded app from dev server on port 5174');
      }

      // Open DevTools in development mode
      mainWindow.webContents.openDevTools();
      console.log('[TRACE] DevTools opened.');
    } else {
      // In production, load the built app
      const indexPath = path.join(__dirname, '../../out/renderer/index.html');
      console.log(`[TRACE] Loading app from: ${indexPath}`);
      await mainWindow.loadFile(indexPath);
      console.log('[TRACE] Loaded app from production build');
    }

    // Handle window close
    mainWindow.on('closed', () => {
      mainWindow = null;
      console.log('[TRACE] Main window closed.');
    });

    ipcMain.handle('window:set-dark-mode', (_, isDarkMode) => {
      if (!mainWindow) return false;

      nativeTheme.themeSource = isDarkMode ? 'dark' : 'light';

      // Optional: Notify renderer about the change if needed
      // mainWindow.webContents.send('theme-updated', isDarkMode);

      return nativeTheme.shouldUseDarkColors; // Return the actual state
    });
    console.log('[TRACE] Dark mode handler registered.');

    console.log('[TRACE] Auto-updater set up.');

    console.log('[TRACE] Main window created successfully');
    return mainWindow;
  } catch (error) {
    console.error('[TRACE] Error creating main window:', error);
    return null;
  }
}

// Initialize the database and enable custom IPC handlers
async function initializeAppServices(): Promise<void> {
  console.log('Initializing application services...');
  
  // Fallback color handlers when database is not available
  function registerFallbackColorHandlers() {
    if (canRegisterHandler(ColorChannels.GET_ALL)) {
      ipcMain.handle(ColorChannels.GET_ALL, async () => {
        console.log('[Fallback] Returning empty color list');
        return [];
      });
    }

    if (canRegisterHandler(ColorChannels.GET_BY_ID)) {
      ipcMain.handle(ColorChannels.GET_BY_ID, async () => {
        return undefined;
      });
    }

    if (canRegisterHandler(ColorChannels.ADD)) {
      ipcMain.handle(ColorChannels.ADD, async () => {
        throw new Error('Database not available');
      });
    }

    if (canRegisterHandler(ColorChannels.UPDATE)) {
      ipcMain.handle(ColorChannels.UPDATE, async () => {
        throw new Error('Database not available');
      });
    }

    if (canRegisterHandler(ColorChannels.DELETE)) {
      ipcMain.handle(ColorChannels.DELETE, async () => {
        throw new Error('Database not available');
      });
    }

    if (canRegisterHandler('product:getAllWithColors')) {
      ipcMain.handle('product:getAllWithColors', async () => {
        return [];
      });
    }

    // Register fallback product handlers
    if (canRegisterHandler('product:add')) {
      ipcMain.handle('product:add', async (_, productData: { name: string, metadata?: any }) => {
        console.log('[Fallback] Cannot add product - database not available');
        throw new Error('Database not available');
      });
    }

    if (canRegisterHandler('product:create')) {
      ipcMain.handle('product:create', async (_, name: string, metadata?: any) => {
        console.log('[Fallback] Cannot create product - database not available');
        throw new Error('Database not available');
      });
    }

    if (canRegisterHandler('product:getAll')) {
      ipcMain.handle('product:getAll', async () => {
        return [];
      });
    }

    console.log('[Fallback] Color and product handlers registered');
  }

  try {
    // Register sync handlers first (doesn't need database)
    registerSyncHandlers();
    console.log('[IPC] Sync handlers registered');
    
    // Register license handlers (doesn't need database initially)
    registerLicenseHandlers(null);
    console.log('[IPC] License handlers registered');
    
    // Initialize database
    const db = await initDatabase();
    
    // Check if database is null and handle gracefully
    if (!db) {
      console.error('[DB] Database is null, registering fallback handlers');
      
      // Register fallback color handlers
      registerFallbackColorHandlers();
      
      // Register sample data handlers as a workaround
      // registerSampleDataHandlers(); // Commented out - needs service instances
      console.log('[IPC] Sample data handlers registration skipped in fallback mode');
      
      // Continue with limited functionality
      return;
    }
    
    // Re-check database after potential migration
    const currentDb = getDatabase();
    if (!currentDb) {
      console.error('[DB] Database is still null after migration attempt, using fallback handlers');
      registerFallbackColorHandlers();
      // registerSampleDataHandlers(); // Commented out - needs service instances
      return;
    }
    
    // Register IPC handlers that need database
    // Note: Color handlers are registered separately below
    // registerIpcHandlers(db);
    
    // Create service instances
    const colorService = new ColorService(db);
    const productService = new ProductService(db, colorService);
    
    // Register color handlers with the service
    registerColorHandlers(colorService);
    console.log('[IPC] Color handlers registered');
    
    // Register datasheet handlers
    const dataService = new DatasheetService(db);
    registerDatasheetHandlers(dataService);
    
    // Register product handlers
    registerProductHandlers(db, productService, colorService);
    
    // Register test data handlers
    registerTestDataHandlers(productService, colorService);
    
    // Register sample data handlers
    registerSampleDataHandlers(productService, colorService);
    
    // Delay sync operations until window is created
    // These will be called after window creation in app.whenReady()
    
    // Setup shared folder manager and IPC
    const sharedFolderConfig = {
      basePath: app.getPath('userData'),
      folderName: 'shared_data'
    };
    const sharedFolderManager = new SharedFolderManager(sharedFolderConfig);
    setupSharedFolderIPC(sharedFolderManager);
    
    console.log('All IPC handlers registered successfully');
  } catch (error) {
    console.error('Error initializing application services:', error);
    throw error;
  }
}

// This method will be called when Electron has finished initialization
import { configStore } from './db/config.js';

app.whenReady().then(async () => {
  console.log('[TRACE] Electron app ready, starting initialization process');
  
  try {
    // First, initialize app services including schema migration
    await initializeAppServices();
    console.log('[TRACE] App services and database migration initialized successfully');
    
    // Create the main window
    console.log('App is ready, creating window...');
    mainWindow = await createWindow();
    
    // Mark first run as complete to avoid sync attempts
    configStore.set('isFirstRun', false);
    
    console.log('[TRACE] Database initialization and services setup complete');
  } catch (error) {
    console.error('[TRACE] Critical error during app initialization:', error);
    // Create window anyway to avoid silent failure
    mainWindow = await createWindow();
  }

  // Create debug window if in development mode or if --debug flag is passed
  if (isDevelopment || process.argv.includes('--debug')) {
    console.log('Creating debug window...');
    createDebugWindow();
  }

  // On macOS, recreate the window when the dock icon is clicked
  app.on('activate', async () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      mainWindow = await createWindow();
    }
  });
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});
