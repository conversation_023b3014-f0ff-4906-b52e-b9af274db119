/**
 * Quick test script to verify OAuth PKCE implementation
 */

const crypto = require('crypto');

// Test state generation
function testStateGeneration() {
  console.log('Testing state generation...');
  const state = crypto.randomBytes(32).toString('base64url');
  console.log('Generated state:', state);
  console.log('State length:', state.length);
  console.log('Is URL safe:', /^[A-Za-z0-9_-]+$/.test(state));
  console.log('✅ State generation test passed\n');
}

// Test PKCE parameters
function testPKCEParams() {
  console.log('Testing PKCE parameters...');
  
  // This would be handled by Supabase internally
  const codeVerifier = crypto.randomBytes(32).toString('base64url');
  const codeChallenge = crypto
    .createHash('sha256')
    .update(codeVerifier)
    .digest('base64url');
  
  console.log('Code verifier length:', codeVerifier.length);
  console.log('Code challenge length:', codeChallenge.length);
  console.log('✅ PKCE parameters test passed\n');
}

// Test URL parsing
function testURLParsing() {
  console.log('Testing URL parsing...');
  
  // Simulate PKCE callback URL
  const callbackURL = 'chromasync://auth/callback?code=test_auth_code&state=test_state_123';
  const urlObj = new URL(callbackURL);
  
  console.log('Protocol:', urlObj.protocol);
  console.log('Pathname:', urlObj.pathname);
  console.log('Code:', urlObj.searchParams.get('code'));
  console.log('State:', urlObj.searchParams.get('state'));
  
  // Test error callback
  const errorURL = 'chromasync://auth/callback?error=access_denied&error_description=User%20denied%20access';
  const errorObj = new URL(errorURL);
  
  console.log('\nError callback:');
  console.log('Error:', errorObj.searchParams.get('error'));
  console.log('Description:', errorObj.searchParams.get('error_description'));
  console.log('✅ URL parsing test passed\n');
}

// Run all tests
console.log('=== OAuth PKCE Implementation Tests ===\n');
testStateGeneration();
testPKCEParams();
testURLParsing();
console.log('=== All tests completed ===');