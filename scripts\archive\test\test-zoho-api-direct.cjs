#!/usr/bin/env node

// Direct test of Zoho API with fresh token
const axios = require('axios');
require('dotenv').config();

const CLIENT_ID = process.env.ZOHO_CLIENT_ID;
const CLIENT_SECRET = process.env.ZOHO_CLIENT_SECRET;
const ACCOUNT_ID = process.env.ZOHO_ACCOUNT_ID;
const REFRESH_TOKEN = process.env.ZOHO_REFRESH_TOKEN;
const REGION = process.env.ZOHO_REGION || 'COM';

const authDomain = REGION === 'EU' ? 'accounts.zoho.eu' : 'accounts.zoho.com';
const apiDomain = REGION === 'EU' ? 'mail.zoho.eu' : 'mail.zoho.com';

async function test() {
  try {
    // Step 1: Get fresh access token
    console.log('Getting fresh access token...');
    console.log('Using auth domain:', authDomain);
    const tokenResponse = await axios.post(
      `https://${authDomain}/oauth/v2/token`,
      null,
      {
        params: {
          refresh_token: REFRESH_TOKEN,
          client_id: CLIENT_ID,
          client_secret: CLIENT_SECRET,
          grant_type: 'refresh_token'
        }
      }
    );
    
    console.log('Token response:', {
      access_token: tokenResponse.data.access_token ? '✓ received' : '✗ missing',
      expires_in: tokenResponse.data.expires_in,
      api_domain: tokenResponse.data.api_domain,
      token_type: tokenResponse.data.token_type
    });
    
    const accessToken = tokenResponse.data.access_token;
    
    // Step 2: Send test emails to both addresses
    const testAddresses = [
      '<EMAIL>',
      '<EMAIL>'
    ];
    
    for (const toAddress of testAddresses) {
      console.log(`\nSending test email to: ${toAddress}`);
      console.log('Using API domain:', apiDomain);
      
      try {
        const emailResponse = await axios.post(
          `https://${apiDomain}/api/accounts/${ACCOUNT_ID}/messages`,
          {
            fromAddress: '<EMAIL>',
            toAddress: toAddress,
            subject: '🎨 ChromaSync Email Delivery Test',
            content: `<html>
<body style="font-family: Arial, sans-serif;">
  <h2>ChromaSync Email Delivery Test</h2>
  <p>This is a test email to verify delivery to your email address.</p>
  <p><strong>Sent to:</strong> ${toAddress}</p>
  <p><strong>Sent from:</strong> <EMAIL></p>
  <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
  <hr>
  <p style="color: #666; font-size: 12px;">
    If you're not receiving ChromaSync invitation emails, please check:
    <ul style="color: #666; font-size: 12px;">
      <li>Your spam/junk folder</li>
      <li>Email filters that might be blocking chromasync.app domain</li>
      <li>Corporate email gateway settings</li>
    </ul>
  </p>
</body>
</html>`,
            mailFormat: 'html',
            // Add headers to improve deliverability
            replyTo: '<EMAIL>'
          },
          {
            headers: {
              'Authorization': `Zoho-oauthtoken ${accessToken}`,
              'Content-Type': 'application/json'
            }
          }
        );
        
        console.log('✅ Email sent successfully!');
        console.log('Response:', {
          messageId: emailResponse.data.data?.messageId,
          mailId: emailResponse.data.data?.mailId
        });
      } catch (error) {
        console.error(`❌ Failed to send to ${toAddress}:`, error.response?.data || error.message);
      }
    }
    
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Headers:', error.response.headers);
    }
  }
}

test();