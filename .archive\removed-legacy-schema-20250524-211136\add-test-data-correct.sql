-- Correct test data insertion script matching actual schema

-- Insert test product
INSERT INTO products (id, name, description, created_at, updated_at)
VALUES 
  ('test-product-001', 'Test Product with Colors', 
   'Test product with flat and gradient colors',
   datetime('now'), datetime('now'));

-- Insert flat color
INSERT INTO colors (id, product, uniqueId, colourCode, hex, cmyk, notes, gradient, is_library, created_at, updated_at)
VALUES 
  ('test-color-flat-001', 'Test Product with Colors', 'TEST-BLUE-001', 'TEST-BLUE', '#2563EB', 
   '{"cyan": 85, "magenta": 60, "yellow": 0, "black": 8}', 'Test flat blue color', '', 0,
   datetime('now'), datetime('now'));

-- Insert gradient color  
INSERT INTO colors (id, product, uniqueId, colourCode, hex, cmyk, notes, gradient, is_library, created_at, updated_at)
VALUES 
  ('test-color-gradient-001', 'Test Product with Colors', 'TEST-GRADIENT-001', 'TEST-GRADIENT', '#2563EB',
   '{"cyan": 85, "magenta": 60, "yellow": 0, "black": 8}', 'Test gradient color',
   '{"type": "linear", "angle": 45, "stops": [{"position": 0, "color": "#2563EB"}, {"position": 0.5, "color": "#7C3AED"}, {"position": 1, "color": "#DC2626"}]}',
   0, datetime('now'), datetime('now'));

-- Associate colors with product
INSERT INTO product_colors (product_id, color_id, added_at)
VALUES 
  ('test-product-001', 'test-color-flat-001', datetime('now')),
  ('test-product-001', 'test-color-gradient-001', datetime('now'));

-- Show what was created
SELECT 
  p.name as product_name,
  c.colourCode as color_code,
  c.uniqueId as unique_id,
  c.hex,
  CASE WHEN c.gradient != '' THEN 'Gradient' ELSE 'Flat' END as color_type,
  c.notes
FROM products p
JOIN product_colors pc ON p.id = pc.product_id
JOIN colors c ON pc.color_id = c.id
WHERE p.id = 'test-product-001'
ORDER BY c.colourCode;