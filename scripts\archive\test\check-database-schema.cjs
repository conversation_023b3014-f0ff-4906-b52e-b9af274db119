/**
 * Check Database Schema - Inspect current database structure
 */

const Database = require('better-sqlite3');
const path = require('path');

function checkDatabaseSchema() {
  const dbPath = '/Users/<USER>/Library/Application Support/ChromaSync/chromasync.db';
  console.log('Opening database:', dbPath);
  
  const db = new Database(dbPath);
  
  try {
    // Get all tables
    const tables = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' 
      ORDER BY name
    `).all();
    
    console.log('\n📊 Available tables:');
    tables.forEach(table => {
      console.log(`  - ${table.name}`);
    });
    
    // Check if we have any user-related data
    if (tables.some(t => t.name === 'users')) {
      const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get();
      console.log(`\n👥 Users: ${userCount.count}`);
      
      if (userCount.count > 0) {
        const users = db.prepare('SELECT id, email, name FROM users LIMIT 5').all();
        users.forEach(user => {
          console.log(`  - ${user.email || user.name || user.id}`);
        });
      }
    }
    
    // Check if we have organization tables
    const hasOrgTables = tables.some(t => t.name === 'organizations');
    if (!hasOrgTables) {
      console.log('\n⚠️  No organization tables found.');
      console.log('   This appears to be a legacy single-user database.');
      console.log('   You may need to run the organization migration first.');
    }
    
    // Check for colors to see if this is an active database
    if (tables.some(t => t.name === 'colors')) {
      const colorCount = db.prepare('SELECT COUNT(*) as count FROM colors').get();
      console.log(`\n🎨 Colors: ${colorCount.count}`);
    }
    
  } catch (error) {
    console.error('❌ Error checking schema:', error);
  } finally {
    db.close();
  }
}

checkDatabaseSchema();