/**
 * Environment utility functions
 */

/**
 * Check if the application is running in development mode
 */
export const isDevelopment = process.env.NODE_ENV === 'development' || 
                            process.env.npm_lifecycle_event === 'dev' || 
                            process.env.npm_lifecycle_event === 'start';

/**
 * Check if the application is running in production mode
 */
export const isProduction = !isDevelopment;

/**
 * Get the application version from package.json
 */
export const getAppVersion = (): string => {
  return process.env.npm_package_version || '1.0.0';
};

/**
 * Get the platform name
 */
export const getPlatform = (): 'win32' | 'darwin' | 'linux' | 'unknown' => {
  if (process.platform === 'win32') {return 'win32';}
  if (process.platform === 'darwin') {return 'darwin';}
  if (process.platform === 'linux') {return 'linux';}
  return 'unknown';
};

/**
 * Check if the application is running on Windows
 */
export const isWindows = process.platform === 'win32';

/**
 * Check if the application is running on macOS
 */
export const isMacOS = process.platform === 'darwin';

/**
 * Check if the application is running on Linux
 */
export const isLinux = process.platform === 'linux';
