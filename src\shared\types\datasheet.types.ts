/**
 * @file datasheet.types.ts
 * @description Type definitions for datasheets
 */

export interface DatasheetEntry {
  id: string;
  productId: string;
  name: string;
  path: string; // Kept for backward compatibility
  url?: string; // The actual URL (SharePoint, etc.)
  fileType: 'pdf' | 'docx' | 'xlsx' | 'pptx' | 'link' | 'other';
  dateAdded: string;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: string;
  description?: string;
  isExternal?: boolean;
  tags?: string[];
  metadata?: Record<string, any>;
}

export type NewDatasheet = Omit<DatasheetEntry, 'id' | 'dateAdded'>;
