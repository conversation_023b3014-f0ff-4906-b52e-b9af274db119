import React, { useMemo } from 'react';
import { 
  getColorTemperature,
  calculateColorSimilarity,
  calculateDeltaE
} from '../../../../../../shared/utils/color/analysis';
import { hexToRgb, hexToHsl, rgbToLab } from '../../../../../../shared/utils/color/conversion';
import { useTokens } from '../../../../../hooks/useTokens';
import { Lightbulb, TrendingUp, AlertCircle, Palette } from 'lucide-react';

interface AnalysisInsightsProps {
  colors: Array<{
    id: string;
    hex: string;
    name: string;
    product_id?: string;
  }>;
}

interface ColorWithAnalysis {
  color: {
    id: string;
    hex: string;
    name: string;
  };
  temperature: 'warm' | 'cool' | 'neutral';
  hsl: { h: number; s: number; l: number };
  lab: { l: number; a: number; b: number };
}

interface SimilarPair {
  colors: [AnalysisInsightsProps['colors'][0], AnalysisInsightsProps['colors'][0]];
  similarity: number;
  deltaE: number;
}

export default function AnalysisInsights({ colors }: AnalysisInsightsProps) {
  const _tokens = useTokens();

  const insights = useMemo(() => {
    // Analyze each color
    const analyzedColors: ColorWithAnalysis[] = colors.map(color => {
      const hsl = hexToHsl(color.hex);
      const rgb = hexToRgb(color.hex);
      const lab = rgb ? rgbToLab(rgb) : { l: 0, a: 0, b: 0 };

      return {
        color,
        temperature: hsl ? getColorTemperature(hsl) : 'neutral',
        hsl: hsl || { h: 0, s: 0, l: 0 },
        lab
      };
    });

    // Temperature distribution
    const temperatureDistribution = {
      warm: analyzedColors.filter(c => c.temperature === 'warm').length,
      cool: analyzedColors.filter(c => c.temperature === 'cool').length,
      neutral: analyzedColors.filter(c => c.temperature === 'neutral').length
    };

    // Find similar colors
    const similarPairs: SimilarPair[] = [];
    for (let i = 0; i < colors.length; i++) {
      for (let j = i + 1; j < colors.length; j++) {
        const rgb1 = hexToRgb(colors[i].hex);
        const rgb2 = hexToRgb(colors[j].hex);
        
        if (rgb1 && rgb2) {
          const similarity = calculateColorSimilarity(rgb1, rgb2);
          if (similarity > 85) {
            const lab1 = rgbToLab(rgb1);
            const lab2 = rgbToLab(rgb2);
            const deltaE = calculateDeltaE(lab1, lab2);
            
            similarPairs.push({ 
              colors: [colors[i], colors[j]], 
              similarity,
              deltaE
            });
          }
        }
      }
    }

    // Saturation analysis
    const avgSaturation = analyzedColors.reduce((sum, c) => sum + c.hsl.s, 0) / analyzedColors.length;
    const avgLightness = analyzedColors.reduce((sum, c) => sum + c.hsl.l, 0) / analyzedColors.length;

    // Color diversity score (0-100)
    const hueRange = Math.max(...analyzedColors.map(c => c.hsl.h)) - Math.min(...analyzedColors.map(c => c.hsl.h));
    const diversityScore = Math.min(100, (hueRange / 360) * 100 + (1 - similarPairs.length / colors.length) * 50);

    // Generate recommendations
    const recommendations = generateRecommendations(
      analyzedColors,
      temperatureDistribution,
      similarPairs,
      avgSaturation,
      avgLightness,
      diversityScore
    );

    return {
      temperatureDistribution,
      similarPairs: similarPairs.slice(0, 5), // Top 5 similar pairs
      avgSaturation,
      avgLightness,
      diversityScore,
      recommendations
    };
  }, [colors]);

  return (
    <div className="space-y-4">
      <div className="bg-ui-accent-subtle rounded-lg p-4 border border-ui-border">
        <div className="flex items-center gap-2 mb-3">
          <Lightbulb className="w-5 h-5 text-ui-accent-primary" />
          <h3 className="text-lg font-medium text-ui-foreground-primary">AI Color Insights</h3>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="bg-ui-background-tertiary/80 backdrop-blur-sm rounded-lg p-3">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm text-ui-foreground-secondary">Color Diversity</span>
              <TrendingUp className="w-4 h-4 text-ui-success" />
            </div>
            <div className="flex items-end gap-2">
              <span className="text-2xl font-bold text-ui-foreground-primary">{Math.round(insights.diversityScore)}%</span>
              <span className="text-xs text-ui-foreground-secondary mb-1">
                {insights.diversityScore > 70 ? 'High' : insights.diversityScore > 40 ? 'Medium' : 'Low'}
              </span>
            </div>
          </div>

          <div className="bg-ui-background-tertiary/80 backdrop-blur-sm rounded-lg p-3">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm text-ui-foreground-secondary">Temperature Balance</span>
              <Palette className="w-4 h-4 text-ui-warning" />
            </div>
            <div className="flex gap-2 text-xs">
              <span className="flex items-center gap-1">
                <div className="w-3 h-3 bg-red-400 rounded" />
                <span className="text-ui-foreground-primary">{insights.temperatureDistribution.warm}</span>
              </span>
              <span className="flex items-center gap-1">
                <div className="w-3 h-3 bg-gray-400 rounded" />
                <span className="text-ui-foreground-primary">{insights.temperatureDistribution.neutral}</span>
              </span>
              <span className="flex items-center gap-1">
                <div className="w-3 h-3 bg-blue-400 rounded" />
                <span className="text-ui-foreground-primary">{insights.temperatureDistribution.cool}</span>
              </span>
            </div>
          </div>

          <div className="bg-ui-background-tertiary/80 backdrop-blur-sm rounded-lg p-3">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm text-ui-foreground-secondary">Avg Saturation</span>
              <div 
                className="w-4 h-4 rounded"
                style={{ 
                  backgroundColor: `hsl(200, ${insights.avgSaturation}%, 50%)` 
                }}
              />
            </div>
            <span className="text-2xl font-bold text-ui-foreground-primary">{Math.round(insights.avgSaturation)}%</span>
          </div>
        </div>

        {/* Similar Colors Warning */}
        {insights.similarPairs.length > 0 && (
          <div className="bg-ui-warning-subtle border border-ui-warning-subtle-border rounded-lg p-3 mb-4">
            <div className="flex items-start gap-2">
              <AlertCircle className="w-4 h-4 text-ui-warning mt-0.5" />
              <div className="flex-1">
                <p className="text-sm font-medium text-ui-foreground-primary">Similar Colors Detected</p>
                <p className="text-xs text-ui-foreground-secondary mt-1">
                  Found {insights.similarPairs.length} color pair{insights.similarPairs.length > 1 ? 's' : ''} with high similarity (ΔE {'<'} 5)
                </p>
                <div className="mt-2 space-y-1">
                  {insights.similarPairs.slice(0, 3).map((pair, index) => (
                    <div key={index} className="flex items-center gap-2 text-xs">
                      <div className="flex items-center gap-1">
                        <div 
                          className="w-3 h-3 rounded border border-ui-border"
                          style={{ backgroundColor: pair.colors[0].hex }}
                        />
                        <span className="text-ui-foreground-primary">{pair.colors[0].name || pair.colors[0].hex}</span>
                      </div>
                      <span className="text-ui-foreground-secondary">↔</span>
                      <div className="flex items-center gap-1">
                        <div 
                          className="w-3 h-3 rounded border border-ui-border"
                          style={{ backgroundColor: pair.colors[1].hex }}
                        />
                        <span className="text-ui-foreground-primary">{pair.colors[1].name || pair.colors[1].hex}</span>
                      </div>
                      <span className="text-ui-foreground-secondary">(ΔE: {pair.deltaE.toFixed(1)})</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Recommendations */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-ui-foreground-secondary">Recommendations</h4>
          {insights.recommendations.map((rec, index) => (
            <div key={index} className="flex items-start gap-2 text-sm">
              <span className="text-ui-accent-primary mt-0.5">•</span>
              <p className="text-ui-foreground-primary">{rec}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

function generateRecommendations(
  analyzedColors: ColorWithAnalysis[],
  temperatureDistribution: { warm: number; cool: number; neutral: number },
  similarPairs: SimilarPair[],
  avgSaturation: number,
  avgLightness: number,
  diversityScore: number
): string[] {
  const recommendations: string[] = [];
  const total = analyzedColors.length;

  // Temperature balance recommendation
  const tempBalance = Math.abs(temperatureDistribution.warm - temperatureDistribution.cool) / total;
  if (tempBalance > 0.6) {
    const dominant = temperatureDistribution.warm > temperatureDistribution.cool ? 'warm' : 'cool';
    const opposite = dominant === 'warm' ? 'cool' : 'warm';
    recommendations.push(
      `Your palette is heavily ${dominant}-toned (${Math.round((temperatureDistribution[dominant] / total) * 100)}%). Consider adding ${opposite} colors for better balance.`
    );
  }

  // Diversity recommendation
  if (diversityScore < 40) {
    recommendations.push(
      'Your color selection has low diversity. Try adding colors from different hue ranges to create more visual interest.'
    );
  } else if (diversityScore > 80) {
    recommendations.push(
      'Excellent color diversity! Your palette covers a wide range of hues for maximum visual impact.'
    );
  }

  // Similar colors recommendation
  if (similarPairs.length > 3) {
    recommendations.push(
      `Found ${similarPairs.length} very similar color pairs. Consider removing duplicates or increasing contrast between similar colors.`
    );
  }

  // Saturation recommendation
  if (avgSaturation < 30) {
    recommendations.push(
      'Your palette consists mainly of desaturated colors. Add some vibrant colors to create focal points.'
    );
  } else if (avgSaturation > 70) {
    recommendations.push(
      'High saturation across your palette. Consider adding some muted tones for visual rest areas.'
    );
  }

  // Lightness recommendation
  if (avgLightness < 35) {
    recommendations.push(
      'Your palette skews dark. Adding lighter tones will improve readability and create better contrast.'
    );
  } else if (avgLightness > 65) {
    recommendations.push(
      'Your palette is predominantly light. Include darker shades for better contrast and depth.'
    );
  }

  // If no specific issues, give general advice
  if (recommendations.length === 0) {
    recommendations.push(
      'Well-balanced color palette! Consider creating variations using tints and shades for a cohesive design system.'
    );
  }

  return recommendations;
}