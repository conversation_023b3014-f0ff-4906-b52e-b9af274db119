/**
 * @file ColorTable/index.tsx
 * @description Table component for displaying color entries in a structured format
 */

import React, { useState, useMemo, useRef, useCallback } from 'react';
import { ColorEntry } from '../../../shared/types/color.types';
import { useColorStore } from '../../store/color.store';
import ColorTableRow from './ColorTableRow';
import ColorForm from '../ColorForm';
import GradientPickerModal from '../GradientPickerModal';
import GradientDetailsModal from '../GradientDetailsModal';
import { useTokens } from '../../hooks/useTokens';
import { TableSkeleton } from '../ui/Skeleton';

interface ColorTableProps {
  view?: 'details' | 'reference';
}

export default function ColorTable({ view = 'details' }: ColorTableProps) {
  const { colors = [], searchQuery, isLoading, error } = useColorStore();
  const [editingEntry, setEditingEntry] = useState<ColorEntry | null>(null);
  const [showGradientModal, setShowGradientModal] = useState<boolean>(false);
  const [showGradientDetails, setShowGradientDetails] = useState<{code: string, gradient: any, product: string} | null>(null);
  const [focusedRowIndex, setFocusedRowIndex] = useState<number>(-1);
  const tokens = useTokens();
  const tableRef = useRef<HTMLTableElement>(null);

  // Filter colors based on search query
  const filteredColors = useMemo(() => {
    if (!colors || !Array.isArray(colors)) {return [];}
    if (!searchQuery) {return colors;}

    // Check if it's a product filter query
    if (searchQuery.toLowerCase().startsWith('product:')) {
      const productName = searchQuery.substring(8).replace(/"/g, '').trim();
      return colors.filter(color =>
        color.product.toLowerCase() === productName.toLowerCase()
      );
    }

    // Regular search query
    const query = searchQuery.toLowerCase();
    return colors.filter((color) => {
      return (
        color.product.toLowerCase().includes(query) ||
        color.name.toLowerCase().includes(query) ||
        color.code.toLowerCase().includes(query) ||
        color.hex.toLowerCase().includes(query) ||
        color.cmyk.toLowerCase().includes(query) ||
        (color.notes && color.notes.toLowerCase().includes(query))
      );
    });
  }, [colors, searchQuery]);

  // Group colors by code for reference view - similar to how swatches group by name
  const groupedByCode = useMemo(() => {
    if (view !== 'reference') {return [];}
    
    // Debug: Log all colors and their codes
    console.log('[DEBUG] Total colors in store:', colors.length);
    console.log('[DEBUG] First 5 colors:', colors.slice(0, 5).map(c => ({
      code: c.code,
      product: c.product,
      name: c.name
    })));
    
    const codeGroups = new Map<string, {
      color: ColorEntry;
      usageCount: number;
      products: string[];
    }>();

    // Helper function to get clean code (same as in ColorTableRow)
    const getCleanCode = (code: string): string => {
      const parts = code.split('-');
      return parts[0];
    };
    
    // Group filtered colors by code
    filteredColors.forEach((color) => {
      const code = color.code;
      const cleanCode = getCleanCode(code);
      
      if (!codeGroups.has(cleanCode)) {
        // Find all colors with the same clean code (without suffix)
        const colorsWithSameCode = colors.filter(c => {
          if (!c.code || !c.product || c.product.trim() === '') {return false;}
          const otherCleanCode = getCleanCode(c.code);
          return otherCleanCode.toLowerCase() === cleanCode.toLowerCase();
        });
        
        // Debug: Log what we found
        if (cleanCode === '001' || cleanCode === '002' || cleanCode === '003') {  // Log first few codes
          console.log(`[DEBUG] Code: ${code} -> Clean: ${cleanCode}`);
          console.log(`[DEBUG] Colors with same code:`, colorsWithSameCode.length);
          console.log(`[DEBUG] Products:`, colorsWithSameCode.map(c => c.product));
        }
        
        // Get unique products by filtering and using a Set
        const validProducts = colorsWithSameCode
          .map(c => c.product?.trim())
          .filter(product => product && product !== '');
        
        // Use Set to eliminate duplicates
        const uniqueProducts = new Set(validProducts);
        const uniqueProductsArray = Array.from(uniqueProducts);
        
        if (cleanCode === '001' || cleanCode === '002' || cleanCode === '003') {  // Log first few codes
          console.log(`[DEBUG] Unique products for ${cleanCode}:`, uniqueProductsArray);
        }
        
        codeGroups.set(cleanCode, {
          color,
          usageCount: uniqueProducts.size,
          products: uniqueProductsArray
        });
      }
    });

    const result = Array.from(codeGroups.values()).sort((a, b) => {
      // Sort by usage count (descending), then by code
      if (b.usageCount !== a.usageCount) {
        return b.usageCount - a.usageCount;
      }
      return a.color.code.localeCompare(b.color.code);
    });
    
    // Debug: Log the grouping results
    console.log('[DEBUG] Grouped codes (client-side):', result.map(g => ({
      code: g.color.code,
      cleanCode: getCleanCode(g.color.code),
      usageCount: g.usageCount,
      products: g.products
    })));
    
    return result;
  }, [filteredColors, view, colors]);

  const handleEdit = (entry: ColorEntry) => {
    // Check if entry is a gradient
    if (entry.gradient) {
      setEditingEntry(entry);
      setShowGradientModal(true);
    } else {
      setEditingEntry(entry);
    }
  };

  const handleEditSuccess = () => {
    setEditingEntry(null);
    setShowGradientModal(false);
  };

  const handleEditCancel = () => {
    setEditingEntry(null);
    setShowGradientModal(false);
  };

  const handleShowGradientDetails = (code: string, gradient: any, product: string) => {
    setShowGradientDetails({ code, gradient, product });
  };

  // Keyboard navigation handler
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLTableElement>) => {
    const rows = tableRef.current?.querySelectorAll('tbody tr');
    if (!rows || rows.length === 0) {
      return;
    }

    const maxIndex = rows.length - 1;
    let newIndex = focusedRowIndex;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        newIndex = focusedRowIndex < maxIndex ? focusedRowIndex + 1 : focusedRowIndex;
        break;
      case 'ArrowUp':
        e.preventDefault();
        newIndex = focusedRowIndex > 0 ? focusedRowIndex - 1 : 0;
        break;
      case 'Home':
        e.preventDefault();
        newIndex = 0;
        break;
      case 'End':
        e.preventDefault();
        newIndex = maxIndex;
        break;
      case 'Enter':
      case ' ':
        e.preventDefault();
        if (focusedRowIndex >= 0 && focusedRowIndex <= maxIndex) {
          const focusedRow = rows[focusedRowIndex];
          const editButton = focusedRow.querySelector('[data-action="edit"]') as HTMLButtonElement;
          editButton?.click();
        }
        break;
      case 'Delete':
        e.preventDefault();
        if (focusedRowIndex >= 0 && focusedRowIndex <= maxIndex) {
          const focusedRow = rows[focusedRowIndex];
          const deleteButton = focusedRow.querySelector('[data-action="delete"]') as HTMLButtonElement;
          deleteButton?.click();
        }
        break;
    }

    if (newIndex !== focusedRowIndex) {
      setFocusedRowIndex(newIndex);
      // Focus the row
      const newRow = rows[newIndex] as HTMLTableRowElement;
      newRow?.focus();
      // Scroll into view if needed
      newRow?.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
    }
  }, [focusedRowIndex]);

  // Set initial focus when table receives focus
  const handleTableFocus = useCallback(() => {
    if (focusedRowIndex === -1 && filteredColors.length > 0) {
      setFocusedRowIndex(0);
    }
  }, [focusedRowIndex, filteredColors.length]);

  // Get header classes using token system (matching PantoneTable)
  const getHeaderClasses = () => {
    return "bg-brand-primary dark:bg-brand-primary text-ui-foreground-inverse py-3 px-4 text-xs font-semibold text-left";
  };

  // Get modal overlay classes using token system (matching PantoneTable)
  const getModalOverlayClasses = () => {
    return "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[var(--zIndex-modal)]";
  };

  // Get modal content classes using token system (matching PantoneTable)
  const getModalContentClasses = () => {
    return "bg-ui-background-primary dark:bg-ui-background-primary p-[var(--spacing-6)] rounded-[var(--radius-lg)] max-w-md w-full";
  };

  // Get table container classes using token system (matching PantoneTable)
  const getTableContainerClasses = () => {
    return "bg-ui-background-primary dark:bg-ui-background-tertiary rounded-[var(--radius-lg)] shadow-md overflow-hidden";
  };

  // @ts-ignore - Intentionally unused
  // Get border classes using token system (matching PantoneTable)
  const _getBorderClasses = () => {
    return "border-b border-ui-border-light dark:border-ui-border-dark";
  };

  // Modal transition styles
  const modalTransitionStyle = {
    animation: `fadeIn ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`,
  };

  // Modal content transition styles
  const modalContentTransitionStyle = {
    animation: `scaleIn ${tokens.transitions.duration[300]} ${tokens.transitions.easing.apple}`,
  };

  // Define CSS animation classes
  React.useEffect(() => {
    // Create a style element
    const styleEl = document.createElement('style');
    styleEl.innerHTML = `
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }

      @keyframes scaleIn {
        from { transform: scale(0.95); opacity: 0; }
        to { transform: scale(1); opacity: 1; }
      }
    `;

    // Append to document head
    document.head.appendChild(styleEl);

    // Clean up when component unmounts
    return () => {
      document.head.removeChild(styleEl);
    };
  }, []);

  return (
    <div className="relative table-view-container">
      <div className={getTableContainerClasses()}>
        {isLoading ? (
          <div className="p-4">
            <TableSkeleton rows={8} columns={view === 'reference' ? 7 : 6} />
          </div>
        ) : error ? (
          <div className="p-4 text-center">
            <p className="text-feedback-error">Error: {error}</p>
          </div>
        ) : filteredColors.length === 0 ? (
          <div className="p-4 text-center">
            <p className="text-ui-foreground-secondary">
              {searchQuery ? 'No colours match your search criteria.' : 'No colours found. Add some colours to get started!'}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto pb-4 max-h-[calc(100vh-300px)] scrollable-content">
            <table 
              ref={tableRef}
              className="w-full table-fixed bg-ui-background-primary dark:bg-ui-background-tertiary border border-ui-border-light dark:border-ui-border-dark rounded-[var(--radius-lg)] overflow-hidden"
              role="table"
              tabIndex={0}
              onKeyDown={handleKeyDown}
              onFocus={handleTableFocus}
              aria-label="Color entries table"
            >
              <thead>
                <tr>
                  {view === 'reference' ? (
                    <>
                      <th className={getHeaderClasses() + " w-[18%]"}>Code</th>
                      <th className={getHeaderClasses() + " w-[18%]"}>Name</th>
                      <th className={`${getHeaderClasses()} text-center w-[12%]`}>Hex</th>
                      <th className={getHeaderClasses() + " w-[18%]"}>CMYK</th>
                      <th className={getHeaderClasses() + " w-[12%]"}>RGB</th>
                      <th className={`${getHeaderClasses()} text-center w-[12%]`}>Usage</th>
                      <th className={getHeaderClasses() + " w-[10%]"}>Actions</th>
                    </>
                  ) : (
                    <>
                      <th className={getHeaderClasses() + " w-[12%]"}>Product</th>
                      <th className={getHeaderClasses() + " w-[12%]"}>Name</th>
                      <th className={getHeaderClasses() + " w-[12%]"}>Code</th>
                      <th className={`${getHeaderClasses()} text-center w-[12%]`}>Hex</th>
                      <th className={getHeaderClasses() + " w-[18%]"}>CMYK</th>
                      <th className={getHeaderClasses() + " w-[25%]"}>Notes</th>
                      <th className={getHeaderClasses() + " w-[9%]"}>Actions</th>
                    </>
                  )}
                </tr>
              </thead>
              <tbody>
                {view === 'reference' ? (
                  // Show grouped colors by code with usage count
                  groupedByCode.map((group, index) => (
                    <ColorTableRow
                      key={group.color.code}
                      entry={group.color}
                      onEdit={handleEdit}
                      onShowGradientDetails={handleShowGradientDetails}
                      view={view}
                      usageCount={group.usageCount}
                      usedInProducts={group.products}
                      isFocused={focusedRowIndex === index}
                      onFocus={() => setFocusedRowIndex(index)}
                    />
                  ))
                ) : (
                  // Default details view
                  filteredColors.map((color, index) => (
                    <ColorTableRow
                      key={color.id}
                      entry={color}
                      onEdit={handleEdit}
                      onShowGradientDetails={handleShowGradientDetails}
                      view={view}
                      isFocused={focusedRowIndex === index}
                      onFocus={() => setFocusedRowIndex(index)}
                    />
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Edit modal for flat colors */}
      {editingEntry && !showGradientModal && (
        <div className={getModalOverlayClasses()} style={modalTransitionStyle}>
          <div className={getModalContentClasses()} style={modalContentTransitionStyle}>
            <h2 className="text-xl font-bold mb-4 text-ui-foreground-primary dark:text-ui-foreground-inverse">Edit Color</h2>
            <ColorForm
              editMode={true}
              color={editingEntry}
              onSuccess={handleEditSuccess}
              onCancel={handleEditCancel}
            />
          </div>
        </div>
      )}

      {/* Edit modal for gradients */}
      {editingEntry && showGradientModal && (
        <GradientPickerModal
          isOpen={showGradientModal}
          onClose={handleEditCancel}
          onSuccess={handleEditSuccess}
          editMode={true}
          color={editingEntry}
          initialValue={editingEntry.gradient}
        />
      )}

      {/* Gradient details modal */}
      {showGradientDetails && (
        <GradientDetailsModal
          isOpen={true}
          onClose={() => setShowGradientDetails(null)}
          gradient={{
            gradientCSS: showGradientDetails.gradient.gradientCSS || `linear-gradient(45deg, ${showGradientDetails.gradient.stops?.map((s: any) => `${s.hex || s.color} ${s.position}%`).join(', ')})`,
            gradientStops: showGradientDetails.gradient.gradientStops || showGradientDetails.gradient.stops?.map((s: any) => ({
              color: s.hex || s.color,
              position: s.position,
              cmyk: s.cmyk
            })) || [],
            type: showGradientDetails.gradient.type || 'linear'
          }}
          product={showGradientDetails.product}
        />
      )}
    </div>
  );
}
