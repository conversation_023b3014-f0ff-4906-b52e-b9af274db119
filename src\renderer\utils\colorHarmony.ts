/**
 * @file colorHarmony.ts
 * @description Utility functions for generating color harmonies
 */

import { hexToHsl, hslToHex, hexToRgb, rgbToHex } from '../../shared/utils/color';

/**
 * Generate complementary color
 * @param hex Base color in hex format
 * @returns Complementary color in hex format
 */
export const getComplementaryColor = (hex: string): string => {
  const hsl = hexToHsl(hex);
  if (!hsl) {return hex;}
  
  // Complementary color is opposite on the color wheel (180 degrees)
  return hslToHex({
    h: (hsl.h + 180) % 360,
    s: hsl.s,
    l: hsl.l
  });
};

/**
 * Generate analogous colors
 * @param hex Base color in hex format
 * @param count Number of colors to generate (including the base)
 * @param angle Angle between colors (default: 30 degrees)
 * @returns Array of analogous colors in hex format
 */
export const getAnalogousColors = (hex: string, count = 3, angle = 30): string[] => {
  const hsl = hexToHsl(hex);
  if (!hsl) {return [hex];}
  
  const result = [hex];
  
  // Generate colors on both sides of the base color
  const halfCount = Math.floor((count - 1) / 2);
  
  // Colors to the right on the color wheel
  for (let i = 1; i <= halfCount; i++) {
    result.push(
      hslToHex({
        h: (hsl.h + i * angle) % 360,
        s: hsl.s,
        l: hsl.l
      })
    );
  }
  
  // Colors to the left on the color wheel
  for (let i = 1; i <= count - 1 - halfCount; i++) {
    result.unshift(
      hslToHex({
        h: (hsl.h - i * angle + 360) % 360,
        s: hsl.s,
        l: hsl.l
      })
    );
  }
  
  return result;
};

/**
 * Generate triadic colors
 * @param hex Base color in hex format
 * @returns Array of 3 triadic colors
 */
export const getTriadicColors = (hex: string): string[] => {
  const hsl = hexToHsl(hex);
  if (!hsl) {return [hex];}
  
  // Triadic colors are 120 degrees apart on the color wheel
  return [
    hex,
    hslToHex({ h: (hsl.h + 120) % 360, s: hsl.s, l: hsl.l }),
    hslToHex({ h: (hsl.h + 240) % 360, s: hsl.s, l: hsl.l })
  ];
};

/**
 * Generate tetradic (rectangular) colors
 * @param hex Base color in hex format
 * @returns Array of 4 tetradic colors
 */
export const getTetradicColors = (hex: string): string[] => {
  const hsl = hexToHsl(hex);
  if (!hsl) {return [hex];}
  
  // Tetradic colors form a rectangle on the color wheel (90 degrees apart)
  return [
    hex,
    hslToHex({ h: (hsl.h + 90) % 360, s: hsl.s, l: hsl.l }),
    hslToHex({ h: (hsl.h + 180) % 360, s: hsl.s, l: hsl.l }),
    hslToHex({ h: (hsl.h + 270) % 360, s: hsl.s, l: hsl.l })
  ];
};

/**
 * Generate split-complementary colors
 * @param hex Base color in hex format
 * @param angle Angle of separation from complement (default: 30 degrees)
 * @returns Array of 3 split-complementary colors
 */
export const getSplitComplementaryColors = (hex: string, angle = 30): string[] => {
  const hsl = hexToHsl(hex);
  if (!hsl) {return [hex];}
  
  // Calculate the complement
  const complementHue = (hsl.h + 180) % 360;
  
  // Split colors are on both sides of the complement
  return [
    hex,
    hslToHex({ h: (complementHue - angle + 360) % 360, s: hsl.s, l: hsl.l }),
    hslToHex({ h: (complementHue + angle) % 360, s: hsl.s, l: hsl.l })
  ];
};

/**
 * Generate monochromatic colors
 * @param hex Base color in hex format
 * @param count Number of colors to generate (including the base)
 * @returns Array of monochromatic colors in hex format
 */
export const getMonochromaticColors = (hex: string, count = 5): string[] => {
  const hsl = hexToHsl(hex);
  if (!hsl) {return [hex];}
  
  const result = [hex];
  
  // Calculate lightness step
  const lightnessRange = 80; // Range from 10% to 90% lightness
  const step = lightnessRange / (count - 1);
  
  // Start from 10% lightness (to avoid pure black)
  for (let i = 1; i < count; i++) {
    const newLightness = 10 + i * step;
    result.push(
      hslToHex({
        h: hsl.h,
        s: hsl.s,
        l: newLightness
      })
    );
  }
  
  return result;
};

/**
 * Generate shades of a color (adding black)
 * @param hex Base color in hex format
 * @param count Number of shades to generate (including the base)
 * @returns Array of shades in hex format
 */
export const getShades = (hex: string, count = 5): string[] => {
  const rgb = hexToRgb(hex);
  if (!rgb) {return [hex];}
  
  const result = [hex];
  
  // Calculate shade step
  const step = 1 / (count - 1);
  
  // Generate darker shades
  for (let i = 1; i < count; i++) {
    const factor = 1 - (i * step);
    result.push(
      rgbToHex({
        r: Math.round(rgb.r * factor),
        g: Math.round(rgb.g * factor),
        b: Math.round(rgb.b * factor)
      })
    );
  }
  
  return result;
};

/**
 * Generate tints of a color (adding white)
 * @param hex Base color in hex format
 * @param count Number of tints to generate (including the base)
 * @returns Array of tints in hex format
 */
export const getTints = (hex: string, count = 5): string[] => {
  const rgb = hexToRgb(hex);
  if (!rgb) {return [hex];}
  
  const result = [hex];
  
  // Calculate tint step
  const step = 1 / (count - 1);
  
  // Generate lighter tints
  for (let i = 1; i < count; i++) {
    const factor = i * step;
    result.push(
      rgbToHex({
        r: Math.round(rgb.r + (255 - rgb.r) * factor),
        g: Math.round(rgb.g + (255 - rgb.g) * factor),
        b: Math.round(rgb.b + (255 - rgb.b) * factor)
      })
    );
  }
  
  return result;
};

/**
 * Generate square color harmony
 * @param hex Base color in hex format
 * @returns Array of 4 colors forming a square on the color wheel
 */
export const getSquareColors = (hex: string): string[] => {
  const hsl = hexToHsl(hex);
  if (!hsl) {return [hex];}
  
  // Square colors are 90 degrees apart (same as tetradic)
  return getTetradicColors(hex);
};

/**
 * Generate compound harmony (base + complementary with analogous colors)
 * @param hex Base color in hex format
 * @returns Array of compound harmony colors
 */
export const getCompoundColors = (hex: string): string[] => {
  const hsl = hexToHsl(hex);
  if (!hsl) {return [hex];}
  
  const complement = getComplementaryColor(hex);
  const analogous = getAnalogousColors(hex, 3).filter(color => color !== hex);
  
  // Return base color, complement, and analogous colors
  return [hex, complement, ...analogous];
}; 