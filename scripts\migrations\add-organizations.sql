-- Add Multi-Tenant Organization Support to ChromaSync
-- This migration adds organization tables and updates existing tables to support multi-tenancy

-- Create organizations table
CREATE TABLE IF NOT EXISTS organizations (
  id INTEGER PRIMARY KEY,
  external_id TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  plan TEXT DEFAULT 'free' CHECK (plan IN ('free', 'team', 'enterprise')),
  settings TEXT DEFAULT '{}',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create organization members table
CREATE TABLE IF NOT EXISTS organization_members (
  organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  user_id TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
  joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  invited_by TEXT,
  PRIMARY KEY (organization_id, user_id)
);

-- Add organization_id to existing tables
ALTER TABLE colors ADD COLUMN organization_id INTEGER REFERENCES organizations(id);
ALTER TABLE products ADD COLUMN organization_id INTEGER REFERENCES organizations(id);
ALTER TABLE product_colors ADD COLUMN organization_id INTEGER REFERENCES organizations(id);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_org_members_user ON organization_members(user_id);
CREATE INDEX IF NOT EXISTS idx_org_slug ON organizations(slug);
CREATE INDEX IF NOT EXISTS idx_colors_org ON colors(organization_id);
CREATE INDEX IF NOT EXISTS idx_products_org ON products(organization_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_org ON product_colors(organization_id);

-- Update views to include organization_id
DROP VIEW IF EXISTS v_colors;
CREATE VIEW v_colors AS
SELECT 
    c.id,
    c.external_id,
    c.source_id,
    c.organization_id,
    c.user_id,
    c.code,
    c.hex,
    c.display_name AS name,
    c.is_gradient,
    c.properties,
    c.is_library,
    c.notes,
    c.tags,
    c.metadata,
    c.created_at,
    c.updated_at,
    c.deleted_at,
    c.created_by,
    c.updated_by,
    cs.name AS source_name,
    cs.type AS source_type,
    -- CMYK values
    cmyk.c AS cmyk_c,
    cmyk.m AS cmyk_m,
    cmyk.y AS cmyk_y,
    cmyk.k AS cmyk_k,
    -- RGB values
    rgb.r AS rgb_r,
    rgb.g AS rgb_g,
    rgb.b AS rgb_b,
    -- LAB values
    lab.l AS lab_l,
    lab.a AS lab_a,
    lab.b AS lab_b,
    -- HSL values
    hsl.h AS hsl_h,
    hsl.s AS hsl_s,
    hsl.l AS hsl_l
FROM colors c
LEFT JOIN color_sources cs ON c.source_id = cs.id
LEFT JOIN color_cmyk cmyk ON c.id = cmyk.color_id
LEFT JOIN color_rgb rgb ON c.id = rgb.color_id
LEFT JOIN color_lab lab ON c.id = lab.color_id
LEFT JOIN color_hsl hsl ON c.id = hsl.color_id;

-- Update product colors view
DROP VIEW IF EXISTS v_product_colors;
CREATE VIEW v_product_colors AS
SELECT 
    pc.product_id,
    pc.color_id,
    pc.organization_id,
    pc.display_order,
    pc.created_at,
    p.name AS product_name,
    p.sku AS product_sku,
    c.hex,
    c.display_name AS color_name,
    c.code AS color_code
FROM product_colors pc
JOIN products p ON pc.product_id = p.id
JOIN colors c ON pc.color_id = c.id
WHERE p.deleted_at IS NULL 
  AND c.deleted_at IS NULL;

-- Update products with colors view
DROP VIEW IF EXISTS v_products_with_colors;
CREATE VIEW v_products_with_colors AS
SELECT 
    p.id,
    p.external_id,
    p.organization_id,
    p.user_id,
    p.name,
    p.sku,
    p.description,
    p.notes,
    p.is_active,
    p.metadata,
    p.created_at,
    p.updated_at,
    p.deleted_at,
    COUNT(pc.color_id) AS color_count,
    GROUP_CONCAT(c.hex) AS color_hexes
FROM products p
LEFT JOIN product_colors pc ON p.id = pc.product_id
LEFT JOIN colors c ON pc.color_id = c.id AND c.deleted_at IS NULL
WHERE p.deleted_at IS NULL
GROUP BY p.id;

-- Create a default organization for existing data (if any exists)
INSERT INTO organizations (id, external_id, name, slug, plan)
SELECT 
    1,
    lower(hex(randomblob(16))),
    'Default Organization',
    'default-org',
    'free'
WHERE EXISTS (SELECT 1 FROM colors UNION SELECT 1 FROM products);

-- Assign existing data to default organization
UPDATE colors SET organization_id = 1 WHERE organization_id IS NULL AND EXISTS (SELECT 1 FROM organizations WHERE id = 1);
UPDATE products SET organization_id = 1 WHERE organization_id IS NULL AND EXISTS (SELECT 1 FROM organizations WHERE id = 1);
UPDATE product_colors SET organization_id = 1 WHERE organization_id IS NULL AND EXISTS (SELECT 1 FROM organizations WHERE id = 1);

-- Add the first user as owner of default organization (if exists)
INSERT INTO organization_members (organization_id, user_id, role)
SELECT DISTINCT 1, user_id, 'owner'
FROM (
    SELECT user_id FROM colors WHERE user_id IS NOT NULL
    UNION 
    SELECT user_id FROM products WHERE user_id IS NOT NULL
) AS users
WHERE EXISTS (SELECT 1 FROM organizations WHERE id = 1)
LIMIT 1;
