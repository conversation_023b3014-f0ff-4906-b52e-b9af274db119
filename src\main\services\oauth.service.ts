/**
 * @file oauth.service.ts - Simplified with Supabase's built-in PKCE
 * @description OAuth service using Supabase's native PKCE flow for secure authentication
 */

import { shell, BrowserWindow } from 'electron';
import { getSupabaseClient } from './supabase-client';
import Store from 'electron-store';
import { getDatabase } from '../db/database';
import { OrganizationService } from '../db/services/organization.service';
import { AuthRedirectServer } from './auth-redirect-server';
import crypto from 'crypto';

export interface AuthResult {
  success: boolean;
  status?: 'needs_organization_setup' | 'needs_organization_selection' | 'authenticated';
  organizations?: any[];
  error?: string;
}

export class OAuthService {
  private store = new Store();
  private authTimeout: NodeJS.Timeout | null = null;
  private notificationWindow: BrowserWindow | null = null;
  private authPromise: {
    resolve: (result: AuthResult) => void;
    reject: (error: Error) => void;
  } | null = null;
  private redirectServer: AuthRedirectServer | null = null;
  private useLocalRedirect = true; // TEMP: Use local redirect for testing
  private stateStore = new Map<string, { 
    timestamp: number; 
    provider: string; 
    codeVerifier?: string;
    nonce: string;
  }>();
  
  // Security constants with configurable defaults
  private readonly STATE_EXPIRY_MS = 5 * 60 * 1000; // 5 minutes
  private readonly DEFAULT_AUTH_TIMEOUT_MS = 120000; // 2 minutes default
  private readonly MAX_CALLBACK_RETRIES = 3;
  private callbackAttempts = 0;
  
  // Session configuration keys
  private readonly CONFIG_KEYS = {
    AUTH_TIMEOUT: 'oauth.authTimeout',
    SESSION_TIMEOUT: 'oauth.sessionTimeout',
    SESSION_WARNING_TIME: 'oauth.sessionWarningTime',
    AUTO_LOGOUT_ENABLED: 'oauth.autoLogoutEnabled'
  } as const;

  /**
   * Generate a cryptographically secure state parameter for CSRF protection
   */
  private generateSecureState(): string {
    return crypto.randomBytes(32).toString('base64url');
  }

  /**
   * Generate a cryptographically secure nonce for additional security
   */
  private generateSecureNonce(): string {
    return crypto.randomBytes(16).toString('base64url');
  }

  /**
   * Validate state parameter and check for expiry
   */
  private validateState(receivedState: string): boolean {
    if (!receivedState) {
      console.error('[OAuth] No state parameter received');
      return false;
    }

    const stored = this.stateStore.get(receivedState);
    if (!stored) {
      console.error('[OAuth] Invalid state parameter - not found in store');
      return false;
    }

    // Check if state has expired
    const isExpired = Date.now() - stored.timestamp > this.STATE_EXPIRY_MS;
    if (isExpired) {
      console.error('[OAuth] State parameter expired');
      this.stateStore.delete(receivedState);
      return false;
    }

    // State is valid - clean it up
    this.stateStore.delete(receivedState);
    return true;
  }

  /**
   * Clean up expired state entries
   */
  private cleanupExpiredStates(): void {
    const now = Date.now();
    for (const [key, value] of this.stateStore.entries()) {
      if (now - value.timestamp > this.STATE_EXPIRY_MS) {
        this.stateStore.delete(key);
      }
    }
  }

  /**
   * Validate callback URL format and origin
   */
  private validateCallbackUrl(url: string): boolean {
    try {
      // Check URL format
      if (!url.startsWith('chromasync://auth/callback') && 
          !url.startsWith('http://localhost:3000/auth/callback')) {
        console.error('[OAuth] Invalid callback URL format:', url.substring(0, 50));
        return false;
      }

      // Parse URL to validate structure
      const urlObj = new URL(url);
      
      // Check for suspicious parameters
      const params = urlObj.searchParams;
      const hash = urlObj.hash;
      
      if (params.get('javascript:') || hash.includes('javascript:')) {
        console.error('[OAuth] Potential XSS attempt in callback URL');
        return false;
      }

      return true;
    } catch (error) {
      console.error('[OAuth] URL parsing error:', error);
      return false;
    }
  }

  async signInWithGoogle(): Promise<AuthResult> {
    console.log('[OAuth] Starting Google sign in with Supabase PKCE flow...');
    
    // Check for auth loop cooldown
    if (this.isAuthLoopCooldownActive()) {
      const remainingMinutes = this.getAuthLoopCooldownRemaining();
      console.warn(`[OAuth] Auth loop cooldown active - ${remainingMinutes} minutes remaining`);
      return this.createUserFriendlyAuthResult(
        false,
        `Authentication temporarily disabled due to repeated failures. Please wait ${remainingMinutes} minutes and try again.`,
        'initialization'
      );
    }
    
    try {
      // Cleanup any existing auth session
      this.cleanup();
      this.callbackAttempts = 0;
      
      const supabase = getSupabaseClient();
      
      // Determine redirect URL
      let redirectTo: string;
      console.log('[OAuth] useLocalRedirect flag:', this.useLocalRedirect);
      
      if (this.useLocalRedirect) {
        // Start local redirect server
        console.log('[OAuth] Starting local redirect server...');
        this.redirectServer = new AuthRedirectServer();
        const serverStarted = this.redirectServer.start((url) => {
          console.log('[OAuth] Received callback from local server:', url);
          this.handleCallback(url);
        });
        
        try {
          await serverStarted;
          redirectTo = 'http://localhost:3000/auth/callback';
          console.log('[OAuth] ✅ Local redirect server started successfully on port 3000');
          console.log('[OAuth] Using local redirect server:', redirectTo);
        } catch (serverError) {
          console.error('[OAuth] ❌ Failed to start local redirect server:', serverError);
          console.error('[OAuth] Falling back to web redirect');
          redirectTo = 'https://auth.chromasync.app/auth/callback';
        }
      } else {
        // Use web redirect page
        redirectTo = 'https://auth.chromasync.app/auth/callback';
        console.log('[OAuth] Using web redirect URL:', redirectTo);
      }
      
      console.log('[OAuth] Final redirect URL:', redirectTo);
      
      // Start OAuth flow - let Supabase handle PKCE and state internally
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectTo,
          skipBrowserRedirect: false,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent'
            // Supabase handles state parameter internally for PKCE flow
          }
        }
      });
      
      if (error || !data.url) {
        console.error('[OAuth] Supabase error:', error);
        throw new Error(error?.message || 'Failed to get OAuth URL');
      }
      
      console.log('[OAuth] OAuth URL received (Supabase PKCE enabled)');
      console.log('[OAuth] Supabase will handle state parameter internally');
      
      // Create promise to handle async OAuth flow
      return new Promise((resolve, reject) => {
        this.authPromise = { resolve, reject };
        
        // Set configurable timeout for OAuth flow
        const authTimeoutMs = this.getAuthTimeout();
        console.log(`[OAuth] Setting authentication timeout to ${authTimeoutMs / 1000} seconds`);
        
        this.authTimeout = setTimeout(() => {
          console.log('[OAuth] Authentication timeout - no callback received');
          this.cleanup();
          resolve({ success: false, error: 'Authentication timeout - callback not received' });
        }, authTimeoutMs);
        
        // Show notification window
        this.showAuthNotification();        
        
        // Option to use in-app auth window instead of system browser
        if (process.env.USE_IN_APP_AUTH === 'true') {
          this.openInAppAuthWindow(data.url, resolve);
        } else {
          // Open OAuth URL in system browser
          console.log('[OAuth] Opening system browser...');
          shell.openExternal(data.url).catch((err) => {
            console.error('[OAuth] Failed to open browser:', err);
            this.cleanup();
            resolve({ success: false, error: 'Failed to open browser' });
          });
        }
      });
      
    } catch (error) {
      console.error('[OAuth] OAuth initialization error:', error);
      
      // Record auth attempt for loop detection
      const technicalError = error instanceof Error ? error.message : 'OAuth initialization failed';
      this.recordAuthAttempt(technicalError);
      
      this.cleanup();
      return this.createUserFriendlyAuthResult(false, technicalError, 'initialization');
    }
  }

  /**
   * Handle OAuth callback with enhanced security validation
   */
  async handleCallback(url: string): Promise<void> {
    console.log('[OAuth] Processing callback URL with security validation');
    
    // Rate limiting for callback attempts
    this.callbackAttempts++;
    if (this.callbackAttempts > this.MAX_CALLBACK_RETRIES) {
      console.error('[OAuth] Too many callback attempts, possible attack');
      if (this.authPromise) {
        this.authPromise.resolve({
          success: false,
          error: 'Too many callback attempts - possible security threat'
        });
      }
      this.cleanup();
      return;
    }
    
    if (!this.authPromise) {
      console.warn('[OAuth] No pending auth promise for callback');
      return;
    }
    
    try {
      // Validate callback URL format and origin
      if (!this.validateCallbackUrl(url)) {
        throw new Error('Invalid or suspicious callback URL');
      }
      
      // Parse callback parameters with error handling
      const urlObj = new URL(url);
      const params = urlObj.searchParams;
      const hashParams = new URLSearchParams(urlObj.hash.substring(1));
      
      // Check for OAuth errors first
      const error = params.get('error') || hashParams.get('error');
      if (error) {
        const errorDescription = params.get('error_description') || hashParams.get('error_description') || 'Unknown OAuth error';
        // Use the raw OAuth error for proper mapping
        throw new Error(error === 'access_denied' ? 'access_denied' : error);
      }
      
      // State validation is handled internally by Supabase's PKCE implementation
      const state = params.get('state') || hashParams.get('state');
      if (state) {
        console.log('[OAuth] State parameter present (handled by Supabase PKCE)');
      }
      
      // Extract authorization code (PKCE flow)
      const code = params.get('code') || hashParams.get('code');
      
      // Check if we have a code (PKCE flow) or tokens (fallback to implicit flow)
      if (code) {
        console.log('[OAuth] Authorization code received, exchanging for tokens...');
        
        // Exchange authorization code for tokens with timeout protection
        const supabase = getSupabaseClient();
        
        // Add timeout protection to prevent hanging requests - increased timeout and added retry
        const exchangePromise = supabase.auth.exchangeCodeForSession(code);
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Code exchange timeout')), 30000) // Increased to 30 seconds
        );
        
        const { data, error: exchangeError } = await Promise.race([
          exchangePromise,
          timeoutPromise
        ]) as any;
        
        if (exchangeError) {
          console.error('[OAuth] Code exchange error (details hidden for security)');
          throw new Error('Authentication failed - please try again');
        }
        
        if (!data?.session) {
          throw new Error('No session returned from code exchange');
        }
        
        console.log('[OAuth] ✅ PKCE code exchange successful');
        console.log('[OAuth] User authenticated:', data.session.user?.email);
        
        // Reset auth loop detection on successful authentication
        this.resetAuthLoopDetection();
        
        // Session is automatically stored by Supabase with our custom storage adapter
        
        // Start session monitoring if auto-logout is enabled
        if (this.isAutoLogoutEnabled()) {
          this.startSessionMonitoring();
        }
        
      } else {
        // Fallback: Check for tokens in hash (implicit flow)
        const accessToken = params.get('access_token') || hashParams.get('access_token');
        const refreshToken = params.get('refresh_token') || hashParams.get('refresh_token');
        
        if (!accessToken || !refreshToken) {
          throw new Error('No authorization code or tokens found in callback');
        }
        
        console.log('[OAuth] Using implicit flow fallback (tokens in URL)');
        
        const supabase = getSupabaseClient();
        const { data, error } = await supabase.auth.setSession({
          access_token: accessToken,
          refresh_token: refreshToken
        });
        
        if (error) {
          throw error;
        }        
        console.log('[OAuth] Implicit flow session established');
        console.log('[OAuth] User:', data.session?.user?.email);
      }
      
      // Handle post-authentication with timeout protection
      console.log('[OAuth] Starting post-authentication handling...');
      const postAuthPromise = this.handlePostAuthentication();
      const postAuthTimeoutPromise = new Promise<AuthResult>((_, reject) => 
        setTimeout(() => reject(new Error('Post-authentication timeout')), 15000) // 15 second timeout
      );
      
      const result = await Promise.race([postAuthPromise, postAuthTimeoutPromise]);
      console.log('[OAuth] Post-authentication completed successfully');
      
      // Resolve the promise
      if (this.authPromise) {
        this.authPromise.resolve(result);
      }
      
      this.cleanup();
      
    } catch (error) {
      console.error('[OAuth] Callback processing error:', error);
      
      // Record auth attempt for loop detection
      const technicalError = error instanceof Error ? error.message : 'Callback processing failed';
      this.recordAuthAttempt(technicalError);
      
      if (this.authPromise) {
        const userFriendlyResult = this.createUserFriendlyAuthResult(false, technicalError, 'callback');
        this.authPromise.resolve(userFriendlyResult);
      }
      
      this.cleanup();
    }
  }
  
  private showAuthNotification(): void {
    this.notificationWindow = new BrowserWindow({
      width: 420,
      height: 280,
      resizable: false,
      minimizable: false,
      maximizable: false,
      alwaysOnTop: true,
      frame: true,
      titleBarStyle: 'default',
      title: 'ChromaSync - Authentication',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            padding: 30px;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 220px;
            box-sizing: border-box;
          }
          .container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 24px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
          }
          h3 {
            margin: 0 0 16px 0;
            font-size: 18px;
            font-weight: 600;
          }          p {
            margin: 8px 0;
            font-size: 14px;
            line-height: 1.5;
            opacity: 0.9;
          }
          .spinner {
            width: 32px;
            height: 32px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top-color: white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
          }
          @keyframes spin {
            to { transform: rotate(360deg); }
          }
          .footer {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 16px;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h3>🔐 Authenticating with Google</h3>
          <p>Please complete the sign-in process in your browser.</p>
          <div class="spinner"></div>
          <p class="footer">This window will close automatically when complete</p>
        </div>
      </body>
      </html>
    `;
    
    this.notificationWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(html)}`);
    
    this.notificationWindow.on('closed', () => {
      this.notificationWindow = null;
    });
  }
  
  private async handlePostAuthentication(): Promise<AuthResult> {
    const user = await this.getCurrentUser();
    if (!user) {
      return this.createUserFriendlyAuthResult(false, 'Failed to get user after authentication', 'session');
    }
    const db = getDatabase();
    
    this.syncUserToLocal(user);
    
    if (!db || typeof db.prepare !== 'function') {
      console.error('[OAuth] Database not available for organization service');
      return this.createUserFriendlyAuthResult(
        true, 
        undefined, 
        undefined, 
        'authenticated', 
        []
      );
    }
    
    const orgService = new OrganizationService(db);
    
    // First sync the current user's profile to local database
    console.log('[OAuth] Syncing current user profile to local database...');
    try {
      await orgService.syncUserProfileToLocal(user.id, user.email, user.user_metadata?.full_name);
    } catch (error) {
      console.error('[OAuth] Failed to sync user profile:', error);
    }
    
    await orgService.syncOrganizationsFromSupabase(user.id);
    
    const organizations = await orgService.getOrganizationsForUser(user.id);
    
    if (organizations.length === 0) {
      return this.createUserFriendlyAuthResult(
        true, 
        undefined, 
        undefined, 
        'needs_organization_setup', 
        []
      );
    } else if (organizations.length === 1) {
      // Single organization - auto-select it
      const currentOrgId = organizations[0].external_id;
      this.store.set('currentOrganizationId', currentOrgId);
      (global as any).currentOrganizationId = currentOrgId;
      
      return this.createUserFriendlyAuthResult(
        true, 
        undefined, 
        undefined, 
        'authenticated', 
        organizations
      );
    } else {
      // Multiple organizations - check if one is already selected
      const storedOrgId = this.store.get('currentOrganizationId') as string;
      const validOrg = organizations.find(org => org.external_id === storedOrgId);
      
      if (validOrg) {
        // Valid stored organization found - auto-authenticate
        (global as any).currentOrganizationId = storedOrgId;
        console.log('[OAuth] Using stored organization:', validOrg.name);
        
        return this.createUserFriendlyAuthResult(
          true, 
          undefined, 
          undefined, 
          'authenticated', 
          organizations
        );
      } else {
        // No valid stored organization - needs selection
        return this.createUserFriendlyAuthResult(
          true, 
          undefined, 
          undefined, 
          'needs_organization_selection', 
          organizations
        );
      }
    }
  }
  
  private syncUserToLocal(user: any): void {
    try {
      const db = getDatabase();
      if (!db || typeof db.prepare !== 'function') {
        console.warn('[OAuth] Database not available, skipping user sync');
        return;
      }      
      const existing = db.prepare('SELECT id FROM users WHERE id = ?').get(user.id);
      
      if (!existing) {
        db.prepare(`
          INSERT INTO users (id, email, name, avatar_url, metadata)
          VALUES (?, ?, ?, ?, ?)
        `).run(
          user.id,
          user.email || '',
          user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
          user.user_metadata?.avatar_url || '',
          JSON.stringify(user.user_metadata || {})
        );
      } else {
        db.prepare(`
          UPDATE users 
          SET email = ?, name = ?, avatar_url = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).run(
          user.email || '',
          user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
          user.user_metadata?.avatar_url || '',
          JSON.stringify(user.user_metadata || {}),
          user.id
        );
      }
    } catch (error) {
      console.error('[OAuth] Failed to sync user to local database:', error);
    }
  }
  
  async getCurrentUser() {
    const supabase = getSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    return user;
  }
  
  async checkGDPRConsent(): Promise<boolean> {
    const supabase = getSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {return false;}
    
    const { data, error } = await supabase
      .from('user_consent')
      .select('*')
      .eq('user_id', user.id)
      .single();
      
    return !!data && !error;
  }  
  async recordGDPRConsent(ip?: string): Promise<void> {
    const supabase = getSupabaseClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {throw new Error('Not authenticated');}
    
    const { error } = await supabase.from('user_consent').upsert({
      user_id: user.id,
      terms_accepted_at: new Date().toISOString(),
      privacy_accepted_at: new Date().toISOString(),
      data_processing_consent: true,
      consent_ip: ip,
      consent_version: '1.0'
    });
    
    if (error) {throw error;}
  }
  
  async signOut(): Promise<void> {
    console.log('[OAuth] Signing out...');
    
    try {
      // Stop session monitoring first
      this.stopSessionMonitoring();
      
      // Clean up any pending auth promises
      if (this.authPromise) {
        this.authPromise.resolve({
          success: false,
          error: 'Sign out initiated'
        });
      }
      
      // Clean up auth resources
      this.cleanup();
      
      // Sign out from Supabase
      const supabase = getSupabaseClient();
      await supabase.auth.signOut();
      
      // Clear all auth-related data but preserve OAuth config
      const oauthConfig = {
        [this.CONFIG_KEYS.AUTH_TIMEOUT]: this.store.get(this.CONFIG_KEYS.AUTH_TIMEOUT),
        [this.CONFIG_KEYS.SESSION_TIMEOUT]: this.store.get(this.CONFIG_KEYS.SESSION_TIMEOUT),
        [this.CONFIG_KEYS.SESSION_WARNING_TIME]: this.store.get(this.CONFIG_KEYS.SESSION_WARNING_TIME),
        [this.CONFIG_KEYS.AUTO_LOGOUT_ENABLED]: this.store.get(this.CONFIG_KEYS.AUTO_LOGOUT_ENABLED)
      };
      
      // Clear store but restore OAuth config
      this.store.clear();
      Object.entries(oauthConfig).forEach(([key, value]) => {
        if (value !== undefined) {
          this.store.set(key, value);
        }
      });
      
      // Clear global organization
      delete (global as any).currentOrganizationId;
      
      // Clear any remaining state
      this.stateStore.clear();
      this.callbackAttempts = 0;
      
      console.log('[OAuth] Sign out completed successfully');
    } catch (error) {
      console.error('[OAuth] Sign out error:', error);
      // Still try to clean up even if error occurs
      this.stopSessionMonitoring();
      this.cleanup();
      throw error;
    }
  }
  
  async getSession() {
    const supabase = getSupabaseClient();
    const { data: { session } } = await supabase.auth.getSession();
    return session;
  }
  
  async restoreSession(): Promise<AuthResult | null> {
    console.log('[OAuth] Attempting to restore session...');
    
    try {
      // Supabase will automatically restore session from our custom storage adapter
      const supabase = getSupabaseClient();
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session && session.user) {
        // Check if session is expired based on configurable timeout
        if (this.isSessionExpired(session)) {
          console.log('[OAuth] Session expired based on configured timeout');
          await this.signOut();
          return null;
        }
        
        console.log('[OAuth] Session restored successfully');
        console.log('[OAuth] User:', session.user.email);        
        const result = await this.handlePostAuthentication();
        
        // Start session monitoring if auto-logout is enabled
        if (this.isAutoLogoutEnabled()) {
          this.startSessionMonitoring();
        }
        
        return result;
      }
      
      console.log('[OAuth] No session to restore');
      return null;
      
    } catch (error) {
      console.error('[OAuth] Session restoration failed:', error);
      return null;
    }
  }

  private cleanup(): void {
    if (this.authTimeout) {
      clearTimeout(this.authTimeout);
      this.authTimeout = null;
    }
    
    if (this.notificationWindow && !this.notificationWindow.isDestroyed()) {
      this.notificationWindow.close();
      this.notificationWindow = null;
    }
    
    if (this.redirectServer) {
      this.redirectServer.stop();
      this.redirectServer = null;
    }
    
    this.authPromise = null;
  }

  /**
   * Get configured authentication timeout
   */
  private getAuthTimeout(): number {
    const configured = this.store.get(this.CONFIG_KEYS.AUTH_TIMEOUT) as number;
    return configured || this.DEFAULT_AUTH_TIMEOUT_MS;
  }
  
  /**
   * Get configured session timeout (in hours)
   */
  private getSessionTimeout(): number {
    const configured = this.store.get(this.CONFIG_KEYS.SESSION_TIMEOUT) as number;
    return configured || 24; // Default 24 hours
  }
  
  /**
   * Get session warning time (minutes before expiry)
   */
  private getSessionWarningTime(): number {
    const configured = this.store.get(this.CONFIG_KEYS.SESSION_WARNING_TIME) as number;
    return configured || 5; // Default 5 minutes warning
  }
  
  /**
   * Check if auto-logout is enabled
   */
  private isAutoLogoutEnabled(): boolean {
    return this.store.get(this.CONFIG_KEYS.AUTO_LOGOUT_ENABLED) !== false; // Default true
  }
  
  /**
   * Check if session is expired based on configured timeout
   */
  private isSessionExpired(session: any): boolean {
    if (!session?.expires_at) return false;
    
    const sessionTimeoutHours = this.getSessionTimeout();
    const sessionTimeoutMs = sessionTimeoutHours * 60 * 60 * 1000;
    
    // Get session creation time from JWT claims or use expires_at
    const sessionStart = session.created_at ? 
      new Date(session.created_at).getTime() : 
      new Date(session.expires_at).getTime() - (60 * 60 * 1000); // 1 hour default session
    
    const now = Date.now();
    const sessionAge = now - sessionStart;
    
    return sessionAge > sessionTimeoutMs;
  }
  
  /**
   * Session monitoring for auto-logout
   */
  private sessionMonitoringInterval: NodeJS.Timeout | null = null;
  private lastActivityTime: number = Date.now();
  private warningShown: boolean = false;
  
  /**
   * Auth loop detection to prevent infinite authentication cycles
   */
  private authAttempts: Array<{ timestamp: number; error?: string }> = [];
  private readonly AUTH_LOOP_WINDOW_MS = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_AUTH_ATTEMPTS = 3;
  private authLoopDetected: boolean = false;
  private authLoopCooldownUntil: number = 0;
  
  /**
   * Start monitoring session for inactivity
   */
  private startSessionMonitoring(): void {
    this.stopSessionMonitoring();
    
    if (!this.isAutoLogoutEnabled()) return;
    
    console.log('[OAuth] Starting session monitoring');
    
    // Reset activity time
    this.lastActivityTime = Date.now();
    this.warningShown = false;
    
    // Monitor every minute
    this.sessionMonitoringInterval = setInterval(() => {
      this.checkSessionActivity();
    }, 60000);
  }
  
  /**
   * Stop session monitoring
   */
  private stopSessionMonitoring(): void {
    if (this.sessionMonitoringInterval) {
      clearInterval(this.sessionMonitoringInterval);
      this.sessionMonitoringInterval = null;
    }
  }
  
  /**
   * Check session activity and handle timeout
   */
  private async checkSessionActivity(): Promise<void> {
    const sessionTimeoutHours = this.getSessionTimeout();
    const sessionTimeoutMs = sessionTimeoutHours * 60 * 60 * 1000;
    const warningTimeMs = this.getSessionWarningTime() * 60 * 1000;
    
    const inactiveTime = Date.now() - this.lastActivityTime;
    const timeUntilLogout = sessionTimeoutMs - inactiveTime;
    
    if (timeUntilLogout <= 0) {
      // Session expired - auto logout
      console.log('[OAuth] Session expired due to inactivity');
      await this.signOut();
      
      // Notify renderer process
      BrowserWindow.getAllWindows().forEach(window => {
        window.webContents.send('auth:session-expired', {
          reason: 'inactivity',
          timeout: sessionTimeoutHours
        });
      });
    } else if (timeUntilLogout <= warningTimeMs && !this.warningShown) {
      // Show warning
      this.warningShown = true;
      console.log('[OAuth] Session expiring soon');
      
      // Notify renderer process
      BrowserWindow.getAllWindows().forEach(window => {
        window.webContents.send('auth:session-warning', {
          minutesRemaining: Math.ceil(timeUntilLogout / 60000)
        });
      });
    }
  }
  
  /**
   * Update last activity time (call from IPC handlers)
   */
  updateActivity(): void {
    this.lastActivityTime = Date.now();
    this.warningShown = false; // Reset warning when activity detected
  }
  
  /**
   * Record an authentication attempt for loop detection
   */
  private recordAuthAttempt(error?: string): void {
    const now = Date.now();
    
    // Clean up old attempts outside the window
    this.authAttempts = this.authAttempts.filter(
      attempt => now - attempt.timestamp < this.AUTH_LOOP_WINDOW_MS
    );
    
    // Add new attempt
    this.authAttempts.push({ timestamp: now, error });
    
    console.log(`[OAuth] Recorded auth attempt. Total in window: ${this.authAttempts.length}`);
    
    // Check for auth loop
    if (this.authAttempts.length >= this.MAX_AUTH_ATTEMPTS) {
      this.detectAuthLoop();
    }
  }
  
  /**
   * Detect and handle authentication loops
   */
  private detectAuthLoop(): void {
    console.warn('[OAuth] Authentication loop detected! Too many failed attempts in short period');
    
    this.authLoopDetected = true;
    this.authLoopCooldownUntil = Date.now() + (15 * 60 * 1000); // 15 minute cooldown
    
    // Clear auth attempts to reset detection
    this.authAttempts = [];
    
    // Notify renderer about auth loop
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send('auth:loop-detected', {
        cooldownMinutes: 15,
        cooldownUntil: this.authLoopCooldownUntil,
        attempts: this.MAX_AUTH_ATTEMPTS,
        windowMinutes: this.AUTH_LOOP_WINDOW_MS / (60 * 1000)
      });
    });
  }
  
  /**
   * Check if auth loop cooldown is active
   */
  private isAuthLoopCooldownActive(): boolean {
    if (!this.authLoopDetected) return false;
    
    const now = Date.now();
    if (now > this.authLoopCooldownUntil) {
      // Cooldown expired - reset
      this.authLoopDetected = false;
      this.authLoopCooldownUntil = 0;
      console.log('[OAuth] Auth loop cooldown expired - authentication re-enabled');
      return false;
    }
    
    return true;
  }
  
  /**
   * Get remaining cooldown time in minutes
   */
  private getAuthLoopCooldownRemaining(): number {
    if (!this.isAuthLoopCooldownActive()) return 0;
    
    const remaining = this.authLoopCooldownUntil - Date.now();
    return Math.ceil(remaining / (60 * 1000));
  }
  
  /**
   * Reset auth loop detection (for manual recovery)
   */
  resetAuthLoopDetection(): void {
    console.log('[OAuth] Manually resetting auth loop detection');
    this.authLoopDetected = false;
    this.authLoopCooldownUntil = 0;
    this.authAttempts = [];
    
    // Notify renderer that auth loop has been reset
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send('auth:loop-reset');
    });
  }
  
  /**
   * Attempt automatic recovery from auth loop
   */
  async attemptAuthRecovery(): Promise<{ success: boolean; message: string }> {
    console.log('[OAuth] Attempting automatic auth recovery...');
    
    try {
      // Step 1: Clear all auth-related storage
      await this.clearAuthStorage();
      
      // Step 2: Reset auth loop detection
      this.resetAuthLoopDetection();
      
      // Step 3: Clear any cached sessions
      await this.clearSupabaseSession();
      
      // Step 4: Reset redirect server if running
      if (this.redirectServer) {
        this.redirectServer.stop();
        this.redirectServer = null;
      }
      
      // Step 5: Clean up any auth resources
      this.cleanup();
      
      console.log('[OAuth] Auth recovery completed successfully');
      
      return {
        success: true,
        message: 'Authentication system has been reset. Please try signing in again.'
      };
      
    } catch (error) {
      console.error('[OAuth] Auth recovery failed:', error);
      
      return {
        success: false,
        message: 'Failed to recover authentication system. Please restart the application.'
      };
    }
  }
  
  /**
   * Clear all authentication-related storage
   */
  private async clearAuthStorage(): Promise<void> {
    try {
      // Clear electron-store auth data but preserve OAuth config
      const oauthConfig = {
        [this.CONFIG_KEYS.AUTH_TIMEOUT]: this.store.get(this.CONFIG_KEYS.AUTH_TIMEOUT),
        [this.CONFIG_KEYS.SESSION_TIMEOUT]: this.store.get(this.CONFIG_KEYS.SESSION_TIMEOUT),
        [this.CONFIG_KEYS.SESSION_WARNING_TIME]: this.store.get(this.CONFIG_KEYS.SESSION_WARNING_TIME),
        [this.CONFIG_KEYS.AUTO_LOGOUT_ENABLED]: this.store.get(this.CONFIG_KEYS.AUTO_LOGOUT_ENABLED)
      };
      
      // Clear all storage
      this.store.clear();
      
      // Restore OAuth config
      Object.entries(oauthConfig).forEach(([key, value]) => {
        if (value !== undefined) {
          this.store.set(key, value);
        }
      });
      
      // Clear global organization
      delete (global as any).currentOrganizationId;
      
      console.log('[OAuth] Auth storage cleared');
    } catch (error) {
      console.error('[OAuth] Failed to clear auth storage:', error);
      throw error;
    }
  }
  
  /**
   * Clear Supabase session
   */
  private async clearSupabaseSession(): Promise<void> {
    try {
      const supabase = getSupabaseClient();
      await supabase.auth.signOut();
      console.log('[OAuth] Supabase session cleared');
    } catch (error) {
      console.error('[OAuth] Failed to clear Supabase session:', error);
      // Don't throw - this is not critical for recovery
    }
  }
  
  /**
   * Check if recovery is needed based on auth state
   */
  async checkAuthHealth(): Promise<{ healthy: boolean; issues: string[] }> {
    const issues: string[] = [];
    
    try {
      // Check 1: Auth loop cooldown
      if (this.isAuthLoopCooldownActive()) {
        issues.push(`Auth loop cooldown active (${this.getAuthLoopCooldownRemaining()} minutes remaining)`);
      }
      
      // Check 2: Recent auth failures
      const recentFailures = this.authAttempts.length;
      if (recentFailures > 0) {
        issues.push(`${recentFailures} recent auth failures in the past ${this.AUTH_LOOP_WINDOW_MS / 60000} minutes`);
      }
      
      // Check 3: Session state
      const session = await this.getSession();
      if (!session && this.store.get('chromasync-auth-session')) {
        issues.push('Session storage exists but no active session');
      }
      
      // Check 4: Redirect server conflicts
      if (this.redirectServer) {
        issues.push('Redirect server still running from previous auth attempt');
      }
      
      return {
        healthy: issues.length === 0,
        issues
      };
      
    } catch (error) {
      issues.push(`Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return { healthy: false, issues };
    }
  }
  
  /**
   * Map technical OAuth errors to user-friendly messages
   */
  private mapAuthErrorToUserMessage(error: string, context?: string): string {
    const errorLower = error.toLowerCase();
    
    // OAuth-specific errors
    if (errorLower.includes('access_denied') || errorLower.includes('user cancelled')) {
      return 'Sign-in was cancelled. Please try again to continue.';
    }
    
    if (errorLower.includes('invalid_client')) {
      return 'There\'s a configuration issue with ChromaSync. Please contact support if this persists.';
    }
    
    if (errorLower.includes('invalid_grant') || errorLower.includes('invalid_request')) {
      return 'The sign-in request expired. Please try signing in again.';
    }
    
    if (errorLower.includes('temporarily_unavailable') || errorLower.includes('server_error')) {
      return 'Google\'s sign-in service is temporarily unavailable. Please try again in a few minutes.';
    }
    
    // Network-related errors
    if (errorLower.includes('network') || errorLower.includes('fetch')) {
      return 'Unable to connect to sign-in service. Please check your internet connection and try again.';
    }
    
    if (errorLower.includes('timeout') || errorLower.includes('time')) {
      return 'Sign-in is taking longer than expected. Please check your connection and try again.';
    }
    
    if (errorLower.includes('failed to open browser')) {
      return 'Unable to open your web browser for sign-in. Please ensure you have a default browser set.';
    }
    
    // Authentication state errors
    if (errorLower.includes('no session') || errorLower.includes('no user')) {
      return 'Sign-in was unsuccessful. Please try again.';
    }
    
    if (errorLower.includes('session expired') || errorLower.includes('token expired')) {
      return 'Your session has expired. Please sign in again to continue.';
    }
    
    // Organization-related errors
    if (errorLower.includes('organization') && errorLower.includes('setup')) {
      return 'Your account needs to be set up with an organization. We\'ll help you create one.';
    }
    
    if (errorLower.includes('organization') && errorLower.includes('selection')) {
      return 'Please select which organization you\'d like to work with.';
    }
    
    // Database/sync errors
    if (errorLower.includes('database') || errorLower.includes('sync')) {
      return 'There was an issue connecting to your data. Please try signing in again.';
    }
    
    // GDPR consent errors
    if (errorLower.includes('gdpr') || errorLower.includes('consent')) {
      return 'Please accept the terms and privacy policy to continue using ChromaSync.';
    }
    
    // Callback/redirect errors
    if (errorLower.includes('callback') || errorLower.includes('redirect')) {
      return 'There was an issue completing sign-in. Please try again.';
    }
    
    // Rate limiting / auth loop errors
    if (errorLower.includes('too many') || errorLower.includes('rate limit') || errorLower.includes('temporarily disabled')) {
      return error; // These already have user-friendly messages
    }
    
    // Generic errors based on context
    if (context === 'initialization') {
      return 'Unable to start the sign-in process. Please try again or restart ChromaSync.';
    }
    
    if (context === 'callback') {
      return 'There was an issue completing your sign-in. Please try again.';
    }
    
    if (context === 'session') {
      return 'Your session could not be restored. Please sign in again.';
    }
    
    // Default fallback
    return 'An unexpected error occurred during sign-in. Please try again or contact support if this persists.';
  }
  
  /**
   * Create a user-friendly auth result with mapped error messages
   */
  private createUserFriendlyAuthResult(
    success: boolean, 
    technicalError?: string, 
    context?: string,
    status?: string,
    organizations?: any[]
  ): AuthResult {
    if (success) {
      return {
        success: true,
        status: status as any,
        organizations: organizations || []
      };
    }
    
    const userFriendlyError = technicalError ? 
      this.mapAuthErrorToUserMessage(technicalError, context) : 
      'An unexpected error occurred during sign-in.';
    
    // Log technical error for debugging
    if (technicalError && technicalError !== userFriendlyError) {
      console.error(`[OAuth] Technical error (${context}):`, technicalError);
    }
    
    return {
      success: false,
      error: userFriendlyError
    };
  }
  
  /**
   * Configure OAuth settings
   */
  configureOAuth(settings: {
    authTimeout?: number;
    sessionTimeout?: number;
    sessionWarningTime?: number;
    autoLogoutEnabled?: boolean;
  }): void {
    if (settings.authTimeout !== undefined) {
      this.store.set(this.CONFIG_KEYS.AUTH_TIMEOUT, settings.authTimeout);
    }
    if (settings.sessionTimeout !== undefined) {
      this.store.set(this.CONFIG_KEYS.SESSION_TIMEOUT, settings.sessionTimeout);
    }
    if (settings.sessionWarningTime !== undefined) {
      this.store.set(this.CONFIG_KEYS.SESSION_WARNING_TIME, settings.sessionWarningTime);
    }
    if (settings.autoLogoutEnabled !== undefined) {
      this.store.set(this.CONFIG_KEYS.AUTO_LOGOUT_ENABLED, settings.autoLogoutEnabled);
      
      // Start or stop monitoring based on setting
      if (settings.autoLogoutEnabled) {
        this.startSessionMonitoring();
      } else {
        this.stopSessionMonitoring();
      }
    }
    
    console.log('[OAuth] Configuration updated:', settings);
  }
  
  /**
   * Get current OAuth configuration
   */
  getConfiguration(): {
    authTimeout: number;
    sessionTimeout: number;
    sessionWarningTime: number;
    autoLogoutEnabled: boolean;
  } {
    return {
      authTimeout: this.getAuthTimeout(),
      sessionTimeout: this.getSessionTimeout(),
      sessionWarningTime: this.getSessionWarningTime(),
      autoLogoutEnabled: this.isAutoLogoutEnabled()
    };
  }
}

export const oauthService = new OAuthService();