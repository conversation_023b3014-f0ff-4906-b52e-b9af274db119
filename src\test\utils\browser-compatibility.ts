import { Browser, BrowserType, chromium, firefox, webkit, Page, BrowserContext } from 'playwright';

export interface BrowserTestOptions {
  url?: string;
  viewportSize?: { width: number; height: number };
  deviceScaleFactor?: number;
  timeout?: number;
}

export type BrowserName = 'chrome' | 'firefox' | 'safari' | 'edge';

const DEFAULT_OPTIONS: BrowserTestOptions = {
  url: 'http://localhost:5173',
  viewportSize: { width: 1280, height: 720 },
  deviceScaleFactor: 1,
  timeout: 30000,
};

export class BrowserCompatibilityTester {
  private browsers: Map<BrowserName, BrowserType> = new Map();
  private activeBrowser: Browser | null = null;
  private activeContext: BrowserContext | null = null;
  private activePage: Page | null = null;
  
  constructor() {
    this.browsers.set('chrome', chromium);
    this.browsers.set('firefox', firefox);
    this.browsers.set('safari', webkit);
    // Edge uses Chromium under the hood
    this.browsers.set('edge', chromium);
  }

  async setup(browserName: BrowserName, options: BrowserTestOptions = {}): Promise<Page> {
    const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
    const browserType = this.browsers.get(browserName);
    
    if (!browserType) {
      throw new Error(`Unsupported browser: ${browserName}`);
    }

    // Launch browser with appropriate flags
    const launchOptions: any = {};
    
    if (browserName === 'edge') {
      launchOptions.channel = 'msedge';
    }

    this.activeBrowser = await browserType.launch(launchOptions);
    
    this.activeContext = await this.activeBrowser.newContext({
      viewport: mergedOptions.viewportSize,
      deviceScaleFactor: mergedOptions.deviceScaleFactor,
    });
    
    this.activePage = await this.activeContext.newPage();
    await this.activePage.goto(mergedOptions.url!, { timeout: mergedOptions.timeout });
    
    return this.activePage;
  }

  async takeScreenshot(name: string): Promise<Buffer | null> {
    if (!this.activePage) {
      throw new Error('No active page. Call setup() first.');
    }
    return this.activePage.screenshot({ path: `screenshots/${name}.png` });
  }

  async executeTest(testFn: (page: Page) => Promise<void>): Promise<void> {
    if (!this.activePage) {
      throw new Error('No active page. Call setup() first.');
    }
    await testFn(this.activePage);
  }

  async teardown(): Promise<void> {
    if (this.activeContext) {
      await this.activeContext.close();
      this.activeContext = null;
    }
    
    if (this.activeBrowser) {
      await this.activeBrowser.close();
      this.activeBrowser = null;
    }
    
    this.activePage = null;
  }
}

export default BrowserCompatibilityTester; 