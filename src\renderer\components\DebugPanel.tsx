/**
 * @file DebugPanel.tsx
 * @description Debug panel for development purposes with token system toggle
 */

import { useState, useEffect } from 'react';
import { checkColorAPI, testIPC } from '../utils/debugTools';
import { useColorStore } from '../store/color.store';
import { useFeatureFlags } from '../context/FeatureFlagContext';
import { useTokens } from '../hooks/useTokens';

interface DebugInfo {
  apiStatus: ReturnType<typeof checkColorAPI>;
  ipcStatus: { success: boolean; message: string } | null;
}

export default function DebugPanel() {
  const [isVisible, setIsVisible] = useState(false);
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({
    apiStatus: { 
      available: false, 
      availableMethods: [], 
      importAvailable: false, 
      exportAvailable: false, 
      diagnosticInfo: {
        electron: 'unknown',
        platform: 'unknown',
        nodeVersion: 'unknown'
      }
    },
    ipcStatus: null
  });
  
  const { importColors, exportColors, colors } = useColorStore();
  const [lastActionResult, setLastActionResult] = useState<{success: boolean; message: string} | null>(null);
  const { useNewTokenSystem, toggleTokenSystem } = useFeatureFlags();
  const tokens = useTokens();
  
  useEffect(() => {
    // Run initial tests
    if (isVisible) {
      runDiagnostics();
    }
  }, [isVisible]);
  
  const runDiagnostics = async () => {
    // Check colorAPI status
    const apiStatus = checkColorAPI();
    
    // Test IPC
    const ipcStatus = await testIPC();
    
    setDebugInfo({
      apiStatus,
      ipcStatus
    });
  };
  
  const testImport = async () => {
    try {
      const result = await importColors();
      setLastActionResult(result);
    } catch (error) {
      setLastActionResult({ 
        success: false, 
        message: error instanceof Error ? error.message : String(error)
      });
    }
  };
  
  const testExport = async () => {
    try {
      const result = await exportColors();
      setLastActionResult(result);
    } catch (error) {
      setLastActionResult({ 
        success: false, 
        message: error instanceof Error ? error.message : String(error)
      });
    }
  };
  
  const testDirectIPC = async () => {
    try {
      if (!window.ipc) {
        setLastActionResult({ success: false, message: 'IPC not available' });
        return;
      }
      
      // Try import directly via IPC
      const result = await window.ipc.invoke('color:import');
      setLastActionResult({ 
        success: true, 
        message: `Direct IPC result: ${JSON.stringify(result)}`
      });
    } catch (error) {
      setLastActionResult({ 
        success: false, 
        message: `Direct IPC error: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  };
  
  const panelClassName = "bg-[var(--color-ui-background-secondary)] p-[var(--spacing-4)] rounded-[var(--radius-md)] mb-[var(--spacing-6)] text-[var(--color-ui-foreground-primary)]";
  const sectionTitleClassName = "text-[var(--font-size-sm)] font-[var(--font-weight-semibold)] mb-[var(--spacing-2)]";
  const toggleClassName = "flex items-center space-x-[var(--spacing-2)] mb-[var(--spacing-4)]";
  const buttonClassName = "px-[var(--spacing-3)] py-[var(--spacing-1)] text-[var(--font-size-xs)] bg-[var(--color-brand-secondary)] text-[var(--color-ui-foreground-inverse)] rounded-[var(--radius-md)]";
  
  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4">
        <button 
          onClick={() => setIsVisible(true)}
          className="bg-gray-800 text-white px-3 py-2 rounded-md hover:bg-gray-700 transition-colors"
        >
          Debug
        </button>
      </div>
    );
  }
  
  return (
    <div className={panelClassName}>
      <div className="flex justify-between items-center mb-4">
        <h2 className={sectionTitleClassName}>Debug Panel</h2>
        <button 
          onClick={() => setIsVisible(false)}
          className="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
        >
          Close
        </button>
      </div>
      
      <div className={toggleClassName}>
        <span className="text-[var(--font-size-sm)]">
          Token System:
        </span>
        <button 
          onClick={toggleTokenSystem}
          className={buttonClassName}
          style={{
            transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
          }}
        >
          {useNewTokenSystem ? 'Enabled' : 'Disabled'}
        </button>
      </div>
      
      <div className="mb-[var(--spacing-4)]">
        <h4 className={sectionTitleClassName}>App State</h4>
        <pre className="text-[var(--font-size-xs)] bg-[var(--color-ui-background-tertiary)] p-[var(--spacing-2)] rounded-[var(--radius-sm)] overflow-auto max-h-40">
          {JSON.stringify({
            colorsCount: colors.length,
            currentTheme: tokens.theme,
            tokenSystemEnabled: useNewTokenSystem
          }, null, 2)}
        </pre>
      </div>
      
      <div className="space-y-4">
        <div>
          <h3 className="text-md font-medium mb-2">API Status</h3>
          <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm">
            <div>Available: <span className={debugInfo.apiStatus.available ? "text-green-600" : "text-red-600"}>{String(debugInfo.apiStatus.available)}</span></div>
            <div>Import Available: <span className={debugInfo.apiStatus.importAvailable ? "text-green-600" : "text-red-600"}>{String(debugInfo.apiStatus.importAvailable)}</span></div>
            <div>Export Available: <span className={debugInfo.apiStatus.exportAvailable ? "text-green-600" : "text-red-600"}>{String(debugInfo.apiStatus.exportAvailable)}</span></div>
            <div>Methods: {debugInfo.apiStatus.availableMethods.join(', ')}</div>
            <div className="mt-1">
              <div>Electron: {debugInfo.apiStatus.diagnosticInfo.electron}</div>
              <div>Platform: {debugInfo.apiStatus.diagnosticInfo.platform}</div>
              <div>Node: {debugInfo.apiStatus.diagnosticInfo.nodeVersion}</div>
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-md font-medium mb-2">IPC Status</h3>
          <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm">
            {debugInfo.ipcStatus ? (
              <div>
                <div>Status: <span className={debugInfo.ipcStatus.success ? "text-green-600" : "text-red-600"}>{debugInfo.ipcStatus.success ? "Working" : "Failed"}</span></div>
                <div>Message: {debugInfo.ipcStatus.message}</div>
              </div>
            ) : (
              <div>Not tested yet</div>
            )}
          </div>
        </div>
        
        <div>
          <h3 className="text-md font-medium mb-2">Last Action Result</h3>
          <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-sm">
            {lastActionResult ? (
              <div>
                <div>Success: <span className={lastActionResult.success ? "text-green-600" : "text-red-600"}>{String(lastActionResult.success)}</span></div>
                <div>Message: {lastActionResult.message}</div>
              </div>
            ) : (
              <div>No action run yet</div>
            )}
          </div>
        </div>
        
        <div className="flex flex-wrap gap-2">
          <button 
            onClick={runDiagnostics}
            className="bg-blue-600 text-white px-3 py-1.5 rounded hover:bg-blue-700 transition-colors text-sm"
          >
            Run Diagnostics
          </button>
          <button 
            onClick={testImport}
            className="bg-green-600 text-white px-3 py-1.5 rounded hover:bg-green-700 transition-colors text-sm"
          >
            Test Import
          </button>
          <button 
            onClick={testExport}
            className="bg-purple-600 text-white px-3 py-1.5 rounded hover:bg-purple-700 transition-colors text-sm"
          >
            Test Export
          </button>
          <button 
            onClick={testDirectIPC}
            className="bg-orange-600 text-white px-3 py-1.5 rounded hover:bg-orange-700 transition-colors text-sm"
          >
            Test Direct IPC
          </button>
        </div>
      </div>
    </div>
  );
} 