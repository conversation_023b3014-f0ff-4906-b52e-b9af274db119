-- ChromaSync Database Schema
-- SQLite database for color management

-- Enable foreign keys
PRAGMA foreign_keys = ON;

-- Products table
CREATE TABLE IF NOT EXISTS products (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    createdAt TEXT NOT NULL DEFAULT (datetime('now')),
    updatedAt TEXT NOT NULL DEFAULT (datetime('now')),
    createdBy TEXT,
    updatedBy TEXT
);

-- Colors table (legacy schema as per CLAUDE.md)
CREATE TABLE IF NOT EXISTS colors (
    id TEXT PRIMARY KEY,
    product TEXT,
    uniqueId TEXT,
    colourCode TEXT NOT NULL,
    hex TEXT NOT NULL,
    cmyk TEXT,
    notes TEXT,
    gradient TEXT,
    is_library INTEGER DEFAULT 0,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now'))
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_colors_product ON colors(product);
CREATE INDEX IF NOT EXISTS idx_colors_pantone ON colors(colourCode);
CREATE INDEX IF NOT EXISTS idx_colors_hex ON colors(hex);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);

-- Triggers to update timestamps
CREATE TRIGGER IF NOT EXISTS update_colors_timestamp 
AFTER UPDATE ON colors
BEGIN
    UPDATE colors SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_products_timestamp 
AFTER UPDATE ON products
BEGIN
    UPDATE products SET updatedAt = datetime('now') WHERE id = NEW.id;
END;