# Sync Foreign Key Constraint Fix

## Problem Identified
The sync is failing with `FOREIGN KEY constraint failed` errors when trying to insert colors into the local SQLite database. The colors reference organization ID `4047153f-7be8-490b-9cb2-a1e3ed04b92b` which doesn't exist in the local `organizations` table.

## Root Cause
The sync process was attempting to sync colors before ensuring the organization exists locally. The foreign key constraint on the `colors` table prevents insertion of colors that reference non-existent organizations.

## Fixes Applied

### 1. Sync Handler Fix (`src/main/ipc/sync-handlers.ts`)
- Modified both sync initialize and manual sync handlers
- Added organization sync before color/product sync
- Ensures organizations exist locally before attempting to sync their data

**Key Changes:**
- Lines 329-333: Added organization sync before initializing sync service
- Lines 251-257: Added organization sync before manual sync operations

### 2. AppInitializer Fix (`src/renderer/components/AppInitializer.tsx`)
- Modified to trigger sync when no organizations are found locally
- Improved fallback handling for organization selection

**Key Changes:**
- Lines 266-283: Added automatic sync trigger when no organizations exist locally

## Expected Results After Fix

1. **Organization Sync First**: Organizations will be synced from Supabase before colors
2. **No More Foreign Key Errors**: Colors can now reference existing organizations
3. **Proper Data Display**: All 451 colors and 21 products should appear in the UI
4. **Organization Selection**: "IVG" organization should be selectable in the header

## Testing Steps

1. Clear local database (or use organization reset in Settings → Advanced)
2. Sign in with OAuth
3. Wait for automatic sync to complete
4. Verify organization appears in header switcher
5. Verify colors appear in the main table (should show 451 colors)

## Technical Details

- The organization `4047153f-7be8-490b-9cb2-a1e3ed04b92b` will be created locally from Supabase data
- User will be added as a member of this organization
- Colors can then be inserted successfully with proper foreign key references

## Backup Manual Fix

If the automatic fix doesn't work, the manual SQL fix in `SYNC_ORGANIZATION_FIX.md` can be applied to create the organization directly in the database.

## Files Modified

1. `src/main/ipc/sync-handlers.ts` - Added organization sync before data sync
2. `src/renderer/components/AppInitializer.tsx` - Added fallback sync trigger

## Build Status

✅ Application builds successfully with no blocking errors
⚠️ Some dynamic import warnings (non-blocking)