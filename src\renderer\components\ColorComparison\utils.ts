/**
 * @file utils.ts
 * @description Shared utility functions for the ColorComparison components
 */

import { 
  parseCMYK,
  hexToRgb,
  rgbToHex,
  rgbToHsl,
  hslToRgb,
  calculateContrastRatio,
  getWcagCompliance
} from '../../../shared/utils/color';

/**
 * ===================================
 * ACCESSIBILITY UTILITIES
 * ===================================
 */

/**
 * Get the contrast ratio between two colors
 * Returns a value from 1 to 21 (1:1 to 21:1)
 */
export const getContrastRatio = (hexColor1: string, hexColor2: string): number => {
  const rgb1 = hexToRgb(hexColor1);
  const rgb2 = hexToRgb(hexColor2);
  
  if (!rgb1 || !rgb2) {
    return 1;
  }
  
  return calculateContrastRatio(rgb1, rgb2);
};

/**
 * Check if a color combination meets WCAG AA standards
 */
export const meetsWCAGStandards = (
  contrast: number,
  level: 'AA' | 'AAA' = 'AA',
  textSize: 'normal' | 'large' = 'normal'
): boolean => {
  const compliance = getWcagCompliance(contrast);
  return compliance[level.toLowerCase() as 'aa' | 'aaa'][textSize];
};

/**
 * Get WCAG level for a contrast ratio
 */
export const getWCAGLevel = (contrast: number): string => {
  if (contrast >= 7) {return 'AAA';}
  if (contrast >= 4.5) {return 'AA';}
  if (contrast >= 3) {return 'AA Large';}
  return 'Fail';
};

/**
 * ===================================
 * COLOR HARMONY UTILITIES
 * ===================================
 */

/**
 * Normalize a hue value to be between 0 and 360
 */
export const normalizeHue = (hue: number): number => {
  return ((hue % 360) + 360) % 360;
};

/**
 * Generate harmonious colors based on the selected harmony type
 */
export const generateHarmoniousColors = (
  baseHex: string,
  harmonyType: string
): string[] => {
  const rgb = hexToRgb(baseHex);
  if (!rgb) {return [baseHex];}
  
  const hsl = rgbToHsl(rgb);
  const colors: string[] = [baseHex];
  
  switch (harmonyType) {
    case 'complementary':
      colors.push(rgbToHex(hslToRgb({
        h: normalizeHue(hsl.h + 180),
        s: hsl.s,
        l: hsl.l
      })));
      break;
      
    case 'analogous':
      colors.push(
        rgbToHex(hslToRgb({ h: normalizeHue(hsl.h + 30), s: hsl.s, l: hsl.l })),
        rgbToHex(hslToRgb({ h: normalizeHue(hsl.h - 30), s: hsl.s, l: hsl.l }))
      );
      break;
      
    case 'triadic':
      colors.push(
        rgbToHex(hslToRgb({ h: normalizeHue(hsl.h + 120), s: hsl.s, l: hsl.l })),
        rgbToHex(hslToRgb({ h: normalizeHue(hsl.h + 240), s: hsl.s, l: hsl.l }))
      );
      break;
      
    case 'tetradic':
      colors.push(
        rgbToHex(hslToRgb({ h: normalizeHue(hsl.h + 90), s: hsl.s, l: hsl.l })),
        rgbToHex(hslToRgb({ h: normalizeHue(hsl.h + 180), s: hsl.s, l: hsl.l })),
        rgbToHex(hslToRgb({ h: normalizeHue(hsl.h + 270), s: hsl.s, l: hsl.l }))
      );
      break;
      
    case 'split-complementary':
      colors.push(
        rgbToHex(hslToRgb({ h: normalizeHue(hsl.h + 150), s: hsl.s, l: hsl.l })),
        rgbToHex(hslToRgb({ h: normalizeHue(hsl.h + 210), s: hsl.s, l: hsl.l }))
      );
      break;
      
    case 'monochromatic':
      colors.push(
        rgbToHex(hslToRgb({ h: hsl.h, s: hsl.s, l: Math.min(90, hsl.l + 20) })),
        rgbToHex(hslToRgb({ h: hsl.h, s: hsl.s, l: Math.max(10, hsl.l - 20) })),
        rgbToHex(hslToRgb({ h: hsl.h, s: Math.max(10, hsl.s - 30), l: hsl.l }))
      );
      break;
  }
  
  return colors;
};

/**
 * Get a readable name for a harmony type
 */
export const getHarmonyName = (harmonyType: string): string => {
  const names: Record<string, string> = {
    'complementary': 'Complementary',
    'analogous': 'Analogous',
    'triadic': 'Triadic',
    'tetradic': 'Square',
    'split-complementary': 'Split Complementary',
    'monochromatic': 'Monochromatic'
  };
  return names[harmonyType] || harmonyType;
};

/**
 * Format CMYK for display
 */
export const formatCmykForDisplay = (cmyk: string): string => {
  try {
    const values = parseCMYK(cmyk);
    return `C:${values.c} M:${values.m} Y:${values.y} K:${values.k}`;
  } catch {
    return cmyk;
  }
};

/**
 * Calculate ink coverage percentage
 */
export const calculateInkCoverage = (cmyk: string): number => {
  try {
    const values = parseCMYK(cmyk);
    return values.c + values.m + values.y + values.k;
  } catch {
    return 0;
  }
};

/**
 * Get the dominant ink color
 */
export const getDominantInk = (cmyk: string): string => {
  try {
    const values = parseCMYK(cmyk);
    const inks = [
      { name: 'Cyan', value: values.c },
      { name: 'Magenta', value: values.m },
      { name: 'Yellow', value: values.y },
      { name: 'Black', value: values.k }
    ];
    
    const dominant = inks.reduce((max, ink) => 
      ink.value > max.value ? ink : max
    );
    
    return dominant.name;
  } catch {
    return 'Unknown';
  }
};