/**
 * @file electron-store-patched.ts
 * @description Patched version of electron-store to handle corrupted files
 */

import Store, { Options } from 'electron-store';
import fs from 'fs';
import path from 'path';

class PatchedStore<T extends Record<string, unknown>> extends Store<T> {
  constructor(options: Options<T>) {
    let handled = false;
    
    try {
      super(options);
      return; // Success, no need to continue
    } catch (error: any) {
      console.error('Error initializing store:', error.message);

      // If the error is related to parsing JSON, try to fix it
      if (error.message.includes('JSON')) {
        handled = true;
        console.log('Attempting to fix corrupted store file...');

        // Get the store file path
        let storePath;
        try {
          // Handle potential undefined values safely
          const cwd = options.cwd || process.cwd();
          const filename = `${options.name || 'config'}.json`;

          if (typeof cwd === 'string' && typeof filename === 'string') {
            storePath = path.join(cwd, filename);
            console.log(`Calculated store path: ${storePath}`);
          } else {
            console.error('Invalid cwd or filename:', { cwd, filename });
            // Create a default path in the app data directory
            const appData = process.env.APPDATA ||
              (process.platform === 'darwin' ? path.join(process.env.HOME || '', 'Library', 'Application Support') :
              path.join(process.env.HOME || '', '.config'));
            storePath = path.join(appData, 'chroma-sync', 'config.json');
            console.log(`Using fallback store path: ${storePath}`);
          }
        } catch (pathError) {
          console.error('Error calculating store path:', pathError);
          // Re-throw to be caught by outer catch block
          throw new Error('Failed to calculate store path');
        }

        // Check if the file exists (only if storePath is defined)
        if (storePath && typeof storePath === 'string' && fs.existsSync(storePath)) {
          console.log(`Found store file at ${storePath}`);

          try {
            // Backup the corrupted file
            const backupPath = `${storePath}.backup.${Date.now()}`;
            fs.copyFileSync(storePath, backupPath);
            console.log(`Backed up corrupted file to ${backupPath}`);

            // Delete the corrupted file
            fs.unlinkSync(storePath);
            console.log(`Deleted corrupted file at ${storePath}`);
          } catch (fileError) {
            console.error('Error handling corrupted file:', fileError);
            // Continue anyway and try to create a new store
          }
        } else {
          console.log('No existing store file found or invalid path');
        }

      } else {
        throw error;
      }
    }
    
    // If we reach here and the error was handled, call super() with fresh store
    if (handled) {
      super(options);
    } else {
      // Re-throw the error since we couldn't handle it
      throw new Error('Failed to initialize store');
    }
  }
}

export default PatchedStore;
