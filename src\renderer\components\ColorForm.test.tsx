/**
 * @file ColorForm.test.tsx
 * @description Test suite for the ColorForm component
 */

import { describe, test, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import ColorForm from './ColorForm';
import { useColorStore } from '../store/color.store';
import { useFeatureFlags } from '../context/FeatureFlagContext';
import { useTokens } from '../hooks/useTokens';

// Mock the store
vi.mock('../store/color.store');

// Mock the FeatureFlagContext
vi.mock('../context/FeatureFlagContext');

// Mock the useTokens hook
vi.mock('../hooks/useTokens');

describe('ColorForm Component', () => {
  beforeEach(() => {
    // Reset mocks before each test
    vi.resetAllMocks();
    
    // Mock color store implementation
    vi.mocked(useColorStore).mockReturnValue({
      addColor: vi.fn(),
      updateColor: vi.fn(),
      selectedColor: null,
      clearSelectedColor: vi.fn(),
      colors: [],
      setSearchQuery: vi.fn(),
      searchQuery: '',
      filteredColors: [],
      viewMode: 'table',
      setViewMode: vi.fn(),
      fetchColors: vi.fn(),
      darkMode: false,
      toggleDarkMode: vi.fn()
    });
    
    // Mock feature flags
    vi.mocked(useFeatureFlags).mockReturnValue({
      useNewTokenSystem: true,
      toggleTokenSystem: vi.fn(),
      setTokenSystem: vi.fn()
    });
    
    // Mock tokens with the correct structure
    vi.mocked(useTokens).mockReturnValue({
      theme: 'light',
      colors: {
        brand: { primary: '#ff0000', secondary: '#00ff00', accent: '#0000ff' },
        ui: { 
          foreground: { primary: '#000000', secondary: '#333333', tertiary: '#666666', inverse: '#ffffff' },
          background: { primary: '#ffffff', secondary: '#f5f5f5', tertiary: '#eeeeee' },
          border: { light: '#e0e0e0', medium: '#cccccc', dark: '#999999' },
          focus: '#0066cc'
        },
        feedback: {
          success: '#00cc66',
          warning: '#ffcc00',
          error: '#ff3333',
          info: '#0099ff'
        }
      },
      spacing: { 
        px: '1px',
        0: '0', 
        0.5: '0.125rem',
        1: '0.25rem',
        1.5: '0.375rem', 
        2: '0.5rem',
        2.5: '0.625rem', 
        3: '0.75rem',
        4: '1rem',
        5: '1.25rem',
        6: '1.5rem',
        8: '2rem',
        10: '2.5rem',
        12: '3rem',
        16: '4rem',
        20: '5rem',
        24: '6rem'
      },
      typography: {
        fontFamily: { 
          sans: ['SF Pro', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica', 'Arial', 'sans-serif'],
          mono: ['SF Mono', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace']
        },
        fontSize: { 
          xs: '0.75rem', 
          sm: '0.875rem', 
          base: '1rem', 
          lg: '1.125rem', 
          xl: '1.25rem', 
          '2xl': '1.5rem', 
          '3xl': '1.875rem', 
          '4xl': '2.25rem' 
        },
        fontWeight: { normal: '400', medium: '500', semibold: '600', bold: '700' },
        lineHeight: { 
          none: '1', 
          tight: '1.25', 
          snug: '1.375',
          normal: '1.5', 
          relaxed: '1.75',
          loose: '2' 
        }
      },
      borderRadius: { 
        none: '0',
        sm: '0.125rem', 
        DEFAULT: '0.25rem',
        md: '0.25rem', 
        lg: '0.5rem', 
        xl: '1rem',
        '2xl': '1.5rem', 
        full: '9999px' 
      },
      shadows: { 
        sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)', 
        DEFAULT: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
      },
      transitions: {
        duration: { 
          75: '75ms', 
          100: '100ms', 
          150: '150ms', 
          200: '200ms', 
          300: '300ms', 
          500: '500ms', 
          700: '700ms', 
          1000: '1000ms' 
        },
        easing: { 
          linear: 'linear', 
          in: 'cubic-bezier(0.4, 0, 1, 1)', 
          out: 'cubic-bezier(0, 0, 0.2, 1)', 
          inOut: 'cubic-bezier(0.4, 0, 0.2, 1)', 
          apple: 'cubic-bezier(0.25, 0.1, 0.25, 1)' 
        }
      },
      zIndex: { 
        0: '0', 
        10: '10', 
        20: '20', 
        30: '30', 
        40: '40', 
        50: '50', 
        auto: 'auto', 
        dropdown: '1000', 
        modal: '1050', 
        tooltip: '1100' 
      },
      breakpoints: { 
        sm: '640px', 
        md: '768px', 
        lg: '1024px', 
        xl: '1280px',
        '2xl': '1536px'
      }
    });
  });

  test('renders the form with all input fields', () => {
    render(<ColorForm />);
    
    // Check for form title
    expect(screen.getByTestId('form-title')).toHaveTextContent('Add Flat Color');
    
    // Check that the form is rendered
    expect(screen.getByTestId('flat-color-form')).toBeInTheDocument();
    expect(screen.getByTestId('color-form-element')).toBeInTheDocument();
    
    // Check for inputs by their name/id attributes instead of data-testid
    expect(screen.getByPlaceholderText('Product name')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Flavour')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('HEX')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Pantone')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('C M Y K')).toBeInTheDocument();
    expect(screen.getByTestId('notes-input')).toBeInTheDocument();
    
    // Check for buttons
    expect(screen.getByTestId('submit-button')).toBeInTheDocument();
    expect(screen.getByTestId('cancel-button')).toBeInTheDocument();
  });
}); 