node_modules
# Electron build output
dist
dist-electron
out
dist-fresh/
dist-new/
dist-clean/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Database files
# *.db
# *.sqlite

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Generated data/artifacts (adjust as needed)
*.json
!package.json
!package-lock.json
!tsconfig*.json
!electron-builder*.json

# Allow migration scripts in src/main/db and migrations
!src/main/db/*.js
!src/main/db/migrations/*.js
*.js
!scripts/**/*.js
!tailwind.config.js
*.html
*.png
*.tsbuildinfo
*.md # Except README.md and those in /docs
!README.md
!docs/**/*.md

# Specific Files/Folders to Ignore
Product data/
Updated comparison files/
color-names/
formatted_data.json
full-response.html
pantone-colors-summary.json
pantone-hardcoded-colors.js
pantone-standard-colors.js
pantone-standard-colors.json
pantone_app_data.json
ral-hardcoded-colors.js
response.html
scraped-pantone-colors.js
scraped-pantone-colors.json
scraped-pantone-progress.json
scraped-pantone.json
scraped-ral-colors.js
scraped-ral-colors.json
scraped-ral.json
test-output.ts
visual-graphics-page.png
visual-graphics-pantone.js
visual-graphics-pantone.json
visual-graphics-scraper.js
visual-graphics-screenshot.png
visual-graphics.html
visual-graphics-summary.json
tmp/
mcp Servers/
.cursor/
tabs-toggle-container.tsx
color-scraping-guide.md
debug*.js
convert_data.ps1
count-colors.js
encyco-scrape.js
import-colors.js
import-hardcoded-colors.js
import-visual-graphics-colors.js
scrape-colors.js

# Editor directories and files
.idea
.vscode
*.swp
*.swo

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Zoho OAuth tokens and credentials
zoho-tokens.json
*zoho-refresh-token*
ZOHO_EMAIL_SETUP.md

# Build-time config (contains credentials)
out/app-config.json

# Added by Task Master AI
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/ 