/**
 * @file run-component-tests.tsx
 * @description Helper for running component tests with token system
 */

import { describe, test, expect } from 'vitest';
import { render } from '@testing-library/react';
import { FeatureFlagContext } from '../renderer/context/FeatureFlagContext';
import { ThemeContext } from '../renderer/context/ThemeContext';
import React from 'react';

export type TestCase = {
  name: string;
  component: React.ReactNode;
  expectations: Array<(_container: HTMLElement) => void>;
};

/**
 * Test runner for components using the token system
 * Tests a component with both old and new token systems
 */
export function runComponentTests(
  componentName: string,
  testCases: TestCase[]
) {
  describe(`${componentName} Component`, () => {
    // Test without token system (legacy styling)
    describe('with legacy styling', () => {
      testCases.forEach(({ name, component, expectations }) => {
        test(`${name}`, () => {
          const { container } = render(
            <FeatureFlagContext.Provider
              value={{ useNewTokenSystem: false, toggleTokenSystem: () => {}, setTokenSystem: () => {} }}
            >
              <ThemeContext.Provider
                value={{ mode: 'light', effectiveTheme: 'light', setMode: () => {}, isTransitioning: false }}
              >
                {component}
              </ThemeContext.Provider>
            </FeatureFlagContext.Provider>
          );
          
          expectations.forEach(expectFn => expectFn(container));
        });
      });
    });

    // Test with token system
    describe('with token system', () => {
      testCases.forEach(({ name, component, expectations }) => {
        test(`${name}`, () => {
          const { container } = render(
            <FeatureFlagContext.Provider
              value={{ useNewTokenSystem: true, toggleTokenSystem: () => {}, setTokenSystem: () => {} }}
            >
              <ThemeContext.Provider
                value={{ mode: 'light', effectiveTheme: 'light', setMode: () => {}, isTransitioning: false }}
              >
                {component}
              </ThemeContext.Provider>
            </FeatureFlagContext.Provider>
          );
          
          expectations.forEach(expectFn => expectFn(container));
        });
      });
    });

    // Test with token system in dark mode
    describe('with token system in dark mode', () => {
      testCases.forEach(({ name, component, expectations }) => {
        test(`${name}`, () => {
          const { container } = render(
            <FeatureFlagContext.Provider
              value={{ useNewTokenSystem: true, toggleTokenSystem: () => {}, setTokenSystem: () => {} }}
            >
              <ThemeContext.Provider
                value={{ mode: 'dark', effectiveTheme: 'dark', setMode: () => {}, isTransitioning: false }}
              >
                {component}
              </ThemeContext.Provider>
            </FeatureFlagContext.Provider>
          );
          
          expectations.forEach(expectFn => expectFn(container));
        });
      });
    });
  });
}

// Example helper functions for common test expectations
export const expectTextContent = (text: string) => (container: HTMLElement) => {
  expect(container.textContent).toContain(text);
};

export const expectClassContent = (className: string) => (container: HTMLElement) => {
  expect(container.innerHTML).toContain(className);
};

// TODO: Add test helpers for ColorTable and ColorSwatches components
  // @ts-ignore - Intentionally unused
// These helpers will verify token-based styling is applied correctly
export const expectColorTableHeaders = () => (_container: HTMLElement) => {
  // Verify header has the correct token-based styling
  // When implemented, this should check for brand tokens instead of hardcoded colors

//   headers.forEach(header => { // TODO: Remove unused header
    // This will need to be updated once we migrate to token-based classes
    // Currently still using hardcoded bg-red-600
  // });
};

export const expectColorSwatchLayout = () => (_container: HTMLElement) => {
  // Verify swatch layout uses token-based spacing
  // When implemented, this should check for token spacing classes
  // This will need to be implemented once we migrate to token-based classes
}; 