#!/usr/bin/env node

const Database = require('better-sqlite3');
const path = require('path');

// Get the database path
const userDataPath = process.env.APPDATA || 
  (process.platform == 'darwin' ? process.env.HOME + '/Library/Application Support' : process.env.HOME + '/.local/share');
const dbPath = path.join(userDataPath, 'chroma-sync', 'chromasync.db');

console.log('Database path:', dbPath);

// Open database
const db = new Database(dbPath, { readonly: true });

// Get the current user ID from config
const configPath = path.join(userDataPath, 'chroma-sync', 'chroma-sync-config.json');
let userId = null;
try {
  const config = require(configPath);
  userId = config.auth?.user?.id;
  console.log('Current user ID:', userId);
} catch (error) {
  console.log('Could not read user ID from config');
}

// Check what the sync query would return
const query = `
  SELECT 
    p.external_id as product_external_id,
    c.external_id as color_external_id,
    pc.display_order,
    p.user_id as product_user_id,
    c.user_id as color_user_id
  FROM product_colors pc
  JOIN products p ON pc.product_id = p.id
  JOIN colors c ON pc.color_id = c.id
  WHERE p.is_active = 1
    AND c.deleted_at IS NULL
  LIMIT 10
`;

console.log('\nRunning sync query...');
const results = db.prepare(query).all();

console.log(`\nFound ${results.length} relationships:`);
results.forEach(r => {
  console.log(`- Product: ${r.product_external_id.substring(0, 8)}... (user: ${r.product_user_id?.substring(0, 8) || 'NULL'})`);
  console.log(`  Color: ${r.color_external_id.substring(0, 8)}... (user: ${r.color_user_id?.substring(0, 8) || 'NULL'})`);
  console.log(`  Order: ${r.display_order}`);
  console.log('');
});

// Check if filtering by user_id would exclude these
if (userId) {
  const filteredQuery = `
    SELECT COUNT(*) as count
    FROM product_colors pc
    JOIN products p ON pc.product_id = p.id
    JOIN colors c ON pc.color_id = c.id
    WHERE p.user_id = ? 
      AND c.user_id = ?
      AND p.is_active = 1
      AND c.deleted_at IS NULL
  `;
  
  const filtered = db.prepare(filteredQuery).get(userId, userId);
  console.log(`\nWith user_id filter (${userId}): ${filtered.count} relationships would sync`);
}

// Check total counts
const stats = db.prepare(`
  SELECT 
    (SELECT COUNT(*) FROM product_colors) as total_relationships,
    (SELECT COUNT(DISTINCT product_id) FROM product_colors) as products_with_colors,
    (SELECT COUNT(DISTINCT color_id) FROM product_colors) as colors_with_products
`).get();

console.log('\nDatabase statistics:');
console.log(`- Total product-color relationships: ${stats.total_relationships}`);
console.log(`- Products with colors: ${stats.products_with_colors}`);
console.log(`- Colors assigned to products: ${stats.colors_with_products}`);

db.close();