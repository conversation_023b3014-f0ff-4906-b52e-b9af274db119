/**
 * @file gdpr.service.ts
 * @description GDPR compliance service for data export and deletion
 */

import { getSupabaseClient } from './supabase-client';
import * as path from 'path';
import { app } from 'electron';

export class GDPRService {
  async exportUserData(userId: string): Promise<Buffer> {
    const supabase = getSupabaseClient();
    
    // Fetch all user data
    const [products, colors, consent] = await Promise.all([
      supabase.from('products').select('*').eq('user_id', userId),
      supabase.from('colors').select('*').eq('user_id', userId),
      supabase.from('user_consent').select('*').eq('user_id', userId)
    ]);
    
    const exportData = {
      exportDate: new Date().toISOString(),
      userData: {
        products: products.data,
        colors: colors.data,
        consent: consent.data
      }
    };
    
    // Create JSON export
    return Buffer.from(JSON.stringify(exportData, null, 2));
  }
  
  async deleteUserData(userId: string): Promise<void> {
    const supabase = getSupabaseClient();
    
    // Schedule deletion (30 day grace period)
    const { error } = await supabase.from('deletion_requests').insert({
      user_id: userId,
      requested_at: new Date().toISOString()
    });
    
    if (error) {throw error;}
    
    // In production, this would trigger an email via Supabase Edge Function
  }
  
  async getDataExportPath(): Promise<string> {
    const downloadsPath = app.getPath('downloads');
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    return path.join(downloadsPath, `chromasync-data-export-${timestamp}.json`);
  }
}

// Export singleton instance
export const gdprService = new GDPRService();
