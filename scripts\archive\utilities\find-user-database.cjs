/**
 * Find User Database - Locate the actual ChromaSync database in user area
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

function findUserDatabase() {
  console.log('🔍 Searching for ChromaSync user database...\n');
  
  // Common Electron app data locations on macOS
  const possiblePaths = [
    path.join(os.homedir(), 'Library', 'Application Support', 'ChromaSync', 'chromasync.db'),
    path.join(os.homedir(), 'Library', 'Application Support', 'chromasync', 'chromasync.db'),
    path.join(os.homedir(), 'Library', 'Application Support', 'Electron', 'chromasync.db'),
    path.join(os.homedir(), '.config', 'ChromaSync', 'chromasync.db'),
    path.join(os.homedir(), '.config', 'chromasync', 'chromasync.db'),
    path.join(os.homedir(), 'Documents', 'ChromaSync', 'chromasync.db'),
    // Also check current directory as fallback
    path.join(__dirname, 'chromasync.db')
  ];
  
  console.log('📍 Checking these locations:');
  possiblePaths.forEach(dbPath => {
    console.log(`  ${dbPath}`);
  });
  console.log();
  
  // Check each path
  for (const dbPath of possiblePaths) {
    try {
      if (fs.existsSync(dbPath)) {
        const stats = fs.statSync(dbPath);
        console.log(`✅ Found database: ${dbPath}`);
        console.log(`   Size: ${Math.round(stats.size / 1024)}KB`);
        console.log(`   Modified: ${stats.mtime.toISOString()}`);
        
        return dbPath;
      }
    } catch (error) {
      // Ignore permission errors etc.
    }
  }
  
  console.log('❌ No ChromaSync database found in standard locations');
  console.log('\n💡 Try these steps:');
  console.log('1. Run ChromaSync app first to create the database');
  console.log('2. Check if app is running from a different location');
  console.log('3. Search manually: find ~ -name "chromasync.db" 2>/dev/null');
  
  return null;
}

const dbPath = findUserDatabase();
if (dbPath) {
  console.log(`\n🎯 Use this path in the migration script: ${dbPath}`);
}