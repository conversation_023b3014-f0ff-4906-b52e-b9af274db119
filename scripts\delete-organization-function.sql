-- <PERSON><PERSON><PERSON> to handle organization deletion with data
-- This allows you to delete specific organizations and all their associated data

-- Function to delete an organization and all its data
CREATE OR REPLACE FUNCTION delete_organization_with_data(org_id UUID, requesting_user_id UUID)
RETURNS TABLE (
    deleted_colors INTEGER,
    deleted_products INTEGER,
    deleted_product_colors INTEGER,
    deleted_members INTEGER
) AS $$
DECLARE
    v_deleted_colors INTEGER := 0;
    v_deleted_products INTEGER := 0;
    v_deleted_product_colors INTEGER := 0;
    v_deleted_members INTEGER := 0;
    v_is_owner BOOLEAN := FALSE;
BEGIN
    -- Check if the requesting user is the owner of the organization
    SELECT EXISTS (
        SELECT 1 FROM organization_members 
        WHERE organization_id = org_id 
        AND user_id = requesting_user_id 
        AND role = 'owner'
    ) INTO v_is_owner;
    
    IF NOT v_is_owner THEN
        RAISE EXCEPTION 'Only organization owners can delete organizations';
    END IF;
    
    -- Delete in correct order to respect foreign key constraints
    
    -- 1. Delete product_colors first
    DELETE FROM product_colors WHERE organization_id = org_id;
    GET DIAGNOSTICS v_deleted_product_colors = ROW_COUNT;
    
    -- 2. Delete colors
    DELETE FROM colors WHERE organization_id = org_id;
    GET DIAGNOSTICS v_deleted_colors = ROW_COUNT;
    
    -- 3. Delete products
    DELETE FROM products WHERE organization_id = org_id;
    GET DIAGNOSTICS v_deleted_products = ROW_COUNT;
    
    -- 4. Delete organization members
    DELETE FROM organization_members WHERE organization_id = org_id;
    GET DIAGNOSTICS v_deleted_members = ROW_COUNT;
    
    -- 5. Finally delete the organization
    DELETE FROM organizations WHERE id = org_id;
    
    RETURN QUERY SELECT v_deleted_colors, v_deleted_products, v_deleted_product_colors, v_deleted_members;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION delete_organization_with_data TO authenticated;

-- Example usage:
-- SELECT * FROM delete_organization_with_data('your-org-id'::UUID, auth.uid());
