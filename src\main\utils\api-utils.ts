/**
 * @file api-utils.ts
 * @description Utility functions for API calls
 */

import axios, { AxiosError } from 'axios';
import { API_ENDPOINTS, FALLBACK_API_ENDPOINTS } from '../../shared/constants/api-endpoints';

/**
 * Call API with retry mechanism and exponential backoff
 * @param endpoint API endpoint URL
 * @param data Request data
 * @param headers Request headers
 * @param maxRetries Maximum number of retries
 * @param initialDelay Initial delay in milliseconds
 * @returns API response
 */
export async function callApiWithRetry(
  endpoint: string,
  data: any,
  headers: Record<string, string> = {},
  maxRetries = 3,
  initialDelay = 1000
): Promise<any> {
  let lastError: Error | AxiosError = new Error('No attempts made');
  let delay = initialDelay;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      // Attempt API call
      const response = await axios.post(endpoint, data, { headers });
      return response.data;
    } catch (error: any) {
      lastError = error;
      
      // Check if we should retry
      if (attempt >= maxRetries) {break;}
      
      // Log retry attempt
      console.log(`API call failed, retrying in ${delay}ms (${attempt + 1}/${maxRetries})`, error.message);
      
      // Wait before retry with exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay));
      delay *= 2; // Exponential backoff
    }
  }
  
  // If we get here, all retries failed
  throw lastError;
}

/**
 * Call API with fallback to secondary endpoint if primary fails
 * @param primaryEndpoint Primary API endpoint
 * @param fallbackEndpoint Fallback API endpoint
 * @param data Request data
 * @param headers Request headers
 * @returns API response
 */
export async function callApiWithFallback(
  primaryEndpoint: string,
  fallbackEndpoint: string,
  data: any,
  headers: Record<string, string> = {}
): Promise<any> {
  try {
    // Try primary endpoint with retry
    return await callApiWithRetry(primaryEndpoint, data, headers);
  } catch (error) {
    console.error('Primary endpoint failed, trying fallback', error);
    
    // Try fallback endpoint with retry
    try {
      return await callApiWithRetry(fallbackEndpoint, data, headers);
    } catch (fallbackError) {
      console.error('Fallback endpoint also failed', fallbackError);
      throw new Error('API call failed: All endpoints unreachable');
    }
  }
}

/**
 * Test API connection to verify endpoints are reachable
 * @returns Connection test results
 */
export async function testApiConnection(): Promise<{
  syncEndpoint: boolean;
  licenseEndpoint: boolean;
  message: string;
}> {
  const result = {
    syncEndpoint: false,
    licenseEndpoint: false,
    message: ''
  };
  
  try {
    // Test sync endpoint
    await axios.post(API_ENDPOINTS.SYNC, { action: 'ping' });
    result.syncEndpoint = true;
  } catch (error: any) {
    // Try fallback sync endpoint
    try {
      await axios.post(FALLBACK_API_ENDPOINTS.SYNC, { action: 'ping' });
      result.syncEndpoint = true;
      result.message += 'Using fallback sync endpoint. ';
    } catch (_fallbackError: any) {
      result.message += `Sync endpoint error: ${error.message}. `;
    }
  }
  
  try {
    // Test license endpoint
    await axios.post(API_ENDPOINTS.LICENSE, { action: 'ping' });
    result.licenseEndpoint = true;
  } catch (error: any) {
    // Try fallback license endpoint
    try {
      await axios.post(FALLBACK_API_ENDPOINTS.LICENSE, { action: 'ping' });
      result.licenseEndpoint = true;
      result.message += 'Using fallback license endpoint. ';
    } catch (_fallbackError: any) {
      result.message += `License endpoint error: ${error.message}.`;
    }
  }
  
  return result;
}

/**
 * Get a readable error message from an API error
 * @param error Error object
 * @returns Human-readable error message
 */
export function getApiErrorMessage(error: any): string {
  if (error.response) {
    // Server responded with error
    const status = error.response.status;
    const message = error.response.data?.message || 'Unknown server error';
    
    if (status === 404) {
      return 'Server not found. Please check your internet connection or contact support.';
    } else if (status === 401 || status === 403) {
      return 'Authentication failed. Please restart the application.';
    } else {
      return `Server error (${status}): ${message}`;
    }
  } else if (error.request) {
    // Request made but no response
    return 'No response from server. Please check your internet connection.';
  } else {
    // Error setting up request
    return `Error: ${error.message}`;
  }
}
