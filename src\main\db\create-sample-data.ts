/**
 * Create sample data for ChromaSync
 * Run this after the database is initialized to populate with test data
 */

import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';

const sampleProducts = [
  {
    name: "Summer Collection 2024",
    sku: "SUMM-2024",
    description: "Vibrant summer fashion line",
    colors: [
      { code: "PANTONE 17-1463", name: "Tangerine Tango", hex: "#DD4124", library: "pantone" },
      { code: "PANTONE 14-4318", name: "Aqua Sky", hex: "#7BC4C4", library: "pantone" },
      { code: "PANTONE 13-0647", name: "Illuminating Yellow", hex: "#F5DF4D", library: "pantone" },
      { code: "PANTONE 18-3838", name: "<PERSON> Violet", hex: "#5F4B8B", library: "pantone" },
      { code: "PANTONE 15-5519", name: "Turquoise", hex: "#45B8AC", library: "pantone" }
    ]
  },
  {
    name: "Urban Office Furniture",
    sku: "URB-OFF-001",
    description: "Modern office furniture collection",
    colors: [
      { code: "RAL 7016", name: "Anthracite Grey", hex: "#293133", library: "ral" },
      { code: "RAL 9003", name: "Signal White", hex: "#F4F4F4", library: "ral" },
      { code: "RAL 5024", name: "Pastel Blue", hex: "#5D9B9B", library: "ral" },
      { code: "RAL 7035", name: "Light Grey", hex: "#D7D7D7", library: "ral" },
      { code: "CUSTOM-001", name: "Executive Brown", hex: "#3E2723", library: "user" }
    ]
  },
  {
    name: "Kids Room Essentials",
    sku: "KIDS-ESS-001",
    description: "Colorful and safe furniture for children",
    colors: [
      { code: "PANTONE 13-4411", name: "Baby Blue", hex: "#B5D3E7", library: "pantone" },
      { code: "PANTONE 12-0752", name: "Buttercup", hex: "#FCE883", library: "pantone" },
      { code: "PANTONE 14-4318", name: "Mint Green", hex: "#98D982", library: "pantone" },
      { code: "PANTONE 14-2311", name: "Pink Lavender", hex: "#E8B5CE", library: "pantone" },
      { code: "PANTONE 16-1441", name: "Peach", hex: "#FFBE98", library: "pantone" }
    ]
  },
  {
    name: "Automotive Paint Series",
    sku: "AUTO-PAINT-001",
    description: "Professional automotive paint collection",
    colors: [
      { code: "RAL 3020", name: "Traffic Red", hex: "#CC0605", library: "ral" },
      { code: "RAL 9005", name: "Jet Black", hex: "#0A0A0A", library: "ral" },
      { code: "METAL-001", name: "Metallic Silver", hex: "#C0C0C0", library: "user", metallic: true },
      { code: "RAL 5002", name: "Ultramarine Blue", hex: "#1E3A8A", library: "ral", metallic: true },
      { code: "PEARL-001", name: "Pearl White", hex: "#F8F8FF", library: "user", metallic: true }
    ]
  },
  {
    name: "Garden Furniture Set",
    sku: "GARD-FURN-001",
    description: "Weather-resistant outdoor furniture",
    colors: [
      { code: "RAL 6005", name: "Moss Green", hex: "#0F4336", library: "ral" },
      { code: "RAL 8017", name: "Chocolate Brown", hex: "#45322E", library: "ral" },
      { code: "RAL 7032", name: "Pebble Grey", hex: "#B8B799", library: "ral" },
      { code: "RAL 1015", name: "Light Ivory", hex: "#E6D690", library: "ral" },
      { code: "PANTONE 19-0509", name: "Forest Night", hex: "#1B3B36", library: "pantone" }
    ]
  }
];

// Add remaining 5 products...

export async function createSampleData(db: Database.Database) {
  console.log('[Sample Data] Creating sample products and colors...');
  
  // Map library names to source IDs
  const sourceMap: Record<string, number> = {
    'user': 1,
    'pantone': 2,
    'ral': 3,
    'ncs': 4
  };
  
  try {
    // Create products and colors
    for (const productData of sampleProducts) {
      // Create product
      const productId = uuidv4();
      db.prepare(`
        INSERT INTO products (external_id, name, sku, metadata)
        VALUES (?, ?, ?, ?)
      `).run(
        productId,
        productData.name,
        productData.sku,
        JSON.stringify({ description: productData.description })
      );
      
      console.log(`[Sample Data] Created product: ${productData.name}`);
      
      // Create colors and associate with product
      for (const colorData of productData.colors) {
        const colorId = uuidv4();
        const sourceId = sourceMap[colorData.library] || 1;
        
        // Insert color
        db.prepare(`
          INSERT INTO colors (external_id, source_id, code, display_name, hex, is_metallic)
          VALUES (?, ?, ?, ?, ?, ?)
        `).run(
          colorId,
          sourceId,
          colorData.code,
          colorData.name,
          colorData.hex,
          colorData.metallic ? 1 : 0
        );
        
        // Get the internal ID
        const color = db.prepare('SELECT id FROM colors WHERE external_id = ?').get(colorId) as { id: number };
        const product = db.prepare('SELECT id FROM products WHERE external_id = ?').get(productId) as { id: number };
        
        // Associate color with product
        db.prepare(`
          INSERT INTO product_colors (product_id, color_id)
          VALUES (?, ?)
        `).run(product.id, color.id);
      }
    }
    
    console.log('[Sample Data] Successfully created sample data!');
    return true;
  } catch (error) {
    console.error('[Sample Data] Error creating sample data:', error);
    return false;
  }
}
