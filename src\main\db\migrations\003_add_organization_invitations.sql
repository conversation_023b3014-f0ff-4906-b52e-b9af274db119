-- Migration: Add organization invitations table for team member invites
-- Version: 003
-- Description: Adds support for email invitations to join organizations

-- Create organization_invitations table
CREATE TABLE IF NOT EXISTS organization_invitations (
  id INTEGER PRIMARY KEY,
  external_id TEXT UNIQUE NOT NULL,
  organization_id INTEGER NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('owner', 'admin', 'member')),
  invited_by TEXT NOT NULL,
  token TEXT UNIQUE NOT NULL,
  expires_at TEXT NOT NULL,
  accepted_at TEXT,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(organization_id, email)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_invitations_org ON organization_invitations(organization_id);
CREATE INDEX IF NOT EXISTS idx_invitations_token ON organization_invitations(token);
CREATE INDEX IF NOT EXISTS idx_invitations_email ON organization_invitations(email);
CREATE INDEX IF NOT EXISTS idx_invitations_expires ON organization_invitations(expires_at);

-- Add trigger to update the updated_at timestamp
CREATE TRIGGER IF NOT EXISTS update_organization_invitations_timestamp 
AFTER UPDATE ON organization_invitations
BEGIN
  UPDATE organization_invitations 
  SET updated_at = CURRENT_TIMESTAMP 
  WHERE id = NEW.id;
END;