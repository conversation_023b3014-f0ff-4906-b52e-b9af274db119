#!/usr/bin/env node

// Check what data exists in Supabase
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkSupabaseData() {
  try {
    console.log('🔍 Checking Supabase Data\n');
    
    // Check organizations
    console.log('📋 Organizations:');
    const { data: orgs, error: orgsError } = await supabase
      .from('organizations')
      .select('*')
      .limit(10);
    
    if (orgsError) {
      console.error('❌ Error fetching organizations:', orgsError);
    } else {
      console.log(`Found ${orgs?.length || 0} organizations`);
      orgs?.forEach(org => {
        console.log(`  - ${org.name} (${org.id})`);
      });
    }
    
    console.log('\n👥 Organization Members:');
    const { data: members, error: membersError } = await supabase
      .from('organization_members')
      .select('*')
      .limit(10);
    
    if (membersError) {
      console.error('❌ Error fetching members:', membersError);
    } else {
      console.log(`Found ${members?.length || 0} organization members`);
      members?.forEach(member => {
        console.log(`  - User ${member.user_id} in org ${member.organization_id} as ${member.role}`);
      });
    }
    
    console.log('\n👤 Profiles:');
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(10);
    
    if (profilesError) {
      console.error('❌ Error fetching profiles:', profilesError);
    } else {
      console.log(`Found ${profiles?.length || 0} profiles`);
      profiles?.forEach(profile => {
        console.log(`  - ${profile.full_name || profile.email} (${profile.id})`);
      });
    }
    
    console.log('\n🎨 Colors:');
    const { data: colors, error: colorsError } = await supabase
      .from('colors')
      .select('*')
      .limit(5);
    
    if (colorsError) {
      console.error('❌ Error fetching colors:', colorsError);
    } else {
      console.log(`Found ${colors?.length || 0} colors (showing first 5)`);
      colors?.forEach(color => {
        console.log(`  - ${color.name} (${color.hex}) in org ${color.organization_id}`);
      });
    }
    
    console.log('\n📦 Products:');
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('*')
      .limit(5);
    
    if (productsError) {
      console.error('❌ Error fetching products:', productsError);
    } else {
      console.log(`Found ${products?.length || 0} products (showing first 5)`);
      products?.forEach(product => {
        console.log(`  - ${product.name} in org ${product.organization_id}`);
      });
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

checkSupabaseData();