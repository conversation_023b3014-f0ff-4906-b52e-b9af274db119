# ChromaSync Security Guide

Comprehensive security documentation covering authentication, data protection, and privacy compliance.

## Table of Contents

1. [Security Architecture](#security-architecture)
2. [Authentication & OAuth](#authentication--oauth)
3. [Data Protection](#data-protection)
4. [GDPR Compliance](#gdpr-compliance)
5. [Network Security](#network-security)
6. [Local Security](#local-security)
7. [Security Best Practices](#security-best-practices)
8. [Incident Response](#incident-response)

---

## Security Architecture

### Security Model Overview

ChromaSync implements a multi-layered security approach:

```
┌─────────────────────────────────────────┐
│ Frontend (Renderer Process)             │
│ ├─ No direct database access            │
│ ├─ Sandboxed environment               │
│ └─ Secure IPC communication            │
├─────────────────────────────────────────┤
│ IPC Bridge (Preload Script)            │
│ ├─ Context isolation enabled           │
│ ├─ Limited API exposure                │
│ └─ Type-safe communication             │
├─────────────────────────────────────────┤
│ Backend (Main Process)                  │
│ ├─ Database access control             │
│ ├─ File system permissions             │
│ ├─ Network communication               │
│ └─ Encryption & secure storage         │
├─────────────────────────────────────────┤
│ Cloud Services (Supabase)              │
│ ├─ Row Level Security (RLS)            │
│ ├─ JWT token validation                │
│ ├─ HTTPS/TLS encryption                │
│ └─ OAuth 2.0 + PKCE                   │
└─────────────────────────────────────────┘
```

### Key Security Principles

1. **Principle of Least Privilege**: Each component has minimal required permissions
2. **Defense in Depth**: Multiple security layers prevent single points of failure
3. **Zero Trust**: All inputs validated, all communications authenticated
4. **Data Minimization**: Only collect and store necessary data
5. **Transparency**: Clear privacy policies and data handling practices

---

## Authentication & OAuth

### OAuth 2.0 + PKCE Implementation

ChromaSync uses the secure PKCE (Proof Key for Code Exchange) flow:

#### Security Benefits
- **No secrets in client**: Code verifier never transmitted
- **CSRF protection**: State parameter validation
- **Replay attack prevention**: One-time code usage
- **Man-in-the-middle protection**: Code challenge verification

#### Implementation Details

```typescript
// PKCE flow configuration
const supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    flowType: 'pkce',
    storage: customSecureStorage,
    persistSession: false,
    detectSessionInUrl: false
  }
});
```

#### Secure Token Storage

```typescript
// Encrypted token storage using Electron safeStorage
const customSecureStorage = {
  getItem: async (key: string): Promise<string | null> => {
    const encrypted = store.get(key) as string;
    if (!encrypted) return null;
    
    if (safeStorage.isEncryptionAvailable()) {
      return safeStorage.decryptString(Buffer.from(encrypted, 'base64'));
    }
    return encrypted;
  },
  
  setItem: async (key: string, value: string): Promise<void> => {
    if (safeStorage.isEncryptionAvailable()) {
      const encrypted = safeStorage.encryptString(value);
      store.set(key, encrypted.toString('base64'));
    }
  }
};
```

### Session Management

#### Session Security
- **Hardware-backed encryption**: Uses OS keychain/credential manager
- **Automatic expiration**: JWT tokens with configurable expiry
- **Secure refresh**: Automatic token refresh without user intervention
- **Logout security**: Complete session cleanup

#### Session Validation
```typescript
// Continuous session validation
async function validateSession(): Promise<boolean> {
  try {
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) return false;
    if (Date.now() >= session.expires_at * 1000) return false;
    
    return true;
  } catch {
    return false;
  }
}
```

### Multi-Factor Authentication (MFA)

While ChromaSync relies on Google's MFA implementation, we enhance security through:

1. **Device Registration**: Track authorized devices
2. **Suspicious Activity Detection**: Monitor for unusual login patterns
3. **Session Monitoring**: Real-time session validation

---

## Data Protection

### Encryption Standards

#### Data at Rest
- **Local Database**: File-level encryption via OS features
- **Cloud Storage**: AES-256 encryption in Supabase
- **Configuration**: Encrypted storage for sensitive settings
- **Tokens**: Hardware-backed encryption using Electron safeStorage

#### Data in Transit
- **HTTPS/TLS 1.3**: All network communication encrypted
- **Certificate Pinning**: Prevent man-in-the-middle attacks
- **WebSocket Security**: Secure real-time sync connections

### Database Security

#### Local SQLite Protection
```sql
-- Enable WAL mode for better concurrency and crash recovery
PRAGMA journal_mode = WAL;

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Secure temp store
PRAGMA secure_delete = ON;
```

#### Cloud PostgreSQL (Supabase)
```sql
-- Row Level Security (RLS) enabled on all tables
ALTER TABLE colors ENABLE ROW LEVEL SECURITY;

-- Organization-scoped access policy
CREATE POLICY "organization_access" ON colors
  FOR ALL USING (
    organization_id IN (
      SELECT organization_id 
      FROM user_profiles 
      WHERE user_id = auth.uid()
    )
  );
```

### Data Classification

| Data Type | Classification | Protection Level |
|-----------|----------------|------------------|
| Color Data | Internal | Standard encryption |
| User Emails | PII | Enhanced protection + GDPR |
| Auth Tokens | Confidential | Hardware-backed encryption |
| Organization Data | Internal | Standard encryption + RLS |
| System Logs | Internal | Encrypted at rest |

---

## GDPR Compliance

### Privacy by Design

ChromaSync implements GDPR compliance from the ground up:

#### Data Minimization
- Collect only essential user data
- No tracking beyond necessary functionality
- Clear data retention policies

#### User Rights Implementation

```typescript
// Right to Access (Data Export)
async function exportUserData(userId: string): Promise<string> {
  const userData = {
    profile: await getUserProfile(userId),
    colors: await getUserColors(userId),
    products: await getUserProducts(userId),
    organizations: await getUserOrganizations(userId),
    metadata: {
      exportDate: new Date().toISOString(),
      dataVersion: '2.0'
    }
  };
  
  return JSON.stringify(userData, null, 2);
}

// Right to Erasure (Account Deletion)
async function scheduleAccountDeletion(userId: string): Promise<void> {
  await supabase.from('deletion_requests').insert({
    user_id: userId,
    requested_at: new Date().toISOString(),
    scheduled_for: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
  });
  
  // Send confirmation email
  await sendDeletionConfirmationEmail(userId);
}
```

### Consent Management

#### GDPR Consent Flow
1. **Clear Consent Request**: Explicit opt-in required
2. **Granular Permissions**: Separate consent for different features
3. **Withdrawal Rights**: Easy consent withdrawal
4. **Consent Tracking**: Audit trail of all consent actions

```typescript
// Consent tracking
interface ConsentRecord {
  userId: string;
  consentType: 'data_processing' | 'marketing' | 'analytics';
  granted: boolean;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
}
```

### Data Subject Rights

| Right | Implementation | Status |
|-------|----------------|---------|
| Access | Data export functionality | ✅ Implemented |
| Rectification | Edit profile/data features | ✅ Implemented |
| Erasure | Account deletion with 30-day grace | ✅ Implemented |
| Portability | JSON/CSV export formats | ✅ Implemented |
| Restriction | Consent withdrawal options | ✅ Implemented |
| Objection | Opt-out mechanisms | ✅ Implemented |

---

## Network Security

### API Security

#### Rate Limiting
```typescript
// API rate limiting configuration
const rateLimits = {
  auth: { requests: 5, window: '15m' },
  sync: { requests: 100, window: '1h' },
  export: { requests: 3, window: '1h' }
};
```

#### Request Validation
```typescript
// Input sanitization and validation
function validateColorInput(input: any): Color | null {
  const schema = {
    name: { type: 'string', maxLength: 100 },
    hex: { type: 'string', pattern: /^#[0-9A-Fa-f]{6}$/ },
    rgb: { 
      type: 'object',
      properties: {
        r: { type: 'number', minimum: 0, maximum: 255 },
        g: { type: 'number', minimum: 0, maximum: 255 },
        b: { type: 'number', minimum: 0, maximum: 255 }
      }
    }
  };
  
  return validateAgainstSchema(input, schema);
}
```

### Content Security Policy (CSP)

```html
<!-- Strict CSP for renderer processes -->
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline';
  style-src 'self' 'unsafe-inline';
  img-src 'self' data: https:;
  connect-src 'self' https://*.supabase.co;
  font-src 'self';
">
```

### Network Monitoring

```typescript
// Network anomaly detection
class NetworkMonitor {
  private requestCounts = new Map<string, number>();
  
  async monitorRequest(endpoint: string, userId: string): Promise<boolean> {
    const key = `${userId}:${endpoint}`;
    const count = this.requestCounts.get(key) || 0;
    
    if (count > this.getThreshold(endpoint)) {
      await this.alertSecurityTeam({
        type: 'rate_limit_exceeded',
        userId,
        endpoint,
        count
      });
      return false;
    }
    
    this.requestCounts.set(key, count + 1);
    return true;
  }
}
```

---

## Local Security

### File System Protection

#### Secure File Paths
```typescript
// Prevent directory traversal attacks
function sanitizePath(userPath: string): string | null {
  const resolved = path.resolve(userPath);
  const allowed = path.resolve(app.getPath('userData'));
  
  if (!resolved.startsWith(allowed)) {
    throw new Error('Access denied: Path outside allowed directory');
  }
  
  return resolved;
}
```

#### File Permissions
```typescript
// Secure file creation with proper permissions
async function createSecureFile(filePath: string, content: string): Promise<void> {
  const securePath = sanitizePath(filePath);
  if (!securePath) throw new Error('Invalid file path');
  
  // Create with restricted permissions (owner read/write only)
  await fs.promises.writeFile(securePath, content, { mode: 0o600 });
}
```

### Application Sandboxing

#### Renderer Process Security
```typescript
// Secure BrowserWindow configuration
const secureWindow = new BrowserWindow({
  webSecurity: true,
  nodeIntegration: false,
  contextIsolation: true,
  enableRemoteModule: false,
  allowRunningInsecureContent: false,
  experimentalFeatures: false
});
```

#### IPC Security
```typescript
// Validated IPC handlers
function createSecureIpcHandler(channel: string, handler: Function) {
  return ipcMain.handle(channel, async (event, ...args) => {
    // Validate source
    if (!isValidSender(event.sender)) {
      throw new Error('Unauthorized IPC access');
    }
    
    // Sanitize inputs
    const sanitizedArgs = args.map(sanitizeInput);
    
    // Execute with error handling
    try {
      return await handler(...sanitizedArgs);
    } catch (error) {
      console.error(`IPC handler error for ${channel}:`, error);
      throw new Error('Internal server error');
    }
  });
}
```

---

## Security Best Practices

### Development Security

#### Secure Coding Standards
1. **Input Validation**: All user inputs validated and sanitized
2. **Output Encoding**: Prevent injection attacks
3. **Error Handling**: No sensitive information in error messages
4. **Dependency Management**: Regular security updates
5. **Code Review**: Security-focused peer review process

#### Security Testing
```bash
# Automated security testing
npm run test:security           # Security-focused unit tests
npm run audit                   # Dependency vulnerability scan
npm run lint:security          # Security linting rules
```

### Deployment Security

#### Environment Configuration
```bash
# Production environment variables
NODE_ENV=production
SUPABASE_URL=https://secure-project.supabase.co
SUPABASE_ANON_KEY=limited_permissions_key

# Secure defaults
ELECTRON_DISABLE_SECURITY_WARNINGS=false
ELECTRON_ENABLE_LOGGING=false
```

#### Build Security
```json
{
  "build": {
    "electronDownload": {
      "mirror": "https://github.com/electron/electron/releases/",
      "verifyChecksum": true
    },
    "compression": "maximum",
    "security": {
      "codeSigningCertPath": "secure/cert.p12",
      "enableCodeSigning": true,
      "notarization": true
    }
  }
}
```

### User Security

#### Security Recommendations for Users
1. **Keep software updated**: Enable automatic updates
2. **Use strong Google account security**: Enable 2FA
3. **Monitor account activity**: Regular security checkups
4. **Report suspicious activity**: Contact support immediately
5. **Data backup**: Regular exports for important data

---

## Incident Response

### Security Incident Classification

| Severity | Description | Response Time |
|----------|-------------|---------------|
| Critical | Data breach, auth bypass | Immediate (< 1 hour) |
| High | Service compromise, PII exposure | 4 hours |
| Medium | Security feature failure | 24 hours |
| Low | Minor vulnerability, DoS | 72 hours |

### Incident Response Process

#### Detection & Assessment
```typescript
// Automated threat detection
class ThreatDetector {
  async detectAnomalies(): Promise<SecurityAlert[]> {
    const alerts: SecurityAlert[] = [];
    
    // Check for unusual authentication patterns
    const authAnomalies = await this.checkAuthPatterns();
    alerts.push(...authAnomalies);
    
    // Monitor API abuse
    const apiAnomalies = await this.checkApiUsage();
    alerts.push(...apiAnomalies);
    
    // Database access patterns
    const dbAnomalies = await this.checkDatabaseAccess();
    alerts.push(...dbAnomalies);
    
    return alerts;
  }
}
```

#### Response Procedures

1. **Immediate Response** (< 1 hour)
   - Isolate affected systems
   - Assess scope and impact
   - Implement containment measures
   - Notify security team

2. **Investigation** (< 4 hours)
   - Forensic analysis
   - Root cause identification
   - Evidence collection
   - Impact assessment

3. **Recovery** (< 24 hours)
   - System restoration
   - Security patches
   - Service restoration
   - User notification

4. **Post-Incident** (< 72 hours)
   - Lessons learned
   - Process improvements
   - Security updates
   - Documentation updates

### Security Monitoring

#### Logging & Monitoring
```typescript
// Security event logging
interface SecurityEvent {
  timestamp: string;
  eventType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  ipAddress?: string;
  details: Record<string, any>;
}

class SecurityLogger {
  async logSecurityEvent(event: SecurityEvent): Promise<void> {
    // Log to secure storage
    await this.writeToSecureLog(event);
    
    // Alert on high severity events
    if (event.severity === 'high' || event.severity === 'critical') {
      await this.sendAlert(event);
    }
  }
}
```

### Contact Information

#### Security Team
- **Email**: <EMAIL>
- **Response Time**: 24 hours maximum
- **Emergency**: <EMAIL> (< 4 hours)

#### Vulnerability Disclosure
- **Process**: Responsible disclosure policy
- **Contact**: <EMAIL>
- **Recognition**: Security researcher acknowledgments
- **Timeline**: 90-day disclosure timeline

---

## Security Checklist

### Pre-Deployment Security Review

- [ ] Authentication flow tested and secure
- [ ] All inputs validated and sanitized
- [ ] Database security policies applied
- [ ] GDPR compliance verified
- [ ] Security headers configured
- [ ] Error handling prevents information disclosure
- [ ] Logging and monitoring enabled
- [ ] Code signing certificates valid
- [ ] Dependency vulnerabilities resolved
- [ ] Security testing completed

### Regular Security Maintenance

- [ ] Monthly dependency updates
- [ ] Quarterly security reviews
- [ ] Annual penetration testing
- [ ] Continuous monitoring active
- [ ] Incident response plan updated
- [ ] Security training completed
- [ ] Access controls reviewed
- [ ] Backup and recovery tested