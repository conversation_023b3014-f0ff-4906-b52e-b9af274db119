/**
 * Full invitation system diagnostic
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 ChromaSync Invitation System Diagnostic\n');

// 1. Check environment variables
console.log('1️⃣ Environment Variables:');
require('dotenv').config();
console.log('   ZOHO_EMAIL:', process.env.ZOHO_EMAIL || '❌ NOT SET');
console.log('   ZOHO_PASSWORD:', process.env.ZOHO_PASSWORD ? '✅ SET (ends with ' + process.env.ZOHO_PASSWORD.slice(-4) + ')' : '❌ NOT SET');
console.log('   ZOHO_SUPPORT_ALIAS:', process.env.ZOHO_SUPPORT_ALIAS || '❌ NOT SET');

// 2. Check if .env file exists
console.log('\n2️⃣ .env File:');
const envPath = path.join(__dirname, '.env');
if (fs.existsSync(envPath)) {
  console.log('   ✅ .env file exists');
  const envContent = fs.readFileSync(envPath, 'utf8');
  const hasZohoConfig = envContent.includes('ZOHO_PASSWORD=') && !envContent.includes('ZOHO_PASSWORD=[');
  console.log('   ' + (hasZohoConfig ? '✅' : '❌') + ' Zoho password configured');
} else {
  console.log('   ❌ .env file not found');
}

// 3. Check application logs
console.log('\n3️⃣ Recent Application Logs:');
const logPaths = [
  path.join(process.env.HOME, 'Library/Logs/ChromaSync/main.log'),
  path.join(process.env.HOME, 'Library/Application Support/ChromaSync/logs/main.log'),
  './logs/main.log'
];

let logFound = false;
for (const logPath of logPaths) {
  if (fs.existsSync(logPath)) {
    console.log('   📄 Found log at:', logPath);
    try {
      const logContent = execSync(`tail -50 "${logPath}" | grep -E "(Organization|invitation|email|SMTP|Zoho)" || true`, { encoding: 'utf8' });
      if (logContent) {
        console.log('   Recent relevant logs:');
        console.log(logContent);
      }
    } catch (e) {
      // Ignore grep errors
    }
    logFound = true;
    break;
  }
}
if (!logFound) {
  console.log('   ⚠️  No log files found');
}

// 4. Test email with full error details
console.log('\n4️⃣ Email System Test:');
const nodemailer = require('nodemailer');

async function testEmailSystem() {
  // Create transporter with debug enabled
  const transporter = nodemailer.createTransport({
    host: 'smtp.zoho.com',
    port: 587,
    secure: false,
    auth: {
      user: process.env.ZOHO_EMAIL || '<EMAIL>',
      pass: process.env.ZOHO_PASSWORD || 'not-set'
    },
    logger: true, // Enable logging
    debug: true, // Enable debug output
    tls: {
      rejectUnauthorized: true,
      minVersion: 'TLSv1.2'
    }
  });

  try {
    console.log('   🔌 Testing SMTP connection...');
    await transporter.verify();
    console.log('   ✅ SMTP connection successful!');
    
    // Try minimal test email
    console.log('   📧 Attempting to send test email...');
    const info = await transporter.sendMail({
      from: process.env.ZOHO_EMAIL || '<EMAIL>', // Try without alias first
      to: '<EMAIL>',
      subject: 'ChromaSync Test - Direct Send',
      text: 'Testing direct send without alias'
    });
    
    console.log('   ✅ Test email sent!');
    console.log('   Message ID:', info.messageId);
    
  } catch (error) {
    console.log('   ❌ Email test failed:', error.message);
    console.log('   Error code:', error.code);
    console.log('   Response:', error.response);
    console.log('   Command:', error.command);
    
    if (error.code === 'EAUTH') {
      console.log('\n   🔐 Authentication Issue Detected:');
      console.log('   1. Verify app password at: https://accounts.zoho.com/home#security/app_passwords');
      console.log('   2. Ensure 2FA is enabled on your Zoho account');
      console.log('   3. Try generating a NEW app-specific password');
      console.log('   4. Make sure you\'re using the app password, NOT your regular password');
    }
  }
}

// 5. Check if the invitation is being attempted
console.log('\n5️⃣ Checking Electron IPC Handlers:');
const ipcHandlerPath = path.join(__dirname, 'src/main/ipc/organization.ipc.ts');
if (fs.existsSync(ipcHandlerPath)) {
  console.log('   ✅ Organization IPC handler exists');
  const ipcContent = fs.readFileSync(ipcHandlerPath, 'utf8');
  const hasInviteHandler = ipcContent.includes('organization:invite-member');
  console.log('   ' + (hasInviteHandler ? '✅' : '❌') + ' Invite member handler registered');
} else {
  console.log('   ❌ Organization IPC handler not found');
}

// 6. Check the actual service implementation
console.log('\n6️⃣ Organization Service Check:');
const servicePath = path.join(__dirname, 'src/main/db/services/organization.service.ts');
if (fs.existsSync(servicePath)) {
  console.log('   ✅ Organization service exists');
  const serviceContent = fs.readFileSync(servicePath, 'utf8');
  
  // Check for key methods
  const checks = [
    { name: 'Email transporter init', pattern: 'initializeEmailTransporter' },
    { name: 'Invite member method', pattern: 'async inviteMember' },
    { name: 'Send email method', pattern: 'sendInvitationEmailDirect' },
    { name: 'Debug logging', pattern: 'Organization.*DEBUG' }
  ];
  
  checks.forEach(check => {
    const exists = serviceContent.includes(check.pattern);
    console.log('   ' + (exists ? '✅' : '❌') + ' ' + check.name);
  });
}

// Run async tests
testEmailSystem().then(() => {
  console.log('\n✅ Diagnostic complete');
}).catch(err => {
  console.error('\n❌ Diagnostic error:', err.message);
});