/**
 * @file Modal.tsx
 * @description A reusable modal component for the application
 */

import React, { useEffect } from 'react';
import { X } from 'lucide-react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  maxWidth?: string;
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  maxWidth = 'max-w-xl'
}) => {
  // Close modal when Escape key is pressed
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    
    // Prevent body scroll when modal is open
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    }

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  if (!isOpen) {return null;}

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center p-4 z-50" role="dialog" aria-modal="true" aria-labelledby={title ? "modal-title" : undefined}>
      <div 
        className={`${maxWidth} w-full bg-ui-background-primary rounded-lg shadow-xl flex flex-col overflow-hidden`}
        onClick={(e) => e.stopPropagation()}
      >
        {title && (
          <div className="px-6 py-4 border-b border-ui-border-light flex items-center justify-between">
            <h3 id="modal-title" className="text-lg font-medium text-ui-foreground-primary">
              {title}
            </h3>
            <button
              onClick={onClose}
              className="text-ui-muted hover:text-ui-foreground-primary transition-colors"
              aria-label="Close modal"
              data-testid="modal-close"
            >
              <X size={20} />
            </button>
          </div>
        )}
        
        {children}
      </div>
    </div>
  );
};

export default Modal; 