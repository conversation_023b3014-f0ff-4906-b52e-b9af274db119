/**
 * Custom hook for color calculations
 * Centralizes all color math and memoizes expensive calculations
 */

import { useMemo } from 'react';
import { calculateDeltaERgb, calculateContrastRatio } from '../../../../../shared/utils/color/analysis';
import { hexToRgb as hexToRgbUtil, rgbToHsl, rgbToCmyk, rgbToLab } from '../../../../../shared/utils/color/conversion';
import type { SimpleColorEntry, ColorMetrics, ContrastResult, DeltaEResult } from '../types';

export const useColorCalculations = (
  primaryColor: SimpleColorEntry | null,
  secondaryColor: SimpleColorEntry | null = null
) => {
  // Memoize color metrics calculation
  const primaryMetrics = useMemo<ColorMetrics | null>(() => {
    if (!primaryColor?.hex) {return null;}
    
    const rgb = hexToRgbUtil(primaryColor.hex);
    if (!rgb) {return null;}
    
    return {
      rgb,
      hsl: rgbToHsl(rgb),
      cmyk: rgbToCmyk(rgb),
      lab: rgbToLab(rgb)
    };
  }, [primaryColor?.hex]);

  const secondaryMetrics = useMemo<ColorMetrics | null>(() => {
    if (!secondaryColor?.hex) {return null;}
    
    const rgb = hexToRgbUtil(secondaryColor.hex);
    if (!rgb) {return null;}
    
    return {
      rgb,
      hsl: rgbToHsl(rgb),
      cmyk: rgbToCmyk(rgb),
      lab: rgbToLab(rgb)
    };
  }, [secondaryColor?.hex]);

  // Memoize Delta E calculation
  const deltaE = useMemo<DeltaEResult | null>(() => {
    if (!primaryMetrics?.rgb || !secondaryMetrics?.rgb) {return null;}
    
    const value = calculateDeltaERgb(primaryMetrics.rgb, secondaryMetrics.rgb);
    
    let interpretation: string;
    let description: string;
    
    if (value < 1) {
      interpretation = 'Imperceptible';
      description = 'Not perceptible by human eyes';
    } else if (value < 2) {
      interpretation = 'Perceptible through close observation';
      description = 'Only perceived by trained eyes';
    } else if (value < 3.5) {
      interpretation = 'Perceptible at a glance';
      description = 'Noticeable difference';
    } else if (value < 5) {
      interpretation = 'Noticeable difference';
      description = 'Clear difference in color';
    } else {
      interpretation = 'Different colors';
      description = 'Colors are distinctly different';
    }
    
    return { value, interpretation, description };
  }, [primaryMetrics?.rgb, secondaryMetrics?.rgb]);

  // Memoize contrast calculations
  const contrastWithWhite = useMemo<ContrastResult | null>(() => {
    if (!primaryMetrics?.rgb) {return null;}
    
    const ratio = calculateContrastRatio(primaryMetrics.rgb, { r: 255, g: 255, b: 255 });
    const level = ratio >= 7 ? 'AAA' : ratio >= 4.5 ? 'AA' : ratio >= 3 ? 'AA Large' : 'Fail';
    const passes = ratio >= 4.5;
    
    return { ratio, level, passes };
  }, [primaryMetrics?.rgb]);

  const contrastWithBlack = useMemo<ContrastResult | null>(() => {
    if (!primaryMetrics?.rgb) {return null;}
    
    const ratio = calculateContrastRatio(primaryMetrics.rgb, { r: 0, g: 0, b: 0 });
    const level = ratio >= 7 ? 'AAA' : ratio >= 4.5 ? 'AA' : ratio >= 3 ? 'AA Large' : 'Fail';
    const passes = ratio >= 4.5;
    
    return { ratio, level, passes };
  }, [primaryMetrics?.rgb]);

  const contrastWithSecondary = useMemo<ContrastResult | null>(() => {
    if (!primaryMetrics?.rgb || !secondaryMetrics?.rgb) {return null;}
    
    const ratio = calculateContrastRatio(primaryMetrics.rgb, secondaryMetrics.rgb);
    const level = ratio >= 7 ? 'AAA' : ratio >= 4.5 ? 'AA' : ratio >= 3 ? 'AA Large' : 'Fail';
    const passes = ratio >= 4.5;
    
    return { ratio, level, passes };
  }, [primaryMetrics?.rgb, secondaryMetrics?.rgb]);

  // Memoize ink coverage calculation
  const inkCoverage = useMemo(() => {
    if (!primaryMetrics?.cmyk) {return 0;}
    
    const { c, m, y, k } = primaryMetrics.cmyk;
    return c + m + y + k;
  }, [primaryMetrics?.cmyk]);

  const dominantInk = useMemo(() => {
    if (!primaryMetrics?.cmyk) {return 'None';}
    
    const { c, m, y, k } = primaryMetrics.cmyk;
    const max = Math.max(c, m, y, k);
    
    if (max === c) {return 'Cyan';}
    if (max === m) {return 'Magenta';}
    if (max === y) {return 'Yellow';}
    return 'Black';
  }, [primaryMetrics?.cmyk]);

  return {
    primaryMetrics,
    secondaryMetrics,
    deltaE,
    contrastWithWhite,
    contrastWithBlack,
    contrastWithSecondary,
    inkCoverage,
    dominantInk
  };
};