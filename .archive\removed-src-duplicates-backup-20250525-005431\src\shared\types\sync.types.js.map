{"version": 3, "file": "sync.types.js", "sourceRoot": "", "sources": ["sync.types.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH;;GAEG;AACH,IAAY,UAKX;AALD,WAAY,UAAU;IACpB,2BAAa,CAAA;IACb,iCAAmB,CAAA;IACnB,6BAAe,CAAA;IACf,iCAAmB,CAAA;AACrB,CAAC,EALW,UAAU,0BAAV,UAAU,QAKrB;AAoCD;;GAEG;AACH,IAAY,0BAIX;AAJD,WAAY,0BAA0B;IACpC,iEAAmC,CAAA;IACnC,+CAAiB,CAAA;IACjB,6CAAe,CAAA;AACjB,CAAC,EAJW,0BAA0B,0CAA1B,0BAA0B,QAIrC;AAgFD;;GAEG;AACH,IAAY,aAMX;AAND,WAAY,aAAa;IACvB,8CAA6B,CAAA;IAC7B,kDAAiC,CAAA;IACjC,0CAAyB,CAAA;IACzB,wDAAuC,CAAA;IACvC,0DAAyC,CAAA;AAC3C,CAAC,EANW,aAAa,6BAAb,aAAa,QAMxB"}