/**
 * Comparison Tab Component
 * Displays color comparison metrics and recommendations
 */

import React, { memo } from 'react';
import { AlertTriangle, Info } from 'lucide-react';
import type { ComparisonTabProps } from '../types';

export const ComparisonTab = memo<ComparisonTabProps>(({ 
  selectedColor, 
  secondaryColor, 
  recommendations,
  similarColors 
}) => {
  if (!selectedColor || !secondaryColor) {
    return (
      <div className="p-4 text-center text-ui-text-muted">
        <Info className="mx-auto mb-2 h-8 w-8 opacity-50" />
        <p>Select two colors to compare</p>
      </div>
    );
  }

  return (
    <div className="p-3">
      {/* Color comparison header */}
      <div className="bg-ui-background-tertiary dark:bg-zinc-800 rounded-md p-3 mb-3">
        <div className="flex items-center gap-3">
          {/* Primary Color */}
          <div className="flex-1">
            <div className="text-[10px] text-ui-text-muted mb-1">Primary Color</div>
            <div className="flex items-center gap-2">
              <div 
                className="w-12 h-12 rounded-md border border-ui-border"
                style={{ backgroundColor: selectedColor.hex }}
              />
              <div>
                <div className="text-xs font-medium">{selectedColor.pantone}</div>
                <div className="text-[10px] font-mono text-ui-text-muted">{selectedColor.hex}</div>
                <div className="text-[10px] text-ui-text-muted">{selectedColor.cmyk}</div>
              </div>
            </div>
          </div>

          {/* VS Indicator */}
          <div className="text-xs font-bold text-ui-text-muted">VS</div>

          {/* Secondary Color */}
          <div className="flex-1">
            <div className="text-[10px] text-ui-text-muted mb-1">Secondary Color</div>
            <div className="flex items-center gap-2">
              <div 
                className="w-12 h-12 rounded-md border border-ui-border"
                style={{ backgroundColor: secondaryColor.hex }}
              />
              <div>
                <div className="text-xs font-medium">{secondaryColor.pantone}</div>
                <div className="text-[10px] font-mono text-ui-text-muted">{secondaryColor.hex}</div>
                <div className="text-[10px] text-ui-text-muted">{secondaryColor.cmyk}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <div className="space-y-2 mb-3">
          {recommendations.map((rec, idx) => (
            <div 
              key={idx}
              className={`p-2 rounded-md text-xs ${
                rec.severity === 'error' 
                  ? 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
                  : rec.severity === 'warning'
                  ? 'bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800'
                  : 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
              }`}
            >
              <div className="flex items-start gap-2">
                {rec.severity === 'error' ? (
                  <AlertTriangle className="h-3 w-3 text-red-500 mt-0.5" />
                ) : rec.severity === 'warning' ? (
                  <AlertTriangle className="h-3 w-3 text-yellow-500 mt-0.5" />
                ) : (
                  <Info className="h-3 w-3 text-blue-500 mt-0.5" />
                )}
                <div className="flex-1">
                  <div className="font-medium mb-0.5">{rec.title}</div>
                  <div className="text-[10px] text-ui-text-muted mb-1">{rec.description}</div>
                  <div className="text-[10px] font-medium">{rec.actionable}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Similar Colors */}
      {similarColors.length > 0 && (
        <div className="bg-ui-background-tertiary dark:bg-zinc-800 rounded-md p-3">
          <h4 className="text-xs font-medium mb-2">Similar Colors</h4>
          <div className="grid grid-cols-3 gap-2">
            {similarColors.map((color) => (
              <div 
                key={color.id}
                className="text-center cursor-pointer hover:opacity-80 transition-opacity"
              >
                <div 
                  className="w-full aspect-square rounded-md border border-ui-border mb-1"
                  style={{ backgroundColor: color.hex }}
                />
                <div className="text-[9px] font-medium truncate">{color.pantone}</div>
                <div className="text-[8px] font-mono text-ui-text-muted">{color.hex}</div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
});

ComparisonTab.displayName = 'ComparisonTab';