# ChromaSync Refactoring Plan

## Priority 1: Database Module Refactoring

### Current Issues with database.ts (1,526 lines)
- Single file handling multiple responsibilities
- Mixing database initialization, connection pooling, CRUD operations, migrations, and utilities
- Hard to maintain and test
- Violates Single Responsibility Principle

### Proposed Structure:
```
src/main/db/
├── core/
│   ├── database-pool.ts         # Connection pooling logic
│   ├── database-init.ts         # Database initialization
│   ├── database-config.ts       # Configuration and settings
│   └── database-types.ts        # Shared types
├── repositories/
│   ├── color.repository.ts      # Color CRUD operations
│   ├── product.repository.ts    # Product CRUD operations
│   └── base.repository.ts       # Base repository class
├── migrations/
│   ├── migration-runner.ts      # Enhanced migration runner
│   ├── safe-migration.ts        # Safe migration utilities
│   └── migration-logger.ts      # Migration logging
├── utils/
│   ├── id-mapper.ts            # ID mapping utilities
│   ├── color-converter.ts      # Color conversion utilities
│   └── database-stats.ts       # Statistics and monitoring
└── index.ts                    # Main export file
```

## Priority 2: Sync Service Refactoring

### Current Issues with realtime-sync.service.ts (2,909 lines)
- Handles too many sync-related responsibilities
- Difficult to test individual sync strategies
- Queue management mixed with sync logic

### Proposed Structure:
```
src/main/services/sync/
├── core/
│   ├── sync-engine.ts          # Main sync orchestrator
│   ├── sync-queue.ts           # Queue management
│   └── sync-types.ts           # Shared types
├── strategies/
│   ├── color-sync.strategy.ts  # Color-specific sync
│   ├── product-sync.strategy.ts # Product-specific sync
│   └── conflict-resolver.ts    # Conflict resolution
├── monitors/
│   ├── sync-monitor.ts         # Performance monitoring
│   └── sync-analytics.ts       # Analytics collection
└── index.ts
```

## Priority 3: Color Data Files

### Current Issues with pantoneColors.ts & ralColors.ts (13,000+ lines combined)
- Huge static data in code files
- Increases bundle size
- Hard to update color definitions

### Proposed Solution:
1. Move to SQLite database tables
2. Create import scripts for initial data
3. Load on-demand instead of bundling

## Implementation Order:

### Phase 1: Database Refactoring (Week 1)
1. Create new directory structure
2. Extract DatabasePool class
3. Create repository pattern for colors/products
4. Migrate existing code to use repositories
5. Update all imports

### Phase 2: Sync Service Refactoring (Week 2)
1. Extract sync strategies
2. Implement queue management separately
3. Create conflict resolution service
4. Add monitoring capabilities

### Phase 3: Color Data Migration (Week 3)
1. Create color_libraries table
2. Write import scripts
3. Update color lookup logic
4. Remove static files

## Benefits:
- Improved maintainability
- Better testability
- Clearer separation of concerns
- Easier to implement new features
- Better performance (lazy loading)
- Reduced bundle size

## Migration Strategy:
1. Create new structure alongside existing code
2. Gradually migrate functionality
3. Update imports one module at a time
4. Ensure all tests pass at each step
5. Remove old code once migration complete