/**
 * @file PendingInvitations.tsx
 * @description Component to display and manage pending organization invitations
 */

import React, { useState, useEffect } from 'react';
import { useOrganizationStore } from '../../store/organization.store';
import { Mail, Clock, X, Loader2 } from 'lucide-react';

interface PendingInvitation {
  id: string;
  email: string;
  role: string;
  invitedBy: {
    id: string;
    name: string;
  };
  expiresAt: string;
  createdAt: string;
}

export const PendingInvitations: React.FC = () => {
  const { currentOrganization } = useOrganizationStore();
  const [invitations, setInvitations] = useState<PendingInvitation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load pending invitations
  const loadInvitations = async () => {
    if (!currentOrganization) {return;}

    setIsLoading(true);
    setError(null);

    try {
      const result = await window.organizationAPI.getPendingInvitations(currentOrganization.external_id);
      
      if (result.success && result.data) {
        setInvitations(result.data);
      } else {
        setError(result.error || 'Failed to load invitations');
      }
    } catch (err) {
      setError('Failed to load invitations');
    } finally {
      setIsLoading(false);
    }
  };

  // Load invitations on mount and when organization changes
  useEffect(() => {
    loadInvitations();
  }, [currentOrganization]);

  // Revoke invitation
  const handleRevokeInvitation = async (invitationId: string) => {
    if (!currentOrganization) {return;}

    const invitation = invitations.find(inv => inv.id === invitationId);
    if (!invitation) {return;}

    const confirmed = confirm(`Are you sure you want to revoke the invitation for ${invitation.email}?`);
    if (!confirmed) {return;}

    try {
      const result = await window.organizationAPI.revokeInvitation({
        organizationId: currentOrganization.external_id,
        invitationId
      });

      if (result.success) {
        // Remove from local state
        setInvitations(prev => prev.filter(inv => inv.id !== invitationId));
      } else {
        setError(result.error || 'Failed to revoke invitation');
      }
    } catch (err) {
      setError('Failed to revoke invitation');
    }
  };

  // Format expiry time
  const formatExpiry = (expiresAt: string) => {
    const expiryDate = new Date(expiresAt);
    const now = new Date();
    const daysLeft = Math.ceil((expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysLeft < 0) {return 'Expired';}
    if (daysLeft === 0) {return 'Expires today';}
    if (daysLeft === 1) {return 'Expires tomorrow';}
    return `Expires in ${daysLeft} days`;
  };

  if (!currentOrganization) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
      </div>
    );
  }

  if (invitations.length === 0) {
    return (
      <div className="text-center text-gray-500 py-8">
        No pending invitations
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900">Pending Invitations</h4>
      
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm">
          {error}
        </div>
      )}

      <div className="space-y-2">
        {invitations.map((invitation) => (
          <div
            key={invitation.id}
            className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
          >
            <div className="flex items-center space-x-3">
              <Mail className="w-4 h-4 text-gray-400" />
              <div>
                <div className="text-sm font-medium text-gray-900">
                  {invitation.email}
                </div>
                <div className="text-xs text-gray-500 flex items-center space-x-2">
                  <span className="capitalize">{invitation.role}</span>
                  <span>•</span>
                  <span>Invited by {invitation.invitedBy.name || 'Unknown'}</span>
                  <span>•</span>
                  <span className="flex items-center">
                    <Clock className="w-3 h-3 mr-1" />
                    {formatExpiry(invitation.expiresAt)}
                  </span>
                </div>
              </div>
            </div>
            
            <button
              onClick={() => handleRevokeInvitation(invitation.id)}
              className="p-1 text-gray-400 hover:text-red-500 transition-colors"
              title="Revoke invitation"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};
