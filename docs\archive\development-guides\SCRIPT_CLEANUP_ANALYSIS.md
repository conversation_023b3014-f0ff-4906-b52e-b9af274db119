# Script Cleanup Analysis

## Scripts Referenced in package.json (MUST KEEP)

### Build Scripts
- `scripts/watch-preload.js` - Used in `npm run dev`
- `scripts/build-config.cjs` - Used in build process
- `scripts/fix-preload.js` - Used in build process
- `scripts/obfuscate.js` - Used in obfuscated build
- `scripts/release-notes-generator.js` - Used for release notes
- `scripts/notarize.js` - Likely used in Mac build process (electron-builder)

## Scripts Used by Migration System (MUST KEEP)

### SQL Migrations in src/main/db/migrations/
- `002_add_organizations.sql`
- `003_add_organization_invitations.sql`
- `003_add_organizations_duplicate.sql`
- `004_add_users_table.sql`
- `005_add_sync_columns.sql`
- `006_add_user_preferences.sql`
- `007_add_code_column.sql`
- `008_fix_product_colors_schema.sql`
- `009_production_sync_compatibility.sql`
- `010_add_org_id_to_product_colors.sql`
- `011_add_rls_performance_indexes.sql`

### Schema Files (KEEP)
- `scripts/create-optimized-schema.sql` - Referenced in CLAUDE.md
- `scripts/supabase-schema.sql` - Referenced in CLAUDE.md
- `scripts/performance-indexes.sql` - Database optimization

## Active Development Scripts (PROBABLY KEEP)

### OAuth/Email Testing (Currently in development based on git status)
- `test-oauth-flow.ts` - OAuth testing
- `test-oauth-pkce.cjs` - PKCE OAuth testing
- `test-zoho-api-direct.cjs` - Zoho API testing
- `test-email-zoho.cjs` - Zoho email testing
- `refresh-zoho-token.cjs` - Token refresh utility
- `quick-zoho-setup.sh` - Zoho setup script
- `zoho-manual-steps.sh` - Manual setup guide

## One-Time Fix Scripts (CAN BE REMOVED/ARCHIVED)

### Product Color Fixes (Likely Applied)
- `fix-product-colors-sync.sql`
- `fix-product-sync.sql`
- `fix-supabase-product-colors.sql`
- `sync-product-colors-fix.sql`
- `sync-product-colors.sql`
- `test-product-colors.sql`
- `scripts/fix-cross-product-colors.sql`
- `scripts/fix-ivg-bar-colors.sql`
- `scripts/separate-product-colors.sql`
- `scripts/fix-product-color-associations.cjs`
- `fix-chromasync-products.sh`

### Legacy Migration Fixes (Likely Applied)
- `fix-legacy-user-migration.cjs`
- `emergency-schema-fix.sql`
- `scripts/URGENT_FIX_RECURSION.sql`
- `scripts/fix-rls-recursion.sql`
- `scripts/quick-fix-rls-recursion.sql`
- `scripts/fix-organization-policies.sql`

### Database Cleanup Scripts (One-time use)
- `scripts/cleanup-duplicate-colors.sql`
- `scripts/cleanup-databases.sh`
- `scripts/reset-databases.sh`
- `scripts/reset-local-db.sh`
- `scripts/clear-local-orgs.sh`
- `scripts/clear-local-db.ts`

## Debug/Check Scripts (CAN BE ARCHIVED)

### Database Inspection
- `check-database-schema.cjs`
- `check-users-schema.cjs`
- `check-supabase-data.cjs`
- `check-invitation-state.cjs`
- `debug-invitation-full.cjs`
- `debug-org-fk.cjs`
- `debug-product-colors.cjs`
- `debug-sync.cjs`
- `scripts/check-database-state.cjs`
- `scripts/check-db-status.sh`
- `scripts/check-db.cjs`
- `scripts/check-product-colors-electron.cjs`
- `scripts/check-product-colors-schema.cjs`
- `scripts/check-product-colors.cjs`
- `scripts/check-schema.cjs`
- `scripts/check-current-schema.js`
- `scripts/print-schema.js`
- `scripts/print-schema.ts`

### Data Analysis Scripts
- `scripts/analyze-database-data.cjs`
- `scripts/analyze-original-data.cjs`
- `scripts/analyze-shared-colors.cjs`
- `scripts/comprehensive-data-comparison.cjs`

## Test Scripts (CAN BE ARCHIVED)

### Email Testing (Keep if still developing email features)
- `test-email-delivery.cjs`
- `test-email-direct.cjs`
- `test-email.cjs`
- `test-invitation-email-ivg.cjs`
- `test-new-invitation-email.cjs`
- `test-smtp-connection.cjs`
- `test-support-alias.cjs`

### Other Test Scripts
- `scripts/test-multitenant.js`
- `wait-for-rate-limit.cjs`

## Utility Scripts (EVALUATE INDIVIDUALLY)

### User Management
- `add-user-as-owner.cjs` - Might be needed for admin tasks
- `find-user-database.cjs` - Might be needed for debugging

### Data Import/Export
- `scripts/export-old-data.sh` - Archive if data already exported
- `scripts/import-chromasync-data.ts` - Archive if import complete
- `scripts/data-migration/direct-import.cjs` - Archive if complete
- `scripts/data-migration/fix-cmyk-format.cjs` - Archive if complete
- `scripts/data-migration/transform-archive-data.cjs` - Archive if complete
- `sample-data-optimized.sql` - Keep for demo/testing

### Other
- `exchange-code.sh` - Purpose unclear
- `scripts/init-database.cjs` - Might be needed for fresh installs
- `scripts/setup-domain.sh` - Might be needed for deployment
- `scripts/update-types-guide.js` - Purpose unclear
- `scripts/clean-export.cjs` - Purpose unclear
- `scripts/build-config.js` - Duplicate of build-config.cjs?
- `scripts/run-org-migration.sh` - One-time migration

## Unused Migration Scripts (CAN BE REMOVED)

### In scripts/migrations/
- `001_create_organizations_table.sql` - Not in main migrations folder
- `add-organizations.sql` - Duplicate functionality

### Other Schema Scripts
- `scripts/create-users-table.sql` - Functionality in migrations
- `scripts/delete-organization-function.sql` - One-time fix
- `scripts/supabase-multi-tenant-migration.sql` - One-time migration
- `scripts/supabase-additional-performance-indexes.sql` - Likely applied

## Installation Scripts (KEEP)
- `install.sh` - Needed for installation
- `install.bat` - Needed for Windows installation

## RECOMMENDATION

1. **Archive these directories:**
   - Create `scripts/archive/` folder
   - Move all one-time fixes, debug scripts, and completed migrations there
   - Keep them for historical reference but out of active codebase

2. **Delete these files:**
   - Duplicate migration files
   - Test scripts for features that are complete
   - One-time fix scripts that have been applied

3. **Keep these files:**
   - All scripts referenced in package.json
   - Active migration files in src/main/db/migrations/
   - OAuth/Zoho scripts (currently in development)
   - Installation scripts
   - Schema definition files

4. **Document remaining scripts:**
   - Add comments to clarify purpose of utility scripts
   - Update README with script descriptions