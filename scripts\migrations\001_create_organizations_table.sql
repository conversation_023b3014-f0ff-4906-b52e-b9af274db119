-- ChromaSync Organization-First Schema
-- Task 1.1: Create organizations table
-- This implements the core organizations table with proper structure and constraints

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ========================================
-- Organizations Table
-- ========================================

-- Drop existing table if needed (for development)
-- DROP TABLE IF EXISTS public.organizations CASCADE;

-- Create the organizations table
CREATE TABLE public.organizations (
    -- Primary key using UUID for better distributed systems compatibility
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    
    -- Organization name (required)
    name TEXT NOT NULL,
    
    -- URL-friendly unique identifier for the organization
    -- Used in routes like: /org/[slug]/products
    slug TEXT UNIQUE NOT NULL,
    
    -- Timestamps for audit trail
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Additional metadata and settings
    settings JSONB DEFAULT '{}' NOT NULL,
    
    -- Billing/Plan information
    plan TEXT DEFAULT 'free' CHECK (plan IN ('free', 'team', 'enterprise')) NOT NULL,
    
    -- Plan limits (can be overridden per organization)
    max_members INTEGER DEFAULT 3 NOT NULL CHECK (max_members > 0),
    max_colors INTEGER DEFAULT 1000 NOT NULL CHECK (max_colors > 0),
    max_products INTEGER DEFAULT 100 NOT NULL CHECK (max_products > 0),
    
    -- Soft delete support
    deleted_at TIMESTAMPTZ,
    
    -- Add constraints
    CONSTRAINT valid_slug CHECK (slug ~ '^[a-z0-9][a-z0-9-]*[a-z0-9]$'),
    CONSTRAINT slug_min_length CHECK (LENGTH(slug) >= 3),
    CONSTRAINT name_not_empty CHECK (TRIM(name) != '')
);

-- ========================================
-- Indexes for Performance Optimization
-- ========================================

-- Index on slug for URL lookups (most common query pattern)
CREATE UNIQUE INDEX idx_organizations_slug ON public.organizations(slug) 
WHERE deleted_at IS NULL;

-- Index on created_at for sorting and pagination
CREATE INDEX idx_organizations_created_at ON public.organizations(created_at DESC);

-- Index for finding active organizations
CREATE INDEX idx_organizations_active ON public.organizations(deleted_at) 
WHERE deleted_at IS NULL;

-- Index for plan-based queries
CREATE INDEX idx_organizations_plan ON public.organizations(plan) 
WHERE deleted_at IS NULL;

-- Composite index for common query patterns
CREATE INDEX idx_organizations_plan_created ON public.organizations(plan, created_at DESC) 
WHERE deleted_at IS NULL;

-- ========================================
-- Row Level Security (RLS)
-- ========================================

-- Enable RLS on the organizations table
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;

-- ========================================
-- Comments for Documentation
-- ========================================

COMMENT ON TABLE public.organizations IS 'Core organizations table for multi-tenant architecture';
COMMENT ON COLUMN public.organizations.id IS 'Unique identifier for the organization (UUID)';
COMMENT ON COLUMN public.organizations.name IS 'Display name of the organization';
COMMENT ON COLUMN public.organizations.slug IS 'URL-friendly unique identifier used in routes';
COMMENT ON COLUMN public.organizations.settings IS 'JSONB field for flexible organization settings';
COMMENT ON COLUMN public.organizations.plan IS 'Subscription plan: free, team, or enterprise';
COMMENT ON COLUMN public.organizations.max_members IS 'Maximum number of members allowed in this organization';
COMMENT ON COLUMN public.organizations.max_colors IS 'Maximum number of colors allowed for this organization';
COMMENT ON COLUMN public.organizations.max_products IS 'Maximum number of products allowed for this organization';
COMMENT ON COLUMN public.organizations.deleted_at IS 'Soft delete timestamp - null means active';

-- ========================================
-- Trigger for updated_at
-- ========================================

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-update updated_at
CREATE TRIGGER update_organizations_updated_at
    BEFORE UPDATE ON public.organizations
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- ========================================
-- Grant Permissions
-- ========================================

-- Grant necessary permissions to authenticated users
GRANT SELECT ON public.organizations TO authenticated;
GRANT INSERT, UPDATE ON public.organizations TO authenticated;
-- DELETE is intentionally not granted - soft deletes only

-- Grant usage on any sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
