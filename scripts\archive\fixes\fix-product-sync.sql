-- Manual sync script to pull products from Supabase to local SQLite database
-- Run this in the local SQLite database at:
-- /Users/<USER>/Library/Application Support/chroma-sync/chromasync.db

-- First, ensure we have the local organization ID
SELECT id, external_id, name FROM organizations WHERE external_id = '4047153f-7be8-490b-9cb2-a1e3ed04b92b';

-- Insert products from Supabase data
-- Note: Adapting to local database schema which doesn't have sku column
INSERT OR REPLACE INTO products (external_id, organization_id, user_id, name, created_at, updated_at, is_master, is_active)
SELECT 
    external_id,
    (SELECT id FROM organizations WHERE external_id = '4047153f-7be8-490b-9cb2-a1e3ed04b92b') as organization_id,
    user_id,
    name,
    created_at,
    updated_at,
    0 as is_master,
    1 as is_active
FROM (
    SELECT '21bd3683-1102-41f0-9cf7-a7b8dda6d958' as external_id, 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf' as user_id, '2400 BARS' as name, '2025-05-29 19:30:11' as created_at, '2025-06-04 06:51:09' as updated_at
    UNION ALL SELECT '9f7f4190-296d-41e2-81fc-e5f9a1bed0f7', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', '4in1 Single Edition', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT 'a615d8e9-d25b-43ab-b4af-984bff828514', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', '4in1 Multi Edition', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT 'a160290d-738f-471c-86f1-c0c01365993b', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', '5500 SMART', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT '738de37d-c480-4e61-9c7a-5a51a5c1341f', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'Air 2in1', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT '6390c9bd-c27a-4fee-9fc4-1c7ae2c8f87a', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'Air 4in1', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT 'c29a84d0-74f3-48dd-8b2e-05365ab22229', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'Blue Raspberry Ice', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT '46fc3504-85fe-4aeb-8669-efddf672cca5', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'CALIPRO', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT 'ae792b25-66b2-4372-ad77-0f3d65d10ac4', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'Classic Mint', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT '0c6c1038-87f5-4134-b2c3-69938020440e', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'Double Apple', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT '851468e0-1ff3-4718-af70-a61b44bd96ba', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'IVG 12k Switch', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT '9f26715f-4c6c-465e-9b2d-6f549a52f533', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'IVG BAR', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT '49ab4936-e4c2-4ac9-8ecf-1213c05b466e', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'IVG Beyond CLK 6000', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT 'c789faf8-ca2c-4dda-919e-aca84404331f', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'IVG Beyond Cybr 6000', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT 'd843657a-8e69-494a-904f-8726676156a6', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'IVG MOSMO 2+10+10 DTL', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT '7d02b294-b080-43d4-a1c6-190ceae96e5f', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'IVG Pro 6000', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT '02bed167-8a19-44d2-aafa-2a99d4efa93e', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'IVG Savr', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT '4d368137-d517-4c86-80e9-1565013a6f60', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'Mango Ice', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT '2a353def-193a-4f81-b56a-dcfb377d1ef7', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'Smart 5500', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT 'b064e607-90f1-48d9-8afc-cbb74b58bac0', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'Smart MAX', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
    UNION ALL SELECT '5d347f2c-979a-4faf-8374-d06063c325c3', 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf', 'Strawberry Watermelon', '2025-05-29 19:30:11', '2025-06-04 06:51:09'
);

-- Verify the products were inserted
SELECT COUNT(*) as product_count FROM products WHERE organization_id = (SELECT id FROM organizations WHERE external_id = '4047153f-7be8-490b-9cb2-a1e3ed04b92b');

-- Show the products
SELECT id, external_id, name FROM products WHERE organization_id = (SELECT id FROM organizations WHERE external_id = '4047153f-7be8-490b-9cb2-a1e3ed04b92b') ORDER BY name;
