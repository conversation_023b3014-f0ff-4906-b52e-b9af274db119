/**
 * Test <NAME_EMAIL> email alias
 * Run this script to verify email configuration is working
 */

const nodemailer = require('nodemailer');
require('dotenv').config();

async function testEmailAlias() {
  console.log('🔧 Testing ChromaSync email alias configuration...\n');
  
  // Check environment variables
  console.log('📋 Environment Check:');
  console.log(`ZOHO_EMAIL: ${process.env.ZOHO_EMAIL ? '✅ Set' : '❌ Missing'} (${process.env.ZOHO_EMAIL || 'Not set'})`);
  console.log(`ZOHO_PASSWORD: ${process.env.ZOHO_PASSWORD ? '✅ Set' : '❌ Missing'} (Length: ${process.env.ZOHO_PASSWORD ? process.env.ZOHO_PASSWORD.length : 0} chars)`);
  console.log(`ZOHO_SUPPORT_ALIAS: ${process.env.ZOHO_SUPPORT_ALIAS ? '✅ Set' : '❌ Missing'} (${process.env.ZOHO_SUPPORT_ALIAS || 'Not set'})\n`);
  
  if (!process.env.ZOHO_EMAIL || !process.env.ZOHO_PASSWORD) {
    console.error('❌ Missing required environment variables');
    return;
  }
  
  // Create transporter
  console.log('🔌 Creating SMTP transporter...');
  const transporter = nodemailer.createTransporter({
    host: 'smtp.zoho.com',
    port: 587,
    secure: false,
    auth: {
      user: process.env.ZOHO_EMAIL,
      pass: process.env.ZOHO_PASSWORD
    },
    pool: true,
    maxConnections: 5,
    rateDelta: 20000,
    rateLimit: 5,
    requireTLS: true,
    tls: {
      ciphers: 'SSLv3'
    }
  });
  
  // Test connection
  console.log('🔌 Testing SMTP connection...');
  try {
    await transporter.verify();
    console.log('✅ SMTP connection successful!\n');
  } catch (error) {
    console.error('❌ SMTP connection failed:', error.message);
    console.log('\nError details:', error);
    return;
  }
  
  // Send test email
  console.log('📧 Sending test email...');
  try {
    const info = await transporter.sendMail({
      from: {
        name: 'ChromaSync Support Team',
        address: process.env.ZOHO_SUPPORT_ALIAS || '<EMAIL>'
      },
      to: process.env.ZOHO_EMAIL,
      subject: '🧪 Email Test - ChromaSync',
      text: 'If you receive this, your Zoho email configuration is working!',
      html: '<h2>✅ Success!</h2><p>Your Zoho email configuration is working correctly.</p>'
    });
    
    console.log('✅ Test email sent successfully!');
    console.log('Message ID:', info.messageId);
    console.log('\n🎉 Email configuration is working! Check your inbox.');
    
  } catch (error) {
    console.error('❌ Failed to send test email:', error.message);
    console.log('\nFull error:', error);
    
    if (error.responseCode === 554) {
      console.log('\n⚠️  The <EMAIL> alias may not be properly configured.');
      console.log('Please check your Zoho email alias settings.');
    }
  }
  
  transporter.close();
}

testEmailAlias().catch(console.error);
