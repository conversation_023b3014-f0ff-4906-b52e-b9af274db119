#!/usr/bin/env node

/**
 * Fix CMYK format in the import file
 * Converts "85,81,0,0" to "C:85 M:81 Y:0 K:0"
 */

const fs = require('fs');
const path = require('path');

function convertCMYKFormat(cmykString) {
  if (!cmykString || cmykString === '') {
    return 'C:0 M:0 Y:0 K:0';
  }
  
  // If already in correct format, return as is
  if (cmykString.includes('C:')) {
    return cmykString;
  }
  
  // Split comma-separated values
  const values = cmykString.split(',').map(v => v.trim());
  
  if (values.length !== 4) {
    console.warn(`Invalid CMYK format: ${cmykString}, using default`);
    return 'C:0 M:0 Y:0 K:0';
  }
  
  const [c, m, y, k] = values;
  return `C:${c} M:${m} Y:${y} K:${k}`;
}

function fixImportFile(inputPath, outputPath) {
  try {
    console.log(`Reading file from: ${inputPath}`);
    const rawData = fs.readFileSync(inputPath, 'utf-8');
    const data = JSON.parse(rawData);
    
    console.log(`Found ${data.length} entries to fix`);
    
    // Fix CMYK format for each entry
    const fixedData = data.map(entry => {
      return {
        ...entry,
        cmyk: convertCMYKFormat(entry.cmyk)
      };
    });
    
    // Write the fixed data
    console.log(`Writing fixed data to: ${outputPath}`);
    fs.writeFileSync(outputPath, JSON.stringify(fixedData, null, 2));
    
    console.log('CMYK format fixed successfully!');
    
    // Show a few examples
    console.log('\nExample conversions:');
    for (let i = 0; i < Math.min(5, data.length); i++) {
      console.log(`  "${data[i].cmyk}" → "${fixedData[i].cmyk}"`);
    }
    
    return fixedData;
  } catch (error) {
    console.error('Error fixing import file:', error);
    throw error;
  }
}

// CLI usage
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length < 1) {
    console.log('Usage: node fix-cmyk-format.cjs <input-file> [output-file]');
    console.log('Example: node fix-cmyk-format.cjs chromasync-import-ready.json chromasync-import-fixed.json');
    process.exit(1);
  }
  
  const inputFile = args[0];
  const outputFile = args[1] || inputFile.replace('.json', '-fixed.json');
  
  const inputPath = path.resolve(inputFile);
  const outputPath = path.resolve(outputFile);
  
  if (!fs.existsSync(inputPath)) {
    console.error(`Input file not found: ${inputPath}`);
    process.exit(1);
  }
  
  fixImportFile(inputPath, outputPath)
    .then(() => {
      console.log(`\nFixed file saved to: ${outputPath}`);
    })
    .catch(error => {
      console.error('\nFix failed:', error);
      process.exit(1);
    });
}

module.exports = { fixImportFile, convertCMYKFormat };
