-- ChromaSync Supabase Schema
-- This schema implements Google Auth, real-time sync, and GDPR compliance

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ========================================
-- GDPR Compliance Tables
-- ========================================

-- User consent and data management
CREATE TABLE public.user_consent (
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    terms_accepted_at TIMESTAMPTZ NOT NULL,
    privacy_accepted_at TIMESTAMPTZ NOT NULL,
    marketing_consent BOOLEAN DEFAULT FALSE,
    data_processing_consent BOOLEAN NOT NULL DEFAULT TRUE,
    consent_ip TEXT,
    consent_version TEXT NOT NULL DEFAULT '1.0',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Data export requests (GDPR Article 20)
CREATE TABLE public.data_export_requests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    requested_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    export_url TEXT,
    expires_at TIMESTAMPTZ
);

-- Deletion requests (GDPR Article 17)
CREATE TABLE public.deletion_requests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    requested_at TIMESTAMPTZ DEFAULT NOW(),
    scheduled_for TIMESTAMPTZ DEFAULT NOW() + INTERVAL '30 days',
    completed_at TIMESTAMPTZ,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'cancelled'))
);

-- ========================================
-- Main Application Tables
-- ========================================

-- Products table
CREATE TABLE public.products (
    id SERIAL PRIMARY KEY,
    external_id UUID DEFAULT uuid_generate_v4() UNIQUE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    sku TEXT,
    metadata JSONB DEFAULT '{}',
    device_id TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    sync_version INTEGER DEFAULT 1,
    UNIQUE(user_id, sku)
);

-- Create index for better performance
CREATE INDEX idx_products_user_active ON products(user_id, created_at DESC);

-- Compressed colors table using JSONB for color spaces
CREATE TABLE public.colors (
    id SERIAL PRIMARY KEY,
    external_id UUID DEFAULT uuid_generate_v4() UNIQUE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    source_id INTEGER DEFAULT 1,
    code TEXT NOT NULL,
    hex CHAR(7) NOT NULL CHECK (hex ~ '^#[0-9A-Fa-f]{6}$'),
    display_name TEXT,
    -- Only store CMYK in JSONB - RGB, LAB, and HSL are calculated from hex in the app
    -- This reduces storage and ensures consistency as these can be accurately derived
    color_spaces JSONB DEFAULT '{}', -- {cmyk: {c,m,y,k}}
    properties JSONB DEFAULT '{}',
    device_id TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    sync_version INTEGER DEFAULT 1,
    UNIQUE(user_id, source_id, code)
);

-- Optimized index for color lookups
CREATE INDEX idx_colors_user_hex ON colors(user_id, hex) WHERE hex IS NOT NULL;

-- Simplified junction table
CREATE TABLE public.product_colors (
    product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
    color_id INTEGER REFERENCES colors(id) ON DELETE CASCADE,
    display_order INTEGER DEFAULT 0,
    PRIMARY KEY (product_id, color_id)
);

-- Minimal sync tracking with last_sync for rate limiting
CREATE TABLE public.sync_metadata (
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    device_id TEXT NOT NULL,
    last_sync TIMESTAMPTZ DEFAULT NOW(),
    sync_count INTEGER DEFAULT 0,
    monthly_sync_count INTEGER DEFAULT 0,
    month_reset_at TIMESTAMPTZ DEFAULT NOW()
);

-- ========================================
-- Enable Row Level Security
-- ========================================

ALTER TABLE user_consent ENABLE ROW LEVEL SECURITY;
ALTER TABLE data_export_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE deletion_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE colors ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_colors ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_metadata ENABLE ROW LEVEL SECURITY;

-- ========================================
-- Row Level Security Policies
-- ========================================

-- User consent policies
CREATE POLICY "Users can manage own consent" ON user_consent 
TO authenticated
USING ((SELECT auth.uid()) = user_id);

-- Export request policies
CREATE POLICY "Users can manage own export requests" ON data_export_requests 
TO authenticated
USING ((SELECT auth.uid()) = user_id);

-- Deletion request policies
CREATE POLICY "Users can manage own deletion requests" ON deletion_requests 
TO authenticated
USING ((SELECT auth.uid()) = user_id);

-- Products policies
CREATE POLICY "Users can view own products" ON products 
FOR SELECT TO authenticated
USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert own products" ON products
FOR INSERT TO authenticated
WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update own products" ON products
FOR UPDATE TO authenticated
USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete own products" ON products
FOR DELETE TO authenticated
USING ((SELECT auth.uid()) = user_id);

-- Colors policies
CREATE POLICY "Users can view own colors" ON colors 
FOR SELECT TO authenticated
USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can insert own colors" ON colors
FOR INSERT TO authenticated
WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update own colors" ON colors
FOR UPDATE TO authenticated
USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete own colors" ON colors
FOR DELETE TO authenticated
USING ((SELECT auth.uid()) = user_id);

-- Product colors policies
CREATE POLICY "Users can manage product colors" ON product_colors 
TO authenticated
USING (
    product_id IN (
        SELECT id FROM products WHERE user_id = (SELECT auth.uid())
    )
);

-- Sync metadata policies
CREATE POLICY "Users can manage own sync data" ON sync_metadata 
TO authenticated
USING ((SELECT auth.uid()) = user_id);

-- ========================================
-- Functions and Triggers
-- ========================================

-- Function to track sync usage (for free tier limits)
CREATE OR REPLACE FUNCTION track_sync_usage()
RETURNS TRIGGER AS $$
BEGIN
    -- Reset monthly counter if new month
    IF OLD.month_reset_at < date_trunc('month', NOW()) THEN
        NEW.monthly_sync_count := 1;
        NEW.month_reset_at := date_trunc('month', NOW());
    ELSE
        NEW.monthly_sync_count := OLD.monthly_sync_count + 1;
    END IF;
    
    NEW.sync_count := OLD.sync_count + 1;
    NEW.last_sync := NOW();
    
    -- Check free tier limits (example: 1000 syncs/month)
    IF NEW.monthly_sync_count > 1000 THEN
        RAISE EXCEPTION 'Monthly sync limit exceeded for free tier';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply sync tracking trigger
CREATE TRIGGER update_sync_usage 
BEFORE UPDATE ON sync_metadata
FOR EACH ROW 
WHEN (OLD.last_sync IS DISTINCT FROM NEW.last_sync)
EXECUTE FUNCTION track_sync_usage();

-- Optimized function for batch operations
CREATE OR REPLACE FUNCTION batch_upsert_colors(
    p_user_id UUID,
    p_colors JSONB
) RETURNS VOID AS $$
BEGIN
    -- Batch insert with conflict handling
    INSERT INTO colors (external_id, user_id, source_id, code, hex, display_name, color_spaces, properties, device_id)
    SELECT 
        (value->>'external_id')::UUID,
        p_user_id,
        COALESCE((value->>'source_id')::INTEGER, 1),
        value->>'code',
        value->>'hex',
        value->>'display_name',
        value->'color_spaces',
        value->'properties',
        value->>'device_id'
    FROM jsonb_array_elements(p_colors)
    ON CONFLICT (user_id, source_id, code) 
    DO UPDATE SET
        hex = EXCLUDED.hex,
        display_name = EXCLUDED.display_name,
        color_spaces = EXCLUDED.color_spaces,
        properties = EXCLUDED.properties,
        device_id = EXCLUDED.device_id,
        updated_at = NOW(),
        sync_version = colors.sync_version + 1;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    NEW.sync_version = COALESCE(OLD.sync_version, 0) + 1;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_colors_updated_at BEFORE UPDATE ON colors
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_user_consent_updated_at BEFORE UPDATE ON user_consent
    FOR EACH ROW EXECUTE FUNCTION update_updated_at();

-- ========================================
-- Utility Functions
-- ========================================

-- Function to get database size for monitoring
CREATE OR REPLACE FUNCTION get_database_size()
RETURNS TABLE(size_mb NUMERIC) AS $$
BEGIN
    RETURN QUERY
    SELECT ROUND(pg_database_size(current_database()) / 1024.0 / 1024.0, 2) as size_mb;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;
