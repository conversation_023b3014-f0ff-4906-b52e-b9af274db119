/**
 * Color Library IPC Handlers
 * Handles communication between frontend and color library services
 */

import { ipcMain } from 'electron';
import { getColorLibraryQueryService } from '../db/services/color-library-query.service';
import { getColorLibraryImportService } from '../db/services/color-library-import.service';
import type { ColorLibrarySearchOptions } from '../db/services/color-library-query.service';

/**
 * Register color library IPC handlers
 */
export function registerColorLibraryIPC(): void {
  const queryService = getColorLibraryQueryService();
  const importService = getColorLibraryImportService();
  
  /**
   * Search colors across libraries
   */
  ipcMain.handle('color-library:search-colors', async (event, options: ColorLibrarySearchOptions) => {
    try {
      return await queryService.searchColors(options);
    } catch (error) {
      console.error('[ColorLibraryIPC] Failed to search colors:', error);
      throw error;
    }
  });
  
  /**
   * Get Pantone colors
   */
  ipcMain.handle('color-library:get-pantone-colors', async (event, options: Omit<ColorLibrarySearchOptions, 'library'>) => {
    try {
      return await queryService.getPantoneColors(options);
    } catch (error) {
      console.error('[ColorLibraryIPC] Failed to get Pantone colors:', error);
      throw error;
    }
  });
  
  /**
   * Get RAL colors
   */
  ipcMain.handle('color-library:get-ral-colors', async (event, options: Omit<ColorLibrarySearchOptions, 'library'>) => {
    try {
      return await queryService.getRalColors(options);
    } catch (error) {
      console.error('[ColorLibraryIPC] Failed to get RAL colors:', error);
      throw error;
    }
  });
  
  /**
   * Full-text search
   */
  ipcMain.handle('color-library:full-text-search', async (event, query: string, options: ColorLibrarySearchOptions) => {
    try {
      return await queryService.fullTextSearch(query, options);
    } catch (error) {
      console.error('[ColorLibraryIPC] Failed to perform full-text search:', error);
      throw error;
    }
  });
  
  /**
   * Get popular colors
   */
  ipcMain.handle('color-library:get-popular-colors', async (event, libraryCode?: string, limit: number = 20) => {
    try {
      return await queryService.getPopularColors(libraryCode, limit);
    } catch (error) {
      console.error('[ColorLibraryIPC] Failed to get popular colors:', error);
      throw error;
    }
  });
  
  /**
   * Get color by external ID
   */
  ipcMain.handle('color-library:get-color-by-external-id', async (event, externalId: string) => {
    try {
      return await queryService.getColorByExternalId(externalId);
    } catch (error) {
      console.error('[ColorLibraryIPC] Failed to get color by external ID:', error);
      throw error;
    }
  });
  
  /**
   * Get color by library and code
   */
  ipcMain.handle('color-library:get-color-by-code', async (event, libraryCode: string, colorCode: string) => {
    try {
      return await queryService.getColorByCode(libraryCode, colorCode);
    } catch (error) {
      console.error('[ColorLibraryIPC] Failed to get color by code:', error);
      throw error;
    }
  });
  
  /**
   * Increment usage count
   */
  ipcMain.handle('color-library:increment-usage', async (event, externalId: string) => {
    try {
      await queryService.incrementUsage(externalId);
    } catch (error) {
      console.error('[ColorLibraryIPC] Failed to increment usage:', error);
      throw error;
    }
  });
  
  /**
   * Get library statistics
   */
  ipcMain.handle('color-library:get-stats', async (event) => {
    try {
      return await importService.getLibraryStats();
    } catch (error) {
      console.error('[ColorLibraryIPC] Failed to get library stats:', error);
      throw error;
    }
  });
  
  /**
   * Force re-import of color libraries (admin function)
   */
  ipcMain.handle('color-library:force-reimport', async (event) => {
    try {
      console.log('[ColorLibraryIPC] Starting forced re-import of color libraries...');
      
      // Clear existing data
      await importService.clearLibraryData();
      
      // Re-import all libraries
      const results = await importService.importAllLibraries();
      
      console.log('[ColorLibraryIPC] Forced re-import completed:', results);
      return results;
    } catch (error) {
      console.error('[ColorLibraryIPC] Failed to force re-import:', error);
      throw error;
    }
  });
  
  /**
   * Check if libraries need importing
   */
  ipcMain.handle('color-library:needs-import', async (event) => {
    try {
      return await importService.needsImport();
    } catch (error) {
      console.error('[ColorLibraryIPC] Failed to check import status:', error);
      throw error;
    }
  });
  
  console.log('[ColorLibraryIPC] Color library IPC handlers registered');
}

/**
 * Unregister color library IPC handlers
 */
export function unregisterColorLibraryIPC(): void {
  const handlers = [
    'color-library:search-colors',
    'color-library:get-pantone-colors',
    'color-library:get-ral-colors',
    'color-library:full-text-search',
    'color-library:get-popular-colors',
    'color-library:get-color-by-external-id',
    'color-library:get-color-by-code',
    'color-library:increment-usage',
    'color-library:get-stats',
    'color-library:force-reimport',
    'color-library:needs-import'
  ];
  
  handlers.forEach(handler => {
    ipcMain.removeAllListeners(handler);
  });
  
  console.log('[ColorLibraryIPC] Color library IPC handlers unregistered');
}
