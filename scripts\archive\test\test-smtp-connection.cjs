// Simple test to verify SMTP connection
const net = require('net');
const tls = require('tls');
require('dotenv').config();

console.log('Testing Zoho SMTP connection...\n');

// Display config (hiding password)
console.log('Configuration:');
console.log('- Email:', process.env.ZOHO_EMAIL);
console.log('- Password:', process.env.ZOHO_PASSWORD ? '***SET***' : 'NOT SET');
console.log('- Support Alias:', process.env.ZOHO_SUPPORT_ALIAS);
console.log('');

// Test basic connection
const client = net.createConnection(587, 'smtp.zoho.com', () => {
  console.log('✅ Connected to smtp.zoho.com:587');
  client.end();
  
  // Now test TLS
  console.log('\nTesting TLS connection...');
  const tlsOptions = {
    host: 'smtp.zoho.com',
    port: 587,
    servername: 'smtp.zoho.com'
  };
  
  const tlsClient = tls.connect(587, 'smtp.zoho.com', tlsOptions, () => {
    console.log('✅ TLS connection established');
    console.log('- Protocol:', tlsClient.getProtocol());
    console.log('- Cipher:', tlsClient.getCipher().name);
    tlsClient.end();
    
    console.log('\n✅ Basic connectivity tests passed!');
    console.log('\nNext steps:');
    console.log('1. Ensure ZOHO_PASSWORD is an app-specific password');
    console.log('2. Verify <EMAIL> alias in Zoho settings');
    console.log('3. Test sending from the application');
  });
  
  tlsClient.on('error', (err) => {
    console.error('❌ TLS connection error:', err.message);
  });
});

client.on('error', (err) => {
  console.error('❌ Connection error:', err.message);
  console.log('\nPossible issues:');
  console.log('- Check internet connection');
  console.log('- Verify firewall settings');
  console.log('- Ensure port 587 is not blocked');
});
