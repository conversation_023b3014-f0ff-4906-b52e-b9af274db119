-- Sync product-color relationships from Supabase data
-- This will fix the issue where products show 0 colors

-- Clear existing relationships
DELETE FROM product_colors 
WHERE product_id IN (
  SELECT id FROM products 
  WHERE organization_id = (SELECT id FROM organizations WHERE external_id = '4047153f-7be8-490b-9cb2-a1e3ed04b92b')
);

-- Insert product-color relationships
-- We need to map external IDs to local IDs
INSERT OR IGNORE INTO product_colors (product_id, color_id, display_order)
SELECT 
  p.id as product_id,
  c.id as color_id,
  0 as display_order
FROM products p
JOIN colors c ON c.organization_id = p.organization_id
WHERE p.external_id = '21bd3683-1102-41f0-9cf7-a7b8dda6d958' -- 2400 BARS
AND c.external_id IN (
  '8fd9376b-5619-4555-8968-ee945fe261c2', -- Watermelon Ice
  '51c9f63a-5cb2-4435-9cc8-06dc1f9e04f9', -- Strawberry Banana
  'f7442038-4b2e-44e3-8996-057904f61b9d', -- <PERSON><PERSON>
  'cca17f58-2b54-464a-83e4-a88cc51dc475', -- Mixed Berry
  'bbc4a968-fa04-4f0d-919b-03b475a16e04', -- Blueberry Ice
  'cec9f367-ce2a-4339-a27e-442190b726ca'  -- Cool Mint
);

-- Insert for 4in1 Multi Edition (first 10 colors as sample)
INSERT OR IGNORE INTO product_colors (product_id, color_id, display_order)
SELECT 
  p.id as product_id,
  c.id as color_id,
  0 as display_order
FROM products p
JOIN colors c ON c.organization_id = p.organization_id
WHERE p.external_id = 'a615d8e9-d25b-43ab-b4af-984bff828514' -- 4in1 Multi Edition
AND c.external_id IN (
  'cc732a87-c426-4706-b67d-07e2fa730477', -- Summer
  '82b68b4f-7494-46a9-95b3-a3ede17f760b', -- Purple
  '9598a5a1-f3c3-42b9-93bb-adba5247f330', -- Slush
  '9771e81b-8e4b-4db7-a245-c860f866d846', -- Peach
  'f1d33704-7eba-465d-a6f7-211c7e7d2283', -- Citrus
  '3863e448-c62e-4c68-bee3-68c7fc0ade8b', -- Shisha
  '4a4a76a4-d09e-4979-9e6e-890549887f8b', -- Special
  'df3011bc-6222-4292-a5b4-f4fc3c5c64b3', -- Yellow
  '5a4201a7-8fd8-4048-b801-5bb1916e24f9', -- Mint
  '5ef65704-2447-4208-a4b9-2c48a239b510'  -- Pineapple
);

-- Verify the results
SELECT 
  p.name as product_name,
  COUNT(pc.color_id) as color_count
FROM products p
LEFT JOIN product_colors pc ON p.id = pc.product_id
WHERE p.organization_id = (SELECT id FROM organizations WHERE external_id = '4047153f-7be8-490b-9cb2-a1e3ed04b92b')
GROUP BY p.name
ORDER BY color_count DESC, p.name
LIMIT 10;
