#!/usr/bin/env node

/**
 * ChromaSync Performance Test Runner
 * Run with: npm run test:performance
 */

import { runPerformanceBenchmarks } from './benchmark';
import * as fs from 'fs';
import * as path from 'path';

console.log('╔═══════════════════════════════════════════════════════════════╗');
console.log('║          ChromaSync Performance Validation Suite              ║');
console.log('║                  Testing with 100k+ Colors                    ║');
console.log('╚═══════════════════════════════════════════════════════════════╝\n');

// Ensure we have enough memory
if (process.argv.includes('--max-old-space-size') === false) {
  console.log('💡 Tip: Run with increased memory for best results:');
  console.log('   node --max-old-space-size=4096 run-performance-tests.js\n');
}

// Check if running with GC exposed
if (!global.gc) {
  console.log('💡 Tip: Run with --expose-gc for memory measurements:');
  console.log('   node --expose-gc run-performance-tests.js\n');
}

async function main() {
  const startTime = Date.now();
  
  try {
    // Run the benchmarks
    await runPerformanceBenchmarks();
    
    // Generate report
    const reportPath = path.join(__dirname, `performance-report-${new Date().toISOString().split('T')[0]}.txt`);
    
    // Capture console output to file
    const originalLog = console.log;
    const output: string[] = [];
    
    console.log = (...args) => {
      output.push(args.join(' '));
      originalLog(...args);
    };
    
    // Save report
    fs.writeFileSync(reportPath, output.join('\n'));
    console.log = originalLog;
    
    console.log(`\n📄 Report saved to: ${reportPath}`);
    
    const totalTime = ((Date.now() - startTime) / 1000).toFixed(2);
    console.log(`\n✅ Performance validation completed in ${totalTime} seconds`);
    
    // Exit with appropriate code
    process.exit(0);
  } catch (error) {
    console.error('\n❌ Performance test failed:', error);
    process.exit(1);
  }
}

// Run the tests
main().catch(console.error);