/**
 * @file startup-optimizer.ts
 * @description Utilities for optimizing application startup performance
 */

import { app } from 'electron';
import { performance } from 'perf_hooks';
import fs from 'fs';
import path from 'path';

interface StartupMetrics {
  appReadyTime: number;
  windowCreationTime: number;
  databaseInitTime: number;
  ipcHandlerRegTime: number;
  totalStartupTime: number;
}

class StartupOptimizer {
  private metrics: Partial<StartupMetrics> = {};
  private startTime: number = performance.now();
  
  markStage(stage: keyof StartupMetrics, value?: number) {
    this.metrics[stage] = value ?? performance.now() - this.startTime;
    console.log(`[Startup] ${stage}: ${this.metrics[stage]}ms`);
  }
  
  getMetrics(): StartupMetrics {
    return {
      appReadyTime: 0,
      windowCreationTime: 0,
      databaseInitTime: 0,
      ipcHandlerRegTime: 0,
      totalStartupTime: 0,
      ...this.metrics
    };
  }
  
  logStartupSummary() {
    const metrics = this.getMetrics();
    console.log('\n=== ChromaSync Startup Performance ===');
    console.log(`App Ready Time: ${metrics.appReadyTime}ms`);
    console.log(`Window Creation: ${metrics.windowCreationTime}ms`);
    console.log(`Database Init: ${metrics.databaseInitTime}ms`);
    console.log(`IPC Handlers: ${metrics.ipcHandlerRegTime}ms`);
    console.log(`Total Startup: ${metrics.totalStartupTime}ms`);
    console.log('=====================================\n');
    
    // Save metrics to file for analysis
    this.saveMetricsToFile(metrics);
  }
  
  private saveMetricsToFile(metrics: StartupMetrics) {
    try {
      const userDataPath = app.getPath('userData');
      const metricsFile = path.join(userDataPath, 'startup-metrics.json');
      
      let historicalMetrics = [];
      if (fs.existsSync(metricsFile)) {
        const data = fs.readFileSync(metricsFile, 'utf8');
        historicalMetrics = JSON.parse(data);
      }
      
      historicalMetrics.push({
        ...metrics,
        timestamp: Date.now(),
        platform: process.platform,
        version: app.getVersion()
      });
      
      // Keep only last 50 startup metrics
      if (historicalMetrics.length > 50) {
        historicalMetrics = historicalMetrics.slice(-50);
      }
      
      fs.writeFileSync(metricsFile, JSON.stringify(historicalMetrics, null, 2));
    } catch (error) {
      console.warn('[Startup] Failed to save metrics:', error);
    }
  }
  
  // Optimization utilities
  optimizeWindowCreation() {
    // Pre-allocate resources before window creation
    return {
      // Optimize window size based on screen
      getOptimalWindowSize: () => {
        const { screen } = require('electron');
        const primaryDisplay = screen.getPrimaryDisplay();
        const { width, height } = primaryDisplay.workAreaSize;
        
        // Use 85% of screen size, but ensure minimum height for 2 rows of swatches
        // With header, controls, and 2 rows of swatches (h-36 each), we need at least 800px
        return {
          width: Math.min(Math.floor(width * 0.85), 1600),
          height: Math.min(Math.floor(height * 0.85), 1000),
          minWidth: 1200,
          minHeight: 800
        };
      },
      
      // Pre-configure common window settings
      getOptimizedWebPreferences: () => ({
        nodeIntegration: false,
        contextIsolation: true,
        sandbox: true,
        webSecurity: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        backgroundThrottling: false,
        offscreen: false
      })
    };
  }
  
  // Memory optimization
  optimizeMemory() {
    if (global.gc) {
      global.gc();
      console.log('[Startup] Garbage collection triggered');
    }
  }
  
  // Async initialization helper
  async initializeInParallel<T>(tasks: Array<() => Promise<T>>): Promise<T[]> {
    const startTime = performance.now();
    const results = await Promise.allSettled(tasks.map(task => task()));
    const endTime = performance.now();
    
    console.log(`[Startup] Parallel initialization completed in ${endTime - startTime}ms`);
    
    // Log any failures
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        console.warn(`[Startup] Task ${index} failed:`, result.reason);
      }
    });
    
    return results
      .filter((result): result is PromiseFulfilledResult<T> => result.status === 'fulfilled')
      .map(result => result.value);
  }
}

// Singleton instance
export const startupOptimizer = new StartupOptimizer();

// Helper functions for common optimizations
export const createOptimizedWindow = () => {
  const optimizer = startupOptimizer.optimizeWindowCreation();
  return {
    ...optimizer.getOptimalWindowSize(),
    webPreferences: optimizer.getOptimizedWebPreferences()
  };
};

export const benchmarkAsync = async <T>(
  name: string, 
  operation: () => Promise<T>
): Promise<T> => {
  const start = performance.now();
  try {
    const result = await operation();
    const end = performance.now();
    console.log(`[Startup] ${name} completed in ${end - start}ms`);
    return result;
  } catch (error) {
    const end = performance.now();
    console.error(`[Startup] ${name} failed after ${end - start}ms:`, error);
    throw error;
  }
};