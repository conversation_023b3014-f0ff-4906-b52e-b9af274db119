/**
 * Debug Organization Foreign Key Issue
 */

const { execSync } = require('child_process');

function debugOrgFK() {
  console.log('🔍 Debugging organization foreign key constraint...');
  
  try {
    // Use the app's npm script to check the database
    console.log('📊 Checking organization data in database...');
    
    const result = execSync(`
      npm run dev -- --mode=debug --sql='
        SELECT 
          o.id, 
          o.external_id, 
          o.name,
          COUNT(om.user_id) as member_count
        FROM organizations o 
        LEFT JOIN organization_members om ON o.id = om.organization_id 
        WHERE o.external_id = "4047153f-7be8-490b-9cb2-a1e3ed04b92b"
        GROUP BY o.id;
      '
    `, { encoding: 'utf8', cwd: __dirname });
    
    console.log('✅ Database query result:');
    console.log(result);
    
  } catch (error) {
    console.error('❌ Error running debug query:', error.message);
    
    // Try alternative approach - check if we can access the database file directly
    console.log('\n🔧 Trying alternative check...');
    
    try {
      // Check if the database file exists
      const fs = require('fs');
      const dbPath = '/Users/<USER>/Library/Application Support/ChromaSync/chromasync.db';
      
      if (fs.existsSync(dbPath)) {
        console.log('✅ Database file exists at:', dbPath);
        
        const stats = fs.statSync(dbPath);
        console.log('📁 File size:', stats.size, 'bytes');
        console.log('📅 Last modified:', stats.mtime);
        
        console.log('\n💡 Possible causes of FK constraint error:');
        console.log('   1. Organization ID from query is null/undefined');
        console.log('   2. Organization ID doesn\'t exist in organizations table');
        console.log('   3. Data type mismatch (string vs integer)');
        console.log('   4. Another FK constraint we haven\'t identified');
        
        console.log('\n🎯 Next steps:');
        console.log('   - Check if org.id is null in the inviteMember function');
        console.log('   - Add detailed logging before the INSERT statement');
        console.log('   - Verify the exact SQL being executed');
        
      } else {
        console.log('❌ Database file not found at:', dbPath);
      }
      
    } catch (fsError) {
      console.error('❌ File system error:', fsError.message);
    }
  }
}

debugOrgFK();