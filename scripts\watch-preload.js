#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const preloadPath = path.join(__dirname, '../out/preload');
const mjsFile = path.join(preloadPath, 'index.mjs');
const jsFile = path.join(preloadPath, 'index.js');

function convertPreload() {
  try {
    if (!fs.existsSync(mjsFile)) {
      return;
    }

    const content = fs.readFileSync(mjsFile, 'utf8');
    const convertedContent = content
      .replace(/^import\s+{([^}]+)}\s+from\s+["']([^"']+)["'];?$/gm, (match, imports, module) => {
        return `const {${imports}} = require("${module}");`;
      })
      .replace(/^import\s+(\w+)\s+from\s+["']([^"']+)["'];?$/gm, (match, name, module) => {
        return `const ${name} = require("${module}");`;
      });

    fs.writeFileSync(jsFile, convertedContent, 'utf8');
    console.log('Preload converted: .mjs → .js');
  } catch (error) {
    console.error('Error converting preload:', error);
  }
}

// Initial conversion
convertPreload();

// Watch for changes
if (fs.existsSync(preloadPath)) {
  fs.watch(preloadPath, (eventType, filename) => {
    if (filename === 'index.mjs') {
      setTimeout(convertPreload, 100);
    }
  });
  console.log('Watching preload for changes...');
}