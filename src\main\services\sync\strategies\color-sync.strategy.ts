/**
 * @file color-sync.strategy.ts
 * @description Color-specific synchronization strategy
 * 
 * This strategy handles synchronization of color data between local SQLite
 * and Supabase, with optimized CMYK-only sync and color space calculations.
 */

import { getSupabaseClient } from '../../supabase-client';
import { getDatabase } from '../../../db/database';
import { 
  ISyncStrategy, 
  SyncOperation, 
  SyncQueueItem, 
  SyncResult, 
  SyncError 
} from '../core/sync-types';

// ============================================================================
// COLOR SYNC STRATEGY
// ============================================================================

/**
 * Strategy for synchronizing color data
 */
export class ColorSyncStrategy implements ISyncStrategy {
  readonly name = 'color-sync';
  readonly priority = 2; // Medium priority

  private userId: string | null = null;
  private organizationId: string | null = null;

  constructor(userId: string, organizationId: string) {
    this.userId = userId;
    this.organizationId = organizationId;
  }

  /**
   * Check if this strategy can handle the given table and operation
   */
  canHandle(table: string, operation: SyncOperation): boolean {
    return table === 'colors';
  }

  /**
   * Execute color synchronization
   */
  async execute(items: SyncQueueItem[]): Promise<SyncResult> {
    const startTime = Date.now();
    const errors: SyncError[] = [];
    let itemsSucceeded = 0;
    let itemsFailed = 0;

    try {
      console.log(`[ColorSync] Processing ${items.length} color items`);

      // Group items by action
      const upsertItems = items.filter(item => item.action === 'upsert');
      const deleteItems = items.filter(item => item.action === 'delete');

      // Process upserts
      if (upsertItems.length > 0) {
        const upsertResult = await this.processUpserts(upsertItems);
        itemsSucceeded += upsertResult.succeeded;
        itemsFailed += upsertResult.failed;
        errors.push(...upsertResult.errors);
      }

      // Process deletes
      if (deleteItems.length > 0) {
        const deleteResult = await this.processDeletes(deleteItems);
        itemsSucceeded += deleteResult.succeeded;
        itemsFailed += deleteResult.failed;
        errors.push(...deleteResult.errors);
      }

      return {
        success: itemsFailed === 0,
        itemsProcessed: items.length,
        itemsSucceeded,
        itemsFailed,
        errors,
        duration: Date.now() - startTime
      };

    } catch (error) {
      console.error('[ColorSync] Strategy execution failed:', error);
      
      return {
        success: false,
        itemsProcessed: items.length,
        itemsSucceeded: 0,
        itemsFailed: items.length,
        errors: [{
          id: `color-sync-error-${Date.now()}`,
          timestamp: Date.now(),
          operation: 'color sync',
          category: 'database',
          severity: 'high',
          message: error.message,
          originalError: error,
          recoverable: true
        }],
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Validate color data structure
   */
  validateData(data: any): boolean {
    if (!data) return false;
    
    // Required fields for color data
    const requiredFields = ['external_id', 'hex', 'code'];
    
    return requiredFields.every(field => 
      data.hasOwnProperty(field) && data[field] !== null && data[field] !== undefined
    );
  }

  /**
   * Transform color data for Supabase sync
   */
  transformData(localColor: any): any {
    // Only sync CMYK values to Supabase (RGB, LAB, HSL calculated from hex)
    const colorSpaces: any = {};
    
    // Extract CMYK if available
    if (localColor.properties) {
      try {
        const props = typeof localColor.properties === 'string' 
          ? JSON.parse(localColor.properties) 
          : localColor.properties;
          
        if (props.cmyk) {
          colorSpaces.cmyk = props.cmyk;
        }
      } catch (error) {
        console.warn('[ColorSync] Failed to parse color properties:', error);
      }
    }

    return {
      external_id: localColor.external_id,
      user_id: this.userId,
      organization_id: this.organizationId,
      source_id: localColor.source_id || 1,
      code: localColor.code,
      hex: localColor.hex,
      display_name: localColor.display_name,
      color_spaces: colorSpaces, // JSONB field in Supabase
      device_id: localColor.device_id,
      created_at: localColor.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Process color upsert operations
   */
  private async processUpserts(items: SyncQueueItem[]): Promise<{
    succeeded: number;
    failed: number;
    errors: SyncError[];
  }> {
    const errors: SyncError[] = [];
    let succeeded = 0;
    let failed = 0;

    try {
      const supabase = getSupabaseClient();
      const db = await getDatabase();

      // Get local color data
      const colorData: any[] = [];
      
      for (const item of items) {
        try {
          let localColor;
          
          if (item.data) {
            // Data provided in queue item
            localColor = item.data;
          } else {
            // Fetch from local database
            const stmt = db.prepare(`
              SELECT * FROM colors 
              WHERE external_id = ? AND organization_id = (
                SELECT id FROM organizations WHERE external_id = ?
              )
            `);
            localColor = stmt.get(item.id, this.organizationId);
          }

          if (!localColor) {
            console.warn(`[ColorSync] Color not found: ${item.id}`);
            failed++;
            continue;
          }

          if (!this.validateData(localColor)) {
            console.warn(`[ColorSync] Invalid color data: ${item.id}`);
            failed++;
            continue;
          }

          const transformedColor = this.transformData(localColor);
          colorData.push(transformedColor);
          
        } catch (error) {
          console.error(`[ColorSync] Error processing color ${item.id}:`, error);
          failed++;
          
          errors.push({
            id: `color-process-error-${Date.now()}-${item.id}`,
            timestamp: Date.now(),
            operation: `process color ${item.id}`,
            category: 'validation',
            severity: 'medium',
            message: `Failed to process color: ${error.message}`,
            originalError: error,
            recoverable: true
          });
        }
      }

      if (colorData.length === 0) {
        return { succeeded, failed, errors };
      }

      // Batch upsert to Supabase
      console.log(`[ColorSync] Upserting ${colorData.length} colors to Supabase`);
      
      const { data, error } = await supabase
        .from('colors')
        .upsert(colorData, {
          onConflict: 'user_id,source_id,code'
        })
        .select();

      if (error) {
        console.error('[ColorSync] Supabase upsert error:', error);
        failed += colorData.length;
        
        errors.push({
          id: `color-upsert-error-${Date.now()}`,
          timestamp: Date.now(),
          operation: 'color batch upsert',
          category: 'database',
          severity: 'high',
          message: `Supabase upsert failed: ${error.message}`,
          originalError: error,
          recoverable: true
        });
      } else {
        succeeded += colorData.length;
        console.log(`[ColorSync] Successfully upserted ${colorData.length} colors`);
        console.log(`[ColorSync] Supabase returned ${data?.length || 0} records`);
      }

    } catch (error) {
      console.error('[ColorSync] Upsert processing failed:', error);
      failed += items.length;
      
      errors.push({
        id: `color-upsert-batch-error-${Date.now()}`,
        timestamp: Date.now(),
        operation: 'color upsert batch',
        category: 'database',
        severity: 'high',
        message: `Batch upsert failed: ${error.message}`,
        originalError: error,
        recoverable: true
      });
    }

    return { succeeded, failed, errors };
  }

  /**
   * Process color delete operations
   */
  private async processDeletes(items: SyncQueueItem[]): Promise<{
    succeeded: number;
    failed: number;
    errors: SyncError[];
  }> {
    const errors: SyncError[] = [];
    let succeeded = 0;
    let failed = 0;

    try {
      const supabase = getSupabaseClient();
      
      // Extract external IDs for deletion
      const externalIds = items.map(item => item.id);
      
      console.log(`[ColorSync] Deleting ${externalIds.length} colors from Supabase`);
      
      const { error } = await supabase
        .from('colors')
        .delete()
        .in('external_id', externalIds)
        .eq('user_id', this.userId)
        .eq('organization_id', this.organizationId);

      if (error) {
        console.error('[ColorSync] Supabase delete error:', error);
        failed += items.length;
        
        errors.push({
          id: `color-delete-error-${Date.now()}`,
          timestamp: Date.now(),
          operation: 'color batch delete',
          category: 'database',
          severity: 'high',
          message: `Supabase delete failed: ${error.message}`,
          originalError: error,
          recoverable: true
        });
      } else {
        succeeded += items.length;
        console.log(`[ColorSync] Successfully deleted ${items.length} colors`);
      }

    } catch (error) {
      console.error('[ColorSync] Delete processing failed:', error);
      failed += items.length;
      
      errors.push({
        id: `color-delete-batch-error-${Date.now()}`,
        timestamp: Date.now(),
        operation: 'color delete batch',
        category: 'database',
        severity: 'high',
        message: `Batch delete failed: ${error.message}`,
        originalError: error,
        recoverable: true
      });
    }

    return { succeeded, failed, errors };
  }
}
