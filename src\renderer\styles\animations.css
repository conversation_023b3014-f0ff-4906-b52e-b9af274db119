/**
 * ChromaSync 60fps Animation System
 * Smooth, performant animations using GPU acceleration
 */

/* Base animation settings for 60fps performance */
:root {
  --animation-duration-fast: 150ms;
  --animation-duration-normal: 250ms;
  --animation-duration-slow: 350ms;
  --animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --animation-easing-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Enable hardware acceleration for better performance */
.animate-gpu {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Smooth fade animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.9);
  }
}

/* Smooth color transitions */
@keyframes colorPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Loading animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Smooth button hover effects */
@keyframes buttonHover {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.02);
  }
}

@keyframes buttonPress {
  from {
    transform: scale(1.02);
  }
  to {
    transform: scale(0.98);
  }
}

/* Modal animations */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes modalSlideOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
}

@keyframes backdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Color swatch animations */
@keyframes swatchHover {
  from {
    transform: scale(1) rotate(0deg);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  to {
    transform: scale(1.05) rotate(1deg);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

@keyframes swatchSelect {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1.05);
  }
}

/* Notification animations */
@keyframes notificationSlideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes notificationSlideOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

/* Progress bar animations */
@keyframes progressFill {
  from {
    width: 0%;
  }
  to {
    width: var(--progress-width, 50%);
  }
}

@keyframes progressStripes {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 30px 0;
  }
}

/* Navigation animations */
@keyframes tabSlide {
  from {
    transform: translateX(var(--tab-start, -100%));
  }
  to {
    transform: translateX(0);
  }
}

/* Utility classes for smooth animations */
.animate-fade-in {
  animation: fadeIn var(--animation-duration-normal) var(--animation-easing) forwards;
}

.animate-fade-out {
  animation: fadeOut var(--animation-duration-normal) var(--animation-easing) forwards;
}

.animate-slide-in-left {
  animation: slideInFromLeft var(--animation-duration-normal) var(--animation-easing) forwards;
}

.animate-slide-in-right {
  animation: slideInFromRight var(--animation-duration-normal) var(--animation-easing) forwards;
}

.animate-scale-in {
  animation: scaleIn var(--animation-duration-fast) var(--animation-easing) forwards;
}

.animate-scale-out {
  animation: scaleOut var(--animation-duration-fast) var(--animation-easing) forwards;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 20%,
    rgba(255, 255, 255, 0.5) 60%,
    rgba(255, 255, 255, 0)
  );
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

/* Smooth transitions for all interactive elements */
.transition-smooth {
  transition: all var(--animation-duration-normal) var(--animation-easing);
}

.transition-fast {
  transition: all var(--animation-duration-fast) var(--animation-easing);
}

.transition-slow {
  transition: all var(--animation-duration-slow) var(--animation-easing);
}

/* Button animations */
.btn-animated {
  transition: all var(--animation-duration-fast) var(--animation-easing);
  transform: translateZ(0); /* Hardware acceleration */
}

.btn-animated:hover {
  animation: buttonHover var(--animation-duration-fast) var(--animation-easing) forwards;
}

.btn-animated:active {
  animation: buttonPress var(--animation-duration-fast) var(--animation-easing) forwards;
}

/* Modal animations */
.modal-overlay {
  animation: backdropFadeIn var(--animation-duration-normal) var(--animation-easing) forwards;
}

.modal-overlay.closing {
  animation: backdropFadeIn var(--animation-duration-normal) var(--animation-easing) reverse forwards;
}

.modal-content {
  animation: modalSlideIn var(--animation-duration-normal) var(--animation-easing) forwards;
}

.modal-content.closing {
  animation: modalSlideOut var(--animation-duration-normal) var(--animation-easing) forwards;
}

/* Color swatch animations */
.color-swatch {
  transition: all var(--animation-duration-fast) var(--animation-easing);
  transform: translateZ(0);
}

.color-swatch:hover {
  animation: swatchHover var(--animation-duration-normal) var(--animation-easing) forwards;
}

.color-swatch.selected {
  animation: swatchSelect var(--animation-duration-fast) var(--animation-easing) forwards;
}

.color-swatch.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* List item animations */
.list-item {
  transition: all var(--animation-duration-fast) var(--animation-easing);
}

.list-item:hover {
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.list-item.entering {
  animation: slideInFromLeft var(--animation-duration-normal) var(--animation-easing) forwards;
}

.list-item.exiting {
  animation: fadeOut var(--animation-duration-fast) var(--animation-easing) forwards;
}

/* Progress indicators */
.progress-bar {
  overflow: hidden;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007acc, #0099ff);
  border-radius: 4px;
  animation: progressFill var(--animation-duration-slow) var(--animation-easing) forwards;
}

.progress-fill.indeterminate {
  background: repeating-linear-gradient(
    45deg,
    #007acc,
    #007acc 10px,
    #0099ff 10px,
    #0099ff 20px
  );
  animation: progressStripes 1s linear infinite;
}

/* Loading skeletons */
.skeleton {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-text {
  height: 1em;
  border-radius: 4px;
  margin: 0.5em 0;
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.skeleton-button {
  height: 2.5em;
  border-radius: 6px;
  width: 100px;
}

/* Notification animations */
.notification {
  animation: notificationSlideIn var(--animation-duration-normal) var(--animation-easing) forwards;
}

.notification.removing {
  animation: notificationSlideOut var(--animation-duration-normal) var(--animation-easing) forwards;
}

/* Tab animations */
.tab-content {
  animation: fadeIn var(--animation-duration-normal) var(--animation-easing) forwards;
}

.tab-indicator {
  transition: all var(--animation-duration-normal) var(--animation-easing);
}

/* Tooltip animations */
.tooltip {
  animation: scaleIn var(--animation-duration-fast) var(--animation-easing) forwards;
  transform-origin: center bottom;
}

.tooltip.removing {
  animation: scaleOut var(--animation-duration-fast) var(--animation-easing) forwards;
}

/* Focus animations */
.focus-ring {
  position: relative;
}

.focus-ring:focus::after {
  content: '';
  position: absolute;
  inset: -2px;
  border: 2px solid #007acc;
  border-radius: inherit;
  animation: fadeIn var(--animation-duration-fast) var(--animation-easing) forwards;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High performance mode for low-end devices */
@media (max-width: 768px) and (max-height: 1024px) {
  :root {
    --animation-duration-fast: 100ms;
    --animation-duration-normal: 150ms;
    --animation-duration-slow: 200ms;
  }
  
  .animate-gpu {
    transform: none;
  }
}

/* Force GPU acceleration for key elements */
.gpu-accelerate {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Animation optimization classes */
.optimize-animations {
  contain: layout style paint;
}

.optimize-paint {
  contain: paint;
}

.optimize-layout {
  contain: layout;
}