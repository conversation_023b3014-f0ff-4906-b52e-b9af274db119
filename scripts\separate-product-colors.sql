-- Separate shared colors into product-specific instances
-- This will fix the issue where colors are incorrectly shared across products

-- Step 1: Create backup
DROP TABLE IF EXISTS product_colors_backup;
CREATE TABLE product_colors_backup AS SELECT * FROM product_colors;

-- Step 2: Create new color instances for shared colors
-- We'll insert new color records for each product that shares a color

INSERT INTO colors (
    external_id, 
    name, 
    display_name, 
    code, 
    hex, 
    source_id, 
    properties, 
    created_at, 
    updated_at, 
    version, 
    organization_id, 
    created_by, 
    user_id, 
    deleted_at, 
    device_id, 
    sync_version
)
SELECT 
    LOWER(HEX(RANDOMBLOB(16))) || '-' || 
    LOWER(HEX(RANDOMBLOB(2))) || '-' || 
    LOWER(HEX(RANDOMBLOB(2))) || '-' || 
    LOWER(HEX(RANDOMBLOB(2))) || '-' || 
    LOWER(HEX(RANDOMBLOB(6))) as external_id,  -- Generate new UUID
    c.name,
    c.display_name,
    c.code,
    c.hex,
    c.source_id,
    json_set(c.properties, '$.product', p.name) as properties,  -- Update product reference
    c.created_at,
    datetime('now') as updated_at,  -- Update timestamp
    c.version,
    c.organization_id,
    c.created_by,
    c.user_id,
    c.deleted_at,
    c.device_id,
    c.sync_version
FROM colors c
JOIN product_colors pc ON c.id = pc.color_id
JOIN products p ON pc.product_id = p.id
WHERE c.id IN (
    -- Only for colors that are shared across multiple products
    SELECT c2.id 
    FROM colors c2
    JOIN product_colors pc2 ON c2.id = pc2.color_id
    GROUP BY c2.id
    HAVING COUNT(DISTINCT pc2.product_id) > 1
)
AND pc.rowid NOT IN (
    -- Keep the first instance of each shared color unchanged
    SELECT MIN(pc3.rowid)
    FROM product_colors pc3
    WHERE pc3.color_id = c.id
);

-- Step 3: Update product_colors to use the new color instances
-- Create a mapping table first
CREATE TEMPORARY TABLE color_mapping AS
SELECT 
    old_pc.product_id,
    old_pc.color_id as old_color_id,
    new_c.id as new_color_id,
    old_pc.display_order,
    old_pc.added_at,
    old_pc.rowid as old_rowid
FROM product_colors old_pc
JOIN colors old_c ON old_pc.color_id = old_c.id
JOIN products p ON old_pc.product_id = p.id
JOIN colors new_c ON (
    new_c.hex = old_c.hex AND 
    new_c.name = old_c.name AND 
    json_extract(new_c.properties, '$.product') = p.name AND
    new_c.id != old_c.id  -- Make sure it's the new instance
)
WHERE old_c.id IN (
    SELECT c2.id 
    FROM colors c2
    JOIN product_colors pc2 ON c2.id = pc2.color_id
    GROUP BY c2.id
    HAVING COUNT(DISTINCT pc2.product_id) > 1
);

-- Step 4: Update product_colors with new color IDs
UPDATE product_colors 
SET color_id = (
    SELECT cm.new_color_id 
    FROM color_mapping cm 
    WHERE cm.old_rowid = product_colors.rowid
)
WHERE rowid IN (SELECT old_rowid FROM color_mapping);

-- Step 5: Verification
SELECT 'VERIFICATION - Colors shared across multiple products:' as status;
SELECT 
    c.name,
    c.hex,
    COUNT(DISTINCT pc.product_id) as product_count,
    GROUP_CONCAT(DISTINCT p.name) as products
FROM colors c
JOIN product_colors pc ON c.id = pc.color_id
JOIN products p ON pc.product_id = p.id
GROUP BY c.id
HAVING product_count > 1
ORDER BY product_count DESC, c.name
LIMIT 10;

-- Step 6: Check final counts
SELECT 'FINAL PRODUCT COLOR COUNTS:' as status;
SELECT 
    p.name as product_name,
    COUNT(*) as color_count
FROM product_colors pc 
JOIN products p ON pc.product_id = p.id 
WHERE p.name IN ('IVG BAR', '2400 BARS', 'IVG 12k Switch', 'IVG Beyond CLK 6000')
GROUP BY p.id, p.name
ORDER BY color_count DESC;