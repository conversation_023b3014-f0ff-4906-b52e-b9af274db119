/**
 * Test data handler for importing sample data
 * This provides a way to populate the database with test data
 */

import { ipcMain } from 'electron';
import { v4 as uuidv4 } from 'uuid';

// Sample data
const sampleProducts = [
  {
    name: "Summer Collection 2024",
    colors: [
      { code: "PANTONE 17-1463", name: "Tangerine Tango", hex: "#DD4124" },
      { code: "PANTONE 14-4318", name: "Aqua Sky", hex: "#7BC4C4" },
      { code: "PANTONE 13-0647", name: "Illuminating Yellow", hex: "#F5DF4D" },
      { code: "PANTONE 18-3838", name: "<PERSON> <PERSON>", hex: "#5F4B8B" },
      { code: "PANTONE 15-5519", name: "Turquoise", hex: "#45B8AC" }
    ]
  },
  {
    name: "Urban Office Furniture",
    colors: [
      { code: "RAL 7016", name: "Anthracite <PERSON>", hex: "#293133" },
      { code: "RAL 9003", name: "<PERSON> <PERSON>", hex: "#F4F4F4" },
      { code: "RAL 5024", name: "Pastel Blue", hex: "#5D9B9B" },
      { code: "RAL 7035", name: "Light Grey", hex: "#D7D7D7" },
      { code: "CUSTOM-001", name: "Executive Brown", hex: "#3E2723" }
    ]
  },
  {
    name: "Kids Room Essentials",
    colors: [
      { code: "PANTONE 13-4411", name: "Baby Blue", hex: "#B5D3E7" },
      { code: "PANTONE 12-0752", name: "Buttercup", hex: "#FCE883" },
      { code: "PANTONE 14-4318", name: "Mint Green", hex: "#98D982" },
      { code: "PANTONE 14-2311", name: "Pink Lavender", hex: "#E8B5CE" },
      { code: "PANTONE 16-1441", name: "Peach", hex: "#FFBE98" }
    ]
  }
];

// In-memory storage when database is not available
const memoryStorage = {
  products: new Map(),
  colors: new Map(),
  productColors: new Map()
};

export function registerSampleDataHandlers(): void {
  console.log('[SampleData] Registering sample data handlers...');

  // Import sample data handler
  ipcMain.removeHandler('sampleData:import');
  ipcMain.handle('sampleData:import', async () => {
    try {
      console.log('[SampleData] Starting import...');
      
      const results = {
        productsCreated: 0,
        colorsCreated: 0,
        errors: []
      };

      for (const productData of sampleProducts) {
        try {
          // Create product
          const productId = uuidv4();
          const product = {
            id: productId,
            name: productData.name,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          
          memoryStorage.products.set(productId, product);
          results.productsCreated++;
          
          // Create product colors mapping
          const colorIds = [];
          
          // Add colors
          for (const colorData of productData.colors) {
            const colorId = uuidv4();
            const color = {
              id: colorId,
              code: colorData.code,
              name: colorData.name,
              hex: colorData.hex,
              library: colorData.code.includes('PANTONE') ? 'pantone' : 
                       colorData.code.includes('RAL') ? 'ral' : 'user',
              createdAt: new Date().toISOString()
            };
            
            memoryStorage.colors.set(colorId, color);
            colorIds.push(colorId);
            results.colorsCreated++;
          }
          
          // Store product-color associations
          memoryStorage.productColors.set(productId, colorIds);
          
        } catch (error) {
          results.errors.push({
            product: productData.name,
            error: error.message
          });
        }
      }
      
      console.log('[SampleData] Import complete:', results);
      return results;
      
    } catch (error) {
      console.error('[SampleData] Import error:', error);
      throw error;
    }
  });

  // Handler to get sample data (for testing)
  ipcMain.removeHandler('sampleData:getAll');
  ipcMain.handle('sampleData:getAll', async () => {
    const products = Array.from(memoryStorage.products.values());
    const productsWithColors = products.map(product => {
      const colorIds = memoryStorage.productColors.get(product.id) || [];
      const colors = colorIds.map(id => memoryStorage.colors.get(id)).filter(Boolean);
      return {
        ...product,
        colors
      };
    });
    
    return productsWithColors;
  });

  // Override the product handlers to use memory storage
  ipcMain.removeHandler('product:getAllWithColors');
  ipcMain.handle('product:getAllWithColors', async () => {
    console.log('[SampleData] Getting all products with colors from memory...');
    const products = Array.from(memoryStorage.products.values());
    const productsWithColors = products.map(product => {
      const colorIds = memoryStorage.productColors.get(product.id) || [];
      const colors = colorIds.map(id => memoryStorage.colors.get(id)).filter(Boolean);
      return {
        ...product,
        colors
      };
    });
    console.log('[SampleData] Returning products:', productsWithColors);
    return productsWithColors;
  });

  // Override product:add to use memory storage
  ipcMain.removeHandler('product:add');
  ipcMain.handle('product:add', async (_, productData: { name: string, metadata?: any }) => {
    console.log('[SampleData] Adding product to memory:', productData);
    const productId = uuidv4();
    const product = {
      id: productId,
      name: productData.name,
      metadata: productData.metadata,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    memoryStorage.products.set(productId, product);
    memoryStorage.productColors.set(productId, []);
    
    return product;
  });

  console.log('[SampleData] Sample data handlers registered');
}
