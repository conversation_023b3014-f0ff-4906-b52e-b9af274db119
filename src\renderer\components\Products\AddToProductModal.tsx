/**
 * @file AddToProductModal.tsx
 * @description Modal for adding colors to a product
 */

import React, { useState, useEffect } from 'react';
import { useProductStore } from '../../store/product.store';
import { useColorStore } from '../../store/color.store';
import { X, Droplet, Palette, LibraryBig, Search } from 'lucide-react';
import ColorForm from '../ColorForm';
import GradientPickerModal from '../GradientPickerModal';
import { ColorEntry } from '../../../shared/types/color.types';

interface AddToProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
}

const AddToProductModal: React.FC<AddToProductModalProps> = ({
  isOpen,
  onClose,
  productId
}) => {
  const { products, addColorToProduct} = useProductStore();
  const { colors, pantoneColors, ralColors } = useColorStore();

  const [showColorForm, setShowColorForm] = useState(false);
  const [showGradientModal, setShowGradientModal] = useState(false);
  const [showLibraryPicker, setShowLibraryPicker] = useState(false);
  const [libraryType, setLibraryType] = useState<'user' | 'code' | 'ral'>('user');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedColor, setSelectedColor] = useState<ColorEntry | null>(null);
  const [prefillColorData, setPrefillColorData] = useState<ColorEntry | null>(null);

  // Find the active product
  const activeProduct = products.find(p => p.id === productId);


  // Reset state when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setShowColorForm(false);
      setShowGradientModal(false);
      setShowLibraryPicker(false);
      setSearchQuery('');
      setSelectedColor(null);
      setPrefillColorData(null);
    }
  }, [isOpen]);


  // Get colors based on library type
  const getLibraryColors = () => {
    switch (libraryType) {
      case 'user': {
        // Only show user colors that are associated with products
        const allProductColors = products.flatMap(p => p.colors || []);
        const uniqueColorIds = new Set(allProductColors.map(c => c.id));
        return colors.filter(c => uniqueColorIds.has(c.id));
      }
      case 'code':
        return pantoneColors;
      case 'ral':
        return ralColors;
      default:
        return [];
    }
  };

  // Filter colors based on search
  const filteredColors = getLibraryColors().filter(color => {
    if (!searchQuery) {return true;}
    const query = searchQuery.toLowerCase();
    return (
      color.code?.toLowerCase().includes(query) ||
      color.name?.toLowerCase().includes(query) ||
      color.hex?.toLowerCase().includes(query) ||
      color.product?.toLowerCase().includes(query)
    );
  });

  // Handle selecting color from library - opens color form with prefilled data
  const handleSelectFromLibrary = () => {
    if (!selectedColor) {return;}
    
    console.log('[AddToProductModal] Selected color from library:', selectedColor);
    
    // Set the selected color data to prefill the form
    setPrefillColorData(selectedColor);
    
    // Close library picker and open color form
    setShowLibraryPicker(false);
    setShowColorForm(true);
    setSelectedColor(null);
  };

  if (!isOpen) {return null;}

  return (
    <>
      {!showLibraryPicker ? (
        <div
          className="fixed inset-0 bg-black/25 dark:bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
          onClick={(e) => {
            if (e.target === e.currentTarget) {onClose();}
          }}
        >
          <div
            className="bg-ui-background-primary dark:bg-zinc-900 rounded-xl shadow-2xl max-w-md w-full mx-4 border border-ui-border-light dark:border-zinc-700"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between px-6 py-4 border-b border-ui-border-light dark:border-zinc-700">
              <div>
                <h2 className="text-lg font-medium text-ui-foreground-primary dark:text-white">
                  Add Color to Product
                </h2>
                <p className="text-sm text-ui-foreground-secondary dark:text-gray-400 mt-1">
                  {activeProduct?.name || 'Product'}
                </p>
              </div>
              <button
                onClick={onClose}
                className="p-1 text-ui-muted dark:text-gray-400 hover:text-ui-foreground-primary dark:hover:text-white rounded-md hover:bg-ui-background-tertiary dark:hover:bg-zinc-800 transition-colors"
              >
                <X size={20} />
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              <p className="text-sm text-ui-foreground-secondary dark:text-gray-400 mb-6">
                Choose how you want to add a color to this product:
              </p>
              
              <div className="space-y-3">
                <button
                  onClick={() => setShowLibraryPicker(true)}
                  className="w-full flex items-center justify-between px-4 py-3 bg-ui-background-secondary dark:bg-zinc-800 hover:bg-ui-background-tertiary dark:hover:bg-zinc-700 rounded-lg border border-ui-border-light dark:border-zinc-700 transition-colors"
                >
                  <div className="flex items-center">
                    <LibraryBig size={20} className="mr-3 text-brand-primary" />
                    <div className="text-left">
                      <p className="font-medium text-ui-foreground-primary dark:text-white">
                        Select from Library
                      </p>
                      <p className="text-sm text-ui-foreground-secondary dark:text-gray-400">
                        Choose from existing colors or Pantone/RAL libraries
                      </p>
                    </div>
                  </div>
                </button>

                <button
                  onClick={() => setShowColorForm(true)}
                  className="w-full flex items-center justify-between px-4 py-3 bg-ui-background-secondary dark:bg-zinc-800 hover:bg-ui-background-tertiary dark:hover:bg-zinc-700 rounded-lg border border-ui-border-light dark:border-zinc-700 transition-colors"
                >
                  <div className="flex items-center">
                    <Droplet size={20} className="mr-3 text-brand-primary" />
                    <div className="text-left">
                      <p className="font-medium text-ui-foreground-primary dark:text-white">
                        Create New Color
                      </p>
                      <p className="text-sm text-ui-foreground-secondary dark:text-gray-400">
                        Create a custom flat color with Pantone reference
                      </p>
                    </div>
                  </div>
                </button>
                
                <button
                  onClick={() => setShowGradientModal(true)}
                  className="w-full flex items-center justify-between px-4 py-3 bg-ui-background-secondary dark:bg-zinc-800 hover:bg-ui-background-tertiary dark:hover:bg-zinc-700 rounded-lg border border-ui-border-light dark:border-zinc-700 transition-colors"
                >
                  <div className="flex items-center">
                    <Palette size={20} className="mr-3 text-brand-primary" />
                    <div className="text-left">
                      <p className="font-medium text-ui-foreground-primary dark:text-white">
                        Create Gradient
                      </p>
                      <p className="text-sm text-ui-foreground-secondary dark:text-gray-400">
                        Create a gradient between multiple colors
                      </p>
                    </div>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div
          className="fixed inset-0 bg-black/25 dark:bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
          onClick={(e) => {
            if (e.target === e.currentTarget) {onClose();}
          }}
        >
          <div
            className="bg-ui-background-primary dark:bg-zinc-900 rounded-xl shadow-2xl max-w-3xl w-full mx-4 max-h-[80vh] flex flex-col border border-ui-border-light dark:border-zinc-700"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between px-6 py-4 border-b border-ui-border-light dark:border-zinc-700">
              <div>
                <h2 className="text-lg font-medium text-ui-foreground-primary dark:text-white">
                  Select Color from Library
                </h2>
                <p className="text-sm text-ui-foreground-secondary dark:text-gray-400 mt-1">
                  {activeProduct?.name || 'Product'}
                </p>
              </div>
              <button
                onClick={() => setShowLibraryPicker(false)}
                className="p-1 text-ui-muted dark:text-gray-400 hover:text-ui-foreground-primary dark:hover:text-white rounded-md hover:bg-ui-background-tertiary dark:hover:bg-zinc-800 transition-colors"
              >
                <X size={20} />
              </button>
            </div>

            {/* Library tabs */}
            <div className="px-6 py-3 border-b border-ui-border-light dark:border-zinc-700">
              <div className="flex space-x-6">
                <button
                  onClick={() => setLibraryType('user')}
                  className={`pb-2 border-b-2 transition-colors ${
                    libraryType === 'user'
                      ? 'border-brand-primary text-brand-primary'
                      : 'border-transparent text-ui-foreground-secondary dark:text-gray-400 hover:text-ui-foreground-primary dark:hover:text-white'
                  }`}
                >
                  <span className="text-sm font-medium">User Colors</span>
                </button>
                <button
                  onClick={() => setLibraryType('code')}
                  className={`pb-2 border-b-2 transition-colors ${
                    libraryType === 'code'
                      ? 'border-brand-primary text-brand-primary'
                      : 'border-transparent text-ui-foreground-secondary dark:text-gray-400 hover:text-ui-foreground-primary dark:hover:text-white'
                  }`}
                >
                  <span className="text-sm font-medium">Pantone</span>
                </button>
                <button
                  onClick={() => setLibraryType('ral')}
                  className={`pb-2 border-b-2 transition-colors ${
                    libraryType === 'ral'
                      ? 'border-brand-primary text-brand-primary'
                      : 'border-transparent text-ui-foreground-secondary dark:text-gray-400 hover:text-ui-foreground-primary dark:hover:text-white'
                  }`}
                >
                  <span className="text-sm font-medium">RAL</span>
                </button>
              </div>
            </div>

            {/* Search */}
            <div className="px-6 py-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-ui-muted dark:text-gray-400" size={18} />
                <input
                  type="text"
                  placeholder="Search colors..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-ui-background-secondary dark:bg-zinc-800 border border-ui-border-light dark:border-zinc-700 rounded-md text-ui-foreground-primary dark:text-white placeholder-ui-foreground-tertiary dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-primary dark:focus:ring-brand-primary focus:border-transparent"
                />
              </div>
            </div>

            {/* Color list */}
            <div className="flex-1 overflow-y-auto px-6 pb-6">
              {filteredColors.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-ui-muted dark:text-gray-400">
                    {searchQuery ? 'No colors match your search' : 'No colors available'}
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                  {filteredColors.map(color => (
                    <button
                      key={color.id}
                      onClick={() => setSelectedColor(color)}
                      className={`relative p-3 rounded-lg border-2 transition-all ${
                        selectedColor?.id === color.id
                          ? 'border-brand-primary bg-brand-primary/10'
                          : 'border-ui-border-light dark:border-zinc-700 hover:border-ui-border-dark dark:hover:border-zinc-600'
                      }`}
                    >
                      <div
                        className="w-full h-16 rounded-md mb-2"
                        style={{ backgroundColor: color.hex }}
                      />
                      <div className="text-left">
                        <p className="text-xs font-medium text-ui-foreground-primary dark:text-white truncate">
                          {color.code || color.product || 'No Code'}
                        </p>
                        <p className="text-xs text-ui-foreground-secondary dark:text-gray-400 truncate">
                          {color.name || 'No Name'}
                        </p>
                      </div>
                      {selectedColor?.id === color.id && (
                        <div className="absolute top-2 right-2 w-5 h-5 bg-brand-primary rounded-full flex items-center justify-center">
                          <svg className="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                          </svg>
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="px-6 py-4 border-t border-ui-border-light dark:border-zinc-700">
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowLibraryPicker(false)}
                  className="px-4 py-2 text-sm font-medium text-ui-foreground-primary dark:text-white bg-ui-background-tertiary dark:bg-zinc-800 rounded-md hover:bg-ui-background-tertiary-hover dark:hover:bg-zinc-700 transition-colors"
                >
                  Back
                </button>
                <button
                  onClick={handleSelectFromLibrary}
                  className="px-4 py-2 text-sm font-medium bg-brand-primary text-white rounded-md hover:bg-brand-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={!selectedColor}
                >
                  Use This Color
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Color Form Modal */}
      {showColorForm && (
        <div
          className="fixed inset-0 bg-black/25 dark:bg-black/50 backdrop-blur-sm flex items-center justify-center z-[60]"
          onClick={(e) => {
            if (e.target === e.currentTarget) {setShowColorForm(false);}
          }}
        >
          <div
            className="bg-ui-background-primary dark:bg-zinc-900 rounded-xl shadow-2xl max-w-2xl w-full mx-4 p-6 max-h-[90vh] overflow-y-auto border border-ui-border-light dark:border-zinc-700"
            onClick={(e) => e.stopPropagation()}
          >
            <ColorForm
              isModal={true}
              hideProductField={true}
              defaultProduct={activeProduct?.name || 'Product'}
              color={prefillColorData || undefined}
              prefillMode={!!prefillColorData}
              onCancel={() => {
                setShowColorForm(false);
                setPrefillColorData(null);
              }}
              onSuccess={(newColorId) => {
                setShowColorForm(false);
                
                // If we have prefill data and the color wasn't modified (no new ID returned),
                // it means we should use the existing library color
                const colorIdToAdd = newColorId || prefillColorData?.id;
                
                if (colorIdToAdd) {
                  addColorToProduct(productId, colorIdToAdd);
                }
                
                setPrefillColorData(null);
                onClose();
              }}
            />
          </div>
        </div>
      )}

      {/* Gradient Picker Modal */}
      {showGradientModal && (
        <GradientPickerModal
          isOpen={showGradientModal}
          onClose={() => setShowGradientModal(false)}
          onSuccess={(newColorId) => {
            setShowGradientModal(false);
            // Automatically add the gradient to the product
            if (newColorId) {
              addColorToProduct(productId, newColorId);
            }
            onClose();
          }}
        />
      )}
    </>
  );
};

export default AddToProductModal;