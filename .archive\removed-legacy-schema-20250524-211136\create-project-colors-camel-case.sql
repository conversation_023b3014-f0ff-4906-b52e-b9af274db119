-- Create a camelCase version of the project_colors table

-- Create the projectColors table with camelCase column names
CREATE TABLE IF NOT EXISTS "projectColors" (
  "projectId" TEXT NOT NULL,
  "colorId" TEXT NOT NULL,
  "usageNotes" TEXT,
  "addedAt" TEXT DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("projectId", "colorId"),
  FOREIGN KEY ("projectId") REFERENCES projects(id) ON DELETE CASCADE,
  FOREIGN KEY ("colorId") REFERENCES colors(id) ON DELETE CASCADE
);

-- Create indexes for the new table
CREATE INDEX IF NOT EXISTS idx_projectColors_projectId ON "projectColors" ("projectId");
CREATE INDEX IF NOT EXISTS idx_projectColors_colorId ON "projectColors" ("colorId");

-- Copy data from the old project_colors table to the new projectColors table
INSERT OR IGNORE INTO "projectColors" ("projectId", "colorId", "usageNotes", "addedAt")
SELECT projectId, colorId, usageNotes, COALESCE(added_at, CURRENT_TIMESTAMP)
FROM project_colors
WHERE EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='project_colors');

-- Drop the snake_case project_colors table
DROP TABLE IF EXISTS project_colors;

-- Verify that the tables have been updated
SELECT CASE 
  WHEN EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='project_colors') 
  THEN 'ERROR: Table project_colors still exists' 
  ELSE 'SUCCESS: Table project_colors has been removed' 
END;

SELECT CASE 
  WHEN EXISTS (SELECT 1 FROM sqlite_master WHERE type='table' AND name='projectColors') 
  THEN 'SUCCESS: Table projectColors exists' 
  ELSE 'ERROR: Table projectColors does not exist' 
END;

-- List all remaining tables
SELECT 'Remaining tables: ' || group_concat(name, ', ') FROM sqlite_master WHERE type='table';
