/**
 * @file useClipboard.ts
 * @description Custom hook for clipboard operations with status feedback
 */

import { useState, useEffect, useCallback } from 'react';

interface UseClipboardResult {
  copiedItem: string | null;
  copyToClipboard: (text: string, label: string) => Promise<void>;
  resetCopiedState: () => void;
  error: string | null;
}

/**
 * Hook for managing clipboard operations with status feedback
 * @param timeout - Time in milliseconds before reset (default: 2000)
 * @returns Object with clipboard methods and state
 */
export function useClipboard(timeout = 2000): UseClipboardResult {
  const [copiedItem, setCopiedItem] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Reset copied state after timeout
  useEffect(() => {
    if (copiedItem) {
      const timer = setTimeout(() => {
        setCopiedItem(null);
      }, timeout);
      return () => clearTimeout(timer);
    }
  }, [copiedItem, timeout]);

  // Reset error function
  const resetCopiedState = useCallback(() => {
    setCopiedItem(null);
    setError(null);
  }, []);

  // Copy to clipboard function
  const copyToClipboard = useCallback(async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedItem(label);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to copy to clipboard');
      setCopiedItem(null);
    }
  }, []);

  return { copiedItem, copyToClipboard, resetCopiedState, error };
} 