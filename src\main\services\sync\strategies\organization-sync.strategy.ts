/**
 * @file organization-sync.strategy.ts
 * @description Organization-specific synchronization strategy
 * 
 * This strategy handles synchronization of organization data and ensures
 * proper organization context for all sync operations.
 */

import { getSupabaseClient } from '../../supabase-client';
import { getDatabase } from '../../../db/database';
import { 
  ISyncStrategy, 
  SyncOperation, 
  SyncQueueItem, 
  SyncResult, 
  SyncError 
} from '../core/sync-types';

// ============================================================================
// ORGANIZATION SYNC STRATEGY
// ============================================================================

/**
 * Strategy for synchronizing organization data
 */
export class OrganizationSyncStrategy implements ISyncStrategy {
  readonly name = 'organization-sync';
  readonly priority = 5; // Highest priority (organizations affect access)

  private userId: string | null = null;
  private organizationId: string | null = null;

  constructor(userId: string, organizationId: string) {
    this.userId = userId;
    this.organizationId = organizationId;
  }

  /**
   * Check if this strategy can handle the given table and operation
   */
  canHandle(table: string, operation: SyncOperation): boolean {
    return table === 'organizations';
  }

  /**
   * Execute organization synchronization
   */
  async execute(items: SyncQueueItem[]): Promise<SyncResult> {
    const startTime = Date.now();
    const errors: SyncError[] = [];
    let itemsSucceeded = 0;
    let itemsFailed = 0;

    try {
      console.log(`[OrganizationSync] Processing ${items.length} organization items`);

      // Organizations are critical - process them one by one for better error handling
      for (const item of items) {
        try {
          const result = await this.processOrganizationItem(item);
          
          if (result.success) {
            itemsSucceeded++;
          } else {
            itemsFailed++;
            errors.push(...result.errors);
          }
          
        } catch (error) {
          console.error(`[OrganizationSync] Error processing organization ${item.id}:`, error);
          itemsFailed++;
          
          errors.push({
            id: `org-process-error-${Date.now()}-${item.id}`,
            timestamp: Date.now(),
            operation: `process organization ${item.id}`,
            category: 'database',
            severity: 'critical',
            message: `Failed to process organization: ${error.message}`,
            originalError: error,
            recoverable: true
          });
        }
      }

      return {
        success: itemsFailed === 0,
        itemsProcessed: items.length,
        itemsSucceeded,
        itemsFailed,
        errors,
        duration: Date.now() - startTime
      };

    } catch (error) {
      console.error('[OrganizationSync] Strategy execution failed:', error);
      
      return {
        success: false,
        itemsProcessed: items.length,
        itemsSucceeded: 0,
        itemsFailed: items.length,
        errors: [{
          id: `org-sync-error-${Date.now()}`,
          timestamp: Date.now(),
          operation: 'organization sync',
          category: 'database',
          severity: 'critical',
          message: error.message,
          originalError: error,
          recoverable: true
        }],
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Validate organization data structure
   */
  validateData(data: any): boolean {
    if (!data) return false;
    
    // Required fields for organization data
    const requiredFields = ['external_id', 'name'];
    
    return requiredFields.every(field => 
      data.hasOwnProperty(field) && data[field] !== null && data[field] !== undefined
    );
  }

  /**
   * Transform organization data for Supabase sync
   */
  transformData(localOrganization: any): any {
    return {
      external_id: localOrganization.external_id,
      name: localOrganization.name,
      description: localOrganization.description,
      settings: localOrganization.settings || {},
      created_by: localOrganization.created_by || this.userId,
      is_active: localOrganization.is_active !== 0,
      created_at: localOrganization.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Process a single organization item
   */
  private async processOrganizationItem(item: SyncQueueItem): Promise<{
    success: boolean;
    errors: SyncError[];
  }> {
    const errors: SyncError[] = [];

    try {
      if (item.action === 'upsert') {
        return await this.processOrganizationUpsert(item);
      } else if (item.action === 'delete') {
        return await this.processOrganizationDelete(item);
      } else {
        errors.push({
          id: `org-unknown-action-${Date.now()}`,
          timestamp: Date.now(),
          operation: `organization ${item.action}`,
          category: 'validation',
          severity: 'medium',
          message: `Unknown action: ${item.action}`,
          recoverable: false
        });
        
        return { success: false, errors };
      }

    } catch (error) {
      errors.push({
        id: `org-item-error-${Date.now()}`,
        timestamp: Date.now(),
        operation: `organization ${item.action}`,
        category: 'database',
        severity: 'high',
        message: error.message,
        originalError: error,
        recoverable: true
      });
      
      return { success: false, errors };
    }
  }

  /**
   * Process organization upsert operation
   */
  private async processOrganizationUpsert(item: SyncQueueItem): Promise<{
    success: boolean;
    errors: SyncError[];
  }> {
    const errors: SyncError[] = [];

    try {
      const supabase = getSupabaseClient();
      const db = await getDatabase();

      // Get local organization data
      let localOrganization;
      
      if (item.data) {
        // Data provided in queue item
        localOrganization = item.data;
      } else {
        // Fetch from local database
        const stmt = db.prepare(`
          SELECT * FROM organizations WHERE external_id = ?
        `);
        localOrganization = stmt.get(item.id);
      }

      if (!localOrganization) {
        errors.push({
          id: `org-not-found-${Date.now()}`,
          timestamp: Date.now(),
          operation: `find organization ${item.id}`,
          category: 'validation',
          severity: 'high',
          message: `Organization not found: ${item.id}`,
          recoverable: false
        });
        
        return { success: false, errors };
      }

      if (!this.validateData(localOrganization)) {
        errors.push({
          id: `org-invalid-data-${Date.now()}`,
          timestamp: Date.now(),
          operation: `validate organization ${item.id}`,
          category: 'validation',
          severity: 'high',
          message: `Invalid organization data: ${item.id}`,
          recoverable: false
        });
        
        return { success: false, errors };
      }

      // Transform and upsert to Supabase
      const transformedOrganization = this.transformData(localOrganization);
      
      console.log(`[OrganizationSync] Upserting organization: ${transformedOrganization.name}`);
      
      const { data, error } = await supabase
        .from('organizations')
        .upsert(transformedOrganization, {
          onConflict: 'external_id'
        })
        .select();

      if (error) {
        console.error('[OrganizationSync] Supabase upsert error:', error);
        
        errors.push({
          id: `org-upsert-error-${Date.now()}`,
          timestamp: Date.now(),
          operation: `upsert organization ${item.id}`,
          category: 'database',
          severity: 'critical',
          message: `Supabase upsert failed: ${error.message}`,
          originalError: error,
          recoverable: true
        });
        
        return { success: false, errors };
      }

      console.log(`[OrganizationSync] Successfully upserted organization: ${transformedOrganization.name}`);
      console.log(`[OrganizationSync] Supabase returned ${data?.length || 0} records`);
      
      return { success: true, errors: [] };

    } catch (error) {
      console.error('[OrganizationSync] Upsert processing failed:', error);
      
      errors.push({
        id: `org-upsert-exception-${Date.now()}`,
        timestamp: Date.now(),
        operation: `upsert organization ${item.id}`,
        category: 'database',
        severity: 'critical',
        message: `Upsert failed: ${error.message}`,
        originalError: error,
        recoverable: true
      });
      
      return { success: false, errors };
    }
  }

  /**
   * Process organization delete operation
   */
  private async processOrganizationDelete(item: SyncQueueItem): Promise<{
    success: boolean;
    errors: SyncError[];
  }> {
    const errors: SyncError[] = [];

    try {
      const supabase = getSupabaseClient();
      
      console.log(`[OrganizationSync] Deleting organization: ${item.id}`);
      
      // Note: Deleting organizations is a critical operation that affects all related data
      // In most cases, organizations should be deactivated rather than deleted
      const { error } = await supabase
        .from('organizations')
        .delete()
        .eq('external_id', item.id);

      if (error) {
        console.error('[OrganizationSync] Supabase delete error:', error);
        
        errors.push({
          id: `org-delete-error-${Date.now()}`,
          timestamp: Date.now(),
          operation: `delete organization ${item.id}`,
          category: 'database',
          severity: 'critical',
          message: `Supabase delete failed: ${error.message}`,
          originalError: error,
          recoverable: true
        });
        
        return { success: false, errors };
      }

      console.log(`[OrganizationSync] Successfully deleted organization: ${item.id}`);
      
      return { success: true, errors: [] };

    } catch (error) {
      console.error('[OrganizationSync] Delete processing failed:', error);
      
      errors.push({
        id: `org-delete-exception-${Date.now()}`,
        timestamp: Date.now(),
        operation: `delete organization ${item.id}`,
        category: 'database',
        severity: 'critical',
        message: `Delete failed: ${error.message}`,
        originalError: error,
        recoverable: true
      });
      
      return { success: false, errors };
    }
  }

  /**
   * Ensure organization exists in local database
   */
  async ensureOrganizationExists(): Promise<boolean> {
    try {
      const db = await getDatabase();
      
      // Check if organization exists locally
      const localOrg = db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `).get(this.organizationId);
      
      if (localOrg) {
        return true; // Organization already exists
      }

      // Fetch organization from Supabase
      const supabase = getSupabaseClient();
      const { data: orgData, error } = await supabase
        .from('organizations')
        .select('*')
        .eq('external_id', this.organizationId)
        .single();

      if (error || !orgData) {
        console.error('[OrganizationSync] Organization not found in Supabase:', error);
        return false;
      }

      // Insert organization into local database
      const insertStmt = db.prepare(`
        INSERT OR REPLACE INTO organizations (
          external_id, name, description, settings, 
          created_by, is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `);

      insertStmt.run(
        orgData.external_id,
        orgData.name,
        orgData.description,
        JSON.stringify(orgData.settings || {}),
        orgData.created_by,
        orgData.is_active ? 1 : 0,
        orgData.created_at,
        orgData.updated_at
      );

      console.log(`[OrganizationSync] Created local organization: ${orgData.name}`);
      return true;

    } catch (error) {
      console.error('[OrganizationSync] Error ensuring organization exists:', error);
      return false;
    }
  }
}
