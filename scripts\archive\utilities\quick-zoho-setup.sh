#!/bin/bash

# One-click Zoho OAuth Setup
clear
echo "🎨 ChromaSync Zoho Email Setup"
echo "=============================="
echo ""
echo "This will open your browser to authorize ChromaSync."
echo "After authorizing, you'll see a localhost error page."
echo ""
echo "Press Enter to continue..."
read

# Open the authorization URL
AUTH_URL="https://accounts.zoho.eu/oauth/v2/auth?client_id=1000.ZN7GG1SU13DXYH2D2JXF934HWFVJDG&response_type=code&redirect_uri=http://localhost:8080/callback&scope=ZohoMail.messages.CREATE,ZohoMail.messages.READ,ZohoMail.accounts.READ&access_type=offline&prompt=consent"

echo "Opening browser..."
open "$AUTH_URL" 2>/dev/null || xdg-open "$AUTH_URL" 2>/dev/null || echo "Please open this URL manually: $AUTH_URL"

echo ""
echo "After authorizing, you'll be redirected to:"
echo "http://localhost:8080/callback?code=1000.xxxxx..."
echo ""
echo "Copy the ENTIRE URL from your browser's address bar and paste it here:"
read FULL_URL

# Extract the code from the URL
CODE=$(echo "$FULL_URL" | grep -o 'code=[^&]*' | cut -d= -f2)

if [ -z "$CODE" ]; then
    echo "❌ No authorization code found in the URL"
    exit 1
fi

echo ""
echo "✅ Got authorization code!"
echo "🔄 Getting refresh token..."

# Exchange for refresh token
RESPONSE=$(curl -s -X POST https://accounts.zoho.eu/oauth/v2/token \
  -d "code=${CODE}" \
  -d "client_id=1000.ZN7GG1SU13DXYH2D2JXF934HWFVJDG" \
  -d "client_secret=4ca85d5c0a70404f8778128ae3b8e1cc08bc585356" \
  -d "redirect_uri=http://localhost:8080/callback" \
  -d "grant_type=authorization_code")

# Extract refresh token
REFRESH_TOKEN=$(echo "$RESPONSE" | grep -o '"refresh_token":"[^"]*' | cut -d'"' -f4)

if [ -z "$REFRESH_TOKEN" ]; then
    echo "❌ Failed to get refresh token. Response:"
    echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"
    exit 1
fi

# Create .env file content
ENV_CONTENT="# Zoho Mail API Configuration (EU Server)
ZOHO_CLIENT_ID=1000.ZN7GG1SU13DXYH2D2JXF934HWFVJDG
ZOHO_CLIENT_SECRET=4ca85d5c0a70404f8778128ae3b8e1cc08bc585356
ZOHO_ACCOUNT_ID=6851937000000002002
ZOHO_REFRESH_TOKEN=${REFRESH_TOKEN}
ZOHO_REGION=EU
ZOHO_SUPPORT_ALIAS=<EMAIL>"

echo ""
echo "✅ SUCCESS! Your refresh token is:"
echo "=================================="
echo "$REFRESH_TOKEN"
echo ""
echo "📝 Add this to your .env file:"
echo "=================================="
echo "$ENV_CONTENT"
echo ""

# Ask if they want to append to .env
echo "Would you like to append this to your .env file? (y/n)"
read -n 1 APPEND
echo ""

if [ "$APPEND" = "y" ] || [ "$APPEND" = "Y" ]; then
    echo "" >> .env
    echo "$ENV_CONTENT" >> .env
    echo "✅ Configuration added to .env file!"
else
    echo "📋 Configuration copied to clipboard (if available)"
    echo "$ENV_CONTENT" | pbcopy 2>/dev/null || echo "$ENV_CONTENT" | xclip -selection clipboard 2>/dev/null
fi

echo ""
echo "🎉 Setup complete! You can now use the email service."
