# ChromaSync Deployment Guide

Complete guide for deploying ChromaSync in production environments with cloud sync capabilities.

## Table of Contents

1. [Build & Deployment](#build--deployment)
2. [Supabase Cloud Setup](#supabase-cloud-setup)
3. [Authentication Configuration](#authentication-configuration)
4. [Database Operations](#database-operations)
5. [Domain & Email Setup](#domain--email-setup)
6. [Security Configuration](#security-configuration)
7. [Production Monitoring](#production-monitoring)
8. [Troubleshooting](#troubleshooting)

---

## Build & Deployment

### Prerequisites
- Node.js 18+ and npm 9+
- Platform-specific build tools:
  - **Windows**: Windows Build Tools
  - **macOS**: Xcode Command Line Tools
  - **Linux**: build-essential package

### Production Build Process

```bash
# Install dependencies
npm ci --production=false

# Run tests and linting
npm run test
npm run lint

# Build application
npm run build

# Create platform packages
npm run package:win   # Windows installer
npm run package:mac   # macOS DMG
npm run package:linux # Linux AppImage
npm run package       # All platforms
```

### Code Signing

#### Windows Code Signing
```bash
# Set environment variables for certificate
export CSC_LINK="path/to/certificate.pfx"
export CSC_KEY_PASSWORD="certificate_password"

# Build with signing
npm run package:win
```

#### macOS Code Signing & Notarization
```bash
# Requires Apple Developer certificate
# Set in environment or electron-builder config
export APPLE_ID="<EMAIL>"
export APPLE_APP_SPECIFIC_PASSWORD="app-specific-password"
export APPLE_TEAM_ID="team-id"

# Build with signing and notarization
npm run package:mac
```

### Release Process

1. **Update Version**
   ```bash
   npm version patch|minor|major
   ```

2. **Build All Platforms**
   ```bash
   npm run package
   ```

3. **Create GitHub Release**
   - Tag version in Git
   - Upload installers to GitHub Releases
   - Include generated release notes

### Auto-Update Configuration

Configure in `electron-builder.json`:
```json
{
  "publish": {
    "provider": "github",
    "owner": "your-org",
    "repo": "chromasync"
  }
}
```

---

## Supabase Cloud Setup

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Create new project
3. Choose region closest to users
4. Set strong database password

### 2. Database Schema Setup

Run the schema setup script:
```bash
# Apply database schema
psql -h db.xxx.supabase.co -p 5432 -d postgres -U postgres -f scripts/supabase-schema.sql
```

**Critical Tables:**
- `colors` - Color data with JSONB optimization
- `products` - Product information
- `organizations` - Multi-tenant support
- `organization_invitations` - Team invitations
- `user_profiles` - Extended user data

### 3. Row Level Security (RLS)

Enable RLS on all tables:
```sql
-- Enable RLS on colors table
ALTER TABLE colors ENABLE ROW LEVEL SECURITY;

-- Create policy for organization access
CREATE POLICY "Users can access colors in their organization" ON colors
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE user_profiles.user_id = auth.uid() 
      AND user_profiles.organization_id = colors.organization_id
    )
  );
```

### 4. Environment Configuration

Create `.env` file:
```bash
# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Environment
NODE_ENV=production
```

---

## Authentication Configuration

### Google OAuth Setup

1. **Google Cloud Console**
   - Create new project or use existing
   - Enable Google+ API
   - Create OAuth 2.0 credentials

2. **Configure Authorized Domains**
   ```
   Authorized JavaScript origins:
   - https://your-project.supabase.co
   - https://your-domain.com
   
   Authorized redirect URIs:
   - https://your-project.supabase.co/auth/v1/callback
   - https://auth.your-domain.com/auth/callback
   ```

3. **Supabase Auth Configuration**
   ```sql
   -- In Supabase Dashboard > Authentication > Providers
   -- Enable Google provider
   -- Add Google Client ID and Secret
   ```

### PKCE Flow Configuration

The app uses PKCE (Proof Key for Code Exchange) for secure OAuth:

```typescript
// Already configured in src/main/services/supabase-client.ts
const supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    flowType: 'pkce', // PKCE flow enabled
    storage: customSecureStorage, // Encrypted token storage
    persistSession: false,
    detectSessionInUrl: false
  }
});
```

---

## Database Operations

### Local to Cloud Migration

```bash
# Export local data
npm run export-data

# Import to Supabase (via app UI)
# Use Settings > Sync > Import Data
```

### Database Maintenance

```sql
-- Monitor table sizes
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Vacuum and analyze
VACUUM ANALYZE colors;
VACUUM ANALYZE products;
```

### Backup Strategy

```bash
# Database backup via Supabase CLI
supabase db dump --file backup.sql

# Automated backups (daily)
# Configure in Supabase Dashboard > Settings > Database
```

---

## Domain & Email Setup

### Custom Domain Configuration

1. **DNS Configuration**
   ```
   # CNAME record for auth subdomain
   auth.yourdomain.com -> your-project.supabase.co
   
   # A record for main domain
   yourdomain.com -> your-server-ip
   ```

2. **SSL Certificate**
   - Use Cloudflare or Let's Encrypt
   - Configure in Supabase Dashboard

### Email Configuration

For organization invitations and notifications:

```bash
# Environment variables for email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
```

**Email Functions Setup:**
```sql
-- Deploy email function to Supabase Edge Functions
supabase functions deploy send-invitation-email
```

---

## Security Configuration

### API Security

1. **Rate Limiting**
   - Configure in Supabase Dashboard
   - Limit requests per IP/user

2. **CORS Configuration**
   ```typescript
   // Allowed origins
   const allowedOrigins = [
     'https://yourdomain.com',
     'chromasync://localhost'
   ];
   ```

3. **Environment Security**
   - Never commit `.env` files
   - Use secure key storage
   - Rotate keys regularly

### GDPR Compliance

Features implemented:
- ✅ Data export functionality
- ✅ Account deletion with 30-day grace
- ✅ Consent tracking
- ✅ Data minimization

```typescript
// GDPR functions available
window.syncAPI.exportData()    // Export user data
window.syncAPI.deleteAccount() // Schedule deletion
window.syncAPI.acceptGDPR()    // Record consent
```

---

## Production Monitoring

### Health Checks

```typescript
// Built-in health check endpoints
GET /health                    // Application health
GET /api/database/health      // Database connectivity
GET /api/sync/status          // Sync service status
```

### Logging

```typescript
// Production logging configuration
const logger = {
  level: process.env.NODE_ENV === 'production' ? 'error' : 'debug',
  transports: [
    new winston.transports.File({ filename: 'chromasync.log' }),
    new winston.transports.Console()
  ]
};
```

### Performance Metrics

Monitor key metrics:
- Database query performance
- Sync latency
- Memory usage
- Color processing speed

```bash
# Performance testing
npm run test:perf:quick  # 10k colors
npm run test:perf:full   # 100k colors
```

---

## Troubleshooting

### Common Issues

#### Authentication Loop
```bash
# Clear authentication state
rm -rf ~/Library/Application\ Support/chroma-sync/
# Or via app: Settings > Reset Application Data
```

#### Sync Issues
```bash
# Check Supabase connectivity
curl -H "apikey: YOUR_ANON_KEY" \
     "https://your-project.supabase.co/rest/v1/colors?limit=1"

# Reset sync state
# Settings > Sync > Reset Sync Data
```

#### Database Connection
```bash
# Test local database
sqlite3 ~/Library/Application\ Support/chroma-sync/chromasync.db ".tables"

# Test Supabase connection
psql -h db.xxx.supabase.co -p 5432 -d postgres -U postgres -c "SELECT version();"
```

### Debug Mode

```bash
# Start with debug logging
npm start -- --enable-logging --verbose

# Electron DevTools
# View > Toggle Developer Tools
```

### Support Resources

- **Documentation**: All guides in this repository
- **Logs**: `~/Library/Application Support/chroma-sync/logs/`
- **Database**: `~/Library/Application Support/chroma-sync/chromasync.db`
- **Configuration**: `~/Library/Application Support/chroma-sync/config.json`

---

## Deployment Checklist

- [ ] Supabase project created and configured
- [ ] Database schema applied with RLS enabled
- [ ] Google OAuth configured and tested
- [ ] Custom domain and SSL configured
- [ ] Email notifications working
- [ ] Code signing certificates configured
- [ ] Auto-update mechanism tested
- [ ] Backup strategy implemented
- [ ] Monitoring and logging enabled
- [ ] Security review completed
- [ ] Performance testing passed
- [ ] Documentation updated