# Zoho Email Rate Limit Fix

## 🚨 Issue
You're seeing this error:
```
[ZohoEmail] Failed to refresh token: {
  error_description: 'You have made too many requests continuously. Please try again after some time.',
  error: 'Access Denied',
  status: 'failure'
}
```

## ✅ Solution

### Step 1: Wait for Rate Limit to Clear
```bash
node wait-for-rate-limit.cjs
```
This will wait 90 seconds (Zoho's rate limit cooldown period).

### Step 2: Manually Refresh Token
After waiting, run:
```bash
node refresh-zoho-token.cjs
```
This will refresh and save a valid token for ChromaSync to use.

### Step 3: Run ChromaSync
Now you can run ChromaSync normally:
```bash
npm run dev
```

## 🔧 What We Fixed
1. **Removed automatic token refresh on startup** - Only refreshes when actually sending email
2. **Added rate limit protection** - 60 second cooldown between refresh attempts
3. **Better error handling** - Detects rate limit errors and handles gracefully
4. **Manual token refresh utility** - Refresh tokens outside the app when needed

## 📝 Prevention Tips
- Don't restart ChromaSync repeatedly in quick succession
- Use the manual refresh script if you need a fresh token
- The token lasts for ~55 minutes (with 5 min safety buffer)

## 🔍 Check Token Status
To see when your current token expires:
```bash
cat ~/Library/Application\ Support/ChromaSync/zoho-tokens.json | grep expiresAt
```

Convert the timestamp to readable date:
```bash
node -e "console.log(new Date(YOUR_TIMESTAMP).toLocaleString())"
```
