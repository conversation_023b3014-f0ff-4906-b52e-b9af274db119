-- Emergency migration to fix missing columns
-- This fixes the critical errors preventing the app from working

-- Add metadata column to users table (needed for OAuth sync)
ALTER TABLE users ADD COLUMN metadata JSON DEFAULT '{}';

-- Add deleted_at columns for soft deletes (needed for sync)
ALTER TABLE products ADD COLUMN deleted_at TEXT;
ALTER TABLE colors ADD COLUMN deleted_at TEXT;

-- Add sync tracking columns if missing
ALTER TABLE products ADD COLUMN sync_version INTEGER DEFAULT 1;
ALTER TABLE colors ADD COLUMN sync_version INTEGER DEFAULT 1;

-- Record this migration
INSERT INTO migrations (name, applied_at) 
VALUES ('010_emergency_schema_fix', datetime('now'));

-- Verify the changes
SELECT 'Schema fixes applied successfully' as status;
