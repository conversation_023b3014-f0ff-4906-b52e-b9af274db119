/**
 * @file color.types.optimized.ts
 * @description Updated type definitions for the optimized color schema
 */

// Color source types
export interface ColorSource {
  id: number;
  code: string;
  name: string;
  is_system: boolean;
  properties?: Record<string, any>;
}

// Main color interface
export interface Color {
  id: number;
  external_id: string;
  source_id: number;
  code: string;
  display_name?: string;
  hex: string;
  is_gradient: boolean;
  is_metallic: boolean;
  is_effect: boolean;
  is_active: boolean;
  properties?: Record<string, any>;
  search_terms?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

// Color space interfaces
export interface ColorCMYK {
  color_id: number;
  c: number; // 0-100
  m: number; // 0-100
  y: number; // 0-100
  k: number; // 0-100
}

export interface ColorRGB {
  color_id: number;
  r: number; // 0-255
  g: number; // 0-255
  b: number; // 0-255
}

export interface ColorLAB {
  color_id: number;
  l: number; // 0-100
  a: number; // -128 to 127
  b: number; // -128 to 127
  illuminant: string;
  observer: string;
}

export interface ColorHSL {
  color_id: number;
  h: number; // 0-360
  s: number; // 0-100
  l: number; // 0-100
}

// Gradient stops
export interface GradientStop {
  gradient_id: number;
  stop_index: number;
  position: number; // 0.0-1.0
  hex: string;
}

// Color delta for comparisons
export interface ColorDelta {
  color_a_id: number;
  color_b_id: number;
  delta_cie76?: number;
  delta_cie94?: number;
  delta_cie2000?: number;
  perception: 'imperceptible' | 'barely_perceptible' | 'perceptible' | 'noticeable' | 'different';
  calculated_at: string;
}

// Complete color with all spaces
export interface ColorComplete extends Color {
  source?: ColorSource;
  cmyk?: ColorCMYK;
  rgb?: ColorRGB;
  lab?: ColorLAB;
  hsl?: ColorHSL;
  gradient_stops?: GradientStop[];
  product_count?: number;
}

// Create/Update types
export type NewColor = Omit<Color, 'id' | 'created_at' | 'updated_at' | 'is_active'> & {
  cmyk?: Omit<ColorCMYK, 'color_id'>;
  rgb?: Omit<ColorRGB, 'color_id'>;
  lab?: Omit<ColorLAB, 'color_id'>;
  hsl?: Omit<ColorHSL, 'color_id'>;
  gradient_stops?: Omit<GradientStop, 'gradient_id'>[];
};

export type UpdateColor = Partial<NewColor>;

// View types
export interface ColorView {
  id: number;
  external_id: string;
  source: string;
  is_library: boolean;
  code: string;
  display_name?: string;
  hex: string;
  cyan?: number;
  magenta?: number;
  yellow?: number;
  black?: number;
  red?: number;
  green?: number;
  blue?: number;
  lab_l?: number;
  lab_a?: number;
  lab_b?: number;
  hue?: number;
  saturation?: number;
  lightness?: number;
  is_gradient: boolean;
  is_metallic: boolean;
  is_effect: boolean;
  is_active: boolean;
  product_count: number;
  properties?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// IPC Channel names
export enum ColorChannels {
  GET_ALL = 'color:getAll',
  GET_BY_ID = 'color:getById',
  GET_COMPLETE = 'color:getComplete',
  ADD = 'color:add',
  UPDATE = 'color:update',
  DELETE = 'color:delete',
  IMPORT = 'color:import',
  EXPORT = 'color:export',
  SEARCH = 'color:search',
  COMPARE = 'color:compare',
  GET_SIMILAR = 'color:getSimilar'
}
