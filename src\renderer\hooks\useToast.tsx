import { useState } from 'react';
import { createRoot } from 'react-dom/client';
import { X } from 'lucide-react';

export interface ToastOptions {
  title: string;
  description?: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
}

interface ToastState {
  id: string;
  title: string;
  description?: string;
  type: 'success' | 'error' | 'warning' | 'info';
}

const createToastContainer = () => {
  const container = document.getElementById('toast-container');
  if (container) {return container;}

  const toastContainer = document.createElement('div');
  toastContainer.id = 'toast-container';
  toastContainer.className = 'fixed top-4 right-4 z-50 flex flex-col gap-2';
  document.body.appendChild(toastContainer);
  return toastContainer;
};

const ToastComponent = ({
  toast,
  onClose
}: {
  toast: ToastState;
  onClose: (id: string) => void
}) => {
  const getBgColor = (type: ToastState['type']): string => {
    switch (type) {
      case 'success': return 'bg-green-50 dark:bg-green-900';
      case 'error': return 'bg-red-50 dark:bg-red-900';
      case 'warning': return 'bg-yellow-50 dark:bg-yellow-900';
      case 'info': return 'bg-blue-50 dark:bg-blue-900';
      default: return 'bg-blue-50 dark:bg-blue-900';
    }
  };

  const getTextColor = (type: ToastState['type']): string => {
    switch (type) {
      case 'success': return 'text-green-800 dark:text-green-100';
      case 'error': return 'text-red-800 dark:text-red-100';
      case 'warning': return 'text-yellow-800 dark:text-yellow-100';
      case 'info': return 'text-blue-800 dark:text-blue-100';
      default: return 'text-blue-800 dark:text-blue-100';
    }
  };

  const getBorderColor = (type: ToastState['type']): string => {
    switch (type) {
      case 'success': return 'border-green-200 dark:border-green-800';
      case 'error': return 'border-red-200 dark:border-red-800';
      case 'warning': return 'border-yellow-200 dark:border-yellow-800';
      case 'info': return 'border-blue-200 dark:border-blue-800';
      default: return 'border-blue-200 dark:border-blue-800';
    }
  };

  return (
    <div
      className={`w-80 p-4 rounded-lg shadow-md border ${getBgColor(toast.type)} ${getBorderColor(toast.type)} transition-all duration-300 ease-in-out`}
      role="alert"
    >
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <h3 className={`text-sm font-medium ${getTextColor(toast.type)}`}>{toast.title}</h3>
          {toast.description && (
            <p className={`mt-1 text-xs ${getTextColor(toast.type)} opacity-90`}>{toast.description}</p>
          )}
        </div>
        <button
          className={`ml-4 inline-flex ${getTextColor(toast.type)} opacity-70 hover:opacity-100`}
          onClick={() => onClose(toast.id)}
        >
          <X className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

export const useToast = () => {
  const [toasts, setToasts] = useState<ToastState[]>([]);

  const toast = (options: ToastOptions) => {
    const id = Math.random().toString(36).substring(2, 9);
    const newToast: ToastState = {
      id,
      title: options.title,
      description: options.description,
      type: options.type || 'info'
    };

    setToasts(prev => [...prev, newToast]);

    // Create container and render toast
    const container = createToastContainer();
    const toastElement = document.createElement('div');
    toastElement.id = `toast-${id}`;
    container.appendChild(toastElement);

    const root = createRoot(toastElement);

    root.render(
      <ToastComponent
        toast={newToast}
        onClose={closeToast}
      />
    );

    // Auto-remove after duration
    if (options.duration !== 0) {
      const duration = options.duration || 3000;
      setTimeout(() => {
        closeToast(id);
      }, duration);
    }

    return id;
  };

  const closeToast = (id: string) => {
    const element = document.getElementById(`toast-${id}`);
    if (element) {
      element.classList.add('opacity-0', 'translate-x-5');
      setTimeout(() => {
        element.remove();
      }, 300);
    }

    setToasts(prev => prev.filter(t => t.id !== id));
  };

  return { toast, closeToast, toasts };
};

export default useToast;