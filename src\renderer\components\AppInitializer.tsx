/**
 * @file AppInitializer.tsx
 * @description Component that handles app initialization including auth and organization setup
 */

import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import { useSyncAuth, useSyncStore } from '../store/sync.store';
import { useOrganizationStore } from '../store/organization.store';
import { SyncAuth } from './Sync/SyncAuth';
import { OrganizationSetup } from './organization/OrganizationSetup';
import { OrganizationSelection } from './organization/OrganizationSelection';

interface AppInitializerProps {
  children: React.ReactNode;
}

export const AppInitializer: React.FC<AppInitializerProps> = ({ children }) => {
  const { isAuthenticated, user } = useSyncAuth();
  const refreshAuthState = useSyncStore(state => state.refreshAuthState);
  const organizationStatus = useSyncStore(state => state.organizationStatus);
  const syncOrganizations = useSyncStore(state => state.organizations);
  
  const { 
    currentOrganization, 
    organizations, 
    loadOrganizations,
    loadCurrentOrganization,
    setOrganizations
  } = useOrganizationStore();
  
  const [isInitializing, setIsInitializing] = useState(true);
  const [showAuth, setShowAuth] = useState(false);
  
  // Watch for currentOrganization changes
  useEffect(() => {
    if (currentOrganization) {
      console.log('[AppInitializer] Current organization changed:', currentOrganization);
      // Trigger a re-render when organization is set
      setIsInitializing(false);
    }
  }, [currentOrganization]);
  
  // Initialize app state
  useEffect(() => {
    const initializeApp = async () => {
      console.log('[AppInitializer] Starting initialization...');
      
      try {
        // Check auth state first
        await refreshAuthState();
        
        // Get current auth state
        const authResponse = await window.syncAPI?.getAuthState();
        console.log('[AppInitializer] Auth state:', authResponse);
        
        if (authResponse?.isAuthenticated) {
          // User is authenticated, load organizations
          await loadOrganizations();
          console.log('[AppInitializer] Organizations loaded');
          
          // Try to load the current organization from backend
          await loadCurrentOrganization();
          console.log('[AppInitializer] Attempted to load current organization from backend');
          
          // Check if current organization was loaded successfully
          const currentOrgAfterLoad = useOrganizationStore.getState().currentOrganization;
          console.log('[AppInitializer] Current organization after load:', currentOrgAfterLoad);
          
          // Get the loaded organizations
          const orgs = useOrganizationStore.getState().organizations;
          
          // If loadCurrentOrganization didn't set a current organization, 
          // but we have organizations available, try to set one
          if (!currentOrgAfterLoad && orgs.length > 0) {
            console.log('[AppInitializer] No current org loaded from backend, but organizations exist. Checking for stored preference...');
            
            // Check if there's a stored organization preference that matches available orgs
            const storedOrgId = localStorage.getItem('chromasync:lastOrganization');
            if (storedOrgId && orgs.some(org => org.external_id === storedOrgId)) {
              console.log('[AppInitializer] Found matching stored organization, switching to:', storedOrgId);
              await useOrganizationStore.getState().switchOrganization(storedOrgId);
            } else if (orgs.length === 1) {
              console.log('[AppInitializer] Only one organization available, auto-selecting:', orgs[0].name);
              await useOrganizationStore.getState().switchOrganization(orgs[0].external_id);
            }
          }
          
          // Re-check current organization after potential switch
          const finalCurrentOrg = useOrganizationStore.getState().currentOrganization;
          
          // Handle different scenarios based on organizations and current selection
          if (finalCurrentOrg) {
            console.log('[AppInitializer] Current organization is set:', finalCurrentOrg.name);
            // Trigger initial sync when organization is already set
            try {
              const { syncData } = await import('../store/sync.store').then(m => m.useSyncStore.getState());
              console.log('[AppInitializer] Triggering initial sync for existing organization...');
              await syncData();
              console.log('[AppInitializer] Initial sync completed');
            } catch (syncError) {
              console.error('[AppInitializer] Initial sync failed:', syncError);
            }
            // All good, will proceed to main app
          } else if (orgs.length === 1) {
            console.log('[AppInitializer] Single organization but not set as current, auto-selecting:', orgs[0].name);
            await useOrganizationStore.getState().switchOrganization(orgs[0].external_id);
          } else if (orgs.length > 1) {
            // Multiple organizations - will show selection UI
            console.log('[AppInitializer] Multiple organizations found, will show selection UI');
          } else {
            // No organizations - will show organization setup
            console.log('[AppInitializer] No organizations found, will show setup');
          }
        } else {
          // Not authenticated, will show login
          console.log('[AppInitializer] Not authenticated');
        }
      } catch (error) {
        console.error('[AppInitializer] Error during initialization:', error);
      } finally {
        setIsInitializing(false);
      }
    };
    
    initializeApp();
  }, [refreshAuthState, loadOrganizations, setOrganizations]);
  
  // Handle authentication success
  const handleAuthSuccess = async () => {
    console.log('[AppInitializer] Auth success');
    await refreshAuthState();
    
    // Check if we need organization setup
    const authResponse = await window.syncAPI?.getState();
    if (!authResponse?.currentOrganizationId) {
      // Will be handled by organization flow
      console.log('[AppInitializer] Need organization setup');
    } else {
      // Reload organizations
      await loadOrganizations();
    }
  };
  
  // Handle organization setup completion
  const handleOrganizationComplete = async () => {
    console.log('[AppInitializer] Organization setup complete');
    
    // Force a state refresh by setting a loading state
    setIsInitializing(true);
    
    try {
      // Give the backend and store a moment to fully update
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Force a full reload of organizations from backend
      await loadOrganizations();
      
      // Get the current organization from the store
      const currentOrg = useOrganizationStore.getState().currentOrganization;
      console.log('[AppInitializer] Current organization after reload:', currentOrg);
      
      if (!currentOrg) {
        // Try one more time with a longer delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        await loadOrganizations();
        
        const finalOrg = useOrganizationStore.getState().currentOrganization;
        if (!finalOrg) {
          console.error('[AppInitializer] Failed to load organization after multiple attempts');
          // Reset to show selection again
          setShowAuth(false);
          setIsInitializing(false);
          return;
        }
      }
      
      // Refresh auth state to ensure everything is synced
      await refreshAuthState();
      
      // Load initial data for the organization
      const { fetchColors } = await import('../store/color.store').then(m => m.useColorStore.getState());
      const { fetchProductsWithColors } = await import('../store/product.store').then(m => m.useProductStore.getState());
      const { syncData } = await import('../store/sync.store').then(m => m.useSyncStore.getState());
      
      // First trigger a sync to ensure we have latest data from Supabase
      console.log('[AppInitializer] Triggering initial sync with Supabase...');
      try {
        await syncData();
        console.log('[AppInitializer] Initial sync completed');
      } catch (syncError) {
        console.error('[AppInitializer] Initial sync failed:', syncError);
        // Continue anyway - we'll use whatever local data we have
      }
      
      // Then fetch the synced data
      await Promise.all([
        fetchColors(),
        fetchProductsWithColors()
      ]);
      
      console.log('[AppInitializer] Organization setup fully complete');
    } catch (error) {
      console.error('[AppInitializer] Error after organization setup:', error);
    } finally {
      // Clear initialization state to trigger re-render
      setIsInitializing(false);
      setShowAuth(false);
    }
  };
  
  // Show loading spinner during initialization
  if (isInitializing) {
    return (
      <div className="flex items-center justify-center h-screen bg-ui-background-primary">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="w-8 h-8 animate-spin text-brand-primary" />
          <p className="text-ui-foreground-secondary">Loading ChromaSync...</p>
        </div>
      </div>
    );
  }
  
  // Debug logging
  console.log('[AppInitializer] Current state:', {
    isAuthenticated,
    user,
    currentOrganization,
    organizationStatus,
    organizations,
    showAuth
  });
  
  // Not authenticated - show login
  if (!isAuthenticated || showAuth) {
    return (
      <div className="flex items-center justify-center h-screen bg-ui-background-primary">
        <div className="w-full max-w-md p-8 bg-ui-background-secondary rounded-lg shadow-xl">
          <h1 className="text-2xl font-bold text-center mb-6">Welcome to ChromaSync</h1>
          <SyncAuth onSuccess={handleAuthSuccess} />
        </div>
      </div>
    );
  }
  
  // Authenticated but no organization - show organization setup or selection
  if (isAuthenticated && user && !currentOrganization) {
    console.log('[AppInitializer] Need organization setup:', {
      organizationStatus,
      organizationsLength: organizations.length,
      syncOrganizationsLength: syncOrganizations?.length || 0
    });
    
    // If we have sync organizations from the auth response, use those
    const availableOrgs = syncOrganizations && syncOrganizations.length > 0 
      ? syncOrganizations 
      : organizations;
    
    // Ensure organizations are synced to the organization store
    if (syncOrganizations && syncOrganizations.length > 0 && organizations.length === 0) {
      console.log('[AppInitializer] Syncing organizations from sync store to organization store');
      setOrganizations(syncOrganizations);
    }
    
    // User has organizations but needs to select one
    if (availableOrgs.length > 0) {
      console.log('[AppInitializer] User has organizations, showing selection');
      return (
        <div className="flex items-center justify-center min-h-screen bg-ui-background-primary p-4">
          <OrganizationSelection
            organizations={availableOrgs}
            onSelect={handleOrganizationComplete}
          />
        </div>
      );
    }
    
    // No organizations found - show setup
    console.log('[AppInitializer] No organizations found, showing setup');
    return (
      <div className="flex items-center justify-center h-screen bg-ui-background-primary">
        <OrganizationSetup 
          user={user} 
          onComplete={handleOrganizationComplete}
        />
      </div>
    );
  }
  
  // Debug: Log the current state when we reach this fallback
  console.log('[AppInitializer] FALLBACK STATE - Something is wrong:', {
    isAuthenticated,
    user: user ? 'exists' : 'null',
    currentOrganization: currentOrganization ? 'exists' : 'null',
    organizationStatus,
    organizations: organizations.length,
    syncOrganizations: syncOrganizations?.length || 0
  });

  // If authenticated but no organization, try to sync organizations from Supabase
  if (isAuthenticated && user && !currentOrganization && organizations.length === 0) {
    console.log('[AppInitializer] Authenticated but no orgs - syncing from cloud');
    
    // Trigger a sync to fetch organizations from Supabase
    window.syncAPI?.sync().then(() => {
      console.log('[AppInitializer] Sync completed, reloading organizations');
      loadOrganizations();
    });
    
    return (
      <div className="flex items-center justify-center h-screen bg-ui-background-primary">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="w-8 h-8 animate-spin text-brand-primary" />
          <p className="text-ui-foreground-secondary">Syncing organization data...</p>
        </div>
      </div>
    );
  }

  // Show sign in button only if truly not authenticated
  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-screen bg-ui-background-primary">
        <div className="text-center">
          <p className="text-ui-foreground-secondary mb-4">
            Please sign in to continue
          </p>
          <button
            onClick={() => setShowAuth(true)}
            className="px-4 py-2 bg-brand-primary text-white rounded hover:bg-brand-secondary"
          >
            Sign In
          </button>
        </div>
      </div>
    );
  }

  // All checks passed - render the main app
  console.log('[AppInitializer] Rendering main app with organization:', currentOrganization);
  return <>{children}</>;
};
