/**
 * @file colorVisualization.ts
 * @description Utility functions for color visualization and data presentation
 */

import { ColorEntry } from '../../shared/types/color.types';
import { hexToRgb, hexToCmyk, hexToHsl } from '../../shared/utils/color';

/**
 * Convert color to complete color data object with all color spaces
 * @param color Color entry
 * @returns Complete color data with RGB, CMYK, HSL values
 */
export interface CompleteColorData {
  name: string;
  code: string;
  hex: string;
  rgb: { r: number; g: number; b: number } | null;
  cmyk: { c: number; m: number; y: number; k: number } | null;
  hsl: { h: number; s: number; l: number } | null;
  isGradient: boolean;
}

/**
 * Get complete color data for a color entry
 * @param color ColorEntry
 * @returns CompleteColorData object
 */
export const getCompleteColorData = (color: ColorEntry): CompleteColorData => {
  return {
    name: color.product,
    code: color.code,
    hex: color.hex,
    rgb: hexToRgb(color.hex),
    cmyk: hex<PERSON>o<PERSON>my<PERSON>(color.hex),
    hsl: hexToHsl(color.hex),
    isGradient: !!color.gradient
  };
};

/**
 * Format RGB values as a string
 * @param rgb RGB object
 * @returns Formatted RGB string (e.g., "R:255 G:0 B:0")
 */
export const formatRgb = (rgb: { r: number; g: number; b: number } | null): string => {
  if (!rgb) {return 'N/A';}
  return `R:${rgb.r} G:${rgb.g} B:${rgb.b}`;
};

/**
 * Format CMYK values as a string
 * @param cmyk CMYK object
 * @returns Formatted CMYK string (e.g., "C:0 M:100 Y:100 K:0")
 */
export const formatCmyk = (cmyk: { c: number; m: number; y: number; k: number } | null): string => {
  if (!cmyk) {return 'N/A';}
  return `C:${cmyk.c} M:${cmyk.m} Y:${cmyk.y} K:${cmyk.k}`;
};

/**
 * Format HSL values as a string
 * @param hsl HSL object
 * @returns Formatted HSL string (e.g., "H:0° S:100% L:50%")
 */
export const formatHsl = (hsl: { h: number; s: number; l: number } | null): string => {
  if (!hsl) {return 'N/A';}
  return `H:${hsl.h}° S:${hsl.s}% L:${hsl.l}%`;
};

/**
 * Calculate color accessibility metrics
 * @param color1 First hex color
 * @param color2 Second hex color
 * @returns Object with accessibility metrics
 */
export interface AccessibilityMetrics {
  contrastRatio: number;
  largeTextAA: boolean;
  normalTextAA: boolean;
  normalTextAAA: boolean;
  wcagLevel: 'fail' | 'AA-large' | 'AA' | 'AAA';
  statusText: string;
}

/**
 * Calculate WCAG accessibility metrics between two colors
 * @param contrastRatio Contrast ratio between two colors
 * @returns AccessibilityMetrics object
 */
export const getAccessibilityMetrics = (contrastRatio: number): AccessibilityMetrics => {
  // WCAG 2.1 thresholds
  const largeTextAA = contrastRatio >= 3.0;
  const normalTextAA = contrastRatio >= 4.5;
  const normalTextAAA = contrastRatio >= 7.0;
  
  // Determine WCAG level
  let wcagLevel: 'fail' | 'AA-large' | 'AA' | 'AAA' = 'fail';
  let statusText = 'Poor contrast';
  
  if (normalTextAAA) {
    wcagLevel = 'AAA';
    statusText = 'Excellent contrast (AAA)';
  } else if (normalTextAA) {
    wcagLevel = 'AA';
    statusText = 'Good contrast (AA)';
  } else if (largeTextAA) {
    wcagLevel = 'AA-large';
    statusText = 'Fair contrast (AA large text)';
  }
  
  return {
    contrastRatio,
    largeTextAA,
    normalTextAA,
    normalTextAAA,
    wcagLevel,
    statusText
  };
};

/**
 * Get status color based on WCAG level
 * @param wcagLevel WCAG level
 * @returns Color hex code
 */
export const getStatusColor = (wcagLevel: 'fail' | 'AA-large' | 'AA' | 'AAA'): string => {
  switch (wcagLevel) {
    case 'AAA':
      return '#38a169'; // Green for excellent
    case 'AA':
      return '#dd6b20'; // Orange for good
    case 'AA-large':
      return '#ecc94b'; // Yellow for fair
    default:
      return '#e53e3e'; // Red for poor
  }
};

/**
 * Generate descriptive relationship between two colors
 * @param color1 First color entry
 * @param color2 Second color entry
 * @returns Description of color relationship
 */
export const getColorRelationshipDescription = (
  color1: ColorEntry,
  color2: ColorEntry
): string => {
  const hsl1 = hexToHsl(color1.hex);
  const hsl2 = hexToHsl(color2.hex);
  
  if (!hsl1 || !hsl2) {
    return 'Unknown relationship';
  }
  
  // Calculate hue difference
  const hueDiff = Math.abs(hsl1.h - hsl2.h);
  const normalizedHueDiff = Math.min(hueDiff, 360 - hueDiff);
  
  // Determine relationship based on hue difference
  if (normalizedHueDiff < 15) {
    return 'Monochromatic-like';
  } else if (normalizedHueDiff >= 15 && normalizedHueDiff <= 45) {
    return 'Analogous-like';
  } else if (normalizedHueDiff >= 85 && normalizedHueDiff <= 95) {
    return 'Square-like';
  } else if (normalizedHueDiff >= 115 && normalizedHueDiff <= 125) {
    return 'Triadic-like';
  } else if (normalizedHueDiff >= 175 && normalizedHueDiff <= 185) {
    return 'Complementary';
  } else {
    return 'Discordant';
  }
}; 