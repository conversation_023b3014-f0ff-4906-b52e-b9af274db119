// import React from 'react'; // TODO: Remove unused React
import Tooltip from './Tooltip';
import { HelpCircle } from 'lucide-react';

interface TooltipTermProps {
  term: string;
  definition: string;
  position?: 'top' | 'right' | 'bottom' | 'left';
}

const TECHNICAL_TERM_DEFINITIONS: Record<string, string> = {
  'Delta E': 'A metric that quantifies the difference between two colors. Values below 2.0 are barely perceptible to the human eye, while values above 5.0 indicate clearly different colors. Example: A Delta E of 1.2 between navy blue and dark navy would be hard to notice in most contexts.',
  'WCAG AA': 'Web Content Accessibility Guidelines (Level AA) requiring a minimum contrast ratio of 4.5:1 for normal text to ensure readability for users with visual impairments. Example: Black text on a light gray background typically meets this standard, while light gray text on white would fail.',
  'Out of gamut': 'Colors that cannot be accurately reproduced in a specific color space, such as CMYK for print. These colors may appear differently when printed compared to screen. Example: Bright neon colors like electric blue often appear duller or shifted when printed with CMYK inks.',
  'Ink coverage': 'The total percentage of all four CMYK inks combined. High ink coverage (>300%) can cause printing issues including slow drying, smudging, and higher costs. Example: A deep black with C:100 M:100 Y:100 K:100 has 400% coverage and would likely cause printing problems.',
  'Protanopia': 'A type of color blindness where the red cone cells in the eye are absent, making it difficult to distinguish between red and green colors. Example: Traffic lights may appear as different brightness levels rather than distinct colors.',
  'Deuteranopia': 'A type of color blindness where the green cone cells in the eye are absent, also making it difficult to distinguish between red and green colors. Example: Red and green apples may look nearly identical to someone with this condition.',
  'Tritanopia': 'A rare type of color blindness where the blue cone cells in the eye are absent, affecting the perception of blue and yellow colors. Example: A blue sky might appear more grayish or greenish compared to normal vision.',
  'Complementary colors': 'Colors located opposite each other on the color wheel that create strong contrast and visual impact when used together. Example: Blue and orange create a vibrant, energetic combination often used in sports team logos and movie posters.',
  'Analogous colors': 'Colors that are adjacent to each other on the color wheel, creating harmonious and pleasing combinations with less contrast. Example: Yellow, yellow-green, and green create a fresh, natural palette commonly seen in organic product packaging.',
  'Triadic colors': 'Three colors equally spaced around the color wheel, providing a balanced and vibrant color scheme. Example: Red, blue, and yellow create a primary color triad used in children\'s toys and educational materials.',
  'Monochromatic colors': 'Various tints, tones, and shades of a single color, creating a cohesive and sophisticated look. Example: Light blue, medium blue, and navy create a monochromatic scheme often used in corporate branding for its professional appearance.',
};

function TooltipTerm({
  term,
  definition,
  position = 'top',
}: TooltipTermProps) {
  // Use predefined definition if available, otherwise use the provided definition
  const tooltipContent = TECHNICAL_TERM_DEFINITIONS[term] || definition;

  return (
    <Tooltip content={tooltipContent} position={position} maxWidth={300}>
      <span className="inline-flex items-center gap-0.5">
        {term}
        <HelpCircle className="w-3 h-3 text-ui-foreground-tertiary dark:text-gray-400" />
      </span>
    </Tooltip>
  );
}

export default TooltipTerm; 