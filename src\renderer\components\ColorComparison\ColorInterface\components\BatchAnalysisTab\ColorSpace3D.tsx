import { useEffect, useRef, useState, useMemo } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { hexToRgb, rgbToLab } from '../../../../../../shared/utils/color/conversion';
import { ColorSpace3DChannels } from '../../../../../../shared/constants/channels';
import { Info, Download, RotateCw, ZoomIn, ZoomOut, RotateCcw, ExternalLink } from 'lucide-react';

interface ColorSpace3DProps {
  colors: Array<{
    id: string;
    hex: string;
    name: string;
  }>;
}

type ColorSpaceType = 'lab' | 'rgb' | 'hsv';

export default function ColorSpace3D({ colors }: ColorSpace3DProps) {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const controlsRef = useRef<OrbitControls | null>(null);
  const animationIdRef = useRef<number | null>(null);
  
  const [colorSpace, setColorSpace] = useState<ColorSpaceType>('lab');
  const [showAxes, setShowAxes] = useState(true);
  const [showGrid, setShowGrid] = useState(true);
  const [pointSize, setPointSize] = useState(5);
  const [autoRotate, setAutoRotate] = useState(false);
  const [rotationSpeed, setRotationSpeed] = useState(1);
  const [zoom, setZoom] = useState(100);
  const [opacity, setOpacity] = useState(1);

  // Limit colors for performance
  const displayColors = useMemo(() => {
    if (colors.length > 500) {
      return colors.slice(0, 500);
    }
    return colors;
  }, [colors]);

  useEffect(() => {
    if (!mountRef.current) {return;}

    try {
      // Check WebGL support
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (!gl) {
        throw new Error('WebGL not supported');
      }

      // Scene setup
      const scene = new THREE.Scene();
      scene.background = new THREE.Color(0xf5f5f5);
      sceneRef.current = scene;

      // Camera
      const camera = new THREE.PerspectiveCamera(
        50,
        mountRef.current.clientWidth / mountRef.current.clientHeight,
        0.1,
        1000
      );
      camera.position.set(150, 150, 150);

      // Renderer with error handling
      let renderer: THREE.WebGLRenderer;
      try {
        renderer = new THREE.WebGLRenderer({ 
          antialias: true,
          preserveDrawingBuffer: true,
          powerPreference: 'default'
        });
      } catch (error) {
        throw new Error('WebGL renderer creation failed');
      }

      renderer.setSize(
        mountRef.current.clientWidth,
        mountRef.current.clientHeight
      );
      renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // Cap pixel ratio for performance
      
      if (mountRef.current) {
        mountRef.current.appendChild(renderer.domElement);
      }
      rendererRef.current = renderer;

    // Controls
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.rotateSpeed = 0.5;
    controls.autoRotate = autoRotate;
    controls.autoRotateSpeed = rotationSpeed;
    controlsRef.current = controls;

    // Lighting
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.4);
    directionalLight.position.set(100, 100, 50);
    scene.add(directionalLight);

    // Add axes helper if enabled
    if (showAxes) {
      const axesHelper = new THREE.AxesHelper(100);
      scene.add(axesHelper);
    }

    // Add grid
    if (showGrid) {
      const gridHelper = new THREE.GridHelper(200, 20, 0x888888, 0xcccccc);
      scene.add(gridHelper);
    }

    // Add color points
    const geometry = new THREE.SphereGeometry(pointSize / 10, 16, 16);
    const colorGroup = new THREE.Group();

    displayColors.forEach(color => {
      const rgb = hexToRgb(color.hex);
      if (!rgb) {return;}

      let position: THREE.Vector3;

      if (colorSpace === 'lab') {
        const lab = rgbToLab(rgb);
        // Map LAB to 3D coordinates (L: 0-100, a: -128 to 127, b: -128 to 127)
        position = new THREE.Vector3(lab.a, lab.l - 50, lab.b);
      } else if (colorSpace === 'rgb') {
        // Map RGB to 3D coordinates (0-255)
        position = new THREE.Vector3(
          (rgb.r - 128) * 0.5,
          (rgb.g - 128) * 0.5,
          (rgb.b - 128) * 0.5
        );
      } else {
        // HSV space - convert RGB to HSV first
        const { h, s, v } = rgbToHsv(rgb);
        // Cylindrical coordinates to Cartesian
        const radius = s * 50;
        const angle = (h / 360) * Math.PI * 2;
        position = new THREE.Vector3(
          radius * Math.cos(angle),
          v * 100 - 50,
          radius * Math.sin(angle)
        );
      }

      const material = new THREE.MeshPhongMaterial({ 
        color: color.hex,
        emissive: color.hex,
        emissiveIntensity: 0.2,
        opacity: opacity,
        transparent: opacity < 1
      });
      const sphere = new THREE.Mesh(geometry, material);
      sphere.position.copy(position);
      sphere.userData = { color };
      colorGroup.add(sphere);
    });

    scene.add(colorGroup);

    // Handle resize
    const handleResize = () => {
      if (!mountRef.current) {return;}
      camera.aspect = mountRef.current.clientWidth / mountRef.current.clientHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);
    };
    window.addEventListener('resize', handleResize);

    // Animation loop
    const animate = () => {
      animationIdRef.current = requestAnimationFrame(animate);
      controls.update();
      renderer.render(scene, camera);
    };
    animate();

      // Cleanup
      return () => {
        if (animationIdRef.current) {
          cancelAnimationFrame(animationIdRef.current);
        }
        window.removeEventListener('resize', handleResize);
        if (mountRef.current && renderer.domElement) {
          try {
            mountRef.current.removeChild(renderer.domElement);
          } catch (error) {
            // Silently ignore removal errors
          }
        }
        geometry.dispose();
        renderer.dispose();
        controls.dispose();
      };
    } catch (error) {
      // Show error message in the mount ref
      if (mountRef.current) {
        mountRef.current.innerHTML = `
          <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f5f5f5; color: #6b7280; font-family: system-ui;">
            <div style="text-align: center;">
              <div style="font-size: 3rem; margin-bottom: 1rem;">⚠️</div>
              <div>WebGL not supported or failed to initialize</div>
              <div style="font-size: 0.875rem; margin-top: 0.5rem;">Try updating your graphics drivers</div>
            </div>
          </div>
        `;
      }
      
      // Return a no-op cleanup function
      return () => {};
    }
  }, [displayColors, colorSpace, showAxes, showGrid, pointSize, autoRotate, rotationSpeed, opacity]);

  // Update zoom when it changes
  useEffect(() => {
    if (controlsRef.current && controlsRef.current.object) {
      const camera = controlsRef.current.object as THREE.PerspectiveCamera;
      const distance = 150 * (100 / zoom);
      camera.position.setLength(distance);
      controlsRef.current.update();
    }
  }, [zoom]);


  const handleReset = () => {
    if (controlsRef.current) {
      controlsRef.current.reset();
      setZoom(100);
      setRotationSpeed(1);
    }
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 10, 200));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 10, 50));
  };

  const handleExport = () => {
    if (rendererRef.current && sceneRef.current) {
      const camera = controlsRef.current?.object as THREE.Camera;
      if (camera) {
        rendererRef.current.render(sceneRef.current, camera);
        const dataURL = rendererRef.current.domElement.toDataURL('image/png');
        const link = document.createElement('a');
        link.download = `color-space-${colorSpace}-${Date.now()}.png`;
        link.href = dataURL;
        link.click();
      }
    }
  };

  const handlePopOut = async () => {
    try {
      const result = await (window as any).ipc.invoke(ColorSpace3DChannels.OPEN_WINDOW, displayColors);
      if (!result.success) {
        // Silently handle failure
      }
    } catch (error) {
      // Silently handle error
    }
  };

  const renderVisualization = () => {
    const canvasHeight = 'h-[400px]';
    
    return (
      <>
        {colors.length > 500 && (
          <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <Info className="w-4 h-4 text-yellow-600 flex-shrink-0" />
            <p className="text-sm text-yellow-800">
              Showing first 500 colors for performance. Select fewer colors for complete visualization.
            </p>
          </div>
        )}

        <div className="bg-ui-background-secondary rounded-lg p-4">
          <div ref={mountRef} className={`w-full ${canvasHeight} rounded`} />
        </div>

        {/* Advanced Controls Panel */}
        <div className="bg-ui-background-secondary rounded-lg p-4 space-y-3">
          <h4 className="text-sm font-medium mb-3">Visualization Controls</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Left Column */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm">Point Size</label>
                <div className="flex items-center gap-2">
                  <input
                    type="range"
                    min="3"
                    max="15"
                    value={pointSize}
                    onChange={(e) => setPointSize(Number(e.target.value))}
                    className="w-24"
                  />
                  <span className="text-xs font-mono w-8">{pointSize}</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm">Opacity</label>
                <div className="flex items-center gap-2">
                  <input
                    type="range"
                    min="0.1"
                    max="1"
                    step="0.1"
                    value={opacity}
                    onChange={(e) => setOpacity(Number(e.target.value))}
                    className="w-24"
                  />
                  <span className="text-xs font-mono w-8">{(opacity * 100).toFixed(0)}%</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm">Auto Rotate</label>
                <button
                  onClick={() => setAutoRotate(!autoRotate)}
                  className={`p-1.5 rounded transition-colors ${
                    autoRotate ? 'bg-brand-primary text-white' : 'bg-ui-background-tertiary'
                  }`}
                >
                  <RotateCcw className="w-4 h-4" />
                </button>
              </div>

              {autoRotate && (
                <div className="flex items-center justify-between">
                  <label className="text-sm">Rotation Speed</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="range"
                      min="0.5"
                      max="5"
                      step="0.5"
                      value={rotationSpeed}
                      onChange={(e) => setRotationSpeed(Number(e.target.value))}
                      className="w-24"
                    />
                    <span className="text-xs font-mono w-8">{rotationSpeed}x</span>
                  </div>
                </div>
              )}
            </div>

            {/* Right Column */}
            <div className="space-y-3">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={showAxes}
                  onChange={(e) => setShowAxes(e.target.checked)}
                  className="rounded border-ui-border-light"
                />
                <span className="text-sm">Show Axes</span>
                <span className="text-xs text-ui-foreground-secondary">(X=Red, Y=Green, Z=Blue)</span>
              </label>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={showGrid}
                  onChange={(e) => setShowGrid(e.target.checked)}
                  className="rounded border-ui-border-light"
                />
                <span className="text-sm">Show Grid</span>
              </label>

              <div className="pt-2">
                <p className="text-xs text-ui-foreground-secondary">
                  {displayColors.length} of {colors.length} colors displayed
                </p>
              </div>
            </div>
          </div>

          {/* Preset Views */}
          <div className="border-t border-ui-border-light pt-3">
            <p className="text-sm font-medium mb-2">Quick Views</p>
            <div className="flex gap-2 flex-wrap">
              <button
                onClick={() => {
                  if (controlsRef.current) {
                    const camera = controlsRef.current.object as THREE.Camera;
                    camera.position.set(150, 0, 0);
                    camera.lookAt(0, 0, 0);
                    controlsRef.current.update();
                  }
                }}
                className="px-3 py-1 text-xs bg-ui-background-tertiary rounded hover:bg-ui-background-primary transition-colors"
              >
                Side View
              </button>
              <button
                onClick={() => {
                  if (controlsRef.current) {
                    const camera = controlsRef.current.object as THREE.Camera;
                    camera.position.set(0, 150, 0);
                    camera.lookAt(0, 0, 0);
                    controlsRef.current.update();
                  }
                }}
                className="px-3 py-1 text-xs bg-ui-background-tertiary rounded hover:bg-ui-background-primary transition-colors"
              >
                Top View
              </button>
              <button
                onClick={() => {
                  if (controlsRef.current) {
                    const camera = controlsRef.current.object as THREE.Camera;
                    camera.position.set(0, 0, 150);
                    camera.lookAt(0, 0, 0);
                    controlsRef.current.update();
                  }
                }}
                className="px-3 py-1 text-xs bg-ui-background-tertiary rounded hover:bg-ui-background-primary transition-colors"
              >
                Front View
              </button>
              <button
                onClick={() => {
                  if (controlsRef.current) {
                    const camera = controlsRef.current.object as THREE.Camera;
                    camera.position.set(106, 106, 106);
                    camera.lookAt(0, 0, 0);
                    controlsRef.current.update();
                  }
                }}
                className="px-3 py-1 text-xs bg-ui-background-tertiary rounded hover:bg-ui-background-primary transition-colors"
              >
                Isometric
              </button>
            </div>
          </div>
        </div>

        {/* Controls Help */}
        <div className="bg-ui-background-tertiary rounded-lg p-3 text-sm">
          <p className="font-medium mb-1">Controls:</p>
          <ul className="space-y-0.5 text-ui-foreground-secondary">
            <li>• Left click + drag: Rotate view</li>
            <li>• Right click + drag: Pan view</li>
            <li>• Scroll: Zoom in/out</li>
          </ul>
        </div>
      </>
    );
  };


  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4 space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">3D Color Space Visualization</h3>
            <p className="text-sm text-ui-foreground-secondary mt-1">
              Interactive 3D view of color relationships in {colorSpace.toUpperCase()} space
            </p>
          </div>

        <div className="flex items-center gap-2">
          <select
            value={colorSpace}
            onChange={(e) => setColorSpace(e.target.value as ColorSpaceType)}
            className="px-3 py-1.5 text-sm border border-ui-border-light rounded bg-ui-background-primary"
          >
            <option value="lab">LAB Space</option>
            <option value="rgb">RGB Space</option>
            <option value="hsv">HSV Space</option>
          </select>

          <div className="flex items-center gap-1 border-l border-ui-border-light pl-2">
            <button
              onClick={handleZoomOut}
              className="p-1.5 hover:bg-ui-background-secondary rounded transition-colors"
              title="Zoom out"
            >
              <ZoomOut className="w-4 h-4" />
            </button>
            <span className="text-xs font-mono w-12 text-center">{zoom}%</span>
            <button
              onClick={handleZoomIn}
              className="p-1.5 hover:bg-ui-background-secondary rounded transition-colors"
              title="Zoom in"
            >
              <ZoomIn className="w-4 h-4" />
            </button>
          </div>

          <button
            onClick={handleReset}
            className="p-1.5 hover:bg-ui-background-secondary rounded transition-colors"
            title="Reset view"
          >
            <RotateCw className="w-4 h-4" />
          </button>

          <button
            onClick={handleExport}
            className="p-1.5 hover:bg-ui-background-secondary rounded transition-colors"
            title="Export as image"
          >
            <Download className="w-4 h-4" />
          </button>

          <button
            onClick={handlePopOut}
            className="p-1.5 hover:bg-ui-background-secondary rounded transition-colors bg-brand-secondary text-white"
            title="Open in separate window"
          >
            <ExternalLink className="w-4 h-4" />
          </button>

        </div>
      </div>

        {renderVisualization()}
      </div>
    </div>
  );
}

// Helper function to convert RGB to HSV
function rgbToHsv(rgb: { r: number; g: number; b: number }) {
  const r = rgb.r / 255;
  const g = rgb.g / 255;
  const b = rgb.b / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const delta = max - min;

  let h = 0;
  if (delta !== 0) {
    if (max === r) {
      h = ((g - b) / delta) % 6;
    } else if (max === g) {
      h = (b - r) / delta + 2;
    } else {
      h = (r - g) / delta + 4;
    }
    h = h * 60;
    if (h < 0) {h += 360;}
  }

  const s = max === 0 ? 0 : delta / max;
  const v = max;

  return { h, s, v };
}