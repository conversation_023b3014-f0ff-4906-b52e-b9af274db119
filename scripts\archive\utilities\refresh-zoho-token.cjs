#!/usr/bin/env node

// Manual token refresh script - run this to refresh and save the token
// This helps avoid rate limits by refreshing the token manually when needed

const axios = require('axios');
const fs = require('fs');
const path = require('path');
const os = require('os');
require('dotenv').config();

// Load environment variables
const CLIENT_ID = process.env.ZOHO_CLIENT_ID;
const CLIENT_SECRET = process.env.ZOHO_CLIENT_SECRET;
const REFRESH_TOKEN = process.env.ZOHO_REFRESH_TOKEN;
const REGION = process.env.ZOHO_REGION || 'COM';

// Determine token file location (matches Electron app's userData path)
const appName = 'ChromaSync';
const userData = process.platform === 'darwin' 
  ? path.join(os.homedir(), 'Library', 'Application Support', appName)
  : process.platform === 'win32'
  ? path.join(process.env.APPDATA, appName)
  : path.join(os.homedir(), '.config', appName);

const tokenFile = path.join(userData, 'zoho-tokens.json');

console.log('🔄 Zoho Token Refresh Utility\n');
console.log('Token file location:', tokenFile);
console.log('Region:', REGION);

async function refreshToken() {
  try {
    // Check if token file exists and when it expires
    if (fs.existsSync(tokenFile)) {
      const existing = JSON.parse(fs.readFileSync(tokenFile, 'utf-8'));
      const expiresAt = new Date(existing.expiresAt);
      console.log('\nExisting token expires at:', expiresAt.toLocaleString());
      
      if (Date.now() < existing.expiresAt) {
        const remainingMinutes = Math.floor((existing.expiresAt - Date.now()) / 60000);
        console.log(`✅ Token is still valid for ${remainingMinutes} minutes`);
        
        const answer = await new Promise(resolve => {
          const readline = require('readline').createInterface({
            input: process.stdin,
            output: process.stdout
          });
          readline.question('\nRefresh anyway? (y/N): ', answer => {
            readline.close();
            resolve(answer);
          });
        });
        
        if (answer.toLowerCase() !== 'y') {
          console.log('Skipping refresh.');
          return;
        }
      }
    }

    // Wait a moment to ensure we're not hitting rate limits
    console.log('\n⏳ Waiting 2 seconds to avoid rate limits...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    const authDomain = REGION === 'EU' ? 'accounts.zoho.eu' : 'accounts.zoho.com';
    
    console.log('🔄 Refreshing token...');
    const response = await axios.post(
      `https://${authDomain}/oauth/v2/token`,
      null,
      {
        params: {
          refresh_token: REFRESH_TOKEN,
          client_id: CLIENT_ID,
          client_secret: CLIENT_SECRET,
          grant_type: 'refresh_token'
        },
        timeout: 10000
      }
    );

    const tokens = {
      accessToken: response.data.access_token,
      refreshToken: REFRESH_TOKEN,
      expiresAt: Date.now() + ((response.data.expires_in - 300) * 1000) // 5 min buffer
    };

    // Ensure directory exists
    if (!fs.existsSync(userData)) {
      fs.mkdirSync(userData, { recursive: true });
    }

    // Save tokens
    fs.writeFileSync(tokenFile, JSON.stringify(tokens, null, 2));
    
    const expiresAt = new Date(tokens.expiresAt);
    console.log('✅ Token refreshed successfully!');
    console.log('   Expires at:', expiresAt.toLocaleString());
    console.log('   Token saved to:', tokenFile);
    
  } catch (error) {
    console.error('\n❌ Failed to refresh token:', error.response?.data || error.message);
    
    if (error.response?.data?.error === 'Access Denied') {
      console.error('\n⚠️  Rate limit detected! Please wait at least 1 minute before trying again.');
    }
    
    process.exit(1);
  }
}

// Run the refresh
refreshToken();
