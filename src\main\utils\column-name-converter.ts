/**
 * @file column-name-converter.ts
 * @description Utility functions to convert between snake_case and camelCase column names
 */

/**
 * Converts snake_case keys to camelCase
 * @param obj Object with snake_case keys
 * @returns Object with camelCase keys
 */
export function snakeToCamel<T extends Record<string, any>>(obj: T): Record<string, any> {
  if (!obj || typeof obj !== 'object') {return obj;}

  const result: Record<string, any> = {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
      result[camelKey] = obj[key];
    }
  }

  return result;
}

/**
 * Converts camelCase keys to snake_case
 * @param obj Object with camelCase keys
 * @returns Object with snake_case keys
 */
export function camelToSnake<T extends Record<string, any>>(obj: T): Record<string, any> {
  if (!obj || typeof obj !== 'object') {return obj;}

  const result: Record<string, any> = {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const snakeKey = key.replace(/([A-Z])/g, '_$1').toLowerCase();
      result[snakeKey] = obj[key];
    }
  }

  return result;
}

/**
 * This handles special cases where simple snake/camel conversion isn't enough
 */
export const columnMappings = {
  snakeToCamel: {
    // colors table
    'created_at': 'createdAt',
    'updated_at': 'updatedAt',
    'is_library': 'isLibrary',
    'created_by': 'createdBy',
    'updated_by': 'updatedBy',

    // datasheets table
    'product_id': 'productId',
    'file_type': 'fileType',
    'date_added': 'dateAdded',

    // product_colors table
    'color_id': 'colorId',
    'added_at': 'addedAt',

    // syncMetadata table
    'device_id': 'deviceId',
    'last_sync': 'lastSync',
    'sync_version': 'syncVersion',
    'app_version': 'appVersion',
    'db_version': 'dbVersion',
    'record_counts': 'recordCounts',
    'sync_status': 'syncStatus'
  },
  camelToSnake: {
    // colors table
    'createdAt': 'created_at',
    'updatedAt': 'updated_at',
    'isLibrary': 'is_library',
    'createdBy': 'created_by',
    'updatedBy': 'updated_by',

    // datasheets table
    'productId': 'product_id',
    'fileType': 'file_type',
    'dateAdded': 'date_added',

    // product_colors table
    'colorId': 'color_id',
    'addedAt': 'added_at',

    // syncMetadata table
    'deviceId': 'device_id',
    'lastSync': 'last_sync',
    'syncVersion': 'sync_version',
    'appVersion': 'app_version',
    'dbVersion': 'db_version',
    'recordCounts': 'record_counts',
    'syncStatus': 'sync_status'
  }
};

/**
 * Converts an object with SQLite column names to JavaScript property names
 * @param obj Object with SQLite column names
 * @returns Object with JavaScript property names
 * @example
 * convertFromDb({ created_at: '2023-01-01', is_library: 1 })
 * // Returns: { createdAt: '2023-01-01', isLibrary: 1 }
 */
export function convertFromDb<T extends Record<string, any>>(obj: T): Record<string, any> {
  if (!obj || typeof obj !== 'object') {return obj;}
  const result: Record<string, unknown> = {};
  for (const key of Object.keys(obj)) {
    // Check special mappings first
    const newKey = columnMappings.snakeToCamel[key as keyof typeof columnMappings.snakeToCamel] || 
                   key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    result[newKey] = obj[key];
  }
  return result;
}

/**
 * Converts an object with JavaScript property names to SQLite column names
 * @param obj Object with JavaScript property names
 * @returns Object with SQLite column names
 * @example
 * convertToDb({ createdAt: '2023-01-01', isLibrary: true })
 * // Returns: { created_at: '2023-01-01', is_library: true }
 */
export function convertToDb<T extends Record<string, any>>(obj: T): Record<string, any> {
  if (!obj || typeof obj !== 'object') {return obj;}
  const result: Record<string, unknown> = {};
  for (const key of Object.keys(obj)) {
    // Check special mappings first
    const newKey = columnMappings.camelToSnake[key as keyof typeof columnMappings.camelToSnake] || 
                   key.replace(/([A-Z])/g, '_$1').toLowerCase();
    result[newKey] = obj[key];
  }
  return result;
}