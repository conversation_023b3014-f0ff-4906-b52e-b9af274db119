# ChromaSync AI Assistant Rules - Quick Reference

## CRITICAL: Always Check These First
1. Database uses INTEGER primary keys, NOT TEXT
2. Views (`v_colors`, `v_products_with_colors`) MUST exist for UI to work
3. Color spaces are in separate tables (color_cmyk, color_rgb, color_lab, color_hsl)
4. Use service layer pattern: UI → Store → IPC → Service → Database

## 10 Unbreakable Rules

### 1. Schema Rule
- PRIMARY KEYS: INTEGER (internal), TEXT UUID (external_id for API)
- NEVER suggest TEXT primary keys or old schema

### 2. Color Validation
- Hex: Always 7 chars with # (validated by CHECK constraint)
- CMYK: 0-100, RGB: 0-255, LAB/HSL: specific ranges
- Use ColorSpaceConverter for automatic calculation

### 3. IPC Security
- ONLY whitelisted patterns: `color:*`, `product:*`, `selection:*`
- No direct DB access from renderer
- Always validate inputs

### 4. Service Pattern
```
Renderer → Zustand Store → window.ipc.invoke() 
    → IPC Handler → Service → Database
```
Never skip layers!

### 5. Required Views
```sql
v_colors -- Joins all color tables
v_products_with_colors -- Product summary
v_product_details -- Full relationships
```

### 6. Transactions
- Multi-table inserts MUST use transactions
- Color creation = colors + all color space tables
- Use better-sqlite3 transaction wrapper

### 7. Type Conversion
- Frontend: camelCase
- Database: snake_case
- Services handle conversion via ColorTypeConverter

### 8. Error Handling
- User-friendly messages in UI
- Technical logs in console
- Never expose SQL errors to users

### 9. Compatibility
- Keep external_id UUIDs for Supabase
- Maintain API compatibility in services
- Don't break existing features

### 10. Performance
- Prepared statements for repeated queries
- WITHOUT ROWID on junction tables
- Indexes on foreign keys and search columns

## Quick Checks
```bash
# View database state
sqlite3 chromasync.db ".tables"

# Check for views
sqlite3 chromasync.db "SELECT name FROM sqlite_master WHERE type='view';"

# Verify data exists
sqlite3 chromasync.db "SELECT COUNT(*) FROM products WHERE is_active=1;"
```

## Common Fixes
- **No data showing**: Create missing views with `fix-database-complete.sh`
- **Color spaces missing**: Run `populate-color-spaces.js`
- **IPC errors**: Check preload whitelist
- **Type errors**: Use compatibility layer

## Key Files
- `src/main/db/database.ts` - Core DB implementation
- `src/main/db/services/*.service.ts` - Business logic
- `src/main/ipc/*.ts` - IPC handlers
- `src/preload/index.ts` - Channel whitelist
