-- Complete fix for product_colors sync issue in Supabase
-- Run this entire script in Supabase SQL Editor

-- 1. First, let's check what we have
SELECT 'Current state:' as info;
SELECT 
    'Products' as table_name,
    COUNT(*) as count
FROM products
UNION ALL
SELECT 
    'Colors' as table_name,
    COUNT(*) as count
FROM colors
UNION ALL
SELECT 
    'Product-Colors' as table_name,
    COUNT(*) as count
FROM product_colors;

-- 2. Drop and recreate product_colors table with correct structure
DROP TABLE IF EXISTS product_colors CASCADE;

CREATE TABLE public.product_colors (
    product_id INTEGER NOT NULL,
    color_id INTEGER NOT NULL,
    display_order INTEGER DEFAULT 0,
    PRIMARY KEY (product_id, color_id),
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (color_id) REFERENCES colors(id) ON DELETE CASCADE
);

-- <PERSON>reate indexes
CREATE INDEX idx_product_colors_product ON product_colors(product_id);
CREATE INDEX idx_product_colors_color ON product_colors(color_id);

-- 3. Enable RLS
ALTER TABLE product_colors ENABLE ROW LEVEL SECURITY;

-- 4. Create simple RLS policies that work with the sync
CREATE POLICY "Enable all access for authenticated users" ON product_colors
    FOR ALL 
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- 5. Grant permissions
GRANT ALL ON product_colors TO authenticated;
GRANT ALL ON product_colors TO anon;

-- 6. Test the structure
SELECT 
    'Table created successfully' as status,
    column_name,
    data_type
FROM information_schema.columns
WHERE table_name = 'product_colors'
ORDER BY ordinal_position;

-- 7. Verify we can insert (this is just a test, we'll delete it)
DO $$
DECLARE
    test_product_id INTEGER;
    test_color_id INTEGER;
BEGIN
    -- Get first product and color
    SELECT id INTO test_product_id FROM products LIMIT 1;
    SELECT id INTO test_color_id FROM colors LIMIT 1;
    
    IF test_product_id IS NOT NULL AND test_color_id IS NOT NULL THEN
        -- Try to insert
        INSERT INTO product_colors (product_id, color_id, display_order)
        VALUES (test_product_id, test_color_id, 999)
        ON CONFLICT (product_id, color_id) DO NOTHING;
        
        -- Check if it worked
        IF EXISTS (SELECT 1 FROM product_colors WHERE display_order = 999) THEN
            RAISE NOTICE 'Test insert successful';
            -- Clean up test data
            DELETE FROM product_colors WHERE display_order = 999;
        ELSE
            RAISE NOTICE 'Test insert failed';
        END IF;
    ELSE
        RAISE NOTICE 'No products or colors found to test with';
    END IF;
END $$;

-- 8. Final check
SELECT 'Ready for sync!' as message;