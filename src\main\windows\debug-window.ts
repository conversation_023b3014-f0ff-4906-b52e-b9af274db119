import { BrowserWindow, ipcMain } from 'electron';
import path from 'path';
import { isDevelopment } from '../utils/env';

let debugWindow: BrowserWindow | null = null;

/**
 * Create a debug window that stays open even if the main window closes
 */
export function createDebugWindow(): BrowserWindow {
  // If debug window already exists, just show it
  if (debugWindow) {
    debugWindow.show();
    return debugWindow;
  }

  // Create the browser window.
  debugWindow = new BrowserWindow({
    width: 800,
    height: 600,
    title: 'ChromaSync Debug Console',
    webPreferences: {
      // For debug window only, we need nodeIntegration for direct access to logs
      // This is acceptable for a debug tool not exposed in production
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: true, // Always enable web security
      allowRunningInsecureContent: false, // Don't allow insecure content
    },
    show: false, // Don't show until ready
  });

  // Load the debug HTML file
  const debugHtmlPath = path.join(__dirname, '../../../src/renderer/debug.html');
  console.log(`Loading debug window from: ${debugHtmlPath}`);
  debugWindow.loadFile(debugHtmlPath);

  // Open the DevTools in development mode
  if (isDevelopment) {
    debugWindow.webContents.openDevTools();
  }

  // Show window when ready
  debugWindow.once('ready-to-show', () => {
    if (debugWindow) {
      debugWindow.show();
    }
  });

  // Handle window close
  debugWindow.on('closed', () => {
    debugWindow = null;
  });

  return debugWindow;
}

/**
 * Log a message to the debug window
 */
export function logToDebugWindow(level: 'info' | 'warn' | 'error', source: string, message: string, data?: any): void {
  if (!debugWindow || debugWindow.isDestroyed()) {
    return;
  }

  const timestamp = new Date().toISOString();
  const logData = {
    timestamp,
    level,
    source,
    message,
    data: data ? JSON.stringify(data, null, 2) : undefined,
  };

  debugWindow.webContents.send('debug-log', logData);
}

/**
 * Initialize debug window IPC handlers
 */
export function initDebugWindowHandlers(): void {
  // Open debug window
  ipcMain.handle('open-debug-window', () => {
    createDebugWindow();
    return true;
  });

  // Clear debug logs
  ipcMain.handle('clear-debug-logs', () => {
    if (debugWindow && !debugWindow.isDestroyed()) {
      debugWindow.webContents.send('clear-logs');
    }
    return true;
  });

  // Test database operations
  ipcMain.handle('test-database-operation', async (_, operation: string) => {
    try {
      logToDebugWindow('info', 'Test', `Testing database operation: ${operation}`);

      // Import database services only when needed
      const { getDatabase } = require('../db/database');
      // const { SelectionService } = require('../db/services/selection.service'); // DISABLED
      const { openSharePointUrl } = require('../utils/browser-opener');

      const db = getDatabase();
      if (!db) {
        throw new Error('Database not initialized');
      }

      // const selectionService = new SelectionService(db); // DISABLED

      // Perform the requested test operation
      let result;
      switch (operation) {
        case 'count-datasheets':
          // Test the COUNT query
          try {
            logToDebugWindow('info', 'Database', 'Executing COUNT query on datasheets table');
            // Use the correct table name 'datasheets'
            const stmt = db.prepare('SELECT COUNT(*) as count FROM datasheets');
            const row = stmt.get();
            result = { count: row.count };
            logToDebugWindow('info', 'Database', `Datasheet count: ${row.count}`);
          } catch (dbError) {
            logToDebugWindow('error', 'Database', 'Error counting datasheets', dbError);
            throw dbError;
          }
          break;

        case 'get-datasheets':
          // Get all datasheets
          try {
            logToDebugWindow('info', 'Database', 'Executing SELECT query on datasheets table');
            // Use the correct table name 'datasheets' and snake_case columns
            const stmt = db.prepare(`
              SELECT id, selection_id, name, url, file_type, date_added
              FROM datasheets
              ORDER BY date_added DESC
            `);
            const rows = stmt.all();
            result = { datasheets: rows };
            logToDebugWindow('info', 'Database', `Retrieved ${rows.length} datasheets`);

            // Log each datasheet
            rows.forEach((datasheet: any, index: number) => {
              logToDebugWindow('info', 'Database', `Datasheet ${index + 1}:`, datasheet);
            });
          } catch (dbError) {
            logToDebugWindow('error', 'Database', 'Error getting datasheets', dbError);
            throw dbError;
          }
          break;

        case 'add-test-datasheet':
          // DISABLED: This case uses selections which have been replaced with products
          /*
          // Add a test datasheet
          try {
            // Get the first selection to add a datasheet to
            logToDebugWindow('info', 'Database', 'Finding a selection to add datasheet to');
            const selectionsStmt = db.prepare('SELECT id, name FROM selections LIMIT 1');
            const selection = selectionsStmt.get();

            if (!selection) {
              throw new Error('No selections found in database');
            }

            logToDebugWindow('info', 'Database', `Found selection: ${selection.name} (${selection.id})`);

            // Create a test datasheet with a timestamp to make it unique
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const testUrl = `https://example.com/test-${timestamp}.pdf`;
            const testName = `Test Datasheet ${timestamp}`;

            logToDebugWindow('info', 'Database', `Adding datasheet: ${testName} to selection ${selection.id}`);

            const datasheet = selectionService.addDatasheet(selection.id, {
              name: testName,
              path: testUrl,
              fileType: 'pdf'
            });

            if (!datasheet) {
              throw new Error('Failed to add datasheet - returned undefined');
            }

            result = { datasheet };
            logToDebugWindow('info', 'Database', 'Added test datasheet successfully', datasheet);
          } catch (dbError) {
            logToDebugWindow('error', 'Database', 'Error adding test datasheet', dbError);
            throw dbError;
          }
          */
          result = { message: 'This operation has been disabled - use products instead of selections' };
          break;

        case 'open-sharepoint':
          // Test opening a SharePoint URL
          try {
            const url = 'https://acmevape.sharepoint.com/:w:/s/Creative/EUYSf-W1vPVDolfrrry0egYBSENvKa5K4307tcNaPKH8_Q';
            logToDebugWindow('info', 'Browser', `Attempting to open SharePoint URL: ${url}`);

            const success = await openSharePointUrl(url);
            result = { success };
            logToDebugWindow('info', 'Browser', `Opened SharePoint URL: ${success ? 'success' : 'failed'}`);
          } catch (browserError) {
            logToDebugWindow('error', 'Browser', 'Error opening SharePoint URL', browserError);
            throw browserError;
          }
          break;

        default:
          throw new Error(`Unknown test operation: ${operation}`);
      }

      return {
        success: true,
        message: `Test operation ${operation} completed successfully`,
        data: result
      };
    } catch (error: unknown) { // Add type annotation for error
      logToDebugWindow('error', 'Test', `Error testing database operation: ${operation}`, error);
      // Type guard for accessing error properties
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        success: false,
        message: `Test failed: ${errorMessage}`,
        error: String(error) // Convert error to string
      };
    }
  });
}

// Export a function to get the debug window
export function getDebugWindow(): BrowserWindow | null {
  return debugWindow;
}
