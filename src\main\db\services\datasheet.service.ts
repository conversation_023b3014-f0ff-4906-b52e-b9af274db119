/**
 * @file datasheet.service.ts
 * @description Service for managing datasheets in the database
 */

import { Database } from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import { shell } from 'electron';
import { DatasheetEntry } from '../../../shared/types/datasheet.types';
import { isWebUrl } from '../../utils/url-utils';
import { openSharePointUrl } from '../../utils/browser-opener';

/**
 * Service for managing datasheets in the database
 */
export class DatasheetService {
  private db: Database;

  constructor(db: Database) {
    console.log('[DatasheetService] Constructor called');
    this.db = db;
    console.log('[DatasheetService] Database instance set');
    this.initTable();
    console.log('[DatasheetService] Constructor completed');
  }

  /**
   * Initializes the datasheets table with optimized schema
   */
  private initTable(): void {
    try {
      console.log('[DatasheetService] Creating optimized datasheets table...');
      
      // Create the optimized datasheets table
      this.db.exec(`
        CREATE TABLE IF NOT EXISTS datasheets (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          external_id TEXT NOT NULL UNIQUE,
          product_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          url TEXT NOT NULL,
          file_type TEXT NOT NULL CHECK (file_type IN ('pdf', 'docx', 'xlsx', 'pptx', 'link', 'other')),
          is_external INTEGER NOT NULL DEFAULT 1,
          description TEXT,
          tags TEXT,
          metadata TEXT,
          created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
          created_by TEXT,
          updated_by TEXT,
          is_active INTEGER NOT NULL DEFAULT 1,
          FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
        )
      `);
      
      // Create indexes for performance
      const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_datasheets_product_id ON datasheets(product_id)',
        'CREATE INDEX IF NOT EXISTS idx_datasheets_external_id ON datasheets(external_id)', 
        'CREATE INDEX IF NOT EXISTS idx_datasheets_is_active ON datasheets(is_active)',
        'CREATE INDEX IF NOT EXISTS idx_datasheets_file_type ON datasheets(file_type)',
        'CREATE INDEX IF NOT EXISTS idx_datasheets_created_at ON datasheets(created_at)'
      ];
      
      for (const indexSql of indexes) {
        this.db.exec(indexSql);
      }
      
      // Create view for easy access with product information
      this.db.exec(`
        CREATE VIEW IF NOT EXISTS v_datasheets AS
        SELECT 
          d.external_id AS id,
          p.external_id AS product_id,
          p.name AS product_name,
          d.name,
          d.url,
          d.file_type,
          d.is_external,
          d.description,
          d.tags,
          d.metadata,
          d.created_at,
          d.updated_at,
          d.created_by,
          d.updated_by
        FROM datasheets d
        JOIN products p ON d.product_id = p.id
        WHERE d.is_active = 1 AND p.is_active = 1
      `);

      // Create trigger for updated_at timestamp
      this.db.exec(`
        CREATE TRIGGER IF NOT EXISTS update_datasheets_timestamp 
        AFTER UPDATE ON datasheets
        FOR EACH ROW
        WHEN NEW.updated_at = OLD.updated_at
        BEGIN
          UPDATE datasheets SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
        END;
      `);

      console.log('[DatasheetService] Optimized datasheets table created successfully');
    } catch (error) {
      console.error('[DatasheetService] Error initializing table:', error);
      throw error;
    }
  }

  /**
   * Convert database row to DatasheetEntry
   */
  private convertToDatasheetEntry(row: any): DatasheetEntry {
    return {
      id: row.external_id || row.id,
      productId: row.product_id,
      name: row.name,
      path: row.url || row.path,
      url: row.url || row.path,
      fileType: row.file_type as DatasheetEntry['fileType'],
      dateAdded: row.created_at,
      createdAt: row.created_at,
      updatedAt: row.updated_at || row.created_at,
      isExternal: Boolean(row.is_external)
    };
  }

  /**
   * Get all datasheets for a product using external ID
   */
  getDatasheetsByProductId(productExternalId: string): DatasheetEntry[] {
    try {
      if (!productExternalId || typeof productExternalId !== 'string') {
        console.error('[DatasheetService] Invalid productId provided:', productExternalId);
        return [];
      }

      // Get the internal product ID first
      const product = this.db.prepare(`
        SELECT id FROM products WHERE external_id = ? AND is_active = 1
      `).get(productExternalId) as { id: number } | undefined;

      if (!product) {
        console.log(`[DatasheetService] Product not found: ${productExternalId}`);
        return [];
      }

      // Query datasheets using internal product ID
      const rows = this.db.prepare(`
        SELECT * FROM datasheets
        WHERE product_id = ? AND is_active = 1
        ORDER BY created_at DESC
      `).all(product.id);

      return rows.map(row => this.convertToDatasheetEntry(row));
    } catch (error) {
      console.error(`[DatasheetService] Error getting datasheets for product ${productExternalId}:`, error);
      return [];
    }
  }

  /**
   * Get datasheets by product name
   */
  getDatasheetsByProductName(productName: string): DatasheetEntry[] {
    try {
      if (!productName || typeof productName !== 'string') {
        console.error('[DatasheetService] Invalid productName provided:', productName);
        return [];
      }

      // Try to use the view first, fall back to direct query if view doesn't exist
      let rows;
      try {
        rows = this.db.prepare(`
          SELECT * FROM v_datasheets
          WHERE product_name = ?
          ORDER BY created_at DESC
        `).all(productName);
      } catch (_viewError) {
        console.log('[DatasheetService] View not available, using direct query');
        // Fall back to direct query
        rows = this.db.prepare(`
          SELECT 
            d.external_id AS id,
            p.external_id AS product_id,
            p.name AS product_name,
            d.name,
            d.url,
            d.file_type,
            d.is_external,
            d.description,
            d.tags,
            d.metadata,
            d.created_at,
            d.updated_at,
            d.created_by,
            d.updated_by
          FROM datasheets d
          JOIN products p ON d.product_id = p.id
          WHERE d.is_active = 1 AND p.is_active = 1
            AND p.name = ?
          ORDER BY d.created_at DESC
        `).all(productName);
      }

      return rows.map(row => this.convertToDatasheetEntry(row));
    } catch (error) {
      console.error(`[DatasheetService] Error getting datasheets for product name ${productName}:`, error);
      return [];
    }
  }

  /**
   * Get a datasheet by ID
   */
  getDatasheetById(datasheetId: string): DatasheetEntry | undefined {
    try {
      if (!datasheetId || typeof datasheetId !== 'string') {
        console.error('[DatasheetService] Invalid datasheetId provided:', datasheetId);
        return undefined;
      }

      const row = this.db.prepare(`
        SELECT * FROM datasheets WHERE id = ?
      `).get(datasheetId);

      return row ? this.convertToDatasheetEntry(row) : undefined;
    } catch (error) {
      console.error(`[DatasheetService] Error getting datasheet ${datasheetId}:`, error);
      return undefined;
    }
  }

  /**
   * Add a datasheet to a product
   */
  addDatasheet(
    productExternalId: string,
    datasheet: Omit<DatasheetEntry, 'id' | 'dateAdded' | 'createdAt' | 'updatedAt'>,
    userId?: string
  ): DatasheetEntry | undefined {
    try {
      // Validate inputs
      if (!productExternalId || typeof productExternalId !== 'string') {
        console.error('[DatasheetService] Invalid productId provided:', productExternalId);
        return undefined;
      }

      if (!datasheet || typeof datasheet !== 'object') {
        console.error('[DatasheetService] Invalid datasheet object provided:', datasheet);
        return undefined;
      }

      if (!datasheet.name || typeof datasheet.name !== 'string') {
        console.error('[DatasheetService] Invalid datasheet name provided:', datasheet.name);
        return undefined;
      }

      const url = datasheet.url || datasheet.path;
      if (!url || typeof url !== 'string') {
        console.error('[DatasheetService] Invalid datasheet URL/path provided:', url);
        return undefined;
      }

      if (!datasheet.fileType || typeof datasheet.fileType !== 'string') {
        console.error('[DatasheetService] Invalid datasheet fileType provided:', datasheet.fileType);
        return undefined;
      }

      // Get the internal product ID
      const product = this.db.prepare(`
        SELECT id FROM products WHERE external_id = ? AND is_active = 1
      `).get(productExternalId) as { id: number } | undefined;

      if (!product) {
        console.error(`[DatasheetService] Product not found: ${productExternalId}`);
        return undefined;
      }

      const externalId = uuidv4();
      const now = new Date().toISOString();
      const isExternal = isWebUrl(url);

      // Prepare tags and metadata if provided
      const tags = datasheet.tags ? JSON.stringify(datasheet.tags) : null;
      const metadata = datasheet.metadata ? JSON.stringify(datasheet.metadata) : null;

      const result = this.db.prepare(`
        INSERT INTO datasheets (
          external_id, product_id, name, url, file_type, 
          is_external, description, tags, metadata, created_by
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        externalId,
        product.id,
        datasheet.name,
        url,
        datasheet.fileType,
        isExternal ? 1 : 0,
        datasheet.description || null,
        tags,
        metadata,
        userId || null
      );

      if (result.changes > 0) {
        return {
          id: externalId,
          productId: productExternalId,
          name: datasheet.name,
          path: url,
          url: url,
          fileType: datasheet.fileType,
          dateAdded: now,
          createdAt: now,
          updatedAt: now,
          createdBy: userId,
          description: datasheet.description,
          isExternal,
          tags: datasheet.tags,
          metadata: datasheet.metadata
        };
      }

      return undefined;
    } catch (error) {
      console.error(`[DatasheetService] Error adding datasheet:`, error);
      return undefined;
    }
  }

  /**
   * Add a SharePoint or web link to a product
   */
  addWebLink(
    productExternalId: string,
    url: string,
    displayName: string,
    description?: string,
    userId?: string
  ): DatasheetEntry | undefined {
    try {
      // Validate inputs
      if (!productExternalId || typeof productExternalId !== 'string') {
        console.error('[DatasheetService] Invalid productId provided:', productExternalId);
        return undefined;
      }

      if (!url || typeof url !== 'string') {
        console.error('[DatasheetService] Invalid URL provided:', url);
        return undefined;
      }

      if (!displayName || typeof displayName !== 'string') {
        console.error('[DatasheetService] Invalid displayName provided:', displayName);
        return undefined;
      }

      // Ensure URL has a protocol
      let normalizedUrl = url;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        normalizedUrl = `https://${url}`;
      }

      // Determine file type from URL if possible
      let fileType: DatasheetEntry['fileType'] = 'link';
      const urlLower = normalizedUrl.toLowerCase();
      if (urlLower.includes('.pdf')) {fileType = 'pdf';}
      else if (urlLower.includes('.docx') || urlLower.includes('.doc')) {fileType = 'docx';}
      else if (urlLower.includes('.xlsx') || urlLower.includes('.xls')) {fileType = 'xlsx';}
      else if (urlLower.includes('.pptx') || urlLower.includes('.ppt')) {fileType = 'pptx';}

      // Add metadata for SharePoint URLs
      const isSharePoint = urlLower.includes('sharepoint.com');
      const metadata = isSharePoint ? { source: 'sharepoint', originalUrl: normalizedUrl } : undefined;

      return this.addDatasheet(
        productExternalId,
        {
          productId: productExternalId,
          name: displayName,
          path: normalizedUrl,
          url: normalizedUrl,
          fileType,
          description,
          metadata,
          tags: isSharePoint ? ['sharepoint'] : undefined
        },
        userId
      );
    } catch (error) {
      console.error(`[DatasheetService] Error adding web link:`, error);
      return undefined;
    }
  }

  /**
   * Remove a datasheet (soft delete)
   */
  removeDatasheet(datasheetExternalId: string): boolean {
    try {
      if (!datasheetExternalId || typeof datasheetExternalId !== 'string') {
        console.error('[DatasheetService] Invalid datasheetId provided:', datasheetExternalId);
        return false;
      }

      const result = this.db.prepare(`
        UPDATE datasheets 
        SET is_active = 0, updated_at = datetime('now')
        WHERE external_id = ? AND is_active = 1
      `).run(datasheetExternalId);

      return result.changes > 0;
    } catch (error) {
      console.error(`[DatasheetService] Error removing datasheet:`, error);
      return false;
    }
  }

  /**
   * Open a datasheet
   */
  async openDatasheet(datasheetId: string): Promise<{ success: boolean; message?: string }> {
    try {
      console.log(`Attempting to open datasheet with ID: ${datasheetId}`);

      // Find the datasheet by ID
      const datasheet = this.getDatasheetById(datasheetId);

      if (!datasheet) {
        const message = `Datasheet with ID ${datasheetId} not found`;
        console.warn(message);
        return { success: false, message };
      }

      // Open the file
      try {
        // Check if it's a web link
        if (isWebUrl(datasheet.path)) {
          // For web links, use our improved URL handler
          const success = await openSharePointUrl(datasheet.path);
          if (!success) {
            const message = `Failed to open web link: ${datasheet.path}`;
            console.error(message);
            return { success: false, message };
          }
        } else {
          // For local files, use shell.openPath
          await shell.openPath(datasheet.path);
        }

        console.log(`Successfully opened datasheet: ${datasheet.name} at ${datasheet.path}`);
        return { success: true };
      } catch (err) {
        const message = `Failed to open datasheet: ${err instanceof Error ? err.message : String(err)}`;
        console.error(message);
        return { success: false, message };
      }
    } catch (error) {
      const message = `Error opening datasheet: ${error instanceof Error ? error.message : String(error)}`;
      console.error(message);
      return { success: false, message };
    }
  }

  /**
   * Open all datasheets for a product
   */
  async openDatasheets(productId: string): Promise<{ success: boolean; count: number; errors: string[] }> {
    let openCount = 0;
    const errors: string[] = [];
    try {
      const datasheets = this.getDatasheetsByProductId(productId);

      if (datasheets.length === 0) {
        console.log(`No datasheets found for product: ${productId}`);
        return { success: true, count: 0, errors: [] };
      }

      console.log(`Attempting to open ${datasheets.length} datasheets for product: ${productId}`);

      // Use Promise.allSettled to handle potential errors for individual files
      const results = await Promise.allSettled(datasheets.map(async (datasheet) => {
        try {
          const result = await this.openDatasheet(datasheet.id);
          if (result.success) {
            return { status: 'fulfilled', path: datasheet.path };
          } else {
            throw new Error(result.message || `Failed to open ${datasheet.path}`);
          }
        } catch (err) {
          const message = `Failed to open datasheet ${datasheet.name} at ${datasheet.path}: ${err instanceof Error ? err.message : String(err)}`;
          console.warn(message);
          return { status: 'rejected', reason: message };
        }
      }));

      // Count successes and collect errors
      results.forEach(result => {
        if (result.status === 'fulfilled') {
          openCount++;
        } else {
          errors.push(result.reason);
        }
      });

      return {
        success: openCount > 0,
        count: openCount,
        errors
      };
    } catch (error) {
      console.error(`Error opening datasheets for product ${productId}:`, error);
      return {
        success: false,
        count: openCount,
        errors: [...errors, `General error: ${error instanceof Error ? error.message : String(error)}`]
      };
    }
  }

  /**
   * Count datasheets for a product
   */
  countDatasheets(productId: string): number {
    try {
      if (!productId || typeof productId !== 'string') {
        console.error('Invalid productId provided to countDatasheets:', productId);
        return 0;
      }

      // Get the internal product ID
      const product = this.db.prepare(`
        SELECT id FROM products WHERE external_id = ? AND is_active = 1
      `).get(productId) as { id: number } | undefined;

      if (!product) {
        return 0;
      }

      const result = this.db.prepare(`
        SELECT COUNT(*) as count FROM datasheets 
        WHERE product_id = ? AND is_active = 1
      `).get(product.id) as { count: number };

      return result?.count || 0;
    } catch (error) {
      console.error(`Error counting datasheets for product ${productId}:`, error);
      return 0;
    }
  }

}
