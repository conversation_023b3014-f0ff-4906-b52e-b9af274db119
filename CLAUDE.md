# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Development
```bash
npm install          # Install dependencies
npm run dev          # Start dev server with hot reload
npm start            # Build + start with debugging enabled
```

### Testing
```bash
npm test             # Run all tests
npm run test:perf    # Performance benchmarks (ts-node)
npm run test:perf:full # Full performance test (up to 100k colors)
npm run test:perf:quick # Quick performance test (10k colors)
npm run lint         # Run ESLint
npm run typecheck    # TypeScript type checking
```

### Building & Packaging
```bash
npm run build        # Build for production (includes build-config.cjs)
npm run package      # Build installer for all platforms
npm run package:win  # Windows installer only
npm run package:mac  # macOS installer only
npm run package:linux # Linux installer only
```

### Email Service Testing
```bash
# Test email functionality
npm run test:email
npm run test-smtp-connection     # Verify SMTP credentials
npm run test-email-zoho         # Test Zoho API directly
npm run test-invitation-email   # Test invitation emails
```

### Database & Utilities
```bash
# Database operations
node scripts/init-database.cjs  # Initialize fresh database
node scripts/check-db.cjs       # Check database status

# Performance testing
npm run test:perf:ui            # UI performance tests
```

## Architecture Overview

ChromaSync is an Electron application with strict process separation:

### Process Architecture
- **Main Process**: Node.js backend - handles database, file system, cloud sync
- **Renderer Process**: React UI - no direct database/file system access
- **Preload Script**: Security bridge - exposes typed IPC channels only

### Critical Architecture Rules
1. **Database access ONLY in main process** - Never attempt database operations in renderer
2. **All IPC through preload** - Use established channel patterns in `src/shared/constants/channels.ts`
3. **No Node.js APIs in renderer** - Use IPC handlers for file system, native features
4. **Type safety required** - No `any` types, full TypeScript coverage
5. **Supabase access ONLY in main process** - Never initialize Supabase client in renderer
6. **Sync through services** - Use RealtimeSyncService for all cloud operations
7. **Offline-first design** - Local SQLite database is always the primary source of truth
8. **GDPR compliance** - Always check consent before sync operations
9. **Snake case for SQL** - All database queries must use snake_case naming
10. **Parameterized queries mandatory** - Prevent SQL injection

### Key Services & Patterns

#### Database Services (`src/main/db/services/`)
- **ColorService**: Color CRUD operations with organization scoping
- **ProductService**: Product management with color associations
- **OrganizationService**: Multi-tenant organization management
- **DatasheetService**: PDF/web link management for products

#### Cloud Sync (`src/main/services/`)
- **RealtimeSyncService**: Bidirectional sync with Supabase
- **OAuthService**: PKCE-based Google authentication
- **SupabaseClient**: Configured with secure storage and PKCE flow

#### State Management (`src/renderer/store/`)
- **Zustand stores**: color.store, product.store, sync.store, organization.store
- **Pattern**: Actions call window.api methods, handle responses, update state

### Multi-Tenant Architecture
- Every data record is scoped to an organization via `organization_id`
- Users can belong to multiple organizations
- Organization context persists across sessions
- Row Level Security (RLS) enforces data isolation in cloud

### Database Schema
- **Local**: SQLite with optimized JSONB storage for color spaces
- **Cloud**: PostgreSQL (Supabase) with matching schema
- **Sync**: Last-write-wins with device attribution
- **Migrations**: Located in `src/main/db/migrations/`

### Environment Configuration

ChromaSync uses a dual-mode configuration system:

**Development Mode** (`npm run dev`):
- Loads directly from `.env` file via dotenv
- Real-time environment variable changes

**Production Mode** (`npm run build` → `npm run package`):
- `build-config.cjs` reads `.env` at build time
- Creates `out/app-config.json` with embedded credentials
- Runtime loads from `config-loader.ts` (env vars → build config → fallback)

```bash
# Required for cloud sync
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key

# Required for email invitations (Zoho OAuth)
ZOHO_CLIENT_ID=your-client-id
ZOHO_CLIENT_SECRET=your-client-secret
ZOHO_REFRESH_TOKEN=your-refresh-token
ZOHO_ACCOUNT_ID=your-account-id
ZOHO_REGION=EU                    # EU for European accounts, omit for US
ZOHO_SUPPORT_ALIAS=<EMAIL>

# Optional
AUTH_REDIRECT_URL=https://auth.chromasync.app/callback
```

⚠️ **Security Warning**: Production builds embed credentials in `app-config.json`!

## Application-Specific Patterns

### Email Service (Zoho OAuth)

**Configuration Loading Strategy**:
1. Environment variables (development)
2. Build config file (`out/app-config.json` - production)
3. Empty fallback (graceful degradation)

**Critical Zoho Patterns**:
- **Rate limiting**: 1-minute cooldown between token refresh attempts
- **Regional domains**: `zoho.com` (US) vs `zoho.eu` (EU) based on `ZOHO_REGION`
- **Token refresh**: Automatic with 5-minute buffer before expiry
- **Fallback pattern**: Manual invitation codes when email deep links fail

```typescript
// Email service initialization pattern
await zohoEmailService.initialize();  // Don't refresh token here
await zohoEmailService.sendEmail(...); // Token refreshed on first send
```

### Organization Context Management

**Switching Organizations**:
```typescript
// Pattern: Clear → Switch → Reload
await window.organizationAPI.setCurrentOrganization(orgId);
useColorStore.getState().clearColors();     // Critical: clear existing data
useProductStore.getState().clearProducts(); 
await useColorStore.getState().fetchColors(); // Reload for new org
```

**Organization Setup Requirements**:
- User must have an organization before sync works
- Auto-created on first sign-in
- Stored in electron-store, persists across sessions

### Database & Sync Patterns

**Color Space Optimization**:
- **Local**: All color spaces stored (RGB, CMYK, LAB, HSL)
- **Cloud**: Only CMYK synced (75% storage reduction)
- **Conversion**: RGB/LAB/HSL calculated on-demand from CMYK

**Sync Service Patterns**:
- **Conflict resolution**: Last-write-wins with device attribution
- **Offline queue**: Changes queued when offline, synced on reconnect
- **Real-time subscriptions**: Must cleanup on component unmount
- **Organization scoping**: All sync operations filtered by current org

### Common Development Tasks

#### Adding a New IPC Channel
1. Define channel in `src/shared/constants/channels.ts`
2. Add handler in appropriate `src/main/ipc/*.ipc.ts` file
3. Add type definition in `src/preload/index.ts`
4. Use in renderer via `window.yourAPI.method()`

#### Database Migrations
- Located in `src/main/db/migrations/`
- Auto-run on app startup
- Use `.sql` files for schema changes
- Always use snake_case for database fields

#### Email Testing Workflow
```bash
# 1. Test SMTP connection
npm run test-smtp-connection

# 2. Test Zoho API directly  
npm run test-email-zoho

# 3. Test full invitation flow
npm run test-invitation-email
```

## Common Gotchas & Troubleshooting

### Email Service Issues
1. **Rate limiting errors**: Use `test-smtp-connection` to verify config
2. **Regional domain mismatch**: Check `ZOHO_REGION` setting (EU vs US)
3. **Token refresh failures**: 1-minute cooldown between attempts
4. **Deep link failures**: Email contains manual invitation codes as fallback

### Authentication & Sync Issues
1. **Authentication loops**: Clear data via Settings → Reset Application Data
2. **GDPR consent blocking**: Required for EU users, blocks sync until accepted
3. **Organization setup**: User needs organization before sync works
4. **Sync not working**: Verify organization exists and user is member

### Database Issues
1. **Database locked**: Close all ChromaSync instances before development
2. **Migration failures**: Check `src/main/db/migrations/` for syntax errors
3. **Performance issues**: Use prepared statements, avoid N+1 queries
4. **Data isolation**: Always filter by `organization_id`

### Build & Configuration Issues
1. **Missing email config**: Check both `.env` and `out/app-config.json`
2. **Environment variables not working**: Use `getConfig()` from `config-loader.ts`
3. **Build failures**: Ensure `build-config.cjs` runs successfully
4. **Production email not working**: Verify `app-config.json` in build output

### Performance Considerations
- Use prepared statements for repeated queries
- Implement virtual scrolling for lists >100 items
- Batch sync operations with 500ms debounce
- Only sync CMYK color data (RGB/LAB/HSL calculated locally)
- Monitor memory usage with large datasets (100k+ colors tested)

### Security Requirements
- All user input must be validated
- IPC channels are whitelisted in preload
- OAuth uses PKCE flow for security
- Tokens stored using hardware-backed encryption (safeStorage)
- Never expose Supabase service role key to renderer
- Row Level Security (RLS) enforces data isolation in cloud