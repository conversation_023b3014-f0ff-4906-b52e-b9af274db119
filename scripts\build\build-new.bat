@echo off
echo ============================================================
echo Pantone Tracker New Build
echo ============================================================

rem Build the application
echo Step 1: Building the application...
call npm run build
if %ERRORLEVEL% NEQ 0 (
    echo Error building the application
    exit /b 1
)
echo Application built successfully

rem Build the installer
echo Step 2: Building the Windows installer...
call npx electron-builder --win --config new-builder.json
if %ERRORLEVEL% NEQ 0 (
    echo Error building the installer
    exit /b 1
)
echo Installer built successfully

echo ============================================================
echo Pantone Tracker Windows installer created successfully!
echo Location: dist-new/
echo ============================================================
