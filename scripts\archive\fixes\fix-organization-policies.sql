-- Fix infinite recursion in organization_members RLS policies
-- The issue is that the policies reference the same table they're protecting

-- First, disable <PERSON><PERSON> temporarily to make changes
ALTER TABLE organization_members DISABLE ROW LEVEL SECURITY;

-- Drop the problematic policies
DROP POLICY IF EXISTS "Users can view organization members" ON organization_members;
DROP POLICY IF EXISTS "Admins and owners can manage members" ON organization_members;
DROP POLICY IF EXISTS "Users can see their organizations" ON organizations;
DROP POLICY IF EXISTS "Members can update their organization" ON organizations;

-- Re-enable RLS
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- Create fixed policies for organizations
-- This policy doesn't create recursion because it only checks the user_id directly
CREATE POLICY "Users can see their organizations" ON organizations
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = organizations.id
            AND om.user_id = auth.uid()
        )
    );

CREATE POLICY "Owners can update organizations" ON organizations
    FOR UPDATE TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = organizations.id
            AND om.user_id = auth.uid()
            AND om.role = 'owner'
        )
    );

CREATE POLICY "Owners can delete organizations" ON organizations
    FOR DELETE TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = organizations.id
            AND om.user_id = auth.uid()
            AND om.role = 'owner'
        )
    );

-- Create fixed policies for organization_members
-- The key is to use auth.uid() directly without querying the same table
CREATE POLICY "Users can view their own memberships" ON organization_members
    FOR SELECT TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Users can view members of their organizations" ON organization_members
    FOR SELECT TO authenticated
    USING (
        organization_id IN (
            SELECT om2.organization_id 
            FROM organization_members om2
            WHERE om2.user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can insert members" ON organization_members
    FOR INSERT TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = organization_members.organization_id
            AND om.user_id = auth.uid()
            AND om.role IN ('owner', 'admin')
        )
    );

CREATE POLICY "Admins can update members" ON organization_members
    FOR UPDATE TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = organization_members.organization_id
            AND om.user_id = auth.uid()
            AND om.role IN ('owner', 'admin')
        )
    );

CREATE POLICY "Admins can delete members" ON organization_members
    FOR DELETE TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = organization_members.organization_id
            AND om.user_id = auth.uid()
            AND om.role IN ('owner', 'admin')
        )
    );

-- Fix colors policies to avoid recursion
DROP POLICY IF EXISTS "Organization members can view colors" ON colors;
DROP POLICY IF EXISTS "Organization members can create colors" ON colors;
DROP POLICY IF EXISTS "Organization members can update colors" ON colors;
DROP POLICY IF EXISTS "Organization members can delete colors" ON colors;

CREATE POLICY "Users can view organization colors" ON colors
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = colors.organization_id
            AND om.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create organization colors" ON colors
    FOR INSERT TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = colors.organization_id
            AND om.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update organization colors" ON colors
    FOR UPDATE TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = colors.organization_id
            AND om.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete organization colors" ON colors
    FOR DELETE TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = colors.organization_id
            AND om.user_id = auth.uid()
        )
    );

-- Fix products policies
DROP POLICY IF EXISTS "Organization members can view products" ON products;
DROP POLICY IF EXISTS "Organization members can create products" ON products;
DROP POLICY IF EXISTS "Organization members can update products" ON products;
DROP POLICY IF EXISTS "Organization members can delete products" ON products;

CREATE POLICY "Users can view organization products" ON products
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = products.organization_id
            AND om.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create organization products" ON products
    FOR INSERT TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = products.organization_id
            AND om.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update organization products" ON products
    FOR UPDATE TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = products.organization_id
            AND om.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete organization products" ON products
    FOR DELETE TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = products.organization_id
            AND om.user_id = auth.uid()
        )
    );

-- Fix product_colors policies
DROP POLICY IF EXISTS "Organization members can view product colors" ON product_colors;
DROP POLICY IF EXISTS "Organization members can manage product colors" ON product_colors;

CREATE POLICY "Users can view organization product colors" ON product_colors
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = product_colors.organization_id
            AND om.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage organization product colors" ON product_colors
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = product_colors.organization_id
            AND om.user_id = auth.uid()
        )
    );

-- Test query to verify policies work
-- This should not cause infinite recursion anymore
-- SELECT * FROM organizations WHERE auth.uid() IS NOT NULL;
-- SELECT * FROM organization_members WHERE auth.uid() IS NOT NULL;
