@echo off
echo Pantone Color Tracker Installation Script

echo Checking Node.js installation...
where node >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
  echo Node.js is not installed. Please install Node.js 16 or later.
  exit /b 1
)

for /f "tokens=1,2,3 delims=v." %%a in ('node -v') do (
  set MAJOR_VERSION=%%b
)

if %MAJOR_VERSION% LSS 16 (
  echo Node.js version must be 16 or later.
  exit /b 1
)

echo Node.js detected successfully.

echo Installing dependencies...
call npm install
if %ERRORLEVEL% NEQ 0 (
  echo Failed to install dependencies.
  exit /b 1
)

echo Dependencies installed successfully.

echo Setting up directory structure...
mkdir src\renderer\components\ColorTable 2>nul
mkdir src\renderer\components\ColorSwatches 2>nul
mkdir src\renderer\store 2>nul
mkdir src\renderer\utils 2>nul
mkdir src\main\db\services 2>nul
mkdir src\main\ipc 2>nul
mkdir src\shared\types 2>nul

echo Directory structure created.

echo Building the application...
call npm run build
if %ERRORLEVEL% NEQ 0 (
  echo Failed to build the application.
  exit /b 1
)

echo Application built successfully.
echo Installation complete! You can now run the application with 'npm run dev'. 