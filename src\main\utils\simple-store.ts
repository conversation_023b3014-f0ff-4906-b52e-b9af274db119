/**
 * @file simple-store.ts
 * @description A simple key-value store implementation that doesn't rely on electron-store
 */

import fs from 'fs';
import path from 'path';
import { app } from 'electron';

/**
 * A simple key-value store that saves data to a JSON file
 */
export class SimpleStore<T extends Record<string, any>> {
  private data: T;
  private filePath: string;
  private defaultData: T;

  /**
   * Create a new SimpleStore
   * @param options Store options
   */
  constructor(options: {
    name: string;
    defaults?: T;
    encryptionKey?: string; // Ignored, just for compatibility
  }) {
    // Get the user data directory
    const userDataPath = app.getPath('userData');

    // Create the file path
    this.filePath = path.join(userDataPath, `${options.name}.json`);

    // Set default data
    this.defaultData = (options.defaults || {}) as T;

    // Initialize data
    this.data = { ...this.defaultData };

    // Load data from file if it exists
    this.load();

    console.log(`SimpleStore initialized at ${this.filePath}`);
  }

  /**
   * Load data from file
   */
  private load(): void {
    try {
      if (fs.existsSync(this.filePath)) {
        const fileData = fs.readFileSync(this.filePath, 'utf8');
        this.data = JSON.parse(fileData);
        console.log(`SimpleStore: Loaded data from ${this.filePath}`);
      } else {
        console.log(`SimpleStore: No existing file at ${this.filePath}, using defaults`);
        this.save(); // Save default data
      }
    } catch (error) {
      console.error(`SimpleStore: Error loading data from ${this.filePath}:`, error);
      console.log('SimpleStore: Using default data and creating new file');
      this.data = { ...this.defaultData };
      this.save();
    }
  }

  /**
   * Save data to file
   */
  private save(): void {
    try {
      // Create directory if it doesn't exist
      const dir = path.dirname(this.filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // Write data to file
      fs.writeFileSync(this.filePath, JSON.stringify(this.data, null, 2));
      console.log(`SimpleStore: Saved data to ${this.filePath}`);
    } catch (error) {
      console.error(`SimpleStore: Error saving data to ${this.filePath}:`, error);
    }
  }

  /**
   * Get a value from the store
   * @param key Key to get
   * @param defaultValue Default value if key doesn't exist
   * @returns Value from store or default value
   */
  get<K extends keyof T>(key: K, defaultValue?: T[K]): T[K] {
    const value = this.data[key];
    return value !== undefined ? value : (defaultValue !== undefined ? defaultValue : this.defaultData[key]);
  }

  /**
   * Set a value in the store
   * @param key Key to set
   * @param value Value to set
   */
  set<K extends keyof T>(key: K, value: T[K]): void {
    this.data[key] = value;
    this.save();
  }

  /**
   * Check if a key exists in the store
   * @param key Key to check
   * @returns True if key exists
   */
  has(key: keyof T): boolean {
    return key in this.data;
  }

  /**
   * Delete a key from the store
   * @param key Key to delete
   */
  delete(key: keyof T): void {
    delete this.data[key];
    this.save();
  }

  /**
   * Clear all data from the store
   */
  clear(): void {
    this.data = { ...this.defaultData };
    this.save();
  }

  /**
   * Get all data from the store
   * @returns All data
   */
  getAll(): T {
    return { ...this.data };
  }
}
