/**
 * Frontend Color Library Service
 * Replaces static color imports with database queries via IPC
 */

import type { ColorEntry } from '../../shared/types/color.types';

export interface ColorLibrarySearchOptions {
  library?: 'PANTONE' | 'RAL' | 'NCS';
  search?: string;
  limit?: number;
  offset?: number;
  sortBy?: 'code' | 'name' | 'popularity';
  sortOrder?: 'asc' | 'desc';
}

export interface ColorLibrarySearchResult {
  colors: ColorEntry[];
  total: number;
  hasMore: boolean;
}

/**
 * Frontend color library service
 * Communicates with the main process to query color library data
 */
export class ColorLibraryService {
  
  /**
   * Search colors across libraries
   */
  async searchColors(options: ColorLibrarySearchOptions = {}): Promise<ColorLibrarySearchResult> {
    try {
      const result = await (window as any).colorLibraryAPI.searchColors(options);
      return this.transformResult(result);
    } catch (error) {
      console.error('[ColorLibraryService] Failed to search colors:', error);
      return { colors: [], total: 0, hasMore: false };
    }
  }
  
  /**
   * Get Pantone colors
   */
  async getPantoneColors(options: Omit<ColorLibrarySearchOptions, 'library'> = {}): Promise<ColorLibrarySearchResult> {
    return this.searchColors({ ...options, library: 'PANTONE' });
  }
  
  /**
   * Get RAL colors
   */
  async getRalColors(options: Omit<ColorLibrarySearchOptions, 'library'> = {}): Promise<ColorLibrarySearchResult> {
    return this.searchColors({ ...options, library: 'RAL' });
  }
  
  /**
   * Get all Pantone colors (for backward compatibility)
   */
  async getAllPantoneColors(): Promise<ColorEntry[]> {
    try {
      const result = await this.getPantoneColors({ limit: 10000 });
      return result.colors;
    } catch (error) {
      console.error('[ColorLibraryService] Failed to get all Pantone colors:', error);
      return [];
    }
  }
  
  /**
   * Get all RAL colors (for backward compatibility)
   */
  async getAllRalColors(): Promise<ColorEntry[]> {
    try {
      const result = await this.getRalColors({ limit: 10000 });
      return result.colors;
    } catch (error) {
      console.error('[ColorLibraryService] Failed to get all RAL colors:', error);
      return [];
    }
  }
  
  /**
   * Search colors with full-text search
   */
  async fullTextSearch(query: string, options: ColorLibrarySearchOptions = {}): Promise<ColorLibrarySearchResult> {
    try {
      const result = await (window as any).colorLibraryAPI.fullTextSearch(query, options);
      return this.transformResult(result);
    } catch (error) {
      console.error('[ColorLibraryService] Failed to perform full-text search:', error);
      return { colors: [], total: 0, hasMore: false };
    }
  }

  /**
   * Get popular colors
   */
  async getPopularColors(libraryCode?: string, limit: number = 20): Promise<ColorEntry[]> {
    try {
      const result = await (window as any).colorLibraryAPI.getPopularColors(libraryCode, limit);
      return this.transformColors(result);
    } catch (error) {
      console.error('[ColorLibraryService] Failed to get popular colors:', error);
      return [];
    }
  }

  /**
   * Get color by external ID
   */
  async getColorByExternalId(externalId: string): Promise<ColorEntry | null> {
    try {
      const result = await (window as any).colorLibraryAPI.getColorByExternalId(externalId);
      return result ? this.transformColor(result) : null;
    } catch (error) {
      console.error('[ColorLibraryService] Failed to get color by external ID:', error);
      return null;
    }
  }

  /**
   * Get color by library and code
   */
  async getColorByCode(libraryCode: string, colorCode: string): Promise<ColorEntry | null> {
    try {
      const result = await (window as any).colorLibraryAPI.getColorByCode(libraryCode, colorCode);
      return result ? this.transformColor(result) : null;
    } catch (error) {
      console.error('[ColorLibraryService] Failed to get color by code:', error);
      return null;
    }
  }

  /**
   * Increment usage count for a color
   */
  async incrementUsage(externalId: string): Promise<void> {
    try {
      await (window as any).colorLibraryAPI.incrementUsage(externalId);
    } catch (error) {
      console.error('[ColorLibraryService] Failed to increment usage:', error);
    }
  }
  
  /**
   * Transform database result to frontend format
   */
  private transformResult(result: any): ColorLibrarySearchResult {
    return {
      colors: this.transformColors(result.colors),
      total: result.total,
      hasMore: result.hasMore
    };
  }
  
  /**
   * Transform array of database colors to frontend format
   */
  private transformColors(colors: any[]): ColorEntry[] {
    return colors.map(color => this.transformColor(color));
  }
  
  /**
   * Transform single database color to frontend format
   */
  private transformColor(color: any): ColorEntry {
    return {
      id: color.external_id,
      product: color.library_name || color.library_code,
      name: color.name,
      code: color.code,
      hex: color.hex,
      cmyk: color.cmyk,
      rgb: color.rgb,
      lab: color.lab,
      hsl: color.hsl,
      notes: color.notes,
      isLibrary: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
  }
}

// Singleton instance
let colorLibraryService: ColorLibraryService | null = null;

/**
 * Get singleton instance of color library service
 */
export function getColorLibraryService(): ColorLibraryService {
  if (!colorLibraryService) {
    colorLibraryService = new ColorLibraryService();
  }
  return colorLibraryService;
}

/**
 * Backward compatibility exports
 * These replace the static imports from pantoneColors.ts and ralColors.ts
 */

/**
 * Get Pantone colors (replaces import from pantoneColors.ts)
 */
export async function getPantoneColors(): Promise<ColorEntry[]> {
  const service = getColorLibraryService();
  return await service.getAllPantoneColors();
}

/**
 * Get RAL colors (replaces import from ralColors.ts)
 */
export async function getRalColors(): Promise<ColorEntry[]> {
  const service = getColorLibraryService();
  return await service.getAllRalColors();
}

/**
 * Search Pantone colors by name or code
 */
export async function searchPantoneColors(query: string, limit: number = 50): Promise<ColorEntry[]> {
  const service = getColorLibraryService();
  const result = await service.getPantoneColors({ search: query, limit });
  return result.colors;
}

/**
 * Search RAL colors by name or code
 */
export async function searchRalColors(query: string, limit: number = 50): Promise<ColorEntry[]> {
  const service = getColorLibraryService();
  const result = await service.getRalColors({ search: query, limit });
  return result.colors;
}

/**
 * Get popular colors across all libraries
 */
export async function getPopularLibraryColors(limit: number = 20): Promise<ColorEntry[]> {
  const service = getColorLibraryService();
  return await service.getPopularColors(undefined, limit);
}

// Export the service as default
export default ColorLibraryService;
