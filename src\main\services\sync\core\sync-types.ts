/**
 * @file sync-types.ts
 * @description Shared types and interfaces for the sync system
 * 
 * This module contains all the TypeScript interfaces, types, and enums
 * used throughout the sync system for type safety and consistency.
 */

import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';

// ============================================================================
// CORE SYNC TYPES
// ============================================================================

/**
 * Sync operation types
 */
export type SyncOperation = 'initial' | 'incremental' | 'manual' | 'realtime';

/**
 * Sync status states
 */
export type SyncStatus = 'idle' | 'initializing' | 'syncing' | 'error' | 'completed';

/**
 * Circuit breaker states
 */
export type CircuitBreakerState = 'CLOSED' | 'OPEN' | 'HALF_OPEN';

/**
 * Network quality levels
 */
export type NetworkQuality = 'excellent' | 'good' | 'fair' | 'poor' | 'offline';

/**
 * Conflict resolution strategies
 */
export enum ConflictStrategy {
  LAST_WRITE_WINS = 'last_write_wins',
  MANUAL_RESOLUTION = 'manual',
  FIELD_LEVEL_MERGE = 'field_merge',
  LOCAL_WINS = 'local_wins',
  REMOTE_WINS = 'remote_wins'
}

// ============================================================================
// SYNC CONFIGURATION
// ============================================================================

/**
 * Batch size configuration for different operations
 */
export interface BatchSizeConfig {
  products: number;
  colors: number;
  relationships: number;
  supabaseQuery: number;
}

/**
 * Timeout configuration
 */
export interface TimeoutConfig {
  connection: number;
  heartbeat: number;
  healthCheck: number;
  recovery: number;
}

/**
 * Retry configuration
 */
export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

/**
 * Main sync configuration
 */
export interface SyncConfig {
  batchSizes: BatchSizeConfig;
  timeouts: TimeoutConfig;
  retry: RetryConfig;
  conflictStrategy: ConflictStrategy;
  enableAnalytics: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
}

// ============================================================================
// SYNC QUEUE TYPES
// ============================================================================

/**
 * Queue item metadata
 */
export interface QueueItemMetadata {
  timestamp: number;
  attempts: number;
  lastAttempt: number;
  priority: number;
  source: 'local' | 'realtime' | 'manual';
}

/**
 * Sync queue item
 */
export interface SyncQueueItem {
  id: string;
  table: 'products' | 'colors' | 'organizations' | 'product_colors';
  action: 'upsert' | 'delete' | 'sync';
  data?: any;
  metadata: QueueItemMetadata;
  eventType?: 'INSERT' | 'UPDATE' | 'DELETE';
}

/**
 * Queue statistics
 */
export interface QueueStats {
  memorySize: number;
  persistentSize: number;
  totalItems: number;
  pendingItems: number;
  failedItems: number;
  oldestItem: number | null;
}

// ============================================================================
// ERROR HANDLING TYPES
// ============================================================================

/**
 * Error severity levels
 */
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

/**
 * Error categories
 */
export type ErrorCategory = 'network' | 'auth' | 'database' | 'validation' | 'conflict' | 'timeout';

/**
 * Sync error details
 */
export interface SyncError {
  id: string;
  timestamp: number;
  operation: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  message: string;
  originalError?: any;
  context?: Record<string, any>;
  recoverable: boolean;
}

/**
 * Error recovery item
 */
export interface ErrorRecoveryItem {
  operation: string;
  data: any;
  attempts: number;
  lastAttempt: number;
  maxAttempts: number;
  nextRetry: number;
}

// ============================================================================
// PERFORMANCE MONITORING TYPES
// ============================================================================

/**
 * Sync performance metrics
 */
export interface SyncMetrics {
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  averageDuration: number;
  slowOperations: number;
  lastSyncDuration: number;
  successRate: number;
}

/**
 * Network performance metrics
 */
export interface NetworkMetrics {
  latency: number;
  bandwidth: number;
  quality: NetworkQuality;
  isOnline: boolean;
  lastCheck: number;
}

/**
 * Sync analytics data
 */
export interface SyncAnalytics {
  syncFrequency: number;
  dataVolume: number;
  errorRate: number;
  peakUsageHours: number[];
  averageItemsPerSync: number;
}

// ============================================================================
// SYNC STRATEGY TYPES
// ============================================================================

/**
 * Sync strategy interface
 */
export interface ISyncStrategy {
  readonly name: string;
  readonly priority: number;
  
  canHandle(table: string, operation: SyncOperation): boolean;
  execute(items: SyncQueueItem[]): Promise<SyncResult>;
  validateData(data: any): boolean;
  transformData?(data: any): any;
}

/**
 * Sync result
 */
export interface SyncResult {
  success: boolean;
  itemsProcessed: number;
  itemsSucceeded: number;
  itemsFailed: number;
  errors: SyncError[];
  duration: number;
  metadata?: Record<string, any>;
}

// ============================================================================
// CONNECTION TYPES
// ============================================================================

/**
 * Connection health status
 */
export interface ConnectionHealth {
  isConnected: boolean;
  lastHealthCheck: number;
  consecutiveFailures: number;
  latency: number;
  quality: NetworkQuality;
}

/**
 * Realtime subscription status
 */
export interface RealtimeStatus {
  channel: RealtimeChannel | null;
  isSubscribed: boolean;
  lastMessage: number;
  messageCount: number;
  errors: string[];
}

// ============================================================================
// SYNC PROGRESS TYPES
// ============================================================================

/**
 * Sync progress phases
 */
export type SyncPhase = 'initializing' | 'pulling' | 'pushing' | 'resolving_conflicts' | 'complete' | 'error';

/**
 * Sync progress information
 */
export interface SyncProgress {
  phase: SyncPhase;
  progress: number; // 0-100
  currentOperation: string;
  itemsProcessed: number;
  totalItems: number;
  estimatedTimeRemaining: number;
  errors: string[];
  warnings: string[];
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Debounced function type
 */
export type DebouncedFunction<T extends (...args: any[]) => any> = T & {
  cancel: () => void;
  flush: () => void;
};

/**
 * Event handler type for sync events
 */
export type SyncEventHandler<T = any> = (data: T) => void | Promise<void>;

/**
 * Sync event types
 */
export interface SyncEvents {
  'sync:started': SyncProgress;
  'sync:progress': SyncProgress;
  'sync:completed': SyncResult;
  'sync:error': SyncError;
  'connection:status': ConnectionHealth;
  'queue:updated': QueueStats;
}

// ============================================================================
// EXPORTS
// ============================================================================

export type {
  RealtimePostgresChangesPayload,
  RealtimeChannel
};
