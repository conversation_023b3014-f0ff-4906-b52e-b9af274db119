#!/bin/bash

# ChromaSync Database Reset Script
# This script clears all ChromaSync databases (both local and Supabase)

echo "🧹 ChromaSync Database Reset Tool"
echo "================================"
echo ""

# Function to get database path based on OS
get_db_path() {
    case "$OSTYPE" in
        darwin*)  # macOS
            echo "$HOME/Library/Application Support/chromasync"
            ;;
        msys*|cygwin*)  # Windows
            echo "$APPDATA/chromasync"
            ;;
        *)  # Linux and others
            echo "$HOME/.config/chromasync"
            ;;
    esac
}

# Get the database directory
DB_DIR=$(get_db_path)
echo "📁 Database directory: $DB_DIR"
echo ""

# Check if directory exists
if [ ! -d "$DB_DIR" ]; then
    echo "❌ Database directory not found. ChromaSync may not be installed."
    exit 1
fi

# Navigate to database directory
cd "$DB_DIR" || exit 1

# Create backup directory
mkdir -p legacy-databases

echo "🗑️  Cleaning up databases..."
echo ""

# Function to safely move a file if it exists
move_if_exists() {
    if [ -f "$1" ]; then
        timestamp=$(date +%Y%m%d-%H%M%S)
        mv "$1" "legacy-databases/${1}.backup-${timestamp}"
        echo "  ✓ Moved $1 to legacy-databases/"
    fi
}

# Clear all database files
move_if_exists "chromasync.db"
move_if_exists "chromasync.db-wal"
move_if_exists "chromasync.db-shm"
move_if_exists "database.sqlite"
move_if_exists "database.sqlite-wal"
move_if_exists "database.sqlite-shm"
move_if_exists "chroma-sync-data.db"
move_if_exists "chroma-sync-data.db-wal"
move_if_exists "chroma-sync-data.db-shm"

# Move any remaining backup files
for file in *.backup-* *.bak.*; do
    if [ -f "$file" ]; then
        mv "$file" legacy-databases/
        echo "  ✓ Moved $file to legacy-databases/"
    fi
done

echo ""
echo "✅ Local databases cleared!"
echo ""
echo "📊 Legacy databases stored in: $DB_DIR/legacy-databases/"
echo ""
echo "🔄 Next steps:"
echo "  1. Start ChromaSync - it will create a fresh optimized database"
echo "  2. Import your color data"
echo "  3. Sign in with Google to sync to Supabase"
echo ""
echo "⚠️  Note: To clear Supabase data, use the Supabase dashboard or run:"
echo "   npx supabase db reset --project-ref tzqnxhsnitogmtihhsrq"
