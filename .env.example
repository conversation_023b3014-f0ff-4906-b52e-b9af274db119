# ChromaSync Environment Variables
# Copy this to .env and fill in your values

# Application
NODE_ENV=development
APP_DOMAIN=chromasync.app

# Supabase Configuration
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here

# Email Configuration (Zoho OAuth)
ZOHO_CLIENT_ID=your-zoho-oauth-client-id
ZOHO_CLIENT_SECRET=your-zoho-oauth-client-secret
ZOHO_ACCOUNT_ID=your-zoho-account-id
ZOHO_REFRESH_TOKEN=your-initial-refresh-token
ZOHO_SUPPORT_ALIAS=<EMAIL>
ZOHO_REGION=EU  # Use 'EU' for European accounts, leave empty for US

# OAuth Configuration
AUTH_REDIRECT_URL=https://auth.chromasync.app/callback

# Optional: Analytics
CLOUDFLARE_ANALYTICS_TOKEN=your-token-here

# Optional: Error Tracking
SENTRY_DSN=your-sentry-dsn-here