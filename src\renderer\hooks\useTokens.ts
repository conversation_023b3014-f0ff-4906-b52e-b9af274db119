/**
 * @file useTokens.ts
 * @description Hook for accessing the design token system with theme awareness
 */

import { useContext, useMemo } from 'react';
import { ThemeContext } from '../context/ThemeContext';
import tokens from '../styles/tokens';
import type { DesignTokens } from '../styles/tokens/types';

/**
 * Custom hook that provides access to the design token system
 * with awareness of the current theme (light/dark)
 * 
 * @returns {DesignTokens & { theme: 'light' | 'dark' }} The design tokens and current theme
 */
export function useTokens(): DesignTokens & { theme: 'light' | 'dark' } {
  const themeContext = useContext(ThemeContext);
  const isDarkMode = themeContext?.mode === 'dark';
  
  // Return appropriate tokens based on theme
  return useMemo(() => ({
    ...tokens,
    theme: isDarkMode ? 'dark' : 'light',
  }), [isDarkMode]);
} 