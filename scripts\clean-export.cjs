#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Read the original export file
const exportPath = process.argv[2];
if (!exportPath) {
  console.error('Usage: node clean-export.js <export-file-path>');
  process.exit(1);
}

console.log('ChromaSync Export Cleaner');
console.log('========================');
console.log(`Reading from: ${exportPath}`);

const data = JSON.parse(fs.readFileSync(exportPath, 'utf8'));

// Helper function to standardize CMYK values
function standardizeCMYK(cmyk) {
  if (!cmyk) return 'C:0 M:0 Y:0 K:0';
  
  // Remove any whitespace
  cmyk = cmyk.trim();
  
  // Handle "C:0 M:60 Y:94 K:0" format - already correct
  if (cmyk.match(/^C:\d+\s+M:\d+\s+Y:\d+\s+K:\d+$/)) {
    return cmyk;
  }
  
  // Handle "0/48/100/77" format
  if (cmyk.match(/^\d+\/\d+\/\d+\/\d+$/)) {
    const parts = cmyk.split('/');
    return `C:${parts[0]} M:${parts[1]} Y:${parts[2]} K:${parts[3]}`;
  }
  
  // Handle "C2, M24, Y100, K0" format
  if (cmyk.match(/^C\d+,\s*M\d+,\s*Y\d+,\s*K\d+$/)) {
    const matches = cmyk.match(/C(\d+),\s*M(\d+),\s*Y(\d+),\s*K(\d+)/);
    if (matches) {
      return `C:${matches[1]} M:${matches[2]} Y:${matches[3]} K:${matches[4]}`;
    }
  }
  
  // Handle comma-separated format "0, 60, 94, 0"
  if (cmyk.match(/^\d+,\s*\d+,\s*\d+,\s*\d+$/)) {
    const parts = cmyk.split(',').map(v => v.trim());
    return `C:${parts[0]} M:${parts[1]} Y:${parts[2]} K:${parts[3]}`;
  }
  
  console.warn(`Unknown CMYK format: ${cmyk}`);
  return cmyk;
}

// Map UUIDs to readable product names
const productNameMap = new Map();
let productCounter = 1;

// Clean up the colors
const cleanedColors = data.colors.map(color => {
  // Handle product names
  let productName = color.product;
  if (productName && productName.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
    // It's a UUID - replace with a readable name
    if (!productNameMap.has(productName)) {
      productNameMap.set(productName, `Product ${productCounter++}`);
    }
    productName = productNameMap.get(productName);
  }
  
  return {
    name: color.name || color.code || 'Unnamed Color',
    code: color.code || '',
    hex: color.hex || '#000000',
    cmyk: standardizeCMYK(color.cmyk),
    product: productName || 'Uncategorized',
    notes: color.notes || '',
    gradient: color.gradient || '',
    isLibrary: color.isLibrary === 'true' || color.isLibrary === true
  };
});

// Group by product for summary
const productGroups = {};
cleanedColors.forEach(color => {
  if (!productGroups[color.product]) {
    productGroups[color.product] = 0;
  }
  productGroups[color.product]++;
});

// Create cleaned export
const cleanedExport = {
  version: "1.0",
  exportDate: new Date().toISOString(),
  metadata: {
    totalColors: cleanedColors.length,
    products: Object.keys(productGroups).length,
    source: "ChromaSync Legacy Database"
  },
  colors: cleanedColors
};

// Write cleaned file
const outputPath = exportPath.replace('.json', '-cleaned.json');
fs.writeFileSync(outputPath, JSON.stringify(cleanedExport, null, 2));

console.log('\nExport cleaned successfully!');
console.log(`Total colors: ${cleanedColors.length}`);
console.log(`\nProducts (${Object.keys(productGroups).length}):`);
Object.entries(productGroups)
  .sort((a, b) => b[1] - a[1])
  .forEach(([product, count]) => {
    console.log(`  - ${product}: ${count} colors`);
  });

console.log(`\nCleaned file saved to: ${outputPath}`);
console.log('\nProduct name mappings:');
if (productNameMap.size > 0) {
  productNameMap.forEach((name, uuid) => {
    console.log(`  ${uuid} → ${name}`);
  });
}
