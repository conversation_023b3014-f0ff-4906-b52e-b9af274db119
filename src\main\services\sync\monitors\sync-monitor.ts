/**
 * @file sync-monitor.ts
 * @description Performance monitoring for sync operations
 * 
 * This module provides comprehensive monitoring of sync performance,
 * including metrics collection, performance analysis, and alerting.
 */

import { 
  SyncMetrics, 
  NetworkMetrics, 
  SyncResult, 
  SyncError,
  NetworkQuality,
  SyncEventHandler
} from '../core/sync-types';

// ============================================================================
// PERFORMANCE MONITORING
// ============================================================================

/**
 * Advanced sync performance monitor
 */
export class SyncMonitor {
  private metrics: SyncMetrics;
  private networkMetrics: NetworkMetrics;
  private performanceHistory: Array<{
    timestamp: number;
    operation: string;
    duration: number;
    success: boolean;
    itemsProcessed: number;
  }> = [];
  
  private eventHandlers = new Map<string, SyncEventHandler[]>();
  private readonly MAX_HISTORY_SIZE = 1000;
  private readonly SLOW_OPERATION_THRESHOLD = 5000; // 5 seconds
  private readonly PERFORMANCE_CHECK_INTERVAL = 60000; // 1 minute

  private performanceCheckInterval: ReturnType<typeof setInterval> | null = null;

  constructor() {
    this.metrics = {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      averageDuration: 0,
      slowOperations: 0,
      lastSyncDuration: 0,
      successRate: 0
    };

    this.networkMetrics = {
      latency: 0,
      bandwidth: 0,
      quality: 'good',
      isOnline: true,
      lastCheck: Date.now()
    };

    this.startPerformanceMonitoring();
  }

  /**
   * Record a sync operation result
   */
  recordSyncOperation(
    operation: string,
    result: SyncResult,
    itemsProcessed: number = result.itemsProcessed
  ): void {
    const { duration, success } = result;

    // Update basic metrics
    this.metrics.totalSyncs++;
    this.metrics.lastSyncDuration = duration;

    if (success) {
      this.metrics.successfulSyncs++;
    } else {
      this.metrics.failedSyncs++;
    }

    // Update average duration
    this.metrics.averageDuration = 
      ((this.metrics.averageDuration * (this.metrics.totalSyncs - 1)) + duration) / this.metrics.totalSyncs;

    // Update success rate
    this.metrics.successRate = Math.round(
      (this.metrics.successfulSyncs / this.metrics.totalSyncs) * 100
    );

    // Track slow operations
    if (duration > this.SLOW_OPERATION_THRESHOLD) {
      this.metrics.slowOperations++;
      console.warn(`[SyncMonitor] Slow operation detected: ${operation} took ${duration}ms`);
      
      this.emitEvent('slow-operation', {
        operation,
        duration,
        threshold: this.SLOW_OPERATION_THRESHOLD
      });
    }

    // Add to performance history
    this.performanceHistory.push({
      timestamp: Date.now(),
      operation,
      duration,
      success,
      itemsProcessed
    });

    // Maintain history size
    if (this.performanceHistory.length > this.MAX_HISTORY_SIZE) {
      this.performanceHistory.shift();
    }

    // Log performance metrics periodically
    if (this.metrics.totalSyncs % 10 === 0) {
      this.logPerformanceMetrics();
    }

    // Emit metrics update event
    this.emitEvent('metrics-updated', this.getMetrics());
  }

  /**
   * Record network performance metrics
   */
  recordNetworkMetrics(latency: number, quality: NetworkQuality, isOnline: boolean): void {
    this.networkMetrics = {
      latency,
      bandwidth: this.estimateBandwidth(latency),
      quality,
      isOnline,
      lastCheck: Date.now()
    };

    this.emitEvent('network-metrics-updated', this.networkMetrics);
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): SyncMetrics {
    return { ...this.metrics };
  }

  /**
   * Get current network metrics
   */
  getNetworkMetrics(): NetworkMetrics {
    return { ...this.networkMetrics };
  }

  /**
   * Get performance analysis
   */
  getPerformanceAnalysis(): {
    averageItemsPerSecond: number;
    peakPerformanceHour: number;
    slowestOperations: Array<{ operation: string; duration: number; timestamp: number }>;
    performanceTrend: 'improving' | 'stable' | 'degrading';
    recommendations: string[];
  } {
    if (this.performanceHistory.length === 0) {
      return {
        averageItemsPerSecond: 0,
        peakPerformanceHour: 0,
        slowestOperations: [],
        performanceTrend: 'stable',
        recommendations: []
      };
    }

    // Calculate average items per second
    const totalItems = this.performanceHistory.reduce((sum, entry) => sum + entry.itemsProcessed, 0);
    const totalDuration = this.performanceHistory.reduce((sum, entry) => sum + entry.duration, 0);
    const averageItemsPerSecond = totalDuration > 0 ? (totalItems / (totalDuration / 1000)) : 0;

    // Find peak performance hour
    const hourlyPerformance = this.groupPerformanceByHour();
    const peakPerformanceHour = this.findPeakPerformanceHour(hourlyPerformance);

    // Get slowest operations
    const slowestOperations = this.performanceHistory
      .filter(entry => entry.duration > this.SLOW_OPERATION_THRESHOLD)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 5)
      .map(entry => ({
        operation: entry.operation,
        duration: entry.duration,
        timestamp: entry.timestamp
      }));

    // Analyze performance trend
    const performanceTrend = this.analyzePerformanceTrend();

    // Generate recommendations
    const recommendations = this.generateRecommendations();

    return {
      averageItemsPerSecond,
      peakPerformanceHour,
      slowestOperations,
      performanceTrend,
      recommendations
    };
  }

  /**
   * Get sync health status
   */
  getHealthStatus(): {
    isHealthy: boolean;
    issues: string[];
    score: number; // 0-100
  } {
    const issues: string[] = [];
    let score = 100;

    // Check success rate
    if (this.metrics.successRate < 90) {
      issues.push(`Low success rate: ${this.metrics.successRate}%`);
      score -= 20;
    }

    // Check for too many slow operations
    const slowOperationRate = this.metrics.totalSyncs > 0 
      ? (this.metrics.slowOperations / this.metrics.totalSyncs) * 100 
      : 0;
    
    if (slowOperationRate > 10) {
      issues.push(`High slow operation rate: ${slowOperationRate.toFixed(1)}%`);
      score -= 15;
    }

    // Check network quality
    if (this.networkMetrics.quality === 'poor') {
      issues.push('Poor network quality detected');
      score -= 25;
    } else if (this.networkMetrics.quality === 'fair') {
      issues.push('Fair network quality detected');
      score -= 10;
    }

    // Check if offline
    if (!this.networkMetrics.isOnline) {
      issues.push('Network connectivity lost');
      score -= 30;
    }

    // Check recent failures
    const recentFailures = this.getRecentFailureRate();
    if (recentFailures > 20) {
      issues.push(`High recent failure rate: ${recentFailures.toFixed(1)}%`);
      score -= 20;
    }

    return {
      isHealthy: issues.length === 0,
      issues,
      score: Math.max(0, score)
    };
  }

  /**
   * Subscribe to monitoring events
   */
  on(event: string, handler: SyncEventHandler): () => void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    
    this.eventHandlers.get(event)!.push(handler);

    // Return unsubscribe function
    return () => {
      const handlers = this.eventHandlers.get(event);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    };
  }

  /**
   * Reset all metrics
   */
  resetMetrics(): void {
    this.metrics = {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      averageDuration: 0,
      slowOperations: 0,
      lastSyncDuration: 0,
      successRate: 0
    };

    this.performanceHistory = [];
    console.log('[SyncMonitor] Metrics reset');
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.performanceCheckInterval) {
      clearInterval(this.performanceCheckInterval);
      this.performanceCheckInterval = null;
    }
    
    this.eventHandlers.clear();
    console.log('[SyncMonitor] Monitor destroyed');
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Start performance monitoring
   */
  private startPerformanceMonitoring(): void {
    this.performanceCheckInterval = setInterval(() => {
      this.performPerformanceCheck();
    }, this.PERFORMANCE_CHECK_INTERVAL);
  }

  /**
   * Perform periodic performance check
   */
  private performPerformanceCheck(): void {
    const healthStatus = this.getHealthStatus();
    
    if (!healthStatus.isHealthy) {
      console.warn('[SyncMonitor] Performance issues detected:', healthStatus.issues);
      this.emitEvent('performance-warning', healthStatus);
    }

    // Emit periodic health check
    this.emitEvent('health-check', healthStatus);
  }

  /**
   * Estimate bandwidth based on latency
   */
  private estimateBandwidth(latency: number): number {
    // Simple bandwidth estimation based on latency
    // This is a rough approximation
    if (latency < 50) return 100; // Excellent
    if (latency < 100) return 50; // Good
    if (latency < 200) return 25; // Fair
    return 10; // Poor
  }

  /**
   * Group performance data by hour
   */
  private groupPerformanceByHour(): Map<number, number[]> {
    const hourlyData = new Map<number, number[]>();
    
    for (const entry of this.performanceHistory) {
      const hour = new Date(entry.timestamp).getHours();
      if (!hourlyData.has(hour)) {
        hourlyData.set(hour, []);
      }
      hourlyData.get(hour)!.push(entry.duration);
    }
    
    return hourlyData;
  }

  /**
   * Find peak performance hour
   */
  private findPeakPerformanceHour(hourlyData: Map<number, number[]>): number {
    let bestHour = 0;
    let bestAverage = Infinity;
    
    for (const [hour, durations] of hourlyData.entries()) {
      const average = durations.reduce((sum, d) => sum + d, 0) / durations.length;
      if (average < bestAverage) {
        bestAverage = average;
        bestHour = hour;
      }
    }
    
    return bestHour;
  }

  /**
   * Analyze performance trend
   */
  private analyzePerformanceTrend(): 'improving' | 'stable' | 'degrading' {
    if (this.performanceHistory.length < 10) {
      return 'stable';
    }

    const recent = this.performanceHistory.slice(-10);
    const older = this.performanceHistory.slice(-20, -10);
    
    if (older.length === 0) {
      return 'stable';
    }

    const recentAvg = recent.reduce((sum, entry) => sum + entry.duration, 0) / recent.length;
    const olderAvg = older.reduce((sum, entry) => sum + entry.duration, 0) / older.length;
    
    const improvement = (olderAvg - recentAvg) / olderAvg;
    
    if (improvement > 0.1) return 'improving';
    if (improvement < -0.1) return 'degrading';
    return 'stable';
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.metrics.slowOperations > 0) {
      recommendations.push('Consider reducing batch sizes for better performance');
    }
    
    if (this.metrics.successRate < 95) {
      recommendations.push('Review error handling and retry logic');
    }
    
    if (this.networkMetrics.quality === 'poor') {
      recommendations.push('Check network connectivity and consider offline mode');
    }
    
    const recentFailures = this.getRecentFailureRate();
    if (recentFailures > 10) {
      recommendations.push('Investigate recent sync failures');
    }
    
    return recommendations;
  }

  /**
   * Get recent failure rate (last 10 operations)
   */
  private getRecentFailureRate(): number {
    const recent = this.performanceHistory.slice(-10);
    if (recent.length === 0) return 0;
    
    const failures = recent.filter(entry => !entry.success).length;
    return (failures / recent.length) * 100;
  }

  /**
   * Log performance metrics
   */
  private logPerformanceMetrics(): void {
    console.log('[SyncMonitor] Performance metrics:', {
      totalSyncs: this.metrics.totalSyncs,
      successRate: `${this.metrics.successRate}%`,
      averageDuration: `${Math.round(this.metrics.averageDuration)}ms`,
      slowOperations: this.metrics.slowOperations,
      networkQuality: this.networkMetrics.quality
    });
  }

  /**
   * Emit event to all registered handlers
   */
  private emitEvent(event: string, data: any): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`[SyncMonitor] Error in event handler for ${event}:`, error);
        }
      });
    }
  }
}
