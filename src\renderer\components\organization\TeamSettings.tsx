/**
 * @file TeamSettings.tsx
 * @description Team management component for inviting members and managing roles
 */

import React, { useState, useEffect } from 'react';
import { useOrganizationStoreWithAliases } from '../../store/organization.store';
import { Users, UserPlus, Shield, ShieldCheck, ShieldAlert, Trash2, Mail, Loader2 } from 'lucide-react';
import { PendingInvitations } from './PendingInvitations';

export const TeamSettings: React.FC = () => {
  const { currentOrg, members, inviteMember, removeMember, updateMemberRole, loadMembers, loadCurrentOrganization } = useOrganizationStoreWithAliases();
  
  // Debug logging
  console.log('[TeamSettings] Current organization:', currentOrg);
  console.log('[TeamSettings] Members:', members);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<'member' | 'admin'>('member');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [isLoadingOrg, setIsLoadingOrg] = useState(false);

  // Load current organization if not loaded
  useEffect(() => {
    if (!currentOrg && !isLoadingOrg) {
      console.log('[TeamSettings] No current org, attempting to load...');
      setIsLoadingOrg(true);
      loadCurrentOrganization().finally(() => setIsLoadingOrg(false));
    }
  }, [currentOrg, loadCurrentOrganization, isLoadingOrg]);

  // Load members when component mounts or organization changes
  useEffect(() => {
    if (currentOrg) {
      console.log('[TeamSettings] Loading members for org:', currentOrg.external_id);
      loadMembers();
    }
  }, [currentOrg, loadMembers]);

  // If no members loaded but we have an organization, try to refresh
  useEffect(() => {
    if (currentOrg && members.length === 0 && !isLoadingOrg) {
      console.log('[TeamSettings] No members found, attempting to reload organization and members...');
      setTimeout(() => {
        loadCurrentOrganization().then(() => {
          loadMembers();
        });
      }, 1000);
    }
  }, [currentOrg, members.length, isLoadingOrg, loadCurrentOrganization, loadMembers]);

  // Get current user's role with fallback to organization data
  const currentUserMember = members.find(m => m.isCurrentUser);
  let currentUserRole = currentUserMember?.role || 'member';
  
  // FALLBACK: If we have organization data showing the user as owner but no member record,
  // use the organization's userRole as fallback
  if (!currentUserMember && currentOrg?.userRole) {
    console.log('[TeamSettings] No member record found, using organization userRole as fallback:', currentOrg.userRole);
    currentUserRole = currentOrg.userRole;
  }
  
  const canManageTeam = currentUserRole === 'owner' || currentUserRole === 'admin';
  
  console.log('[TeamSettings] Permission check:', {
    currentUserMember: !!currentUserMember,
    currentUserRole,
    orgUserRole: currentOrg?.userRole,
    canManageTeam,
    membersCount: members.length
  });

  const handleInviteMember = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inviteEmail.trim() || !canManageTeam) {return;}

    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      const result = await inviteMember(inviteEmail, inviteRole);
      if (result.success) {
        setSuccessMessage(`Invitation sent to ${inviteEmail}`);
        setInviteEmail('');
        setInviteRole('member');
      } else {
        setError(result.error || 'Failed to send invitation');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send invitation');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    if (!canManageTeam) {return;}

    const member = members.find(m => m.userId === memberId);
    if (!member) {return;}

    const confirmed = confirm(`Are you sure you want to remove ${member.user?.email || 'this member'} from the team?`);
    if (!confirmed) {return;}

    setError(null);
    setSuccessMessage(null);

    try {
      const result = await removeMember(memberId);
      if (result.success) {
        setSuccessMessage('Member removed successfully');
      } else {
        setError(result.error || 'Failed to remove member');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove member');
    }
  };

  const handleUpdateRole = async (memberId: string, newRole: 'admin' | 'member') => {
    if (!canManageTeam || currentUserRole !== 'owner') {return;}

    setError(null);
    setSuccessMessage(null);

    try {
      const result = await updateMemberRole(memberId, newRole);
      if (result.success) {
        setSuccessMessage('Role updated successfully');
      } else {
        setError(result.error || 'Failed to update role');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update role');
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return <ShieldAlert className="w-4 h-4" />;
      case 'admin':
        return <ShieldCheck className="w-4 h-4" />;
      default:
        return <Shield className="w-4 h-4" />;
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'owner':
        return 'bg-purple-100 text-purple-700';
      case 'admin':
        return 'bg-blue-100 text-blue-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const getSeatsForPlan = (plan?: string) => {
    switch (plan) {
      case 'free':
        return 5;
      case 'team':
        return 20;
      case 'enterprise':
        return 'Unlimited';
      default:
        return 5;
    }
  };

  if (isLoadingOrg) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="w-5 h-5 animate-spin text-gray-500 mr-2" />
        <span className="text-gray-500">Loading organization...</span>
      </div>
    );
  }

  if (!currentOrg) {
    return (
      <div className="text-center text-gray-500 py-8">
        <p>No organization selected</p>
        <p className="text-sm mt-2">Please select an organization from the header dropdown.</p>
      </div>
    );
  }

  const maxSeats = getSeatsForPlan(currentOrg.plan);
  const seatsUsed = members.length;
  const canInviteMore = maxSeats === 'Unlimited' || seatsUsed < maxSeats;

  return (
    <div className="space-y-6">
      {/* Team Overview */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Users className="w-5 h-5 text-gray-600" />
            <div>
              <h3 className="font-medium text-gray-900">Team Members</h3>
              <p className="text-sm text-gray-500">
                {seatsUsed} of {maxSeats} seats used
              </p>
            </div>
          </div>
          {currentOrg.plan === 'free' && seatsUsed >= 3 && (
            <button className="text-sm text-blue-600 hover:text-blue-700">
              Upgrade for more seats
            </button>
          )}
        </div>
      </div>

      {/* Messages */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm">
          {error}
        </div>
      )}
      {successMessage && (
        <div className="p-3 bg-green-50 border border-green-200 text-green-700 rounded-lg text-sm">
          {successMessage}
        </div>
      )}

      {/* Invite Member Form */}
      {canManageTeam && canInviteMore && (
        <form onSubmit={handleInviteMember} className="bg-white border border-gray-200 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-3 flex items-center">
            <UserPlus className="w-4 h-4 mr-2" />
            Invite Team Member
          </h4>
          
          <div className="space-y-3">
            <div className="flex space-x-3">
              <input
                type="email"
                value={inviteEmail}
                onChange={(e) => setInviteEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isLoading}
                required
              />
              
              <select
                value={inviteRole}
                onChange={(e) => setInviteRole(e.target.value as 'member' | 'admin')}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={isLoading}
              >
                <option value="member">Member</option>
                <option value="admin">Admin</option>
              </select>
              
              <button
                type="submit"
                disabled={isLoading || !inviteEmail.trim()}
                className={`px-4 py-2 rounded-lg font-medium transition-colors flex items-center ${
                  isLoading || !inviteEmail.trim()
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-blue-500 text-white hover:bg-blue-600'
                }`}
              >
                {isLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <>
                    <Mail className="w-4 h-4 mr-2" />
                    Invite
                  </>
                )}
              </button>
            </div>
            
            <p className="text-xs text-gray-500">
              They'll receive an email invitation to join your workspace
            </p>
          </div>
        </form>
      )}

      {/* Members List */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="border-b border-gray-200 px-4 py-3">
          <h4 className="font-medium text-gray-900">Current Members</h4>
        </div>
        
        <div className="divide-y divide-gray-200">
          {members.map((member) => (
            <div key={member.userId} className="px-4 py-3 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-gray-600">
                    {member.user?.email?.[0]?.toUpperCase() || '?'}
                  </span>
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-900">
                      {member.user?.email || 'Unknown'}
                    </span>
                    {member.isCurrentUser && (
                      <span className="text-xs text-gray-500">(You)</span>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 mt-0.5">
                    <span className={`inline-flex items-center space-x-1 text-xs px-2 py-0.5 rounded-full ${getRoleBadgeColor(member.role)}`}>
                      {getRoleIcon(member.role)}
                      <span className="capitalize">{member.role}</span>
                    </span>
                    <span className="text-xs text-gray-500">
                      Joined {new Date(member.joinedAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {/* Role selector for owners to change admin/member roles */}
                {canManageTeam && currentUserRole === 'owner' && 
                 member.role !== 'owner' && !member.isCurrentUser && (
                  <select
                    value={member.role}
                    onChange={(e) => handleUpdateRole(member.userId, e.target.value as 'admin' | 'member')}
                    className="text-sm px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="member">Member</option>
                    <option value="admin">Admin</option>
                  </select>
                )}
                
                {/* Remove button */}
                {canManageTeam && !member.isCurrentUser && member.role !== 'owner' && (
                  <button
                    onClick={() => handleRemoveMember(member.userId)}
                    className="text-red-500 hover:text-red-600 p-1"
                    title="Remove member"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Pending Invitations */}
      {canManageTeam && (
        <PendingInvitations />
      )}

      {/* Join Organization Button */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900">Have an invitation code?</h4>
            <p className="text-sm text-gray-500 mt-1">
              Enter your invitation code to join an organization
            </p>
          </div>
          <button
            onClick={() => {
              import('../organization/AcceptInvitation').then(({ showAcceptInvitationModal }) => {
                showAcceptInvitationModal();
              });
            }}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 
                     transition-colors flex items-center gap-2"
          >
            <UserPlus className="w-4 h-4" />
            Join Organization
          </button>
        </div>
      </div>

      {/* Role Descriptions */}
      <div className="bg-gray-50 rounded-lg p-4 text-sm">
        <h4 className="font-medium text-gray-900 mb-2">Role Permissions</h4>
        <div className="space-y-2 text-gray-600">
          <div className="flex items-start space-x-2">
            <ShieldAlert className="w-4 h-4 text-purple-600 mt-0.5" />
            <div>
              <span className="font-medium text-gray-700">Owner:</span> Full access, can manage billing and delete workspace
            </div>
          </div>
          <div className="flex items-start space-x-2">
            <ShieldCheck className="w-4 h-4 text-blue-600 mt-0.5" />
            <div>
              <span className="font-medium text-gray-700">Admin:</span> Can manage team members and all colors
            </div>
          </div>
          <div className="flex items-start space-x-2">
            <Shield className="w-4 h-4 text-gray-600 mt-0.5" />
            <div>
              <span className="font-medium text-gray-700">Member:</span> Can view and edit colors
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
