const Database = require('better-sqlite3');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');

// Paths
const dbPath = path.join(__dirname, '../../chromasync.db');
const importFilePath = path.join(__dirname, '../../chromasync-import-fixed.json');

console.log('Opening database:', dbPath);
const db = new Database(dbPath);

// Enable foreign keys
db.pragma('foreign_keys = ON');

// Read import data
console.log('Reading import data from:', importFilePath);
const importData = JSON.parse(fs.readFileSync(importFilePath, 'utf-8'));

console.log(`Found ${importData.length} colors to import`);

// Extract unique products
const products = new Map();
importData.forEach(color => {
  if (color.product && !products.has(color.product)) {
    products.set(color.product, {
      id: uuidv4(),
      name: color.product,
      sku: color.product.replace(/\s+/g, '-').toUpperCase()
    });
  }
});

console.log(`\nFound ${products.size} unique products`);

// Start transaction
const transaction = db.transaction(() => {
  // Create products
  const insertProduct = db.prepare(`
    INSERT OR IGNORE INTO products (external_id, name, sku, is_active, created_at, updated_at)
    VALUES (?, ?, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
  `);
  
  const getProduct = db.prepare('SELECT id, external_id FROM products WHERE name = ?');
  
  const productIdMap = new Map();
  
  for (const [productName, productData] of products) {
    // Check if product exists
    let product = getProduct.get(productName);
    
    if (!product) {
      // Create new product
      insertProduct.run(productData.id, productData.name, productData.sku);
      product = getProduct.get(productName);
      console.log(`Created product: ${productName}`);
    }
    
    productIdMap.set(productName, product.id);
  }
  
  // Import colors
  const insertColor = db.prepare(`
    INSERT INTO colors (
      external_id, source_id, code, display_name, hex, 
      is_gradient, is_metallic, is_effect, properties,
      created_at, updated_at
    )
    VALUES (?, 1, ?, ?, ?, 0, 0, 0, ?, ?, ?)
  `);
  
  const insertCMYK = db.prepare(`
    INSERT INTO color_cmyk (color_id, c, m, y, k)
    VALUES (?, ?, ?, ?, ?)
  `);
  
  const insertRGB = db.prepare(`
    INSERT INTO color_rgb (color_id, r, g, b)
    VALUES (?, ?, ?, ?)
  `);
  
  const insertProductColor = db.prepare(`
    INSERT OR IGNORE INTO product_colors (
      product_id, color_id, display_order, usage_type, added_at
    )
    VALUES (?, ?, ?, 'standard', CURRENT_TIMESTAMP)
  `);
  
  let imported = 0;
  let skipped = 0;
  
  importData.forEach((color, index) => {
    try {
      // Parse CMYK
      const cmykMatch = color.cmyk.match(/C:(\d+)\s*M:(\d+)\s*Y:(\d+)\s*K:(\d+)/);
      if (!cmykMatch) {
        console.error(`Invalid CMYK format for ${color.code}: ${color.cmyk}`);
        skipped++;
        return;
      }
      
      const [, c, m, y, k] = cmykMatch.map(n => parseInt(n, 10));
      
      // Parse hex to RGB
      const hex = color.hex.replace('#', '');
      const r = parseInt(hex.substr(0, 2), 16);
      const g = parseInt(hex.substr(2, 2), 16);
      const b = parseInt(hex.substr(4, 2), 16);
      
      // Insert color
      const colorInfo = insertColor.run(
        color.id,
        color.code,
        color.name,
        color.hex,
        JSON.stringify({
          product: color.product,
          notes: color.notes,
          isLibrary: color.isLibrary
        }),
        color.createdAt,
        color.updatedAt
      );
      
      const colorId = colorInfo.lastInsertRowid;
      
      // Insert CMYK
      insertCMYK.run(colorId, c, m, y, k);
      
      // Insert RGB
      insertRGB.run(colorId, r, g, b);
      
      // Associate with product if specified
      if (color.product && productIdMap.has(color.product)) {
        insertProductColor.run(productIdMap.get(color.product), colorId, index);
      }
      
      imported++;
      
      if (imported % 50 === 0) {
        console.log(`Imported ${imported} colors...`);
      }
    } catch (error) {
      console.error(`Error importing color ${color.code}:`, error.message);
      skipped++;
    }
  });
  
  console.log(`\nImport complete: ${imported} imported, ${skipped} skipped`);
});

try {
  transaction();
  
  // Verify results
  const stats = db.prepare(`
    SELECT 
      (SELECT COUNT(*) FROM colors WHERE deleted_at IS NULL) as colors,
      (SELECT COUNT(*) FROM products WHERE is_active = 1) as products,
      (SELECT COUNT(*) FROM product_colors) as associations,
      (SELECT COUNT(*) FROM color_cmyk) as cmyk_records
  `).get();
  
  console.log('\nDatabase statistics after import:');
  console.log(`- Colors: ${stats.colors}`);
  console.log(`- Products: ${stats.products}`);
  console.log(`- Product-color associations: ${stats.associations}`);
  console.log(`- CMYK records: ${stats.cmyk_records}`);
  
} catch (error) {
  console.error('Import failed:', error);
} finally {
  db.close();
}

console.log('\n✅ Direct import complete!');
