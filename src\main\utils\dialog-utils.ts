/**
 * @file dialog-utils.ts
 * @description Extended dialog utilities for Electron
 */

import { dialog, BrowserWindow } from 'electron';

/**
 * Options for the input box dialog
 */
export interface InputBoxOptions {
  title: string;
  message: string;
  placeholder?: string;
  value?: string;
  inputValidator?: (input: string) => string | null;
  cancelId?: number;
}

/**
 * Result of the input box dialog
 */
export interface InputBoxResult {
  canceled: boolean;
  response: string | null;
}

/**
 * Shows an input box dialog
 * @param options Options for the input box
 * @returns Promise resolving to the input box result
 */
export async function showInputBox(options: InputBoxOptions): Promise<InputBoxResult> {
  // Get the focused window or the first window
  const focusedWindow = BrowserWindow.getFocusedWindow() || BrowserWindow.getAllWindows()[0];
  
  if (!focusedWindow) {
    return { canceled: true, response: null };
  }
  
  // Create a temporary HTML file with an input form
  const _inputHtml = `
    <html>
      <head>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
          }
          .container {
            display: flex;
            flex-direction: column;
            gap: 15px;
          }
          h3 {
            margin: 0 0 10px 0;
          }
          input {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            width: 100%;
          }
          .buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 10px;
          }
          button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
          }
          .cancel {
            background-color: #e0e0e0;
          }
          .ok {
            background-color: #0078d7;
            color: white;
          }
          .error {
            color: #d32f2f;
            font-size: 12px;
            margin-top: 5px;
            display: none;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <h3>${options.title}</h3>
          <p>${options.message}</p>
          <input 
            type="text" 
            id="inputField" 
            placeholder="${options.placeholder || ''}" 
            value="${options.value || ''}"
          />
          <div id="errorMessage" class="error"></div>
          <div class="buttons">
            <button class="cancel" id="cancelButton">Cancel</button>
            <button class="ok" id="okButton">OK</button>
          </div>
        </div>
        <script>
          const { ipcRenderer } = require('electron');
          
          // Set up button handlers
          document.getElementById('cancelButton').addEventListener('click', () => {
            ipcRenderer.send('input-dialog-response', { canceled: true, response: null });
          });
          
          document.getElementById('okButton').addEventListener('click', () => {
            const input = document.getElementById('inputField').value;
            ipcRenderer.send('input-dialog-response', { canceled: false, response: input });
          });
          
          // Handle Enter key
          document.getElementById('inputField').addEventListener('keyup', (event) => {
            if (event.key === 'Enter') {
              const input = document.getElementById('inputField').value;
              ipcRenderer.send('input-dialog-response', { canceled: false, response: input });
            }
          });
          
          // Focus the input field
          document.getElementById('inputField').focus();
        </script>
      </body>
    </html>
  `;
  
  // Log for future implementation
  // console.debug('Input HTML template prepared:', inputHtml.length);
  
  // Show the dialog
  const { response } = await dialog.showMessageBox(focusedWindow, {
    type: 'question',
    title: options.title,
    message: options.message,
    buttons: ['OK', 'Cancel'],
    defaultId: 0,
    cancelId: options.cancelId || 1
  });
  
  // If canceled
  if (response === options.cancelId || response === 1) {
    return { canceled: true, response: null };
  }
  
  // Show prompt dialog
  const promptResponse = await dialog.showMessageBox(focusedWindow, {
    type: 'question',
    title: options.title,
    message: options.message,
    detail: options.placeholder || '',
    defaultId: 0,
    cancelId: options.cancelId || 1,
    buttons: ['OK', 'Cancel']
  });
  
  // If canceled
  if (promptResponse.response === options.cancelId || promptResponse.response === 1) {
    return { canceled: true, response: null };
  }
  
  // For simplicity, we'll use a simple prompt
  const userInput = prompt(options.message, options.value || '');
  
  if (userInput === null) {
    return { canceled: true, response: null };
  }
  
  // Validate input if validator is provided
  if (options.inputValidator) {
    const validationError = options.inputValidator(userInput);
    if (validationError) {
      // Show error and ask again
      alert(validationError);
      return showInputBox(options);
    }
  }
  
  return { canceled: false, response: userInput };
}
