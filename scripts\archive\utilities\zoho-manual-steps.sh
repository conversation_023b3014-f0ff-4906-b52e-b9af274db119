echo "🎨 ChromaSync Zoh<PERSON> OAuth - Quick Setup"
echo ""
echo "1️⃣  Click this link to authorize (Cmd+Click to open):"
echo ""
echo "https://accounts.zoho.eu/oauth/v2/auth?client_id=1000.ZN7GG1SU13DXYH2D2JXF934HWFVJDG&response_type=code&redirect_uri=http://localhost:8080/callback&scope=ZohoMail.messages.CREATE,ZohoMail.messages.READ,ZohoMail.accounts.READ&access_type=offline&prompt=consent"
echo ""
echo "2️⃣  After authorizing, copy the FULL URL from your browser"
echo "   (it will show an error page - that's normal!)"
echo ""
echo "3️⃣  The URL will look like:"
echo "   http://localhost:8080/callback?code=1000.xxxxx..."
echo ""
echo "4️⃣  Copy just the CODE part (after 'code=') and run:"
echo ""
echo 'curl -X POST https://accounts.zoho.eu/oauth/v2/token \'
echo '  -d "code=PASTE_YOUR_CODE_HERE" \'
echo '  -d "client_id=1000.ZN7GG1SU13DXYH2D2JXF934HWFVJDG" \'
echo '  -d "client_secret=4ca85d5c0a70404f8778128ae3b8e1cc08bc585356" \'
echo '  -d "redirect_uri=http://localhost:8080/callback" \'
echo '  -d "grant_type=authorization_code"'
echo ""
echo "5️⃣  Copy the refresh_token from the response"
echo ""
echo "6️⃣  Add to your .env file:"
echo ""
echo "ZOHO_CLIENT_ID=1000.ZN7GG1SU13DXYH2D2JXF934HWFVJDG"
echo "ZOHO_CLIENT_SECRET=4ca85d5c0a70404f8778128ae3b8e1cc08bc585356"
echo "ZOHO_ACCOUNT_ID=6851937000000002002"
echo "ZOHO_REFRESH_TOKEN=<your-refresh-token>"
echo "ZOHO_REGION=EU"
echo "ZOHO_SUPPORT_ALIAS=<EMAIL>"