{"appId": "com.chromasync.app", "productName": "ChromaSync", "copyright": "Copyright © 2025 ChromaSync", "directories": {"output": "dist-fresh", "buildResources": "build"}, "files": ["out/**/*", "node_modules/**/*", "package.json"], "mac": {"category": "public.app-category.graphics-design", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist", "target": ["dmg", "zip"], "icon": "assets/Colour Tracker.svg"}, "dmg": {"sign": false, "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "win": {"target": ["nsis"], "icon": "build/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "installerIcon": "build/icon.ico"}, "linux": {"target": ["AppImage", "deb", "rpm"], "category": "Graphics", "icon": "build/icons"}, "asar": true, "asarUnpack": ["**/node_modules/better-sqlite3/**/*"], "protocols": [{"name": "chromasync", "schemes": ["chromasync"]}], "afterSign": "scripts/notarize.js"}