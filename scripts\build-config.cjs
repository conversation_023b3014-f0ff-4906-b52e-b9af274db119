/**
 * @file build-config.js
 * @description Inject environment variables at build time
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Create a config file with environment variables at build time
const config = {
  ZOHO_CLIENT_ID: process.env.ZOHO_CLIENT_ID,
  ZOHO_CLIENT_SECRET: process.env.ZOHO_CLIENT_SECRET,
  ZOHO_ACCOUNT_ID: process.env.ZOHO_ACCOUNT_ID,
  ZOHO_REFRESH_TOKEN: process.env.ZOHO_REFRESH_TOKEN,
  ZOHO_REGION: process.env.ZOHO_REGION,
  ZOHO_SUPPORT_ALIAS: process.env.ZOHO_SUPPORT_ALIAS,
  SUPABASE_URL: process.env.SUPABASE_URL,
  SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY
};

// Ensure output directory exists
const outDir = path.join(__dirname, '..', 'out');
if (!fs.existsSync(outDir)) {
  fs.mkdirSync(outDir, { recursive: true });
}

// Write config to file
const configPath = path.join(outDir, 'app-config.json');
fs.writeFileSync(configPath, JSON.stringify(config, null, 2));

console.log('✅ Build config created at:', configPath);
console.log('   Zoho Region:', config.ZOHO_REGION);
console.log('   Zoho Account:', config.ZOHO_ACCOUNT_ID ? '✓' : '✗');
console.log('   Supabase URL:', config.SUPABASE_URL ? '✓' : '✗');
