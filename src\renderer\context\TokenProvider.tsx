import React from 'react';
import { ThemeProvider } from './ThemeContext';
import { FeatureFlagProvider } from './FeatureFlagContext';

interface TokenProviderProps {
  children: React.ReactNode;
}

/**
 * Provider component that wraps the application with theme and feature flag contexts
 * This component should be used near the root of the application
 */
export const TokenProvider: React.FC<TokenProviderProps> = ({ children }) => {
  return (
    <ThemeProvider>
      <FeatureFlagProvider>
        {children}
      </FeatureFlagProvider>
    </ThemeProvider>
  );
}; 