/**
 * @file zoho-email.service.ts
 * @description Zoho Mail API service for sending emails
 * Updated with better invitation instructions
 */

import axios from 'axios';
import { app } from 'electron';
import path from 'path';
import fs from 'fs/promises';
import { getConfig } from '../../utils/config-loader';
import { emailRetryQueue } from './email-retry-queue';

interface ZohoTokens {
  accessToken: string;
  refreshToken: string;
  expiresAt: number;
}

interface EmailOptions {
  to: string | string[];
  subject: string;
  content: string;
  fromAddress?: string;
  isHtml?: boolean;
  cc?: string[];
  bcc?: string[];
  replyTo?: string;
}

export class ZohoEmailService {
  private clientId: string;
  private clientSecret: string;
  private accountId: string;
  private tokens: ZohoTokens;
  private tokenFile: string;
  private lastRefreshAttempt: number = 0;
  private readonly REFRESH_COOLDOWN = 60000; // 1 minute cooldown between refresh attempts
  
  // Enhanced exponential backoff for rate limiting
  private retryAttempts: number = 0;
  private maxRetryAttempts: number = 5;
  private baseRetryDelay: number = 1000; // Start with 1 second
  private maxRetryDelay: number = 300000; // Max 5 minutes
  
  // Circuit breaker pattern for email service reliability
  private circuitBreakerState: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';
  private failureCount: number = 0;
  private maxFailures: number = 3;
  private circuitBreakerTimeout: number = 60000; // 1 minute
  private lastFailureTime: number = 0;

  constructor() {
    this.clientId = getConfig('ZOHO_CLIENT_ID') || '';
    this.clientSecret = getConfig('ZOHO_CLIENT_SECRET') || '';
    this.accountId = getConfig('ZOHO_ACCOUNT_ID') || '';
    this.tokenFile = path.join(app.getPath('userData'), 'zoho-tokens.json');
    
    this.tokens = {
      accessToken: '',
      refreshToken: getConfig('ZOHO_REFRESH_TOKEN') || '',
      expiresAt: 0
    };
  }

  /**
   * Initialize the service and load tokens
   */
  async initialize(): Promise<void> {
    try {
      // Load tokens from file if exists
      const tokenData = await fs.readFile(this.tokenFile, 'utf-8');
      const savedTokens = JSON.parse(tokenData);
      this.tokens = { ...this.tokens, ...savedTokens };
      
      // Handle invalid expiration time
      if (!this.tokens.expiresAt || this.tokens.expiresAt === null) {
        this.tokens.expiresAt = 0; // Force refresh
      }
      
      if (this.tokens.expiresAt > 0) {
        console.log('[ZohoEmail] Loaded saved tokens, expires at:', new Date(this.tokens.expiresAt).toISOString());
      } else {
        console.log('[ZohoEmail] Loaded saved tokens, but expiration time is invalid - will refresh');
      }
    } catch (error) {
      // File doesn't exist, use env refresh token
      console.log('[ZohoEmail] No saved tokens, using environment refresh token');
    }

    // Validate required configuration
    if (!this.clientId || !this.clientSecret || !this.accountId || !this.tokens.refreshToken) {
      console.error('[ZohoEmail] Missing Zoho configuration. Please set:');
      console.error('- ZOHO_CLIENT_ID');
      console.error('- ZOHO_CLIENT_SECRET');
      console.error('- ZOHO_ACCOUNT_ID');
      console.error('- ZOHO_REFRESH_TOKEN');
      throw new Error('Zoho Email Service not configured');
    }

    // Validate ZOHO_REGION configuration
    const zohoRegion = getConfig('ZOHO_REGION');
    if (zohoRegion && !this.isValidZohoRegion(zohoRegion)) {
      console.error(`[ZohoEmail] Invalid ZOHO_REGION: ${zohoRegion}`);
      console.error('Valid values are: EU (for zoho.eu) or omit for US (zoho.com)');
      console.warn('[ZohoEmail] Falling back to US region (zoho.com)');
    } else if (zohoRegion) {
      console.log(`[ZohoEmail] Using ${zohoRegion} region (${this.getZohoDomain(zohoRegion)})`);
    } else {
      console.log('[ZohoEmail] Using default US region (zoho.com)');
    }

    // Initialize the retry queue
    try {
      await emailRetryQueue.initialize();
      console.log('[ZohoEmail] Retry queue initialized successfully');
    } catch (error) {
      console.error('[ZohoEmail] Failed to initialize retry queue:', error);
      // Continue without retry queue if it fails
    }

    // Don't refresh token on initialization - only when needed for sending
    console.log('[ZohoEmail] Service initialized successfully (token refresh deferred until first email)');
  }

  /**
   * Validate Zoho region configuration
   */
  private isValidZohoRegion(region: string): boolean {
    const validRegions = ['EU', 'US'];
    return validRegions.includes(region.toUpperCase());
  }

  /**
   * Auto-detect Zoho region by testing connectivity to both regions
   */
  private async detectZohoRegion(): Promise<'EU' | 'US'> {
    console.log('[ZohoEmail] Auto-detecting Zoho region...');
    
    // Test both regions with a lightweight endpoint
    const testRegions = [
      { region: 'EU', domain: 'accounts.zoho.eu' },
      { region: 'US', domain: 'accounts.zoho.com' }
    ];
    
    for (const test of testRegions) {
      try {
        // Use a simple OAuth info endpoint that doesn't require authentication
        const response = await axios.get(`https://${test.domain}/oauth/userinfo`, {
          timeout: 3000, // Quick timeout for detection
          validateStatus: (status) => status === 401 || status === 200 // 401 is expected without auth
        });
        
        if (response.status === 401) {
          console.log(`[ZohoEmail] Region ${test.region} (${test.domain}) is accessible`);
          return test.region as 'EU' | 'US';
        }
      } catch (error: any) {
        // If we get a 401, that means the endpoint is accessible
        if (error.response?.status === 401) {
          console.log(`[ZohoEmail] Region ${test.region} (${test.domain}) is accessible`);
          return test.region as 'EU' | 'US';
        }
        
        console.log(`[ZohoEmail] Region ${test.region} test failed:`, error.code || error.message);
      }
    }
    
    // Fallback to US if detection fails
    console.warn('[ZohoEmail] Could not auto-detect region, falling back to US');
    return 'US';
  }

  /**
   * Detect if account has been migrated between regions
   */
  private async detectAccountMigration(currentRegion: string): Promise<boolean> {
    console.log(`[ZohoEmail] Checking for account migration from ${currentRegion}...`);
    
    try {
      // Try to refresh token with current region
      const domains = this.getZohoDomain(currentRegion);
      
      const response = await axios.post(`https://${domains.auth}/oauth/v2/token`,
        null,
        {
          params: {
            refresh_token: this.tokens.refreshToken,
            client_id: this.clientId,
            client_secret: this.clientSecret,
            grant_type: 'refresh_token'
          },
          timeout: 5000
        }
      );
      
      // If successful, no migration detected
      return false;
    } catch (error: any) {
      // Check for specific migration error patterns
      if (error.response?.status === 400 && 
          (error.response?.data?.error === 'invalid_client' ||
           error.response?.data?.error_description?.includes('client'))) {
        
        console.log('[ZohoEmail] Possible account migration detected, testing alternate region...');
        
        // Test the other region
        const alternateRegion = currentRegion === 'EU' ? 'US' : 'EU';
        const alternateDomains = this.getZohoDomain(alternateRegion);
        
        try {
          await axios.post(`https://${alternateDomains.auth}/oauth/v2/token`,
            null,
            {
              params: {
                refresh_token: this.tokens.refreshToken,
                client_id: this.clientId,
                client_secret: this.clientSecret,
                grant_type: 'refresh_token'
              },
              timeout: 5000
            }
          );
          
          console.log(`[ZohoEmail] Account migration confirmed: ${currentRegion} → ${alternateRegion}`);
          return true;
        } catch (alternateError) {
          console.log('[ZohoEmail] No migration detected, original error confirmed');
          return false;
        }
      }
      
      return false;
    }
  }

  /**
   * Get Zoho domain for a given region with auto-detection fallback
   */
  private getZohoDomain(region?: string): { auth: string; api: string } {
    const isEU = region?.toUpperCase() === 'EU';
    return {
      auth: isEU ? 'accounts.zoho.eu' : 'accounts.zoho.com',
      api: isEU ? 'mail.zoho.eu' : 'mail.zoho.com'
    };
  }

  /**
   * Get or detect the correct Zoho region with caching
   */
  private async getZohoRegion(): Promise<string> {
    const configuredRegion = getConfig('ZOHO_REGION');
    
    // If valid region is configured, use it
    if (configuredRegion && this.isValidZohoRegion(configuredRegion)) {
      return configuredRegion.toUpperCase();
    }
    
    // Check if we have a cached detected region
    const cachedRegion = getConfig('ZOHO_DETECTED_REGION');
    if (cachedRegion && this.isValidZohoRegion(cachedRegion)) {
      console.log(`[ZohoEmail] Using cached detected region: ${cachedRegion}`);
      return cachedRegion.toUpperCase();
    }
    
    // Auto-detect region
    try {
      const detectedRegion = await this.detectZohoRegion();
      
      // Cache the detected region for future use
      // Note: In a real implementation, this would require storing to config file
      console.log(`[ZohoEmail] Auto-detected region: ${detectedRegion} (consider setting ZOHO_REGION=${detectedRegion})`);
      
      return detectedRegion;
    } catch (error) {
      console.warn('[ZohoEmail] Region auto-detection failed, using US as fallback');
      return 'US';
    }
  }

  /**
   * Calculate exponential backoff delay with jitter
   */
  private calculateBackoffDelay(): number {
    const exponentialDelay = this.baseRetryDelay * Math.pow(2, this.retryAttempts);
    const jitteredDelay = exponentialDelay * (0.5 + Math.random() * 0.5); // Add 50% jitter
    return Math.min(jitteredDelay, this.maxRetryDelay);
  }

  /**
   * Reset retry attempts after successful operation
   */
  private resetRetryAttempts(): void {
    this.retryAttempts = 0;
  }

  /**
   * Increment retry attempts and check if max reached
   */
  private incrementRetryAttempts(): boolean {
    this.retryAttempts++;
    return this.retryAttempts <= this.maxRetryAttempts;
  }

  /**
   * Check circuit breaker state before making API calls
   */
  private checkCircuitBreaker(): boolean {
    const now = Date.now();
    
    switch (this.circuitBreakerState) {
      case 'OPEN':
        if (now - this.lastFailureTime >= this.circuitBreakerTimeout) {
          console.log('[ZohoEmail] Circuit breaker moving to HALF_OPEN state');
          this.circuitBreakerState = 'HALF_OPEN';
          return true;
        }
        console.log('[ZohoEmail] Circuit breaker is OPEN, blocking email operation');
        return false;
        
      case 'HALF_OPEN':
      case 'CLOSED':
        return true;
        
      default:
        return false;
    }
  }

  /**
   * Record successful email operation for circuit breaker
   */
  private recordEmailSuccess(): void {
    if (this.circuitBreakerState === 'HALF_OPEN') {
      console.log('[ZohoEmail] Circuit breaker moving to CLOSED state after successful operation');
      this.circuitBreakerState = 'CLOSED';
    }
    this.failureCount = 0;
  }

  /**
   * Record failed email operation for circuit breaker
   */
  private recordEmailFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    
    if (this.failureCount >= this.maxFailures && this.circuitBreakerState === 'CLOSED') {
      console.log(`[ZohoEmail] Circuit breaker opening after ${this.failureCount} failures`);
      this.circuitBreakerState = 'OPEN';
    } else if (this.circuitBreakerState === 'HALF_OPEN') {
      console.log('[ZohoEmail] Circuit breaker reopening after failed test operation');
      this.circuitBreakerState = 'OPEN';
    }
  }

  /**
   * Refresh the access token using the refresh token with exponential backoff
   */
  private async refreshAccessToken(): Promise<void> {
    // Check if current token is still valid
    if (this.tokens.accessToken && Date.now() < this.tokens.expiresAt) {
      return;
    }

    // Check circuit breaker before attempting refresh
    if (!this.checkCircuitBreaker()) {
      throw new Error('Email service circuit breaker is OPEN - too many recent failures');
    }

    // Check if we're in cooldown period after a recent refresh attempt
    const timeSinceLastAttempt = Date.now() - this.lastRefreshAttempt;
    if (timeSinceLastAttempt < this.REFRESH_COOLDOWN) {
      const waitTime = this.REFRESH_COOLDOWN - timeSinceLastAttempt;
      console.log(`[ZohoEmail] Rate limit cooldown: waiting ${Math.ceil(waitTime / 1000)}s before next refresh attempt`);
      throw new Error(`Rate limit: Please wait ${Math.ceil(waitTime / 1000)} seconds before retrying`);
    }

    console.log(`[ZohoEmail] Refreshing access token... (attempt ${this.retryAttempts + 1}/${this.maxRetryAttempts})`);
    this.lastRefreshAttempt = Date.now();

    try {
      // Get the correct Zoho region with auto-detection
      const region = await this.getZohoRegion();
      const domains = this.getZohoDomain(region);
      
      const response = await axios.post(`https://${domains.auth}/oauth/v2/token`,
        null,
        {
          params: {
            refresh_token: this.tokens.refreshToken,
            client_id: this.clientId,
            client_secret: this.clientSecret,
            grant_type: 'refresh_token'
          },
          timeout: 10000 // 10 second timeout
        }
      );

      this.tokens.accessToken = response.data.access_token;
      // Default to 1 hour if expires_in is not provided
      const expiresIn = response.data.expires_in || 3600;
      // Refresh 5 minutes before expiry
      this.tokens.expiresAt = Date.now() + ((expiresIn - 300) * 1000);

      // Save tokens to file
      await fs.writeFile(this.tokenFile, JSON.stringify(this.tokens, null, 2));
      
      console.log('[ZohoEmail] Access token refreshed successfully, expires at:', new Date(this.tokens.expiresAt).toISOString());
      
      // Reset retry attempts after successful refresh
      this.resetRetryAttempts();
      
      // Record success for circuit breaker
      this.recordEmailSuccess();
    } catch (error: any) {
      console.error('[ZohoEmail] Failed to refresh token:', error.response?.data || error.message);
      
      // Check if we should retry with exponential backoff
      const shouldRetry = this.incrementRetryAttempts();
      
      // Handle rate limiting errors specifically
      if (error.response?.data?.error === 'Access Denied' && 
          error.response?.data?.error_description?.includes('too many requests')) {
        
        if (shouldRetry) {
          const delay = this.calculateBackoffDelay();
          console.log(`[ZohoEmail] Rate limit exceeded, retrying in ${Math.ceil(delay / 1000)}s (attempt ${this.retryAttempts}/${this.maxRetryAttempts})`);
          
          // Wait for backoff delay then retry
          await new Promise(resolve => setTimeout(resolve, delay));
          return this.refreshAccessToken();
        } else {
          // Max retries reached, extend cooldown period
          this.lastRefreshAttempt = Date.now() + this.REFRESH_COOLDOWN;
          throw new Error(`Zoho API rate limit exceeded. Max retries (${this.maxRetryAttempts}) reached.`);
        }
      }
      
      // Handle other retryable errors (network timeouts, 5xx errors)
      if (shouldRetry && (
        error.code === 'ENOTFOUND' || 
        error.code === 'ECONNRESET' || 
        error.code === 'ETIMEDOUT' ||
        (error.response?.status >= 500 && error.response?.status < 600)
      )) {
        const delay = this.calculateBackoffDelay();
        console.log(`[ZohoEmail] Retryable error, retrying in ${Math.ceil(delay / 1000)}s (attempt ${this.retryAttempts}/${this.maxRetryAttempts})`);
        
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.refreshAccessToken();
      }
      
      // Check for account migration before giving up
      if (error.response?.status === 400 && shouldRetry) {
        const currentRegion = await this.getZohoRegion();
        const migrationDetected = await this.detectAccountMigration(currentRegion);
        
        if (migrationDetected) {
          console.log('[ZohoEmail] Account migration detected, retrying with new region...');
          const delay = this.calculateBackoffDelay();
          await new Promise(resolve => setTimeout(resolve, delay));
          return this.refreshAccessToken();
        }
      }
      
      // Reset retry count for non-retryable errors
      this.resetRetryAttempts();
      
      // Record failure for circuit breaker
      this.recordEmailFailure();
      
      throw new Error('Failed to refresh Zoho access token');
    }
  }

  /**
   * Queue an email for background retry if immediate send fails
   */
  private async queueEmailForRetry(options: EmailOptions, priority: 'high' | 'medium' | 'low' = 'medium'): Promise<string> {
    try {
      const emailId = await emailRetryQueue.queueEmail({
        to: options.to,
        subject: options.subject,
        content: options.content,
        fromAddress: options.fromAddress,
        isHtml: options.isHtml,
        cc: options.cc,
        bcc: options.bcc,
        replyTo: options.replyTo
      }, priority);
      
      console.log(`[ZohoEmail] Queued email for background retry: ${emailId}`);
      return emailId;
    } catch (error) {
      console.error('[ZohoEmail] Failed to queue email for retry:', error);
      throw error;
    }
  }

  /**
   * Send an email using Zoho Mail API with retry logic and fallback queuing
   */
  async sendEmail(options: EmailOptions, enableRetryQueue: boolean = true): Promise<boolean> {
    const result = await this.sendEmailWithRetry(options, 0);
    
    // If immediate send failed and retry queue is enabled, queue for background retry
    if (!result && enableRetryQueue) {
      try {
        await this.queueEmailForRetry(options, 'medium');
        console.log('[ZohoEmail] Email queued for background retry after immediate failure');
        // Return true to indicate the email will be retried, even though immediate send failed
        return true;
      } catch (queueError) {
        console.error('[ZohoEmail] Failed to queue email for retry:', queueError);
        return false;
      }
    }
    
    return result;
  }

  /**
   * Internal method to send email with retry logic
   */
  private async sendEmailWithRetry(options: EmailOptions, attemptCount: number): Promise<boolean> {
    // Check circuit breaker before attempting to send email
    if (!this.checkCircuitBreaker()) {
      console.error('[ZohoEmail] Circuit breaker is OPEN, cannot send email');
      return false;
    }

    try {
      await this.refreshAccessToken();

      const emailData: any = {
        fromAddress: options.fromAddress || getConfig('ZOHO_SUPPORT_ALIAS') || '<EMAIL>',
        toAddress: Array.isArray(options.to) ? options.to.join(',') : options.to,
        subject: options.subject,
        content: options.content,
        mailFormat: options.isHtml ? 'html' : 'plaintext'
      };

      if (options.cc?.length) {
        emailData.ccAddress = options.cc.join(',');
      }
      if (options.bcc?.length) {
        emailData.bccAddress = options.bcc.join(',');
      }
      if (options.replyTo) {
        emailData.replyTo = options.replyTo;
      }

      // Get the correct Zoho API domain with auto-detection
      const region = await this.getZohoRegion();
      const domains = this.getZohoDomain(region);
      
      const response = await axios.post(
        `https://${domains.api}/api/accounts/${this.accountId}/messages`,
        emailData,
        {
          headers: {
            'Authorization': `Zoho-oauthtoken ${this.tokens.accessToken}`,
            'Content-Type': 'application/json'
          },
          timeout: 15000 // 15 second timeout
        }
      );

      console.log('[ZohoEmail] Email sent successfully:', {
        messageId: response.data.data?.messageId,
        to: options.to,
        subject: options.subject
      });

      // Record success for circuit breaker
      this.recordEmailSuccess();

      return true;
    } catch (error: any) {
      console.error(`[ZohoEmail] Failed to send email (attempt ${attemptCount + 1}):`, {
        error: error.response?.data || error.message,
        to: options.to,
        subject: options.subject
      });

      // Check if we should retry
      const shouldRetry = attemptCount < this.maxRetryAttempts;
      
      // Handle retryable errors
      if (shouldRetry && (
        error.response?.status === 429 || // Rate limit
        error.response?.status === 401 || // Auth error (may need token refresh)
        error.response?.status >= 500 ||  // Server errors
        error.code === 'ENOTFOUND' ||     // Network errors
        error.code === 'ECONNRESET' ||
        error.code === 'ETIMEDOUT'
      )) {
        const delay = this.baseRetryDelay * Math.pow(2, attemptCount);
        const jitteredDelay = delay * (0.5 + Math.random() * 0.5);
        const finalDelay = Math.min(jitteredDelay, this.maxRetryDelay);
        
        console.log(`[ZohoEmail] Retrying email send in ${Math.ceil(finalDelay / 1000)}s (attempt ${attemptCount + 1}/${this.maxRetryAttempts})`);
        
        await new Promise(resolve => setTimeout(resolve, finalDelay));
        return this.sendEmailWithRetry(options, attemptCount + 1);
      }

      // Log specific non-retryable error details
      if (error.response?.status === 400) {
        console.error('[ZohoEmail] Bad request - check email format and aliases (non-retryable)');
      } else if (error.response?.status === 403) {
        console.error('[ZohoEmail] Forbidden - check permissions (non-retryable)');
      } else if (!shouldRetry) {
        console.error(`[ZohoEmail] Max retries (${this.maxRetryAttempts}) reached for email send`);
      }

      // Record failure for circuit breaker (only on final failure)
      if (!shouldRetry) {
        this.recordEmailFailure();
      }

      return false;
    }
  }

  /**
   * Get retry queue statistics for monitoring
   */
  getRetryQueueStats(): {
    totalQueued: number;
    readyToProcess: number;
    processed: number;
    failed: number;
    oldestEmail?: number;
  } {
    return emailRetryQueue.getQueueStats();
  }

  /**
   * Send a ChromaSync team invitation email with high priority
   */
  async sendInvitationEmail(
    to: string,
    invitation: {
      organizationName: string;
      inviterName: string;
      role: string;
      token: string;
      expiresAt: Date;
    }
  ): Promise<boolean> {    const invitationUrl = `chromasync://invite/${invitation.token}`;
    
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ChromaSync Team Invitation</title>
  <style>
    body { 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
      line-height: 1.6; 
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f8fafc;
    }
    .container { 
      max-width: 600px;
      margin: 0 auto;
      background: #ffffff; 
      border-radius: 12px; 
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 40px 30px;
      text-align: center;
    }
    .logo {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    .content {
      padding: 40px 30px;
    }
    .invitation-card {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-left: 4px solid #3b82f6;
      border-radius: 8px;
      padding: 24px;
      margin: 24px 0;
    }
    .instructions {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      margin: 24px 0;
    }
    .instructions h3 {
      margin-top: 0;
      color: #1f2937;
    }
    .instructions ol {
      margin: 10px 0;
      padding-left: 20px;
    }
    .instructions li {
      margin: 8px 0;
    }
    .token-display {
      background: #1f2937;
      color: #10b981;
      font-family: 'Monaco', 'Courier New', monospace;
      padding: 12px 16px;
      border-radius: 6px;
      margin: 12px 0;
      word-break: break-all;
      text-align: center;
      font-size: 14px;
    }
    .button { 
      display: inline-block; 
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: white; 
      padding: 12px 30px; 
      text-decoration: none; 
      border-radius: 8px;
      font-weight: 600;
      margin: 20px 0;
    }
    .footer {
      padding: 30px;
      background: #f8fafc;
      text-align: center;
      font-size: 14px;
      color: #64748b;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">🎨 ChromaSync</div>
      <p>Professional Color Management</p>
    </div>
    
    <div class="content">
      <h2>You're invited to join ${invitation.organizationName}!</h2>
      
      <div class="invitation-card">
        <p><strong>${invitation.inviterName}</strong> has invited you to join their ChromaSync team as a <strong>${invitation.role}</strong>.</p>
        <p>ChromaSync helps teams organize and sync their color palettes across projects.</p>
      </div>
      
      <div class="instructions">
        <h3>📋 How to Accept Your Invitation:</h3>
        
        <div style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 16px; margin: 16px 0;">
          <h4 style="margin: 0 0 12px 0; color: #0c4a6e; font-size: 16px;">✅ Easy Steps:</h4>
          <ol style="margin: 0; padding-left: 20px;">
            <li><strong>Open ChromaSync</strong> on your computer</li>
            <li>Sign in with your Google account</li>
            <li>Go to <strong>Settings → Team</strong></li>
            <li>Click <strong>"Join Organization"</strong></li>
            <li><strong>Enter this invitation code:</strong></li>
          </ol>
        </div>
        
        <div class="token-display" style="position: relative;">
          <div style="position: absolute; top: -8px; right: 8px; background: #10b981; color: white; padding: 2px 8px; border-radius: 4px; font-size: 10px; text-transform: uppercase; font-weight: bold;">
            Copy This Code
          </div>
          ${invitation.token}
        </div>
        
        <div style="background: #fffbeb; border: 1px solid #f59e0b; border-radius: 8px; padding: 12px; margin: 16px 0; text-align: center;">
          <p style="margin: 0; color: #92400e; font-size: 14px;">
            💡 <strong>Tip:</strong> Select the code above, copy it (Ctrl+C or Cmd+C), then paste it into ChromaSync
          </p>
        </div>

        <div style="background: #fef2f2; border: 1px solid #f87171; border-radius: 8px; padding: 12px; margin: 16px 0;">
          <h4 style="margin: 0 0 8px 0; color: #991b1b; font-size: 14px;">🚨 Troubleshooting:</h4>
          <ul style="margin: 0; padding-left: 20px; color: #991b1b; font-size: 13px;">
            <li>If the email button doesn't work, always use the manual code above</li>
            <li>Make sure you're signed into the same Google account</li>
            <li>Check that ChromaSync is updated to the latest version</li>
            <li>If issues persist, forward this email to your IT admin</li>
          </ul>
        </div>
      </div>
      
      <p style="text-align: center; color: #64748b; font-size: 14px;">
        <em>⚠️ Email links may not work in all email clients. The manual invitation code above always works.</em>
      </p>
    </div>
    
    <div class="footer">
      <p><strong>⏰ This invitation expires on ${invitation.expiresAt.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })}.</strong></p>
      <p>Don't have ChromaSync yet? Download it at <a href="https://chromasync.app" style="color: #3b82f6;">chromasync.app</a></p>
      <p>Questions? Contact us at <a href="mailto:<EMAIL>" style="color: #3b82f6;"><EMAIL></a></p>
      <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 24px 0;">
      <p>© 2024 ChromaSync. All rights reserved.</p>
    </div>
  </div>
</body>
</html>`;

    const textContent = `🎨 ChromaSync Team Invitation

Hi there!

${invitation.inviterName} has invited you to join ${invitation.organizationName} on ChromaSync as a ${invitation.role}.

═══════════════════════════════════════════════════════════════
📋 HOW TO ACCEPT YOUR INVITATION:
═══════════════════════════════════════════════════════════════

✅ EASY STEPS:
1. Open ChromaSync on your computer
2. Sign in with your Google account  
3. Go to Settings → Team
4. Click "Join Organization"
5. Enter this invitation code:

┌─────────────────────────────────────────────────────────────┐
│  INVITATION CODE (Copy this exactly):                      │
│                                                             │
│  ${invitation.token}                      │
│                                                             │
└─────────────────────────────────────────────────────────────┘

💡 TIP: Select the code above, copy it (Ctrl+C or Cmd+C), then paste it into ChromaSync

🚨 TROUBLESHOOTING:
• If email links don't work, always use the manual code above
• Make sure you're signed into the same Google account
• Check that ChromaSync is updated to the latest version
• If issues persist, forward this email to your IT admin

⏰ This invitation expires on ${invitation.expiresAt.toLocaleDateString('en-US', { 
  weekday: 'long', 
  year: 'numeric', 
  month: 'long', 
  day: 'numeric' 
})}.

═══════════════════════════════════════════════════════════════

Don't have ChromaSync? Download it at: https://chromasync.app

Questions? Reply to this email or contact <NAME_EMAIL>

Best regards,
ChromaSync Support Team`;

    // Send invitation email with high priority (enables background retry queue)
    const emailOptions = {
      to,
      subject: `🎨 ${invitation.inviterName} invited you to join ${invitation.organizationName}`,
      content: htmlContent,
      isHtml: true,
      fromAddress: getConfig('ZOHO_SUPPORT_ALIAS') || '<EMAIL>'
    };

    // Try immediate send first
    const result = await this.sendEmailWithRetry(emailOptions, 0);
    
    // If immediate send failed, queue with high priority for background retry
    if (!result) {
      try {
        await this.queueEmailForRetry(emailOptions, 'high');
        console.log('[ZohoEmail] Invitation email queued with high priority for background retry');
        // Return true to indicate the invitation will be retried
        return true;
      } catch (queueError) {
        console.error('[ZohoEmail] Failed to queue invitation email for retry:', queueError);
        return false;
      }
    }
    
    return result;
  }
}

// Singleton instance
export const zohoEmailService = new ZohoEmailService();