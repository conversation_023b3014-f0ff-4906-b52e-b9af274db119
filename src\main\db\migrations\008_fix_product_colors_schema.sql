-- Migration 008: Fix product_colors table schema
-- This migration renames the 'position' column to 'display_order' for consistency

-- Check if the column exists and rename it
-- SQLite doesn't support RENAME COLUMN directly, so we need to recreate the table

-- First, check if we need to do anything by seeing if position column exists
-- If position exists, we need to migrate to display_order

-- Backup existing data
CREATE TABLE IF NOT EXISTS product_colors_backup AS 
SELECT * FROM product_colors WHERE 1=0; -- Create empty backup table

-- Check if position column exists and copy data
INSERT OR IGNORE INTO product_colors_backup 
SELECT 
  product_id, 
  color_id, 
  COALESCE(position, display_order, 0) as display_order,
  added_at 
FROM product_colors;

-- Drop the old table
DROP TABLE IF EXISTS product_colors;

-- Recreate with correct schema
CREATE TABLE product_colors (
  product_id INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  color_id INTEGER NOT NULL REFERENCES colors(id) ON DELETE CASCADE,
  display_order INTEGER NOT NULL DEFAULT 0,
  added_at TEXT DEFAULT CURRENT_TIMESTAMP,
  <PERSON>IMARY KEY (product_id, color_id)
);

-- Restore data
INSERT OR IGNORE INTO product_colors (product_id, color_id, display_order, added_at)
SELECT product_id, color_id, display_order, added_at 
FROM product_colors_backup;

-- Drop backup table
DROP TABLE product_colors_backup;

-- Recreate indexes
CREATE INDEX IF NOT EXISTS idx_product_colors_product ON product_colors(product_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_color ON product_colors(color_id);