/**
 * @file datasheet.ipc.ts
 * @description IPC handlers for datasheet operations
 */

import { ipcMain } from 'electron';
import { DatasheetService } from '../db/services/datasheet.service';
import { DatasheetChannels } from '../../shared/constants/channels';
import { getErrorMessage } from '../utils/error-utils';

/**
 * Register datasheet IPC handlers
 * @param datasheetService Instance of DatasheetService
 */
export function registerDatasheetHandlers(datasheetService: DatasheetService): void {
  console.log('[DatasheetIPC] Registering datasheet handlers...');
  console.log('[DatasheetIPC] DatasheetService instance:', !!datasheetService);
  console.log('[DatasheetIPC] DatasheetChannels.GET_BY_PRODUCT:', DatasheetChannels.GET_BY_PRODUCT);

  // Get datasheets for a product
  try {
    ipcMain.removeHandler(DatasheetChannels.GET_BY_PRODUCT);
    console.log('[DatasheetIPC] Removed previous handler for', DatasheetChannels.GET_BY_PRODUCT);
  } catch (_e) {
    console.log('[DatasheetIPC] No previous handler to remove for', DatasheetChannels.GET_BY_PRODUCT);
  }
  
  ipcMain.handle(DatasheetChannels.GET_BY_PRODUCT, async (_, { productId }) => {
      console.log('[DatasheetIPC] Handler called for GET_BY_PRODUCT with productId:', productId);
      try {
        const datasheets = datasheetService.getDatasheetsByProductId(productId);
        return {
          success: true,
          data: datasheets
        };
      } catch (error) {
        console.error(`Error in datasheet:getByProduct for product ${productId}:`, error);
        return {
          success: false,
          message: `Error getting datasheets: ${getErrorMessage(error)}`
        };
      }
    });
  console.log('[DatasheetIPC] Registered handler for', DatasheetChannels.GET_BY_PRODUCT);

  // Add datasheet to product
  try {
    ipcMain.removeHandler(DatasheetChannels.ADD_TO_PRODUCT);
    console.log('[DatasheetIPC] Removed previous handler for', DatasheetChannels.ADD_TO_PRODUCT);
  } catch (_e) {
    console.log('[DatasheetIPC] No previous handler to remove for', DatasheetChannels.ADD_TO_PRODUCT);
  }
  ipcMain.handle(DatasheetChannels.ADD_TO_PRODUCT, async (_, { productId, datasheet }) => {
      try {
        const result = datasheetService.addDatasheet(productId, datasheet);

        if (!result) {
          return {
            success: false,
            message: 'Failed to add datasheet to product'
          };
        }

        return {
          success: true,
          data: result
        };
      } catch (error) {
        console.error(`Error in datasheet:addToProduct for product ${productId}:`, error);
        return {
          success: false,
          message: `Error adding datasheet: ${getErrorMessage(error)}`
        };
      }
    });

  // Add web link to product
  try {
    ipcMain.removeHandler(DatasheetChannels.ADD_WEB_LINK);
    console.log('[DatasheetIPC] Removed previous handler for', DatasheetChannels.ADD_WEB_LINK);
  } catch (_e) {
    console.log('[DatasheetIPC] No previous handler to remove for', DatasheetChannels.ADD_WEB_LINK);
  }
  
  ipcMain.handle(DatasheetChannels.ADD_WEB_LINK, async (_, { productId, url, displayName }) => {
      console.log('[DatasheetIPC] Handler called for ADD_WEB_LINK with productId:', productId, 'url:', url);
      try {
        const result = datasheetService.addWebLink(productId, url, displayName);

        if (!result) {
          return {
            success: false,
            message: 'Failed to add web link to selection'
          };
        }

        return {
          success: true,
          data: result
        };
      } catch (error) {
        console.error(`Error in datasheet:addWebLink for product ${productId}:`, error);
        return {
          success: false,
          message: `Error adding web link: ${getErrorMessage(error)}`
        };
      }
    });

  // Remove datasheet
  ipcMain.removeHandler(DatasheetChannels.REMOVE);
  ipcMain.handle(DatasheetChannels.REMOVE, async (_, { datasheetId }) => {
      try {
        const success = datasheetService.removeDatasheet(datasheetId);

        return {
          success,
          message: success
            ? 'Datasheet removed successfully'
            : 'Failed to remove datasheet'
        };
      } catch (error) {
        console.error(`Error in datasheet:remove for datasheet ${datasheetId}:`, error);
        return {
          success: false,
          message: `Error deleting datasheet: ${getErrorMessage(error)}`
        };
      }
    });

  // Open a single datasheet
  ipcMain.removeHandler(DatasheetChannels.OPEN);
  ipcMain.handle(DatasheetChannels.OPEN, async (_, { datasheetId }) => {
      try {
        console.log(`Received request to open datasheet with ID: ${datasheetId}`);
        const result = await datasheetService.openDatasheet(datasheetId);

        if (!result.success) {
          return {
            success: false,
            message: result.message || 'Failed to open datasheet'
          };
        }

        return {
          success: true,
          message: 'Datasheet opened successfully'
        };
      } catch (error) {
        console.error(`Error in datasheet:open for datasheet ${datasheetId}:`, error);
        return {
          success: false,
          message: `Error opening datasheet: ${getErrorMessage(error)}`
        };
      }
    });

  // Open all datasheets for a product
  ipcMain.removeHandler(DatasheetChannels.OPEN_ALL);
  ipcMain.handle(DatasheetChannels.OPEN_ALL, async (_, { productId }) => {
      try {
        const result = await datasheetService.openDatasheets(productId);

        return {
          success: result.success,
          count: result.count,
          errors: result.errors
        };
      } catch (error) {
        console.error(`Error in datasheet:openAll for product ${productId}:`, error);
        return {
          success: false,
          count: 0,
          errors: [`Error opening datasheets: ${getErrorMessage(error)}`]
        };
      }
    });

  // Migrate datasheets from old table (removed since there's no legacy data)
  // ipcMain.removeHandler(DatasheetChannels.MIGRATE);
  // ipcMain.handle(DatasheetChannels.MIGRATE, async () => {
  //   return {
  //     success: true,
  //     count: 0,
  //     errors: []
  //   };
  // });

  console.log('[DatasheetIPC] All datasheet handlers registered successfully');
  
  // Debug: List all registered channels
  const registeredChannels = [
    DatasheetChannels.GET_BY_PRODUCT,
    DatasheetChannels.ADD_TO_PRODUCT,
    DatasheetChannels.ADD_WEB_LINK,
    DatasheetChannels.REMOVE,
    DatasheetChannels.OPEN,
    DatasheetChannels.OPEN_ALL
  ];
  console.log('[DatasheetIPC] Expected channels:', registeredChannels);
}
