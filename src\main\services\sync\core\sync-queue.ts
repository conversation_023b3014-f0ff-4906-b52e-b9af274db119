/**
 * @file sync-queue.ts
 * @description Queue management system for sync operations
 * 
 * This module provides a robust queue system with persistent storage,
 * priority handling, and automatic cleanup capabilities.
 */

import Store from 'electron-store';
import { 
  SyncQueueItem, 
  QueueStats, 
  QueueItemMetadata,
  SyncEventHandler 
} from './sync-types';

// ============================================================================
// QUEUE MANAGER
// ============================================================================

/**
 * Advanced queue manager for sync operations
 */
export class SyncQueueManager {
  private memoryQueue = new Map<string, SyncQueueItem>();
  private persistentStore: Store;
  private organizationId: string | null = null;
  private userId: string | null = null;
  private eventHandlers = new Map<string, SyncEventHandler[]>();

  // Queue configuration
  private readonly MAX_QUEUE_SIZE = 10000;
  private readonly MAX_ITEM_AGE_MS = 24 * 60 * 60 * 1000; // 24 hours
  private readonly CLEANUP_INTERVAL_MS = 5 * 60 * 1000; // 5 minutes

  private cleanupInterval: ReturnType<typeof setInterval> | null = null;

  constructor() {
    this.persistentStore = new Store({ name: 'sync-queue' });
    this.startCleanupTimer();
  }

  /**
   * Initialize queue with user and organization context
   */
  initialize(userId: string, organizationId: string): void {
    this.userId = userId;
    this.organizationId = organizationId;
    this.loadPersistentQueue();
  }

  /**
   * Add item to the sync queue
   */
  enqueue(item: Omit<SyncQueueItem, 'metadata'>): void {
    if (this.memoryQueue.size >= this.MAX_QUEUE_SIZE) {
      throw new Error('Queue size limit exceeded');
    }

    const queueItem: SyncQueueItem = {
      ...item,
      metadata: {
        timestamp: Date.now(),
        attempts: 0,
        lastAttempt: 0,
        priority: this.calculatePriority(item),
        source: 'local'
      }
    };

    this.memoryQueue.set(item.id, queueItem);
    this.savePersistentQueue();
    this.emitEvent('queue:updated', this.getStats());
  }

  /**
   * Get next batch of items to process
   */
  dequeue(batchSize: number): SyncQueueItem[] {
    if (this.memoryQueue.size === 0) {
      return [];
    }

    // Sort by priority (higher first) then by timestamp (older first)
    const sortedItems = Array.from(this.memoryQueue.values())
      .sort((a, b) => {
        if (a.metadata.priority !== b.metadata.priority) {
          return b.metadata.priority - a.metadata.priority;
        }
        return a.metadata.timestamp - b.metadata.timestamp;
      });

    return sortedItems.slice(0, batchSize);
  }

  /**
   * Remove items from queue after successful processing
   */
  removeItems(itemIds: string[]): void {
    let removed = 0;
    
    for (const id of itemIds) {
      if (this.memoryQueue.delete(id)) {
        removed++;
      }
    }

    if (removed > 0) {
      this.savePersistentQueue();
      this.emitEvent('queue:updated', this.getStats());
    }
  }

  /**
   * Mark items as failed and update retry metadata
   */
  markItemsFailed(itemIds: string[]): void {
    let updated = 0;

    for (const id of itemIds) {
      const item = this.memoryQueue.get(id);
      if (item) {
        item.metadata.attempts++;
        item.metadata.lastAttempt = Date.now();
        updated++;
      }
    }

    if (updated > 0) {
      this.savePersistentQueue();
      this.emitEvent('queue:updated', this.getStats());
    }
  }

  /**
   * Get queue statistics
   */
  getStats(): QueueStats {
    const persistentData = this.getPersistentQueueData();
    const now = Date.now();
    
    let pendingItems = 0;
    let failedItems = 0;
    let oldestItem: number | null = null;

    for (const item of this.memoryQueue.values()) {
      if (item.metadata.attempts === 0) {
        pendingItems++;
      } else {
        failedItems++;
      }

      if (oldestItem === null || item.metadata.timestamp < oldestItem) {
        oldestItem = item.metadata.timestamp;
      }
    }

    return {
      memorySize: this.memoryQueue.size,
      persistentSize: Object.keys(persistentData).length,
      totalItems: this.memoryQueue.size,
      pendingItems,
      failedItems,
      oldestItem
    };
  }

  /**
   * Clear all items from queue
   */
  clear(): void {
    this.memoryQueue.clear();
    this.clearPersistentQueue();
    this.emitEvent('queue:updated', this.getStats());
  }

  /**
   * Get all items in queue (for debugging)
   */
  getAllItems(): SyncQueueItem[] {
    return Array.from(this.memoryQueue.values());
  }

  /**
   * Check if queue contains specific item
   */
  hasItem(itemId: string): boolean {
    return this.memoryQueue.has(itemId);
  }

  /**
   * Get specific item from queue
   */
  getItem(itemId: string): SyncQueueItem | undefined {
    return this.memoryQueue.get(itemId);
  }

  /**
   * Subscribe to queue events
   */
  on(event: string, handler: SyncEventHandler): () => void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    
    this.eventHandlers.get(event)!.push(handler);

    // Return unsubscribe function
    return () => {
      const handlers = this.eventHandlers.get(event);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    };
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    this.savePersistentQueue();
    this.memoryQueue.clear();
    this.eventHandlers.clear();
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Calculate priority for queue item
   */
  private calculatePriority(item: Omit<SyncQueueItem, 'metadata'>): number {
    let priority = 1; // Base priority

    // Higher priority for delete operations
    if (item.action === 'delete') {
      priority += 2;
    }

    // Higher priority for organizations (they affect access)
    if (item.table === 'organizations') {
      priority += 3;
    }

    // Higher priority for products (they're typically more important than colors)
    if (item.table === 'products') {
      priority += 1;
    }

    return priority;
  }

  /**
   * Get persistent queue storage key
   */
  private getQueueKey(): string {
    return `queue-${this.organizationId || 'default'}-${this.userId || 'anonymous'}`;
  }

  /**
   * Load queue from persistent storage
   */
  private loadPersistentQueue(): void {
    try {
      const queueKey = this.getQueueKey();
      const persistentData = this.persistentStore.get(queueKey, {}) as Record<string, any>;
      
      // Convert persistent data back to queue items
      for (const [key, itemData] of Object.entries(persistentData)) {
        if (this.isValidQueueItem(itemData)) {
          this.memoryQueue.set(key, itemData);
        }
      }

      console.log(`[SyncQueue] Loaded ${this.memoryQueue.size} items from persistent storage`);
    } catch (error) {
      console.error('[SyncQueue] Error loading persistent queue:', error);
    }
  }

  /**
   * Save queue to persistent storage
   */
  private savePersistentQueue(): void {
    try {
      const queueKey = this.getQueueKey();
      const queueData: Record<string, SyncQueueItem> = {};
      
      for (const [key, item] of this.memoryQueue.entries()) {
        queueData[key] = {
          ...item,
          metadata: {
            ...item.metadata,
            timestamp: item.metadata.timestamp || Date.now()
          }
        };
      }
      
      this.persistentStore.set(queueKey, queueData);
    } catch (error) {
      console.error('[SyncQueue] Error saving persistent queue:', error);
    }
  }

  /**
   * Get persistent queue data
   */
  private getPersistentQueueData(): Record<string, any> {
    const queueKey = this.getQueueKey();
    return this.persistentStore.get(queueKey, {}) as Record<string, any>;
  }

  /**
   * Clear persistent queue storage
   */
  private clearPersistentQueue(): void {
    const queueKey = this.getQueueKey();
    this.persistentStore.delete(queueKey);
  }

  /**
   * Validate queue item structure
   */
  private isValidQueueItem(item: any): item is SyncQueueItem {
    return (
      item &&
      typeof item.id === 'string' &&
      typeof item.table === 'string' &&
      typeof item.action === 'string' &&
      item.metadata &&
      typeof item.metadata.timestamp === 'number'
    );
  }

  /**
   * Start automatic cleanup timer
   */
  private startCleanupTimer(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldItems();
    }, this.CLEANUP_INTERVAL_MS);
  }

  /**
   * Remove old items from queue
   */
  private cleanupOldItems(): void {
    const now = Date.now();
    const itemsToRemove: string[] = [];

    for (const [id, item] of this.memoryQueue.entries()) {
      const age = now - item.metadata.timestamp;
      
      // Remove items older than MAX_ITEM_AGE_MS
      if (age > this.MAX_ITEM_AGE_MS) {
        itemsToRemove.push(id);
      }
    }

    if (itemsToRemove.length > 0) {
      console.log(`[SyncQueue] Cleaning up ${itemsToRemove.length} old items`);
      this.removeItems(itemsToRemove);
    }
  }

  /**
   * Emit event to all registered handlers
   */
  private emitEvent(event: string, data: any): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`[SyncQueue] Error in event handler for ${event}:`, error);
        }
      });
    }
  }
}
