/**
 * @file errors.ts
 * @description Custom error types for the application
 */

/**
 * Base application error class
 */
export class AppError extends Error {
  constructor(message: string) {
    super(message);
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Error thrown when a HEX color code is invalid
 */
export class InvalidHexCodeError extends AppError {
  code: string;
  issue: string;

  constructor(code: string, issue: string) {
    super(`Invalid HEX color code: ${code} - ${issue}`);
    this.code = code;
    this.issue = issue;
  }
}

/**
 * Error thrown when a CMYK color code is invalid
 */
export class InvalidCMYKError extends AppError {
  value: string;
  issue: string;

  constructor(value: string, issue: string) {
    super(`Invalid CMYK color value: ${value} - ${issue}`);
    this.value = value;
    this.issue = issue;
  }
}
