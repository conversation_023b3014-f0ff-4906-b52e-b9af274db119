const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

const dbPath = path.join(
  process.env.HOME,
  'Library',
  'Application Support',
  'chroma-sync',
  'chromasync.db'
);

console.log('Creating database at:', dbPath);

// Ensure directory exists
const dir = path.dirname(dbPath);
if (!fs.existsSync(dir)) {
  fs.mkdirSync(dir, { recursive: true });
}

// Create database
const db = new Database(dbPath);

// Enable foreign keys and WAL mode
db.pragma('foreign_keys = ON');
db.pragma('journal_mode = WAL');

// Create schema
console.log('Creating schema...');

// Run the optimized schema
const schemaPath = path.join(__dirname, 'create-optimized-schema.sql');
if (fs.existsSync(schemaPath)) {
  const schema = fs.readFileSync(schemaPath, 'utf8');
  db.exec(schema);
  console.log('Optimized schema created');
}

// Create migrations table
db.exec(`
  CREATE TABLE IF NOT EXISTS schema_migrations (
    version INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  )
`);

// Run migrations
const migrationsDir = path.join(__dirname, '..', 'src', 'main', 'db', 'migrations');
if (fs.existsSync(migrationsDir)) {
  const files = fs.readdirSync(migrationsDir)
    .filter(f => f.endsWith('.sql'))
    .sort();

  for (const file of files) {
    const match = file.match(/^(\d+)_(.+)\.sql$/);
    if (!match) continue;

    const version = parseInt(match[1], 10);
    const name = match[2];
    
    // Check if already applied
    const applied = db.prepare('SELECT 1 FROM schema_migrations WHERE version = ?').get(version);
    if (applied) {
      console.log(`Skipping already applied: ${file}`);
      continue;
    }

    console.log(`Applying migration: ${file}`);
    const sql = fs.readFileSync(path.join(migrationsDir, file), 'utf8');
    
    try {
      db.transaction(() => {
        db.exec(sql);
        db.prepare('INSERT INTO schema_migrations (version, name) VALUES (?, ?)').run(version, name);
      })();
      console.log(`✓ Applied: ${file}`);
    } catch (error) {
      console.error(`Failed to apply ${file}:`, error.message);
      process.exit(1);
    }
  }
}

console.log('\nDatabase initialized successfully!');
db.close();