/**
 * @file debugTools.ts
 * @description Debugging utilities for helping diagnose IPC and other issues
 */

/**
 * Check if the colorAPI is available and working correctly
 * @returns An object with diagnostic information
 */
export function checkColorAPI() {
  try {
    // Check if colorAPI exists
    const hasColorAPI = !!window.colorAPI;
    
    // Get a list of all available methods
    const availableMethods = hasColorAPI 
      ? Object.keys(window.colorAPI as object).filter(key => 
          typeof (window.colorAPI as any)[key] === 'function'
        ) 
      : [];
    
    // Check specific methods
    const hasImport = hasColorAPI && typeof window.colorAPI.importColors === 'function';
    const hasExport = hasColorAPI && typeof window.colorAPI.exportColors === 'function';
    
    return {
      available: hasColorAPI,
      availableMethods,
      importAvailable: hasImport,
      exportAvailable: hasExport,
      diagnosticInfo: {
        electron: window.electron?.process?.versions?.electron || 'unknown',
        platform: window.electron?.process?.platform || 'unknown',
        nodeVersion: window.electron?.process?.versions?.node || 'unknown'
      }
    };
  } catch (error) {
    console.error('Error checking colorAPI:', error);
    return {
      available: false,
      availableMethods: [],
      importAvailable: false,
      exportAvailable: false,
      error: error instanceof Error ? error.message : String(error),
      diagnosticInfo: {
        electron: window.electron?.process?.versions?.electron || 'unknown',
        platform: window.electron?.process?.platform || 'unknown',
        nodeVersion: window.electron?.process?.versions?.node || 'unknown'
      }
    };
  }
}

/**
 * Test if the IPC is working by calling a test method
 * @returns Promise with the test result
 */
export async function testIPC(): Promise<{success: boolean; message: string}> {
  try {
    if (!window.ipc || typeof window.ipc.invoke !== 'function') {
      return { success: false, message: 'IPC not available' };
    }
    
    // Try to call the test IPC handler
    const result = await window.ipc.invoke('test-ipc-available');
    return { 
      success: true, 
      message: typeof result === 'object' && result !== null 
        ? (result as any).message || 'IPC working'
        : 'IPC working but returned unexpected format' 
    };
  } catch (error) {
    console.error('Error testing IPC:', error);
    return { 
      success: false, 
      message: error instanceof Error ? error.message : String(error)
    };
  }
} 