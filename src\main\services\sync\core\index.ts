/**
 * @file index.ts
 * @description Core sync system exports
 * 
 * This module provides the main exports for the core sync system,
 * making it easy to import and use the sync functionality.
 */

// Core engine and managers
export { SyncEngine } from './sync-engine';
export { SyncQueueManager } from './sync-queue';
export { SyncConfigManager, getSyncConfig, resetSyncConfig } from './sync-config';

// Types and interfaces
export * from './sync-types';

// Default configurations
export {
  DEFAULT_CONFIG,
  DEFAULT_BATCH_SIZES,
  DEFAULT_TIMEOUTS,
  DEFAULT_RETRY
} from './sync-config';
