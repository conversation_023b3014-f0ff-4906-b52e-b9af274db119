import React, { createContext, useContext } from 'react';

interface FeatureFlagContextType {
  useNewTokenSystem: boolean;
  toggleTokenSystem: () => void;
  setTokenSystem: (enabled: boolean) => void;
}

const defaultContext: FeatureFlagContextType = {
  useNewTokenSystem: true,
  toggleTokenSystem: () => {
    console.warn('[Deprecated] Token system is now fully implemented and cannot be toggled.');
  },
  setTokenSystem: () => {
    console.warn('[Deprecated] Token system is now fully implemented and cannot be toggled.');
  },
};

export const FeatureFlagContext = createContext<FeatureFlagContextType>(defaultContext);

interface FeatureFlagProviderProps {
  children: React.ReactNode;
}

/**
 * Feature flag provider that always uses the token system
 * NOTE: The token system migration has been completed and legacy styling removed.
 * This context is kept only for backward compatibility with existing components.
 */
export const FeatureFlagProvider: React.FC<FeatureFlagProviderProps> = ({ children }) => {
  // Now that the migration is complete, we always return true
  const useNewTokenSystem = true;
  
  const toggleTokenSystem = () => {
    console.warn('[Deprecated] Token system is now fully implemented and cannot be toggled.');
  };
  
  const setTokenSystem = () => {
    console.warn('[Deprecated] Token system is now fully implemented and cannot be toggled.');
  };
  
  const value = {
    useNewTokenSystem,
    toggleTokenSystem,
    setTokenSystem,
  };
  
  return (
    <FeatureFlagContext.Provider value={value}>
      {children}
    </FeatureFlagContext.Provider>
  );
};

/**
 * Custom hook for consuming the feature flags
 * @deprecated Token system is now fully implemented, this hook always returns true for useNewTokenSystem
 */
export const useFeatureFlags = () => useContext(FeatureFlagContext); 