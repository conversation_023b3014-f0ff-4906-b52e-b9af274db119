-- Quick fix for RLS infinite recursion in organization_members table
-- Run this in Supabase SQL Editor to fix the current error

-- 1. Create security definer functions to avoid recursion
CREATE OR REPLACE FUNCTION is_organization_member(
    p_user_id UUID,
    p_organization_id UUID
) RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM organization_members 
        WHERE user_id = p_user_id 
        AND organization_id = p_organization_id
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Fix the problematic policies
DROP POLICY IF EXISTS "Users can view their organizations" ON organizations;

CREATE POLICY "Users can view their organizations" ON organizations
    FOR SELECT TO authenticated
    USING (
        is_organization_member(auth.uid(), id)
    );

-- 3. Grant permissions
GRANT EXECUTE ON FUNCTION is_organization_member(UUID, UUID) TO authenticated;

-- Test if it's working
SELECT id, name, slug FROM organizations
WHERE is_organization_member(auth.uid(), id);
