-- ChromaSync Multi-Tenant Migration Script
-- Adds organization support to existing Supabase schema
-- Run this script to enable multi-tenant functionality

-- ========================================
-- 1. Organization Management Tables
-- ========================================

-- Organizations table
CREATE TABLE public.organizations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    plan TEXT DEFAULT 'free' CHECK (plan IN ('free', 'team', 'enterprise')),
    settings JSONB DEFAULT '{}',
    max_members INTEGER DEFAULT 3, -- Free tier limit
    max_colors INTEGER DEFAULT 1000, -- Free tier limit
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Organization members table
CREATE TABLE public.organization_members (
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
    joined_at TIMESTAMPTZ DEFAULT NOW(),
    invited_by UUID REFERENCES auth.users(id),
    invitation_token TEXT,
    invitation_expires_at TIMESTAMPTZ,
    PRIMARY KEY (organization_id, user_id)
);

-- Organization invitations table
CREATE TABLE public.organization_invitations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('admin', 'member')),
    invited_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    token TEXT UNIQUE NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    accepted_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_org_members_user ON organization_members(user_id);
CREATE INDEX idx_org_slug ON organizations(slug);
CREATE INDEX idx_org_invitations_token ON organization_invitations(token);
CREATE INDEX idx_org_invitations_email ON organization_invitations(email);

-- ========================================
-- 2. Add organization_id to existing tables
-- ========================================

-- Add organization_id to products table
ALTER TABLE public.products ADD COLUMN organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE;

-- Add organization_id to colors table  
ALTER TABLE public.colors ADD COLUMN organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE;

-- Add organization_id to product_colors table
ALTER TABLE public.product_colors ADD COLUMN organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE;

-- Add organization_id to sync_metadata table
ALTER TABLE public.sync_metadata ADD COLUMN organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE;

-- ========================================
-- 3. Create indexes for organization-based queries
-- ========================================

-- Products indexes
CREATE INDEX idx_products_org_active ON products(organization_id, created_at DESC) WHERE organization_id IS NOT NULL;
CREATE INDEX idx_products_org_name ON products(organization_id, name) WHERE organization_id IS NOT NULL;

-- Colors indexes
CREATE INDEX idx_colors_org_hex ON colors(organization_id, hex) WHERE organization_id IS NOT NULL;
CREATE INDEX idx_colors_org_code ON colors(organization_id, source_id, code) WHERE organization_id IS NOT NULL;
CREATE INDEX idx_colors_org_updated ON colors(organization_id, updated_at DESC) WHERE organization_id IS NOT NULL;

-- Product colors indexes
CREATE INDEX idx_product_colors_org ON product_colors(organization_id) WHERE organization_id IS NOT NULL;

-- Sync metadata indexes
CREATE INDEX idx_sync_metadata_org ON sync_metadata(organization_id) WHERE organization_id IS NOT NULL;

-- ========================================
-- 4. Enable RLS on new tables
-- ========================================

ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_invitations ENABLE ROW LEVEL SECURITY;

-- ========================================
-- 5. Update RLS policies for organization-based access
-- ========================================

-- Drop existing user-based policies
DROP POLICY IF EXISTS "Users can view own products" ON products;
DROP POLICY IF EXISTS "Users can insert own products" ON products;
DROP POLICY IF EXISTS "Users can update own products" ON products;
DROP POLICY IF EXISTS "Users can delete own products" ON products;

DROP POLICY IF EXISTS "Users can view own colors" ON colors;
DROP POLICY IF EXISTS "Users can insert own colors" ON colors;
DROP POLICY IF EXISTS "Users can update own colors" ON colors;
DROP POLICY IF EXISTS "Users can delete own colors" ON colors;

DROP POLICY IF EXISTS "Users can manage product colors" ON product_colors;
DROP POLICY IF EXISTS "Users can manage own sync data" ON sync_metadata;

-- Organization policies
CREATE POLICY "Users can view their organizations" ON organizations
    FOR SELECT TO authenticated
    USING (
        id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Organization owners can update organization" ON organizations
    FOR UPDATE TO authenticated
    USING (
        id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND role = 'owner'
        )
    );

-- Organization members policies
CREATE POLICY "Users can view organization members" ON organization_members
    FOR SELECT TO authenticated
    USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Admins and owners can manage members" ON organization_members
    FOR ALL TO authenticated
    USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
        )
    );

-- Organization invitations policies
CREATE POLICY "Organization admins can manage invitations" ON organization_invitations
    FOR ALL TO authenticated
    USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
        )
    );

-- Products policies (organization-based)
CREATE POLICY "Organization members can view products" ON products
    FOR SELECT TO authenticated
    USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid()
        )
        OR (organization_id IS NULL AND user_id = auth.uid()) -- Legacy user data
    );

CREATE POLICY "Organization members can create products" ON products
    FOR INSERT TO authenticated
    WITH CHECK (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Organization members can update products" ON products
    FOR UPDATE TO authenticated
    USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid()
        )
        OR (organization_id IS NULL AND user_id = auth.uid()) -- Legacy user data
    );

CREATE POLICY "Organization members can delete products" ON products
    FOR DELETE TO authenticated
    USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid()
        )
        OR (organization_id IS NULL AND user_id = auth.uid()) -- Legacy user data
    );

-- Colors policies (organization-based)
CREATE POLICY "Organization members can view colors" ON colors
    FOR SELECT TO authenticated
    USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid()
        )
        OR (organization_id IS NULL AND user_id = auth.uid()) -- Legacy user data
    );

CREATE POLICY "Organization members can create colors" ON colors
    FOR INSERT TO authenticated
    WITH CHECK (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Organization members can update colors" ON colors
    FOR UPDATE TO authenticated
    USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid()
        )
        OR (organization_id IS NULL AND user_id = auth.uid()) -- Legacy user data
    );

CREATE POLICY "Organization members can delete colors" ON colors
    FOR DELETE TO authenticated
    USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid()
        )
        OR (organization_id IS NULL AND user_id = auth.uid()) -- Legacy user data
    );

-- Product colors policies
CREATE POLICY "Organization members can manage product colors" ON product_colors
    FOR ALL TO authenticated
    USING (
        product_id IN (
            SELECT id FROM products 
            WHERE organization_id IN (
                SELECT organization_id FROM organization_members 
                WHERE user_id = auth.uid()
            )
            OR (organization_id IS NULL AND user_id = auth.uid()) -- Legacy user data
        )
    );

-- Sync metadata policies
CREATE POLICY "Organization members can manage sync data" ON sync_metadata
    FOR ALL TO authenticated
    USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid()
        )
        OR (organization_id IS NULL AND user_id = auth.uid()) -- Legacy user data
    );

-- ========================================
-- 6. Update batch functions for organization context
-- ========================================

-- Updated batch upsert function with organization support
CREATE OR REPLACE FUNCTION batch_upsert_colors(
    p_user_id UUID,
    p_organization_id UUID,
    p_colors JSONB
) RETURNS VOID AS $$
BEGIN
    -- Verify user is member of organization
    IF NOT EXISTS (
        SELECT 1 FROM organization_members 
        WHERE user_id = p_user_id AND organization_id = p_organization_id
    ) THEN
        RAISE EXCEPTION 'User is not a member of this organization';
    END IF;

    -- Batch insert with conflict handling
    INSERT INTO colors (external_id, user_id, organization_id, source_id, code, hex, display_name, color_spaces, properties, device_id)
    SELECT 
        (value->>'external_id')::UUID,
        p_user_id,
        p_organization_id,
        COALESCE((value->>'source_id')::INTEGER, 1),
        value->>'code',
        value->>'hex',
        value->>'display_name',
        value->'color_spaces',
        value->'properties',
        value->>'device_id'
    FROM jsonb_array_elements(p_colors)
    ON CONFLICT (user_id, source_id, code) 
    DO UPDATE SET
        organization_id = EXCLUDED.organization_id,
        hex = EXCLUDED.hex,
        display_name = EXCLUDED.display_name,
        color_spaces = EXCLUDED.color_spaces,
        properties = EXCLUDED.properties,
        device_id = EXCLUDED.device_id,
        updated_at = NOW(),
        sync_version = colors.sync_version + 1;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 7. Utility functions for organization management
-- ========================================

-- Function to create organization and add owner
CREATE OR REPLACE FUNCTION create_organization_with_owner(
    p_name TEXT,
    p_slug TEXT,
    p_owner_id UUID
) RETURNS UUID AS $$
DECLARE
    org_id UUID;
BEGIN
    -- Create organization
    INSERT INTO organizations (name, slug)
    VALUES (p_name, p_slug)
    RETURNING id INTO org_id;
    
    -- Add owner as first member
    INSERT INTO organization_members (organization_id, user_id, role)
    VALUES (org_id, p_owner_id, 'owner');
    
    RETURN org_id;
END;
$$ LANGUAGE plpgsql;

-- Function to invite user to organization
CREATE OR REPLACE FUNCTION invite_user_to_organization(
    p_organization_id UUID,
    p_email TEXT,
    p_role TEXT,
    p_invited_by UUID
) RETURNS TEXT AS $$
DECLARE
    invitation_token TEXT;
BEGIN
    -- Verify inviter has permission
    IF NOT EXISTS (
        SELECT 1 FROM organization_members 
        WHERE organization_id = p_organization_id 
        AND user_id = p_invited_by 
        AND role IN ('owner', 'admin')
    ) THEN
        RAISE EXCEPTION 'Insufficient permissions to invite users';
    END IF;
    
    -- Generate invitation token
    invitation_token := encode(gen_random_bytes(32), 'hex');
    
    -- Create invitation
    INSERT INTO organization_invitations (
        organization_id, 
        email, 
        role, 
        invited_by, 
        token, 
        expires_at
    ) VALUES (
        p_organization_id,
        p_email,
        p_role,
        p_invited_by,
        invitation_token,
        NOW() + INTERVAL '7 days'
    );
    
    RETURN invitation_token;
END;
$$ LANGUAGE plpgsql;

-- Function to accept organization invitation
CREATE OR REPLACE FUNCTION accept_organization_invitation(
    p_token TEXT,
    p_user_id UUID
) RETURNS UUID AS $$
DECLARE
    invitation RECORD;
BEGIN
    -- Get and validate invitation
    SELECT * INTO invitation
    FROM organization_invitations
    WHERE token = p_token
    AND expires_at > NOW()
    AND accepted_at IS NULL;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Invalid or expired invitation token';
    END IF;
    
    -- Add user to organization
    INSERT INTO organization_members (organization_id, user_id, role, invited_by)
    VALUES (invitation.organization_id, p_user_id, invitation.role, invitation.invited_by)
    ON CONFLICT (organization_id, user_id) DO NOTHING;
    
    -- Mark invitation as accepted
    UPDATE organization_invitations
    SET accepted_at = NOW()
    WHERE id = invitation.id;
    
    RETURN invitation.organization_id;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 8. Organization migration for existing users
-- ========================================

-- Function to migrate existing user data to organizations
CREATE OR REPLACE FUNCTION migrate_user_to_organization(
    p_user_id UUID,
    p_organization_name TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    org_id UUID;
    user_email TEXT;
    org_name TEXT;
    org_slug TEXT;
BEGIN
    -- Get user email for organization name
    SELECT email INTO user_email FROM auth.users WHERE id = p_user_id;
    
    -- Use provided name or generate from email
    org_name := COALESCE(p_organization_name, split_part(user_email, '@', 1) || '''s Workspace');
    org_slug := lower(regexp_replace(org_name, '[^a-zA-Z0-9\s]', '', 'g'));
    org_slug := regexp_replace(org_slug, '\s+', '-', 'g');
    
    -- Ensure unique slug
    WHILE EXISTS (SELECT 1 FROM organizations WHERE slug = org_slug) LOOP
        org_slug := org_slug || '-' || floor(random() * 1000)::text;
    END LOOP;
    
    -- Create organization with owner
    org_id := create_organization_with_owner(org_name, org_slug, p_user_id);
    
    -- Migrate existing data
    UPDATE products 
    SET organization_id = org_id 
    WHERE user_id = p_user_id AND organization_id IS NULL;
    
    UPDATE colors 
    SET organization_id = org_id 
    WHERE user_id = p_user_id AND organization_id IS NULL;
    
    UPDATE product_colors 
    SET organization_id = org_id 
    WHERE organization_id IS NULL 
    AND product_id IN (SELECT id FROM products WHERE user_id = p_user_id);
    
    UPDATE sync_metadata 
    SET organization_id = org_id 
    WHERE user_id = p_user_id AND organization_id IS NULL;
    
    RETURN org_id;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 9. Grant permissions
-- ========================================

GRANT ALL ON organizations TO authenticated;
GRANT ALL ON organization_members TO authenticated;
GRANT ALL ON organization_invitations TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- ========================================
-- 10. Update triggers for organization constraints
-- ========================================

-- Add organization validation trigger
CREATE OR REPLACE FUNCTION validate_organization_limits()
RETURNS TRIGGER AS $$
DECLARE
    org_plan TEXT;
    member_count INTEGER;
    color_count INTEGER;
BEGIN
    -- Get organization plan
    SELECT plan INTO org_plan 
    FROM organizations 
    WHERE id = NEW.organization_id;
    
    -- Check member limits for invitations
    IF TG_TABLE_NAME = 'organization_members' THEN
        SELECT COUNT(*) INTO member_count
        FROM organization_members
        WHERE organization_id = NEW.organization_id;
        
        -- Free tier: 3 members, Team tier: 10 members
        IF org_plan = 'free' AND member_count >= 3 THEN
            RAISE EXCEPTION 'Free tier organizations are limited to 3 members';
        ELSIF org_plan = 'team' AND member_count >= 10 THEN
            RAISE EXCEPTION 'Team tier organizations are limited to 10 members';
        END IF;
    END IF;
    
    -- Check color limits
    IF TG_TABLE_NAME = 'colors' THEN
        SELECT COUNT(*) INTO color_count
        FROM colors
        WHERE organization_id = NEW.organization_id;
        
        -- Free tier: 1000 colors, Team tier: 10000 colors
        IF org_plan = 'free' AND color_count >= 1000 THEN
            RAISE EXCEPTION 'Free tier organizations are limited to 1000 colors';
        ELSIF org_plan = 'team' AND color_count >= 10000 THEN
            RAISE EXCEPTION 'Team tier organizations are limited to 10000 colors';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply validation triggers
CREATE TRIGGER validate_organization_member_limits
    BEFORE INSERT ON organization_members
    FOR EACH ROW
    EXECUTE FUNCTION validate_organization_limits();

CREATE TRIGGER validate_organization_color_limits
    BEFORE INSERT ON colors
    FOR EACH ROW
    WHEN (NEW.organization_id IS NOT NULL)
    EXECUTE FUNCTION validate_organization_limits();

-- Cleanup expired invitations trigger
CREATE OR REPLACE FUNCTION cleanup_expired_invitations()
RETURNS VOID AS $$
BEGIN
    DELETE FROM organization_invitations
    WHERE expires_at < NOW() - INTERVAL '1 day'
    AND accepted_at IS NULL;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- Migration complete
-- ========================================

-- Add a comment to track migration
COMMENT ON TABLE organizations IS 'Multi-tenant organizations - Migration completed at ' || NOW();