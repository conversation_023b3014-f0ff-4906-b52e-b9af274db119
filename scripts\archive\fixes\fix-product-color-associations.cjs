const Database = require('better-sqlite3');
const path = require('path');

// Path to the database
const dbPath = '/Users/<USER>/Library/Application Support/chroma-sync/chromasync.db';

console.log('Opening database:', dbPath);
const db = new Database(dbPath);

// Enable foreign keys
db.pragma('foreign_keys = ON');

console.log('\n=== Fixing Product-Color Associations ===');

try {
  // Get all colors with product information in their properties
  const colorsWithProducts = db.prepare(`
    SELECT 
      c.id as color_id,
      c.external_id as color_external_id,
      c.code,
      c.display_name,
      c.properties,
      c.organization_id
    FROM colors c
    WHERE c.deleted_at IS NULL 
    AND c.properties IS NOT NULL
    AND c.properties != '{}'
  `).all();

  console.log(`Found ${colorsWithProducts.length} colors with properties`);

  let fixedCount = 0;
  let errorCount = 0;
  const errors = [];

  // Start transaction
  db.exec('BEGIN');

  for (const color of colorsWithProducts) {
    try {
      const properties = JSON.parse(color.properties);
      const productName = properties.product;

      if (!productName || typeof productName !== 'string' || productName.trim() === '') {
        continue;
      }

      // Find the product by name within the same organization
      const product = db.prepare(`
        SELECT id, external_id, name
        FROM products 
        WHERE name = ? AND is_active = 1 AND organization_id = ?
        LIMIT 1
      `).get(productName.trim(), color.organization_id);

      if (!product) {
        console.warn(`Product not found: ${productName} (for color ${color.code})`);
        continue;
      }

      // Check if association already exists
      const existingAssociation = db.prepare(`
        SELECT product_id FROM product_colors 
        WHERE product_id = ? AND color_id = ?
      `).get(product.id, color.color_id);

      if (existingAssociation) {
        // Association already exists
        continue;
      }

      // Get next display order for this product
      const maxOrder = db.prepare(`
        SELECT MAX(display_order) as max_order 
        FROM product_colors 
        WHERE product_id = ?
      `).get(product.id);

      const displayOrder = (maxOrder?.max_order || 0) + 1;

      // Create the association
      db.prepare(`
        INSERT INTO product_colors (
          product_id, 
          color_id, 
          display_order, 
          organization_id
        )
        VALUES (?, ?, ?, ?)
      `).run(
        product.id,
        color.color_id,
        displayOrder,
        color.organization_id
      );

      fixedCount++;
      console.log(`✓ Associated "${color.code}" with "${productName}"`);

    } catch (error) {
      errorCount++;
      const errorMsg = `Failed to process color ${color.code}: ${error.message}`;
      errors.push(errorMsg);
      console.error(`✗ ${errorMsg}`);
    }
  }

  // Commit transaction
  db.exec('COMMIT');

  console.log('\n=== Results ===');
  console.log(`✅ Fixed ${fixedCount} product-color associations`);
  console.log(`❌ Errors: ${errorCount}`);

  if (errors.length > 0) {
    console.log('\nErrors encountered:');
    errors.slice(0, 10).forEach(error => console.log(`  - ${error}`));
    if (errors.length > 10) {
      console.log(`  ... and ${errors.length - 10} more errors`);
    }
  }

  // Verify the fix
  console.log('\n=== Verification ===');
  const totalColors = db.prepare('SELECT COUNT(*) as count FROM colors WHERE deleted_at IS NULL').get();
  const totalProducts = db.prepare('SELECT COUNT(*) as count FROM products WHERE is_active = 1').get();
  const totalAssociations = db.prepare('SELECT COUNT(*) as count FROM product_colors').get();
  const orphanedColors = db.prepare(`
    SELECT COUNT(*) as count 
    FROM colors c
    WHERE c.deleted_at IS NULL
    AND NOT EXISTS (
      SELECT 1 FROM product_colors pc WHERE pc.color_id = c.id
    )
  `).get();

  console.log(`Total colors: ${totalColors.count}`);
  console.log(`Total products: ${totalProducts.count}`);
  console.log(`Total associations: ${totalAssociations.count}`);
  console.log(`Orphaned colors: ${orphanedColors.count}`);

  // Show some sample associations
  console.log('\n=== Sample Associations ===');
  const sampleAssociations = db.prepare(`
    SELECT 
      p.name as product_name,
      c.code as color_code,
      c.display_name as color_name,
      pc.display_order
    FROM product_colors pc
    JOIN products p ON pc.product_id = p.id
    JOIN colors c ON pc.color_id = c.id
    WHERE p.is_active = 1 AND c.deleted_at IS NULL
    ORDER BY p.name, pc.display_order
    LIMIT 10
  `).all();

  sampleAssociations.forEach(assoc => {
    console.log(`  ${assoc.product_name} → ${assoc.color_code} "${assoc.color_name}"`);
  });

} catch (error) {
  console.error('Fix failed:', error);
  db.exec('ROLLBACK');
} finally {
  db.close();
  console.log('\n✅ Product-color association fix complete');
}