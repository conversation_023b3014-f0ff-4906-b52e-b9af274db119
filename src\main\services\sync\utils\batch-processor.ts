/**
 * @file batch-processor.ts
 * @description Batch processing utilities for sync operations
 * 
 * This module provides efficient batch processing capabilities with
 * configurable batch sizes, parallel processing, and error handling.
 */

import { 
  SyncResult, 
  SyncError, 
  BatchSizeConfig,
  NetworkQuality 
} from '../core/sync-types';
import { getSyncConfig } from '../core/sync-config';

// ============================================================================
// BATCH PROCESSING TYPES
// ============================================================================

interface BatchProcessorOptions {
  maxConcurrency?: number;
  retryAttempts?: number;
  retryDelay?: number;
  onProgress?: (processed: number, total: number) => void;
  onBatchComplete?: (batchIndex: number, result: SyncResult) => void;
}

interface BatchResult<T> {
  success: boolean;
  processedItems: T[];
  failedItems: T[];
  results: SyncResult[];
  totalDuration: number;
  errors: SyncError[];
}

// ============================================================================
// BATCH PROCESSOR
// ============================================================================

/**
 * Advanced batch processor for sync operations
 */
export class BatchProcessor {
  private maxConcurrency: number;
  private retryAttempts: number;
  private retryDelay: number;

  constructor(options: BatchProcessorOptions = {}) {
    this.maxConcurrency = options.maxConcurrency || 3;
    this.retryAttempts = options.retryAttempts || 3;
    this.retryDelay = options.retryDelay || 1000;
  }

  /**
   * Process items in batches with configurable batch sizes
   */
  async processBatches<T>(
    items: T[],
    processor: (batch: T[]) => Promise<SyncResult>,
    batchSize: number,
    options: BatchProcessorOptions = {}
  ): Promise<BatchResult<T>> {
    const startTime = Date.now();
    const allResults: SyncResult[] = [];
    const allErrors: SyncError[] = [];
    const processedItems: T[] = [];
    const failedItems: T[] = [];

    try {
      console.log(`[BatchProcessor] Processing ${items.length} items in batches of ${batchSize}`);

      // Split items into batches
      const batches = this.createBatches(items, batchSize);
      
      // Process batches with concurrency control
      const batchResults = await this.processBatchesConcurrently(
        batches,
        processor,
        options
      );

      // Aggregate results
      for (let i = 0; i < batchResults.length; i++) {
        const result = batchResults[i];
        const batch = batches[i];
        
        allResults.push(result);
        allErrors.push(...result.errors);

        if (result.success) {
          processedItems.push(...batch);
        } else {
          failedItems.push(...batch);
        }
      }

      const totalDuration = Date.now() - startTime;
      const overallSuccess = failedItems.length === 0;

      console.log(`[BatchProcessor] Completed processing: ${processedItems.length} succeeded, ${failedItems.length} failed`);

      return {
        success: overallSuccess,
        processedItems,
        failedItems,
        results: allResults,
        totalDuration,
        errors: allErrors
      };

    } catch (error) {
      console.error('[BatchProcessor] Batch processing failed:', error);
      
      return {
        success: false,
        processedItems: [],
        failedItems: items,
        results: [],
        totalDuration: Date.now() - startTime,
        errors: [{
          id: `batch-processor-error-${Date.now()}`,
          timestamp: Date.now(),
          operation: 'batch processing',
          category: 'database',
          severity: 'high',
          message: `Batch processing failed: ${error.message}`,
          originalError: error,
          recoverable: true
        }]
      };
    }
  }

  /**
   * Process items with adaptive batch sizing based on network quality
   */
  async processWithAdaptiveBatching<T>(
    items: T[],
    processor: (batch: T[]) => Promise<SyncResult>,
    networkQuality: NetworkQuality,
    table: 'products' | 'colors' | 'relationships' = 'products',
    options: BatchProcessorOptions = {}
  ): Promise<BatchResult<T>> {
    const config = getSyncConfig();
    const optimizedBatchSizes = config.getOptimizedBatchSizes(networkQuality);
    
    let batchSize: number;
    switch (table) {
      case 'colors':
        batchSize = optimizedBatchSizes.colors;
        break;
      case 'relationships':
        batchSize = optimizedBatchSizes.relationships;
        break;
      default:
        batchSize = optimizedBatchSizes.products;
    }

    console.log(`[BatchProcessor] Using adaptive batch size ${batchSize} for ${networkQuality} network quality`);

    return this.processBatches(items, processor, batchSize, options);
  }

  /**
   * Process a single batch with retry logic
   */
  async processBatchWithRetry<T>(
    batch: T[],
    processor: (batch: T[]) => Promise<SyncResult>,
    batchIndex: number
  ): Promise<SyncResult> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        console.log(`[BatchProcessor] Processing batch ${batchIndex} (attempt ${attempt}/${this.retryAttempts})`);
        
        const result = await processor(batch);
        
        if (result.success) {
          return result;
        } else {
          // If processor returns failure but doesn't throw, treat as error
          lastError = new Error(`Batch processor returned failure: ${result.errors.map(e => e.message).join(', ')}`);
        }
        
      } catch (error) {
        lastError = error as Error;
        console.warn(`[BatchProcessor] Batch ${batchIndex} attempt ${attempt} failed:`, error);
      }
      
      // Wait before retry (except on last attempt)
      if (attempt < this.retryAttempts) {
        const delay = this.retryDelay * attempt; // Linear backoff
        console.log(`[BatchProcessor] Retrying batch ${batchIndex} in ${delay}ms...`);
        await this.delay(delay);
      }
    }
    
    // All attempts failed
    const errorMessage = lastError?.message || 'Unknown error';
    console.error(`[BatchProcessor] Batch ${batchIndex} failed after ${this.retryAttempts} attempts: ${errorMessage}`);
    
    return {
      success: false,
      itemsProcessed: batch.length,
      itemsSucceeded: 0,
      itemsFailed: batch.length,
      errors: [{
        id: `batch-retry-error-${Date.now()}-${batchIndex}`,
        timestamp: Date.now(),
        operation: `batch ${batchIndex} processing`,
        category: 'database',
        severity: 'high',
        message: `Batch failed after ${this.retryAttempts} attempts: ${errorMessage}`,
        originalError: lastError,
        recoverable: true
      }],
      duration: 0
    };
  }

  /**
   * Calculate optimal batch size based on item size and network conditions
   */
  calculateOptimalBatchSize(
    averageItemSize: number,
    networkQuality: NetworkQuality,
    maxBatchSizeBytes: number = 1024 * 1024 // 1MB default
  ): number {
    // Base batch size calculation
    const baseBatchSize = Math.floor(maxBatchSizeBytes / averageItemSize);
    
    // Adjust based on network quality
    let multiplier: number;
    switch (networkQuality) {
      case 'excellent':
        multiplier = 1.5;
        break;
      case 'good':
        multiplier = 1.0;
        break;
      case 'fair':
        multiplier = 0.7;
        break;
      case 'poor':
        multiplier = 0.4;
        break;
      case 'offline':
        multiplier = 0.1;
        break;
      default:
        multiplier = 1.0;
    }
    
    const optimalSize = Math.floor(baseBatchSize * multiplier);
    
    // Ensure minimum and maximum bounds
    return Math.max(1, Math.min(optimalSize, 1000));
  }

  /**
   * Estimate processing time for given items and batch size
   */
  estimateProcessingTime(
    itemCount: number,
    batchSize: number,
    averageBatchDuration: number = 2000 // 2 seconds default
  ): {
    estimatedDuration: number;
    numberOfBatches: number;
    batchesPerMinute: number;
  } {
    const numberOfBatches = Math.ceil(itemCount / batchSize);
    const sequentialDuration = numberOfBatches * averageBatchDuration;
    
    // Account for concurrency
    const concurrentDuration = Math.ceil(numberOfBatches / this.maxConcurrency) * averageBatchDuration;
    
    return {
      estimatedDuration: concurrentDuration,
      numberOfBatches,
      batchesPerMinute: 60000 / averageBatchDuration
    };
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Split items into batches of specified size
   */
  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    
    return batches;
  }

  /**
   * Process batches with concurrency control
   */
  private async processBatchesConcurrently<T>(
    batches: T[][],
    processor: (batch: T[]) => Promise<SyncResult>,
    options: BatchProcessorOptions
  ): Promise<SyncResult[]> {
    const results: SyncResult[] = new Array(batches.length);
    const semaphore = new Semaphore(this.maxConcurrency);
    
    // Create promises for all batches
    const batchPromises = batches.map(async (batch, index) => {
      await semaphore.acquire();
      
      try {
        const result = await this.processBatchWithRetry(batch, processor, index);
        results[index] = result;
        
        // Call progress callback
        if (options.onProgress) {
          const completed = results.filter(r => r !== undefined).length;
          options.onProgress(completed, batches.length);
        }
        
        // Call batch complete callback
        if (options.onBatchComplete) {
          options.onBatchComplete(index, result);
        }
        
        return result;
        
      } finally {
        semaphore.release();
      }
    });
    
    // Wait for all batches to complete
    await Promise.all(batchPromises);
    
    return results;
  }

  /**
   * Simple delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// ============================================================================
// SEMAPHORE FOR CONCURRENCY CONTROL
// ============================================================================

/**
 * Simple semaphore implementation for controlling concurrency
 */
class Semaphore {
  private permits: number;
  private waitQueue: Array<() => void> = [];

  constructor(permits: number) {
    this.permits = permits;
  }

  async acquire(): Promise<void> {
    if (this.permits > 0) {
      this.permits--;
      return Promise.resolve();
    }

    return new Promise<void>(resolve => {
      this.waitQueue.push(resolve);
    });
  }

  release(): void {
    this.permits++;
    
    if (this.waitQueue.length > 0) {
      const next = this.waitQueue.shift();
      if (next) {
        this.permits--;
        next();
      }
    }
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Create a batch processor with default configuration
 */
export function createBatchProcessor(options: BatchProcessorOptions = {}): BatchProcessor {
  return new BatchProcessor(options);
}

/**
 * Process items in a single batch (utility function)
 */
export async function processSingleBatch<T>(
  items: T[],
  processor: (batch: T[]) => Promise<SyncResult>
): Promise<SyncResult> {
  const batchProcessor = new BatchProcessor();
  const result = await batchProcessor.processBatches(items, processor, items.length);
  
  return result.results[0] || {
    success: false,
    itemsProcessed: 0,
    itemsSucceeded: 0,
    itemsFailed: items.length,
    errors: [],
    duration: 0
  };
}
