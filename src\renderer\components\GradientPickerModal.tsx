/**
 * @file GradientPickerModal.tsx
 * @description Modal component for creating and editing gradients with CMYK support
 * Enhanced to function as a complete form for adding/editing gradient colors
 */

import React, { useState, useEffect, useMemo } from 'react';
import { GradientInfo, GradientStop, ColorEntry } from '../../shared/types/color.types';
import { useColorStore } from '../store/color.store';
import { hexToCmyk } from '../../shared/utils/color';
import FormInput from './FormInput';

// Type for internal CMYK representation
interface CMYKValues {
  c: number;
  m: number;
  y: number;
  k: number;
}

// Internal extended gradient stop with structured CMYK values
interface GradientStopInternal {
  color: string;
  position: number;
  cmykObj: CMYKValues; // Use a different property name to avoid conflict
}

interface GradientPickerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (newColorId?: string) => void;  // Update callback to accept new color ID
  initialValue?: GradientInfo | null;
  editMode?: boolean;
  color?: ColorEntry;
}

// Helper function to convert CMYK to HEX
const cmykToHex = (c: number, m: number, y: number, k: number): string => {
  // Ensure values are in range 0-100
  c = Math.max(0, Math.min(100, c)) / 100;
  m = Math.max(0, Math.min(100, m)) / 100;
  y = Math.max(0, Math.min(100, y)) / 100;
  k = Math.max(0, Math.min(100, k)) / 100;
  
  // Convert to RGB
  const r = Math.round(255 * (1 - c) * (1 - k));
  const g = Math.round(255 * (1 - m) * (1 - k));
  const b = Math.round(255 * (1 - y) * (1 - k));
  
  // Convert to hex
  const toHex = (n: number): string => {
    const hex = n.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };
  
  return `#${toHex(r)}${toHex(g)}${toHex(b)}`.toUpperCase();
};

// Format CMYK object to string
const formatCmykToString = (cmyk: CMYKValues): string => {
  return `C${cmyk.c}, M${cmyk.m}, Y${cmyk.y}, K${cmyk.k}`;
};


const GradientPickerModal: React.FC<GradientPickerModalProps> = ({ 
  isOpen, 
  onClose, 
  onSuccess, 
  initialValue, 
  editMode = false,
  color
}) => {
  const { addColor, updateColor } = useColorStore();
  
  // Form state
  const [product, setProduct] = useState<string>(color?.product || '');
  const [name, setName] = useState<string>(color?.name || '');
  const [notes, setNotes] = useState<string>(color?.notes || '');
  const [error, setError] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  
  // Set up state for gradient properties
  const [gradientStops, setGradientStops] = useState<GradientStopInternal[]>([
    { color: '#0077CC', position: 0, cmykObj: { c: 100, m: 42, y: 0, k: 0 } },
    { color: '#FFFFFF', position: 100, cmykObj: { c: 0, m: 0, y: 0, k: 0 } }
  ]);

  // Validation state
  const [validation, setValidation] = useState<{
    product: boolean;
    name: boolean;
  }>({
    product: true,
    name: true
  });

  // Load initial values if provided
  useEffect(() => {
    if (initialValue?.gradientStops) {
      // Convert any existing gradient stops to include CMYK values
      const stopsWithCmyk = initialValue.gradientStops.map(stop => {
        const cmykValues = hexToCmyk(stop.color) || { c: 0, m: 0, y: 0, k: 0 };
        return { 
          color: stop.color, 
          position: stop.position, 
          cmykObj: cmykValues 
        };
      });
      
      setGradientStops(stopsWithCmyk);
    }
    
    if (color) {
      setProduct(color.product || '');
      setName(color.name || '');
      setNotes(color.notes || '');
    }
  }, [initialValue, color, isOpen]);

  // Generate CSS gradient string (fixed at 45deg)
  const gradientCSS = useMemo(() => {
    const sortedStops = [...gradientStops].sort((a, b) => a.position - b.position);
    const stopsString = sortedStops.map(stop => `${stop.color} ${stop.position}%`).join(', ');
    
    return `linear-gradient(45deg, ${stopsString})`;
  }, [gradientStops]);

  // Function to update a gradient stop's color
  const updateStopColor = (index: number, hex: string) => {
    const cmykValues = hexToCmyk(hex) || { c: 0, m: 0, y: 0, k: 0 };
    
    const newStops = [...gradientStops];
    newStops[index] = { 
      ...newStops[index], 
      color: hex,
      cmykObj: cmykValues
    };
    setGradientStops(newStops);
  };
  
  // Function to update a gradient stop's CMYK values
  const updateStopCMYK = (index: number, cmykValues: CMYKValues) => {
    const { c, m, y, k } = cmykValues;
    const hex = cmykToHex(c, m, y, k);
    
    const newStops = [...gradientStops];
    newStops[index] = { 
      ...newStops[index], 
      color: hex,
      cmykObj: { c, m, y, k }
    };
    setGradientStops(newStops);
  };

  // Function to update gradient stop position
  const updatePosition = (index: number, position: number) => {
    const newStops = [...gradientStops];
    newStops[index] = { 
      ...newStops[index], 
      position: Math.max(0, Math.min(100, position)) 
    };
    setGradientStops(newStops);
  };

  // Function to add a new gradient stop
  const addGradientStop = () => {
    if (!gradientStops || gradientStops.length >= 5) {return;} // Limit to 5 stops
    
    // Find a midpoint position
    const positions = gradientStops.map(stop => stop.position).sort((a, b) => a - b);
    const middlePosition = positions.length >= 2 
      ? Math.floor((positions[positions.length - 1] + positions[0]) / 2)
      : 50;
    
    // Default to medium gray for new stops
    const grayColor = '#808080';
    const cmykValues = hexToCmyk(grayColor) || { c: 0, m: 0, y: 0, k: 50 };
    
    setGradientStops([
      ...gradientStops, 
      { color: grayColor, position: middlePosition, cmykObj: cmykValues }
    ]);
  };

  // Function to remove a gradient stop
  const removeGradientStop = (index: number) => {
    if (!gradientStops || gradientStops.length <= 2) {return;} // Keep at least 2 stops
    const newStops = [...gradientStops];
    newStops.splice(index, 1);
    setGradientStops(newStops);
  };

  // Validate form fields
  const validateForm = (): boolean => {
    const newValidation = {
      product: product.trim().length > 0,
      name: name.trim().length > 0
    };
    
    setValidation(newValidation);
    
    return Object.values(newValidation).every(isValid => isValid);
  };

  // Create gradient info object
  const createGradientInfo = (): GradientInfo => {
    // Convert to regular GradientStop format for saving
    const regularStops: GradientStop[] = gradientStops.map(({ color, position, cmykObj }) => ({
      color,
      position,
      cmyk: formatCmykToString(cmykObj) // Convert CMYK object to string format for saving
    }));
    
    return {
      gradientStops: regularStops,
      gradientCSS
    };
  };

  // Handle save to database
  const handleSaveToDatabase = async () => {
    if (!validateForm()) {
      setError('Please fill out all required fields.');
      return;
    }
    
    setIsSubmitting(true);
    setError('');
    
    try {
      // Create gradient info
      const gradientInfo = createGradientInfo();
      
      // Determine representative color for the flat color fields
      const representativeColor = gradientStops[0].color;
      
      // Create color entry
      // For gradients, we'll use the first stop's CMYK values as the representative CMYK
      const firstStopCmyk = gradientStops[0].cmykObj;
      const representativeCmyk = `C:${firstStopCmyk.c} M:${firstStopCmyk.m} Y:${firstStopCmyk.y} K:${firstStopCmyk.k}`;
      
      const colorEntry: Partial<ColorEntry> = {
        product,
        name: name,
        hex: representativeColor, // Use first color as representative
        notes,
        gradient: gradientInfo,
        // Use a descriptive code and valid CMYK from first stop
        code: `Gradient-${Date.now()}`, // Unique identifier for the gradient
        cmyk: representativeCmyk // Valid CMYK format from first stop
      };
      
      if (editMode && color) {
        // Update existing color
        await updateColor(color.id, colorEntry);
        onSuccess?.();
      } else {
        // Add new color
        const newColor = await addColor(colorEntry as ColorEntry);
        if (newColor?.id) {
          console.log(`Successfully created gradient color: ${newColor.id}`);
          onSuccess?.(newColor.id); // Pass the new color ID for collection adding
        }
      }
      
      setIsSubmitting(false);
      onClose();
    } catch (err) {
      setIsSubmitting(false);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  };

  // Form header text
  const headerText = useMemo(() => {
    return {
      title: editMode ? 'Edit Gradient Color' : 'Add Gradient Color',
      description: editMode 
        ? 'Update the properties of this gradient color.' 
        : 'Create a new gradient color for your collection.'
    };
  }, [editMode]);

  if (!isOpen) {return null;}

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-ui-background-primary dark:bg-zinc-900 rounded-lg shadow-xl w-full max-w-xl overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-zinc-700 flex justify-between items-center">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {headerText.title}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {headerText.description}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none"
            aria-label="Close gradient editor"
          >
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Content */}
        <div className="px-6 py-4 max-h-[70vh] overflow-y-auto">
          {error && (
            <div 
              className="mb-4 bg-[#FFF5F5] dark:bg-[rgba(255,59,48,0.1)] p-3 rounded-lg border border-[rgba(255,59,48,0.2)] text-sm flex items-start text-feedback-error" 
              role="alert"
              aria-live="assertive"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>{error}</span>
            </div>
          )}
        
          {/* Product and Flavor fields */}
          <div className="grid grid-cols-1 gap-4 mb-4">
            <div className="w-full">
              <FormInput
                id="product"
                name="product"
                value={product}
                onChange={(e) => setProduct(e.target.value)}
                required
                placeholder="Product name"
                isValid={validation.product || product === ''}
                errorMessage="Product name is required"
                hideLabel={true}
                aria-label="Product Name"
                aria-required="true"
                aria-invalid={!validation.product && product !== ''}
                data-testid="product-input"
                inputClassName="px-4 py-3 text-gray-700 dark:text-gray-300"
              />
            </div>
            
            <div className="w-full">
              <FormInput
                id="name"
                name="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                placeholder="Color name"
                isValid={validation.name || name === ''}
                errorMessage="Color name is required"
                hideLabel={true}
                aria-label="Color Name"
                aria-required="true"
                aria-invalid={!validation.name && name !== ''}
                data-testid="name-input"
                inputClassName="px-4 py-3 text-gray-700 dark:text-gray-300"
              />
            </div>
          </div>
          
          {/* Gradient Preview */}
          <div 
            className="w-full h-24 rounded-lg mb-4"
            style={{ background: gradientCSS }}
            aria-label="Gradient preview"
          />
          
          {/* Direction note */}
          <div className="mb-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Linear gradient at 45° angle
            </p>
          </div>
          
          {/* Color Stops */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Color Stops
              </label>
              <button
                type="button"
                className="px-2 py-1 text-xs font-medium rounded bg-brand-primary text-white hover:bg-opacity-90"
                onClick={addGradientStop}
                disabled={!gradientStops || gradientStops.length >= 5}
                aria-label="Add gradient stop"
              >
                Add Stop
              </button>
            </div>
            
            <div className="space-y-4">
              {gradientStops.map((stop, index) => (
                <div key={index} className="bg-gray-50 dark:bg-zinc-800 p-3 rounded-md border border-gray-200 dark:border-zinc-700">
                  <div className="flex items-center space-x-2 mb-3">
                    <div 
                      className="w-8 h-8 rounded border border-gray-300 dark:border-gray-700 cursor-pointer flex-shrink-0"
                      style={{ backgroundColor: stop.color }}
                    >
                      <input
                        type="color"
                        value={stop.color}
                        onChange={(e) => updateStopColor(index, e.target.value)}
                        className="opacity-0 w-full h-full cursor-pointer"
                        aria-label={`Color picker for stop ${index + 1}`}
                      />
                    </div>
                    
                    <div className="flex-1">
                      <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">Position (%)</label>
                      <div className="flex items-center space-x-2">
                        <input
                          type="range"
                          min="0"
                          max="100"
                          value={stop.position}
                          onChange={(e) => updatePosition(index, parseInt(e.target.value))}
                          className="flex-1 h-2 bg-gray-200 dark:bg-zinc-700 rounded-lg appearance-none cursor-pointer"
                        />
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={stop.position}
                          onChange={(e) => updatePosition(index, parseInt(e.target.value) || 0)}
                          className="w-16 px-2 py-1 text-sm border border-gray-300 dark:border-zinc-600 rounded-md bg-white dark:bg-zinc-800 text-gray-700 dark:text-gray-300"
                        />
                      </div>
                    </div>
                    
                    <button
                      type="button"
                      onClick={() => removeGradientStop(index)}
                      className="text-gray-400 hover:text-red-500 self-start"
                      disabled={!gradientStops || gradientStops.length <= 2}
                      aria-label={`Remove stop ${index + 1}`}
                    >
                      <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {/* Hex Color */}
                    <div>
                      <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">Hex Color</label>
                      <input
                        type="text"
                        value={stop.color}
                        onChange={(e) => updateStopColor(index, e.target.value)}
                        className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-zinc-600 rounded-md bg-white dark:bg-zinc-800 text-gray-700 dark:text-gray-300"
                        placeholder="e.g. #4A90E2"
                      />
                    </div>
                    
                    {/* CMYK Values */}
                    <div>
                      <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">CMYK Values</label>
                      <div className="grid grid-cols-4 gap-1">
                        {['c', 'm', 'y', 'k'].map((channel) => (
                          <div key={`${index}-${channel}`}>
                            <input
                              type="number"
                              min="0"
                              max="100"
                              value={stop.cmykObj[channel as keyof CMYKValues]}
                              onChange={(e) => {
                                const value = Math.max(0, Math.min(100, parseInt(e.target.value) || 0));
                                const newCmyk = { ...stop.cmykObj, [channel]: value };
                                updateStopCMYK(index, newCmyk);
                              }}
                              className="w-full px-1 py-2 text-sm border border-gray-300 dark:border-zinc-600 rounded-md bg-white dark:bg-zinc-800 text-gray-700 dark:text-gray-300"
                              aria-label={`${channel.toUpperCase()} value`}
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* Notes Field */}
          <div className="w-full mb-4">
            <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">Additional Notes</label>
            <textarea 
              id="notes"
              name="notes" 
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Add any details about this gradient color here..."
              className="w-full p-3 border border-gray-200 dark:border-zinc-700 rounded-md bg-gray-50 dark:bg-zinc-800 focus:outline-none focus:ring-1 focus:ring-pantone-red focus:border-transparent transition-all resize-none text-gray-600 dark:text-gray-300 text-xs"
              rows={3}
              aria-label="Additional Notes"
              data-testid="notes-input"
            />
          </div>
        </div>
        
        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-zinc-700 flex justify-end space-x-3">
          <button
            type="button"
            className="px-4 py-2 text-sm font-medium rounded-md border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            type="button"
            className="px-4 py-2 text-sm font-medium rounded-md bg-brand-primary text-white hover:bg-opacity-90"
            onClick={handleSaveToDatabase}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
                </svg>
                Saving...
              </span>
            ) : (
              <span>
                {editMode ? 'Update Gradient' : 'Save Gradient'}
              </span>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default GradientPickerModal; 