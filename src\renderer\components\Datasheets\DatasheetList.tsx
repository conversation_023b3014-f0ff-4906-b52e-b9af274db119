/**
 * @file DatasheetList.tsx
 * @description Component for displaying and managing datasheets
 */

import React, { useState, useEffect } from 'react';
import { Trash2, FileText, ChevronDown, ChevronUp, Globe } from 'lucide-react';
import { useDatasheetStore } from '../../store/datasheet.store';
import { DatasheetEntry } from '../../../shared/types/datasheet.types';
import { isWebUrl } from '../../utils/url-utils';
import WebLinkDialog from '../WebLinkDialog';
import Modal from '../ui/Modal';

interface DatasheetListProps {
  productId: string;
  productName: string;
}

const DatasheetList: React.FC<DatasheetListProps> = ({ productId, productName }) => {
  const [showDatasheetList, setShowDatasheetList] = useState(false);
  const [showWebLinkDialog, setShowWebLinkDialog] = useState(false);
  const [showDeleteDatasheetConfirm, setShowDeleteDatasheetConfirm] = useState<string | null>(null);

  const {
    datasheets,
    getDatasheetsByProduct,
    addWebLinkToProduct,
    removeDatasheet,
    openDatasheet
  } = useDatasheetStore();

  // Get datasheets for this product
  const productDatasheets = datasheets[productId] || [];

  // Load datasheets when the component mounts or productId changes
  useEffect(() => {
    if (productId) {
      getDatasheetsByProduct(productId);
    }
  }, [productId, getDatasheetsByProduct]);



  // Handle adding web link
  const handleWebLinkSubmit = async (url: string, displayName: string) => {
    if (!productId) {return;}

    try {
      const result = await addWebLinkToProduct(productId, url, displayName);
      if (result) {
        console.log('Web link added successfully:', result);
        setShowWebLinkDialog(false);
      }
    } catch (err) {
      console.error('Failed to add web link:', err);
    }
  };

  // Handle removing datasheet
  const handleRemoveDatasheet = async (datasheetId: string) => {
    try {
      console.log('Removing datasheet with ID:', datasheetId);
      const success = await removeDatasheet(datasheetId);
      console.log('Datasheet removal result:', success);

      // Close the confirmation dialog
      setShowDeleteDatasheetConfirm(null);
    } catch (err) {
      console.error('Failed to remove datasheet:', err);
    }
  };

  // Handle opening a datasheet
  const handleOpenDatasheet = async (datasheetId: string) => {
    try {
      // Validate input
      if (!datasheetId || typeof datasheetId !== 'string') {
        console.error('Invalid datasheetId provided to handleOpenDatasheet:', datasheetId);
        return;
      }

      console.log(`Attempting to open datasheet with ID: ${datasheetId}`);
      const result = await openDatasheet(datasheetId);

      if (!result.success) {
        console.error('Failed to open datasheet:', result.message);
      } else {
        console.log('Successfully opened datasheet');
      }
    } catch (err) {
      console.error('Error opening datasheet:', err);
    }
  };

  // Compact datasheet item component
  const CompactDatasheetItem = ({ datasheet }: { datasheet: DatasheetEntry }) => {
    // Validate datasheet object
    if (!datasheet || !datasheet.id) {
      console.error('Invalid datasheet object:', datasheet);
      return null;
    }

    // Ensure path is defined
    const path = datasheet.path || '';
    const name = datasheet.name || 'Unnamed Datasheet';
    const fileType = datasheet.fileType || 'other';

    return (
      <li
        className="px-4 py-2 hover:bg-ui-background-tertiary/30 dark:hover:bg-zinc-700/50 transition-colors cursor-pointer"
        onClick={() => handleOpenDatasheet(datasheet.id)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center min-w-0">
            {isWebUrl(path) ? (
              <Globe className="h-4 w-4 text-blue-500 mr-2 flex-shrink-0" />
            ) : fileType === 'pdf' ? (
              <svg className="h-4 w-4 text-red-500 mr-2 flex-shrink-0" viewBox="0 0 24 24" fill="currentColor">
                <path d="M7,3C5.9,3 5,3.9 5,5V19C5,20.1 5.9,21 7,21H17C18.1,21 19,20.1 19,19V5C19,3.9 18.1,3 17,3H7M7,5H17V19H7V5M9,8V10H15V8H9M9,12V14H15V12H9M9,16V18H13V16H9Z" />
              </svg>
            ) : fileType === 'docx' ? (
              <svg className="h-4 w-4 text-blue-600 mr-2 flex-shrink-0" viewBox="0 0 24 24" fill="currentColor">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20M9,13H15V15H9V13M9,9H15V11H9V9M9,17H15V19H9V17Z" />
              </svg>
            ) : fileType === 'xlsx' ? (
              <svg className="h-4 w-4 text-green-600 mr-2 flex-shrink-0" viewBox="0 0 24 24" fill="currentColor">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20M10,13H8V11H10V13M14,13H16V11H14V13M10,17H8V15H10V17M14,17H16V15H14V17Z" />
              </svg>
            ) : (
              <FileText className="h-4 w-4 text-ui-muted dark:text-gray-400 mr-2 flex-shrink-0" />
            )}
            <span
              className="text-xs font-medium text-ui-foreground-primary dark:text-white truncate"
              title={isWebUrl(path) ? `${name} (Web Link: ${path})` : name}
            >
              {name}
              {isWebUrl(path) && (
                <span className="ml-1 text-xs text-blue-500">(Web)</span>
              )}
            </span>
          </div>
          <button
            onClick={(e) => {
              e.stopPropagation(); // Prevent the click from bubbling up to the li element
              setShowDeleteDatasheetConfirm(datasheet.id);
            }}
            className="ml-2 p-1 text-ui-muted dark:text-gray-400 hover:text-red-500 rounded-full hover:bg-ui-background-tertiary dark:hover:bg-zinc-600 transition-colors"
            title="Remove datasheet"
          >
            <Trash2 size={12} />
          </button>
        </div>
      </li>
    );
  };

  return (
    <div>
      {/* Compact Datasheets section */}
      <div className="border-b border-ui-border-light dark:border-zinc-700 bg-ui-background-secondary dark:bg-zinc-800">
        <div
          className="px-6 py-2 flex items-center justify-between cursor-pointer hover:bg-ui-background-tertiary/30 dark:hover:bg-zinc-700/50 transition-colors"
          onClick={() => setShowDatasheetList(!showDatasheetList)}
        >
          <div className="flex items-center">
            <FileText className="h-4 w-4 text-ui-muted dark:text-gray-400 mr-2" />
            <span className="text-sm font-medium text-ui-foreground-primary dark:text-white">
              Datasheets
              {productDatasheets.length > 0 ?
                <span className="ml-2 text-xs bg-ui-background-tertiary dark:bg-zinc-700 px-2 py-0.5 rounded-full">
                  {productDatasheets.length}
                </span> : null
              }
            </span>
          </div>
          <div className="flex items-center">

            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowWebLinkDialog(true);
              }}
              className="inline-flex items-center px-2 py-1 text-xs font-medium rounded bg-ui-background-tertiary dark:bg-zinc-700 text-ui-foreground-primary dark:text-white hover:bg-ui-background-tertiary-hover dark:hover:bg-zinc-600 transition-colors"
              title="Add web link"
            >
              <Globe size={12} className="mr-1" />
              Add link
            </button>
            {showDatasheetList ?
              <ChevronUp size={16} className="ml-2 text-ui-muted dark:text-gray-400" /> :
              <ChevronDown size={16} className="ml-2 text-ui-muted dark:text-gray-400" />
            }
          </div>
        </div>

        {/* Collapsible datasheet list */}
        {showDatasheetList && (
          <div className="border-t border-ui-border-light/50 dark:border-zinc-700/50 mx-6 pb-2">
            {productDatasheets.length === 0 ? (
              <div className="text-center py-2">
                <p className="text-xs text-ui-muted dark:text-gray-400">No datasheets attached</p>
              </div>
            ) : (
              <ul className="max-h-40 overflow-y-auto">
                {productDatasheets.map((datasheet, index) => (
                  <CompactDatasheetItem key={datasheet.id || `datasheet-${index}`} datasheet={datasheet} />
                ))}
              </ul>
            )}
          </div>
        )}
      </div>

      {/* Delete Datasheet Confirmation Modal */}
      {showDeleteDatasheetConfirm && (
        <Modal
          isOpen={!!showDeleteDatasheetConfirm}
          onClose={() => setShowDeleteDatasheetConfirm(null)}
          title="Remove Datasheet"
        >
          <div className="p-4">
            <p className="text-sm text-ui-foreground-primary dark:text-white mb-4">
              Are you sure you want to remove this datasheet? This action cannot be undone.
            </p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setShowDeleteDatasheetConfirm(null)}
                className="px-3 py-1 text-sm rounded bg-ui-background-tertiary dark:bg-zinc-700 text-ui-foreground-primary dark:text-white hover:bg-ui-background-tertiary-hover dark:hover:bg-zinc-600 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => showDeleteDatasheetConfirm && handleRemoveDatasheet(showDeleteDatasheetConfirm)}
                className="px-3 py-1 text-sm rounded bg-red-500 text-white hover:bg-red-600 transition-colors"
              >
                Remove
              </button>
            </div>
          </div>
        </Modal>
      )}

      {/* Web Link Dialog */}
      <WebLinkDialog
        isOpen={showWebLinkDialog}
        onClose={() => setShowWebLinkDialog(false)}
        onSubmit={handleWebLinkSubmit}
        initialName={`${productName} Specification`}
      />
    </div>
  );
};

export default DatasheetList;
