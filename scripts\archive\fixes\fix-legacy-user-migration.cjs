/**
 * Fix Legacy User Migration - Add creator as organization owner
 * This script fixes the data migration issue where the original developer
 * wasn't properly added as a member of their organization.
 */

const Database = require('better-sqlite3');
const path = require('path');

async function fixLegacyUserMigration() {
  const dbPath = '/Users/<USER>/Library/Application Support/ChromaSync/chromasync.db';
  console.log('Opening database:', dbPath);
  
  const db = new Database(dbPath);
  
  try {
    // Get the IVG organization
    const org = db.prepare(`
      SELECT id, external_id, name FROM organizations 
      WHERE name = 'IVG' OR slug = 'ivg'
      LIMIT 1
    `).get();
    
    if (!org) {
      console.error('❌ IVG organization not found');
      return;
    }
    
    console.log('✅ Found organization:', org);
    
    // Check if there are any users in the system
    const users = db.prepare('SELECT * FROM users').all();
    console.log('📊 Users in system:', users.length);
    
    if (users.length === 0) {
      console.log('⚠️  No users found. You may need to authenticate first.');
      return;
    }
    
    // Check current members of the organization
    const existingMembers = db.prepare(`
      SELECT om.*, u.email 
      FROM organization_members om 
      LEFT JOIN users u ON om.user_id = u.id
      WHERE om.organization_id = ?
    `).all(org.id);
    
    console.log('📊 Existing members:', existingMembers.length);
    existingMembers.forEach(member => {
      console.log(`  - ${member.email || member.user_id} (${member.role})`);
    });
    
    // If no members, we need to add you as the owner
    if (existingMembers.length === 0) {
      console.log('🔧 No members found. Adding creator as owner...');
      
      // For legacy migration, we'll assume the first user is the creator
      // Or if there's only one user, that's definitely you
      let creatorUser = users[0];
      
      if (users.length > 1) {
        // If multiple users, look for one with your email pattern
        const probableCreator = users.find(u => 
          u.email?.includes('michael') || 
          u.email?.includes('chromasync') ||
          u.email?.includes('ivg')
        );
        if (probableCreator) {
          creatorUser = probableCreator;
        }
      }
      
      console.log('👤 Adding user as owner:', creatorUser.email || creatorUser.id);
      
      // Add the creator as organization owner
      db.prepare(`
        INSERT INTO organization_members (organization_id, user_id, role, joined_at)
        VALUES (?, ?, 'owner', CURRENT_TIMESTAMP)
      `).run(org.id, creatorUser.id);
      
      console.log('✅ Successfully added creator as organization owner');
      
    } else {
      console.log('✅ Organization already has members');
      
      // Check if any member has owner role
      const hasOwner = existingMembers.some(m => m.role === 'owner');
      if (!hasOwner) {
        console.log('⚠️  No owner found. Promoting first member to owner...');
        const firstMember = existingMembers[0];
        
        db.prepare(`
          UPDATE organization_members 
          SET role = 'owner' 
          WHERE organization_id = ? AND user_id = ?
        `).run(org.id, firstMember.user_id);
        
        console.log('✅ Promoted first member to owner');
      }
    }
    
    // Verify the fix
    const finalMembers = db.prepare(`
      SELECT om.*, u.email 
      FROM organization_members om 
      LEFT JOIN users u ON om.user_id = u.id
      WHERE om.organization_id = ?
    `).all(org.id);
    
    console.log('\n🎉 Final member list:');
    finalMembers.forEach(member => {
      console.log(`  - ${member.email || member.user_id} (${member.role})`);
    });
    
    console.log('\n✅ Legacy user migration complete!');
    console.log('   You should now see the invite option in Settings → Team');
    
  } catch (error) {
    console.error('❌ Error during migration:', error);
  } finally {
    db.close();
  }
}

// Run the migration
fixLegacyUserMigration().catch(console.error);