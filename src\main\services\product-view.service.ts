/**
 * @file product-view.service.ts
 * @description Service for product view operations
 */

import Database from 'better-sqlite3';
import { ColorService } from '../db/services/color.service'; // Corrected import path
import { ColorEntry } from '../../shared/types/color.types';
import { Product, ProductWithColors, DatasheetEntry } from '../../shared/types/product.types';

export class ProductViewService {
  private db: Database.Database;
  private colorService: ColorService;

  constructor(db: Database.Database, colorService: ColorService) {
    this.db = db;
    this.colorService = colorService;
  }

  /**
   * Get all products with colors and datasheets
   */
  getAllProductsWithColorsAndDatasheets(): ProductWithColors[] {
    try {
      console.log('[ProductViewService] Getting all products with colors and datasheets');

      // Get all products
      const products = this.getAllProducts();

      // Add colors and datasheets to each product
      return products.map(product => ({
        ...product,
        colors: this.getProductColors(product.id),
        datasheets: this.getProductDatasheets(product.id)
      }));
    } catch (error) {
      console.error('[ProductViewService] Error getting all products with colors and datasheets:', error);
      return [];
    }
  }

  /**
   * Get a product by ID with colors and datasheets
   */
  getProductByIdWithColorsAndDatasheets(productId: string): ProductWithColors | null {
    try {
      console.log(`[ProductViewService] Getting product ${productId} with colors and datasheets`);

      // Get the product
      const product = this.getProductById(productId);
      if (!product) {
        console.log(`[ProductViewService] Product ${productId} not found`);
        return null;
      }

      // Add colors and datasheets
      return {
        ...product,
        colors: this.getProductColors(productId),
        datasheets: this.getProductDatasheets(productId)
      };
    } catch (error) {
      console.error(`[ProductViewService] Error getting product ${productId} with colors and datasheets:`, error);
      return null;
    }
  }

  /**
   * Add a color to a product
   */
  addColorToProduct(productId: string, colorId: string): { success: boolean; message: string } {
    try {
      console.log(`[ProductViewService] Adding color ${colorId} to product ${productId}`);

      // Check if the product exists
      const product = this.getProductById(productId);
      if (!product) {
        console.log(`[ProductViewService] Product ${productId} not found`);
        return { success: false, message: 'Product not found' };
      }

      // Check if the color exists
      const color = this.colorService.getById(colorId);
      if (!color) {
        console.log(`[ProductViewService] Color ${colorId} not found`);
        return { success: false, message: 'Color not found' };
      }

      // Check if the color is already in the product
      const existingColor = this.db.prepare(`
        SELECT 1 FROM product_colors
        WHERE product_id = ? AND color_id = ?
      `).get(productId, colorId);

      if (existingColor) {
        console.log(`[ProductViewService] Color ${colorId} is already in product ${productId}`);
        return { success: true, message: 'Color is already in product' };
      }

      // Add the color to the product
      const now = new Date().toISOString();
      this.db.prepare(`
        INSERT INTO product_colors (product_id, color_id, added_at)
        VALUES (?, ?, ?)
      `).run(productId, colorId, now);

      console.log(`[ProductViewService] Added color ${colorId} to product ${productId}`);
      return { success: true, message: 'Color added to product' };
    } catch (error) {
      console.error(`[ProductViewService] Error adding color ${colorId} to product ${productId}:`, error);
      return { success: false, message: `Error adding color to product: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  /**
   * Remove a color from a product
   */
  removeColorFromProduct(productId: string, colorId: string): { success: boolean; message: string } {
    try {
      console.log(`[ProductViewService] Removing color ${colorId} from product ${productId}`);

      // Check if the product exists
      const product = this.getProductById(productId);
      if (!product) {
        console.log(`[ProductViewService] Product ${productId} not found`);
        return { success: false, message: 'Product not found' };
      }

      // Check if the color exists
      const color = this.colorService.getById(colorId);
      if (!color) {
        console.log(`[ProductViewService] Color ${colorId} not found`);
        return { success: false, message: 'Color not found' };
      }

      // Remove the color from the product
      const result = this.db.prepare(`
        DELETE FROM product_colors
        WHERE product_id = ? AND color_id = ?
      `).run(productId, colorId);

      if (result.changes === 0) {
        console.log(`[ProductViewService] Color ${colorId} is not in product ${productId}`);
        return { success: false, message: 'Color is not in product' };
      }

      console.log(`[ProductViewService] Removed color ${colorId} from product ${productId}`);
      return { success: true, message: 'Color removed from product' };
    } catch (error) {
      console.error(`[ProductViewService] Error removing color ${colorId} from product ${productId}:`, error);
      return { success: false, message: `Error removing color from product: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  /**
   * Get all products
   */
  private getAllProducts(): Product[] {
    try {
      console.log('[ProductViewService] Getting all products');

      const products = this.db.prepare(`
        SELECT id, name, description, createdAt, updatedAt FROM products ORDER BY name ASC
      `).all() as any[];

      return products.map(row => ({
        id: row.id,
        name: row.name,
        description: row.description,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt
      }));
    } catch (error) {
      console.error('[ProductViewService] Error getting all products:', error);
      return [];
    }
  }

  /**
   * Get a product by ID
   */
  private getProductById(productId: string): Product | null {
    try {
      console.log(`[ProductViewService] Getting product ${productId}`);

      const row = this.db.prepare(`
        SELECT id, name, description, createdAt, updatedAt FROM products WHERE id = ?
      `).get(productId) as any;

      if (!row) {
        return null;
      }

      return {
        id: row.id,
        name: row.name,
        description: row.description,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt
      };
    } catch (error) {
      console.error(`[ProductViewService] Error getting product ${productId}:`, error);
      return null;
    }
  }

  /**
   * Get colors for a product
   */
  private getProductColors(productId: string): ColorEntry[] {
    try {
      console.log(`[ProductViewService] Getting colors for product ${productId}`);

      // Get color IDs from product_colors table
      const colorIds = this.db.prepare(`
        SELECT color_id FROM product_colors
        WHERE product_id = ?
        ORDER BY added_at DESC
      `).all(productId) as { color_id: string }[];

      // Get full color objects
      const colors: ColorEntry[] = [];
      for (const { color_id } of colorIds) {
        const color = this.colorService.getById(color_id);
        if (color) {
          colors.push(color);
        }
      }

      console.log(`[ProductViewService] Found ${colors.length} colors for product ${productId}`);
      return colors;
    } catch (error) {
      console.error(`[ProductViewService] Error getting colors for product ${productId}:`, error);
      return [];
    }
  }

  /**
   * Get datasheets for a product
   */
  private getProductDatasheets(productId: string): DatasheetEntry[] {
    try {
      console.log(`[ProductViewService] Getting datasheets for product ${productId}`);

      // Check if the datasheets table exists and has the expected schema
      const tableInfo = this.db.prepare("PRAGMA table_info(datasheets)").all() as { name: string }[];
      if (tableInfo.length === 0) {
        console.log(`[ProductViewService] Datasheets table does not exist`);
        return [];
      }

      const columnNames = tableInfo.map(col => col.name);
      console.log(`[ProductViewService] Datasheets table columns: ${columnNames.join(', ')}`);

      // Handle different schema versions
      if (!columnNames.includes('product_id')) {
        console.log(`[ProductViewService] Datasheets table does not have product_id column`);
        return [];
      }

      const query = `
        SELECT
          id,
          name,
          path,
          fileType,
          dateAdded
        FROM datasheets
        WHERE product_id = ?
        ORDER BY dateAdded DESC
      `;

      const datasheets = this.db.prepare(query).all(productId) as DatasheetEntry[];

      console.log(`[ProductViewService] Found ${datasheets.length} datasheets for product ${productId}`);
      return datasheets;
    } catch (error) {
      console.error(`[ProductViewService] Error getting datasheets for product ${productId}:`, error);
      return [];
    }
  }
}
