-- Migration 010: Add organization_id to product_colors table
-- This migration adds the missing organization_id column to product_colors
-- and populates it based on the product's organization

-- Add organization_id column to product_colors
ALTER TABLE product_colors ADD COLUMN organization_id INTEGER REFERENCES organizations(id);

-- Populate organization_id based on the product's organization
UPDATE product_colors 
SET organization_id = (
  SELECT p.organization_id 
  FROM products p 
  WHERE p.id = product_colors.product_id
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_product_colors_org ON product_colors(organization_id);

-- Add constraint to ensure organization_id is not null for future inserts
-- Note: SQLite doesn't support adding NOT NULL constraints to existing columns,
-- so we'll handle this in the application layer