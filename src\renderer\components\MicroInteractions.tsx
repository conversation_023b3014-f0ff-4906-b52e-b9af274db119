import React, { useState, useEffect, useRef, useCallback } from 'react';

interface MicroInteractionProps {
  children: React.ReactNode;
  type?: 'button' | 'card' | 'swatch' | 'tab' | 'list-item';
  effect?: 'hover' | 'click' | 'focus' | 'select' | 'loading';
  disabled?: boolean;
  className?: string;
  onClick?: () => void;
  onHover?: (isHovered: boolean) => void;
}

interface RippleEffect {
  id: string;
  x: number;
  y: number;
  timestamp: number;
}

interface TooltipProps {
  content: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
  children: React.ReactNode;
}

interface FeedbackProps {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number;
  onClose?: () => void;
}

// Main Micro-Interaction Component
export function MicroInteraction({
  children,
  type = 'button',
  effect = 'hover',
  disabled = false,
  className = '',
  onClick,
  onHover
}: MicroInteractionProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [ripples, setRipples] = useState<RippleEffect[]>([]);
  const elementRef = useRef<HTMLDivElement>(null);

  const handleMouseEnter = useCallback(() => {
    if (disabled) {return;}
    setIsHovered(true);
    onHover?.(true);
  }, [disabled, onHover]);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
    setIsPressed(false);
    onHover?.(false);
  }, [onHover]);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (disabled) {return;}
    setIsPressed(true);
    
    // Create ripple effect
    if (effect === 'click' && elementRef.current) {
      const rect = elementRef.current.getBoundingClientRect();
      const ripple: RippleEffect = {
        id: `ripple-${Date.now()}-${Math.random()}`,
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
        timestamp: Date.now()
      };
      setRipples(prev => [...prev, ripple]);
      
      // Remove ripple after animation
      setTimeout(() => {
        setRipples(prev => prev.filter(r => r.id !== ripple.id));
      }, 600);
    }
  }, [disabled, effect]);

  const handleMouseUp = useCallback(() => {
    setIsPressed(false);
  }, []);

  const handleClick = useCallback(() => {
    if (disabled) {return;}
    onClick?.();
  }, [disabled, onClick]);

  const handleFocus = useCallback(() => {
    if (disabled) {return;}
    setIsFocused(true);
  }, [disabled]);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
  }, []);

  const getInteractionClasses = () => {
    const classes = ['micro-interaction', `micro-${type}`, className];
    
    if (disabled) {classes.push('disabled');}
    if (isHovered && !disabled) {classes.push('hovered');}
    if (isPressed && !disabled) {classes.push('pressed');}
    if (isFocused && !disabled) {classes.push('focused');}
    
    // Add effect-specific classes
    switch (effect) {
      case 'hover':
        classes.push('effect-hover');
        break;
      case 'click':
        classes.push('effect-click');
        break;
      case 'focus':
        classes.push('effect-focus');
        break;
      case 'select':
        classes.push('effect-select');
        break;
      case 'loading':
        classes.push('effect-loading');
        break;
    }
    
    return classes.join(' ');
  };

  return (
    <div
      ref={elementRef}
      className={getInteractionClasses()}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onClick={handleClick}
      onFocus={handleFocus}
      onBlur={handleBlur}
      tabIndex={disabled ? -1 : 0}
      role={type === 'button' ? 'button' : undefined}
      aria-disabled={disabled}
      style={{ position: 'relative', overflow: 'hidden' }}
    >
      {children}
      
      {/* Ripple effects */}
      {ripples.map(ripple => (
        <div
          key={ripple.id}
          className="ripple-effect"
          style={{
            position: 'absolute',
            left: ripple.x - 10,
            top: ripple.y - 10,
            width: 20,
            height: 20,
            borderRadius: '50%',
            background: 'rgba(255, 255, 255, 0.6)',
            transform: 'scale(0)',
            animation: 'rippleExpand 0.6s ease-out forwards',
            pointerEvents: 'none',
            zIndex: 1
          }}
        />
      ))}
    </div>
  );
}

// Enhanced Button with Micro-Interactions
export function InteractiveButton({
  children,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  icon,
  onClick,
  className = '',
  ...props
}: {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  onClick?: () => void;
  className?: string;
}) {
  const [isPressed, setIsPressed] = useState(false);

  const buttonClasses = [
    'interactive-button',
    `variant-${variant}`,
    `size-${size}`,
    className
  ].join(' ');

  return (
    <MicroInteraction
      type="button"
      effect="click"
      disabled={disabled || loading}
      onClick={onClick}
      className={buttonClasses}
    >
      <div className="button-content">
        {loading && (
          <div className="loading-spinner">
            <div className="spinner" />
          </div>
        )}
        {icon && !loading && <span className="button-icon">{icon}</span>}
        <span className="button-text">{children}</span>
      </div>
    </MicroInteraction>
  );
}

// Enhanced Color Swatch with Micro-Interactions
export function InteractiveColorSwatch({
  color,
  selected = false,
  size = 'medium',
  onClick,
  onHover,
  className = ''
}: {
  color: string;
  selected?: boolean;
  size?: 'small' | 'medium' | 'large';
  onClick?: () => void;
  onHover?: (color: string) => void;
  className?: string;
}) {
  const [isHovered, setIsHovered] = useState(false);

  const swatchClasses = [
    'interactive-swatch',
    `size-${size}`,
    selected ? 'selected' : '',
    className
  ].join(' ');

  return (
    <MicroInteraction
      type="swatch"
      effect="select"
      onClick={onClick}
      onHover={(hovered) => {
        setIsHovered(hovered);
        if (hovered) {onHover?.(color);}
      }}
      className={swatchClasses}
    >
      <div
        className="swatch-color"
        style={{ backgroundColor: color }}
      />
      {selected && (
        <div className="selection-indicator">
          <svg viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        </div>
      )}
      {isHovered && (
        <div className="hover-overlay">
          <span className="color-value">{color}</span>
        </div>
      )}
    </MicroInteraction>
  );
}

// Animated Tooltip Component
export function AnimatedTooltip({
  content,
  position = 'top',
  delay = 500,
  children
}: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [showTimeout, setShowTimeout] = useState<number | null>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  const handleMouseEnter = () => {
    const timeout = setTimeout(() => {
      setIsVisible(true);
    }, delay);
    setShowTimeout(timeout as unknown as number);
  };

  const handleMouseLeave = () => {
    if (showTimeout) {
      clearTimeout(showTimeout);
      setShowTimeout(null);
    }
    setIsVisible(false);
  };

  const getTooltipClasses = () => {
    return [
      'animated-tooltip',
      `position-${position}`,
      isVisible ? 'visible' : 'hidden'
    ].join(' ');
  };

  return (
    <div
      className="tooltip-container"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{ position: 'relative', display: 'inline-block' }}
    >
      {children}
      {isVisible && (
        <div
          ref={tooltipRef}
          className={getTooltipClasses()}
          role="tooltip"
        >
          <div className="tooltip-content">
            {content}
          </div>
          <div className="tooltip-arrow" />
        </div>
      )}
    </div>
  );
}

// Feedback Toast Component
export function FeedbackToast({
  type,
  message,
  duration = 4000,
  onClose
}: FeedbackProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      handleClose();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      setIsVisible(false);
      onClose?.();
    }, 300);
  };

  if (!isVisible) {return null;}

  const getToastClasses = () => {
    return [
      'feedback-toast',
      `type-${type}`,
      isExiting ? 'exiting' : 'entering'
    ].join(' ');
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return '✓';
      case 'error':
        return '✕';
      case 'warning':
        return '⚠';
      case 'info':
        return 'ℹ';
      default:
        return '';
    }
  };

  return (
    <div className={getToastClasses()}>
      <div className="toast-content">
        <span className="toast-icon">{getIcon()}</span>
        <span className="toast-message">{message}</span>
        <button
          className="toast-close"
          onClick={handleClose}
          aria-label="Close notification"
        >
          ×
        </button>
      </div>
    </div>
  );
}

// Loading Pulse Component
export function LoadingPulse({
  size = 'medium',
  color = 'primary',
  className = ''
}: {
  size?: 'small' | 'medium' | 'large';
  color?: 'primary' | 'secondary' | 'accent';
  className?: string;
}) {
  const pulseClasses = [
    'loading-pulse',
    `size-${size}`,
    `color-${color}`,
    className
  ].join(' ');

  return (
    <div className={pulseClasses}>
      <div className="pulse-dot pulse-dot-1" />
      <div className="pulse-dot pulse-dot-2" />
      <div className="pulse-dot pulse-dot-3" />
    </div>
  );
}

// Progress Ring Component
export function ProgressRing({
  progress,
  size = 40,
  strokeWidth = 4,
  color = '#007acc',
  backgroundColor = '#e0e0e0',
  showPercentage = false,
  className = ''
}: {
  progress: number;
  size?: number;
  strokeWidth?: number;
  color?: string;
  backgroundColor?: string;
  showPercentage?: boolean;
  className?: string;
}) {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <div className={`progress-ring ${className}`} style={{ width: size, height: size }}>
      <svg width={size} height={size} className="progress-ring-svg">
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className="progress-ring-circle"
          style={{
            transition: 'stroke-dashoffset 0.3s ease-in-out',
            transform: 'rotate(-90deg)',
            transformOrigin: '50% 50%'
          }}
        />
      </svg>
      {showPercentage && (
        <div className="progress-ring-text">
          {Math.round(progress)}%
        </div>
      )}
    </div>
  );
}

// Floating Action Button with Micro-Interactions
export function FloatingActionButton({
  icon,
  onClick,
  tooltip,
  position = 'bottom-right',
  className = ''
}: {
  icon: React.ReactNode;
  onClick: () => void;
  tooltip?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  className?: string;
}) {
  const [isExpanded, setIsExpanded] = useState(false);

  const fabClasses = [
    'floating-action-button',
    `position-${position}`,
    isExpanded ? 'expanded' : '',
    className
  ].join(' ');

  const ButtonComponent = (
    <MicroInteraction
      type="button"
      effect="click"
      onClick={onClick}
      onHover={setIsExpanded}
      className={fabClasses}
    >
      <div className="fab-icon">{icon}</div>
    </MicroInteraction>
  );

  if (tooltip) {
    return (
      <AnimatedTooltip content={tooltip} position="left">
        {ButtonComponent}
      </AnimatedTooltip>
    );
  }

  return ButtonComponent;
}

export default MicroInteraction;