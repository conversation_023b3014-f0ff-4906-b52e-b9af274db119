export interface SharedFolderConfig {
  basePath: string;
  folderName: string;
}

export interface SharedFolderFile {
  name: string;
  path: string;
  isDirectory: boolean;
  size?: number;
  modified?: Date;
}

export interface SharedFolderAPI {
  getPath: () => Promise<string>;
  readFile: (fileName: string) => Promise<string>;
  writeFile: (fileName: string, content: string) => Promise<boolean>;
  listFiles: () => Promise<SharedFolderFile[]>;
  ensureExists: () => Promise<boolean>;
}