/**
 * Check Users Table Schema
 */

const Database = require('better-sqlite3');

function checkUsersSchema() {
  const dbPath = '/Users/<USER>/Library/Application Support/ChromaSync/chromasync.db';
  console.log('Opening database:', dbPath);
  
  const db = new Database(dbPath);
  
  try {
    // Get users table schema
    const schema = db.prepare(`
      SELECT sql FROM sqlite_master 
      WHERE type='table' AND name='users'
    `).get();
    
    console.log('Users table schema:');
    console.log(schema?.sql || 'Table not found');
    
    // Check organization_members schema too
    const membersSchema = db.prepare(`
      SELECT sql FROM sqlite_master 
      WHERE type='table' AND name='organization_members'
    `).get();
    
    console.log('\nOrganization members table schema:');
    console.log(membersSchema?.sql || 'Table not found');
    
    // Show some sample data if any exists
    try {
      const sampleUsers = db.prepare('SELECT * FROM users LIMIT 3').all();
      console.log('\nSample users:', sampleUsers);
    } catch (e) {
      console.log('\nNo users found or error reading users table');
    }
    
    try {
      const sampleMembers = db.prepare('SELECT * FROM organization_members LIMIT 3').all();
      console.log('\nSample organization members:', sampleMembers);
    } catch (e) {
      console.log('\nNo organization members found or error reading table');
    }
    
  } catch (error) {
    console.error('❌ Error checking schema:', error);
  } finally {
    db.close();
  }
}

checkUsersSchema();