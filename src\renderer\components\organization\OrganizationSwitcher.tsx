/**
 * @file OrganizationSwitcher.tsx
 * @description Dropdown component for switching between organizations
 */

import React, { useState, useRef, useEffect } from 'react';
import { useOrganizationStoreWithAliases } from '../../store/organization.store';
import { Building2, ChevronDown, Plus, Settings, Mail } from 'lucide-react';
import { CreateOrganizationModal } from './CreateOrganizationModal';
import { showAcceptInvitationModal } from './AcceptInvitation';

export const OrganizationSwitcher: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  const { currentOrg, organizations, switchOrganization } = useOrganizationStoreWithAliases();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSwitch = async (orgId: string) => {
    if (orgId === currentOrg?.external_id) {
      setIsOpen(false);
      return;
    }

    const result = await switchOrganization(orgId);
    if (result.success) {
      setIsOpen(false);
    }
  };

  const handleCreateNew = () => {
    setIsOpen(false);
    setShowCreateModal(true);
  };

  const handleManageTeam = () => {
    setIsOpen(false);
    // Navigate to organization settings
    // This would typically use your router, but for now we'll just log
    console.log('Navigate to organization settings');
    // TODO: Implement navigation to /settings/organization
  };

  // Show a "Select Organization" button if no org is selected
  if (!currentOrg) {
    return (
      <div className="relative" ref={dropdownRef}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors text-red-600"
        >
          <Building2 className="w-4 h-4" />
          <span className="text-sm font-medium">Select Organization</span>
          <ChevronDown className={`w-4 h-4 transition-transform ${
            isOpen ? 'transform rotate-180' : ''
          }`} />
        </button>

        {isOpen && (
          <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
            {/* Organizations List */}
            {organizations.length > 0 ? (
              <div className="max-h-64 overflow-y-auto">
                {organizations.map((org) => (
                  <button
                    key={org.external_id}
                    onClick={() => handleSwitch(org.external_id)}
                    className="w-full text-left px-4 py-2 hover:bg-gray-50 flex items-center justify-between group"
                  >
                    <span className="text-sm text-gray-700 group-hover:text-gray-900">
                      {org.name}
                    </span>
                    <span className={`text-xs px-2 py-0.5 rounded-full ${getPlanBadge(org.plan)}`}>
                      {org.plan}
                    </span>
                  </button>
                ))}
              </div>
            ) : (
              <div className="p-4 text-center text-sm text-gray-500">
                No organizations found
              </div>
            )}

            {/* Actions */}
            <div className="border-t border-gray-100 p-2">
              <button
                onClick={handleCreateNew}
                className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded flex items-center space-x-2 text-sm"
              >
                <Plus className="w-4 h-4 text-gray-500" />
                <span>Create New Workspace</span>
              </button>
            </div>
          </div>
        )}

        {showCreateModal && (
          <CreateOrganizationModal
            isOpen={showCreateModal}
            onClose={() => setShowCreateModal(false)}
          />
        )}
      </div>
    );
  }

  const getPlanBadge = (plan: string) => {
    const badges = {
      free: 'bg-gray-100 text-gray-600',
      team: 'bg-blue-100 text-blue-600',
      enterprise: 'bg-purple-100 text-purple-600'
    };
    return badges[plan as keyof typeof badges] || badges.free;
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors"
      >
        <Building2 className="w-4 h-4 text-gray-600" />
        <span className="text-sm font-medium text-gray-900 max-w-[150px] truncate">
          {currentOrg.name}
        </span>
        <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform ${
          isOpen ? 'transform rotate-180' : ''
        }`} />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          {/* Current Organization */}
          <div className="p-3 border-b border-gray-100">
            <div className="text-xs text-gray-500 uppercase tracking-wider mb-1">
              Current Workspace
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium text-gray-900">{currentOrg.name}</span>
              <span className={`text-xs px-2 py-0.5 rounded-full ${getPlanBadge(currentOrg.plan)}`}>
                {currentOrg.plan}
              </span>
            </div>
          </div>

          {/* Organizations List */}
          {organizations.length > 1 && (
            <div className="py-2 border-b border-gray-100">
              <div className="px-3 text-xs text-gray-500 uppercase tracking-wider mb-2">
                Switch Workspace
              </div>
              {organizations
                .filter(org => org.external_id !== currentOrg.external_id)
                .map(org => (
                  <button
                    key={org.external_id}
                    onClick={() => handleSwitch(org.external_id)}
                    className="w-full px-3 py-2 text-left hover:bg-gray-50 transition-colors flex items-center justify-between group"
                  >
                    <span className="text-sm text-gray-700 group-hover:text-gray-900">
                      {org.name}
                    </span>
                    <span className={`text-xs px-2 py-0.5 rounded-full ${getPlanBadge(org.plan)}`}>
                      {org.plan}
                    </span>
                  </button>
                ))}
            </div>
          )}

          {/* Actions */}
          <div className="py-2">
            <button
              onClick={handleManageTeam}
              className="w-full px-3 py-2 text-left hover:bg-gray-50 transition-colors flex items-center space-x-2 text-sm text-gray-700 hover:text-gray-900"
            >
              <Settings className="w-4 h-4" />
              <span>Manage Team</span>
            </button>
            <button
              onClick={handleCreateNew}
              className="w-full px-3 py-2 text-left hover:bg-gray-50 transition-colors flex items-center space-x-2 text-sm text-gray-700 hover:text-gray-900"
            >
              <Plus className="w-4 h-4" />
              <span>Create New Workspace</span>
            </button>
            <button
              onClick={() => {
                setIsOpen(false);
                showAcceptInvitationModal();
              }}
              className="w-full px-3 py-2 text-left hover:bg-gray-50 transition-colors flex items-center space-x-2 text-sm text-gray-700 hover:text-gray-900"
            >
              <Mail className="w-4 h-4" />
              <span>Join by Invitation</span>
            </button>
          </div>
        </div>
      )}
      
      {/* Create Organization Modal */}
      <CreateOrganizationModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={(org) => {
          // The organization is already set as current in the modal
          console.log('New organization created:', org.name);
        }}
      />
    </div>
  );
};
