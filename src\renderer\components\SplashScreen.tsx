/**
 * @file SplashScreen.tsx
 * @description Splash screen component to improve perceived startup performance
 */

import React from 'react';
import { Palette } from 'lucide-react';

interface SplashScreenProps {
  stage: 'initializing' | 'loading-database' | 'loading-ui' | 'preloading-components' | 'ready';
  progress: number;
}

export const SplashScreen: React.FC<SplashScreenProps> = ({ stage, progress }) => {
  const getStageMessage = (stage: string) => {
    switch (stage) {
      case 'initializing':
        return 'Initializing ChromaSync...';
      case 'loading-database':
        return 'Loading color database...';
      case 'loading-ui':
        return 'Preparing interface...';
      case 'preloading-components':
        return 'Optimizing components...';
      case 'ready':
        return 'Ready!';
      default:
        return 'Loading...';
    }
  };

  return (
    <div className="splash-screen">
      <div className="splash-content">
        <div className="splash-logo">
          <Palette size={64} className="logo-icon" />
          <h1 className="logo-text">ChromaSync</h1>
        </div>
        
        <div className="splash-progress">
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${progress}%` }}
            />
          </div>
          <p className="progress-text">{getStageMessage(stage)}</p>
        </div>
      </div>
      
      <style>{`
        .splash-screen {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 9999;
          animation: fadeIn 0.3s ease-out;
        }
        
        .splash-content {
          text-align: center;
          color: white;
          animation: slideInUp 0.6s ease-out;
        }
        
        .splash-logo {
          margin-bottom: 2rem;
        }
        
        .logo-icon {
          margin-bottom: 1rem;
          animation: pulse 2s infinite;
        }
        
        .logo-text {
          font-size: 2rem;
          font-weight: 300;
          margin: 0;
          letter-spacing: 0.1em;
        }
        
        .splash-progress {
          width: 300px;
          margin: 0 auto;
        }
        
        .progress-bar {
          width: 100%;
          height: 4px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 2px;
          overflow: hidden;
          margin-bottom: 1rem;
        }
        
        .progress-fill {
          height: 100%;
          background: rgba(255, 255, 255, 0.8);
          border-radius: 2px;
          transition: width 0.3s ease;
          animation: shimmer 1.5s infinite;
        }
        
        .progress-text {
          font-size: 0.9rem;
          opacity: 0.9;
          margin: 0;
        }
        
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        
        @keyframes slideInUp {
          from {
            transform: translateY(30px);
            opacity: 0;
          }
          to {
            transform: translateY(0);
            opacity: 1;
          }
        }
        
        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }
        
        @keyframes shimmer {
          0% { background-position: -300px 0; }
          100% { background-position: 300px 0; }
        }
        
        .progress-fill {
          background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0.6) 0%,
            rgba(255, 255, 255, 0.9) 50%,
            rgba(255, 255, 255, 0.6) 100%
          );
          background-size: 300px 100%;
        }
      `}</style>
    </div>
  );
};

export default SplashScreen;