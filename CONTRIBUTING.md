# Contributing to ChromaSync

Welcome to ChromaSync! We're excited about your interest in contributing to the leading enterprise color management application. This guide covers everything you need to know about contributing effectively.

## Table of Contents

1. [Code of Conduct](#code-of-conduct)
2. [Getting Started](#getting-started)
3. [Development Workflow](#development-workflow)
4. [Coding Standards](#coding-standards)
5. [Testing Guidelines](#testing-guidelines)
6. [Pull Request Process](#pull-request-process)
7. [Issue Management](#issue-management)
8. [Security Guidelines](#security-guidelines)

---

## Code of Conduct

By participating in this project, you agree to:
- **Be respectful and inclusive** to all contributors
- **Welcome newcomers** and help them get started
- **Focus on constructive feedback** and community benefit
- **Show empathy** towards other community members
- **Respect different viewpoints** and experiences

## Getting Started

### Prerequisites

- **Node.js 18+** and **npm 9+**
- **Git** for version control
- **SQLite** support (included with Node.js)
- Platform-specific build tools:
  - **Windows**: Visual Studio Build Tools
  - **macOS**: Xcode Command Line Tools
  - **Linux**: build-essential package

### Initial Setup

1. **Fork the repository** on GitHub
2. **Clone your fork**:
   ```bash
   git clone https://github.com/your-username/chromasync.git
   cd chromasync
   ```
3. **Install dependencies**:
   ```bash
   npm install
   ```
4. **Run the application**:
   ```bash
   npm run dev
   ```
5. **Verify installation**:
   ```bash
   npm test
   npm run lint
   ```

### Project Structure

```
chromasync/
├── src/
│   ├── main/          # Electron main process
│   ├── renderer/      # React UI components
│   ├── preload/       # IPC bridge (secure)
│   └── shared/        # Shared types and utilities
├── docs/              # Documentation
├── scripts/           # Build and utility scripts
└── tests/             # Test files
```

---

## Development Workflow

### Branch Management

- **main**: Production-ready code
- **develop**: Integration branch for features
- **feature/**: Individual feature branches
- **hotfix/**: Critical bug fixes

### Creating a Feature

1. **Create feature branch**:
   ```bash
   git checkout main
   git pull origin main
   git checkout -b feature/your-feature-name
   ```

2. **Make changes** following our coding standards

3. **Test thoroughly**:
   ```bash
   npm test                # Unit tests
   npm run test:e2e       # End-to-end tests
   npm run test:perf      # Performance tests
   npm run lint           # Code quality
   ```

4. **Commit with conventional format**:
   ```bash
   git commit -m "feat(colors): add batch color import functionality"
   ```

5. **Push and create PR**:
   ```bash
   git push origin feature/your-feature-name
   ```

### Conventional Commits

Format: `type(scope): description`

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code formatting (no logic change)
- `refactor`: Code restructuring (no behavior change)
- `perf`: Performance improvements
- `test`: Adding or updating tests
- `chore`: Build process or auxiliary tool changes

**Examples:**
```bash
feat(sync): implement real-time cloud synchronization
fix(color): resolve CMYK conversion accuracy issue
docs(api): add comprehensive IPC documentation
perf(colors): optimize rendering for 100k+ colors
```

---

## Coding Standards

### TypeScript Standards

#### Strict Type Safety
```typescript
// ✅ Good: Explicit types
interface Color {
  id: string;
  name: string;
  hex: string;
  rgb: { r: number; g: number; b: number };
}

// ❌ Bad: Any types
const color: any = { /* ... */ };
```

#### Naming Conventions
```typescript
// Components: PascalCase
export const ColorPicker: React.FC = () => {};

// Functions/variables: camelCase
const calculateContrastRatio = (color1: Color, color2: Color) => {};

// Constants: UPPER_SNAKE_CASE
const MAX_COLOR_COUNT = 100000;

// Database fields: snake_case
const query = 'SELECT color_name FROM colors WHERE created_at > ?';
```

### Architecture Patterns

#### Electron Process Separation
```typescript
// ✅ Good: Proper IPC usage
// In renderer
const colors = await window.colorAPI.getColors();

// ❌ Bad: Direct database access in renderer
// const db = new Database(); // Never do this
```

#### State Management
```typescript
// ✅ Good: Zustand store pattern
export const useColorStore = create<ColorState>()((set, get) => ({
  colors: [],
  addColor: async (color) => {
    const result = await window.colorAPI.addColor(color);
    if (result.success) {
      set(state => ({ colors: [...state.colors, result.data] }));
    }
  }
}));
```

#### Error Handling
```typescript
// ✅ Good: Comprehensive error handling
async function addColor(color: Partial<Color>): Promise<ApiResponse<Color>> {
  try {
    const result = await window.colorAPI.addColor(color);
    return result;
  } catch (error) {
    console.error('Failed to add color:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
```

### Performance Guidelines

#### Component Optimization
```typescript
// ✅ Good: Memoized components for large lists
const ColorSwatch = React.memo<ColorSwatchProps>(({ color }) => {
  return <div style={{ backgroundColor: color.hex }}>{color.name}</div>;
});

// ✅ Good: Virtual scrolling for large datasets
const ColorList = () => {
  const virtualizer = useVirtualizer({
    count: colors.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 50
  });
  
  return (/* virtual list implementation */);
};
```

#### Database Optimization
```typescript
// ✅ Good: Parameterized queries with indexes
const getColorsByProduct = db.prepare(`
  SELECT c.* FROM colors c
  JOIN product_colors pc ON c.id = pc.color_id
  WHERE pc.product_id = ?
  ORDER BY c.created_at DESC
`);
```

---

## Testing Guidelines

### Test Structure

```
tests/
├── unit/              # Unit tests for individual functions
├── integration/       # IPC and service integration tests
├── e2e/              # End-to-end application tests
└── performance/      # Performance benchmarks
```

### Unit Testing
```typescript
// Example unit test
import { hexToRgb } from '../src/shared/utils/color/conversion';

describe('Color Conversion', () => {
  test('hexToRgb converts hex colors correctly', () => {
    expect(hexToRgb('#FF5733')).toEqual({ r: 255, g: 87, b: 51 });
    expect(hexToRgb('#000000')).toEqual({ r: 0, g: 0, b: 0 });
    expect(hexToRgb('#FFFFFF')).toEqual({ r: 255, g: 255, b: 255 });
  });
  
  test('hexToRgb handles invalid input', () => {
    expect(() => hexToRgb('invalid')).toThrow();
    expect(() => hexToRgb('#GG0000')).toThrow();
  });
});
```

### Integration Testing
```typescript
// Example IPC integration test
import { app } from 'electron';
import { testIPC } from '../helpers/ipc-test-helper';

describe('Color IPC', () => {
  test('addColor creates and returns color', async () => {
    const testColor = {
      name: 'Test Color',
      hex: '#FF5733'
    };
    
    const result = await testIPC('color:add', testColor);
    
    expect(result.success).toBe(true);
    expect(result.data.name).toBe('Test Color');
    expect(result.data.id).toBeDefined();
  });
});
```

### Performance Testing
```typescript
// Example performance test
import { performance } from 'perf_hooks';

describe('Color Performance', () => {
  test('can handle 10,000 colors under 1 second', async () => {
    const start = performance.now();
    
    const colors = Array.from({ length: 10000 }, (_, i) => ({
      name: `Color ${i}`,
      hex: `#${Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')}`
    }));
    
    await window.colorAPI.importColors(colors);
    
    const end = performance.now();
    expect(end - start).toBeLessThan(1000);
  });
});
```

### Test Commands
```bash
npm test                 # All unit tests
npm run test:watch      # Watch mode for development
npm run test:coverage   # Generate coverage report
npm run test:e2e        # End-to-end tests
npm run test:perf       # Performance benchmarks
```

---

## Pull Request Process

### Before Submitting

1. **Ensure tests pass**:
   ```bash
   npm test
   npm run lint
   npm run typecheck
   ```

2. **Update documentation** if needed

3. **Add changelog entry** for user-facing changes

4. **Test manually** in the application

### PR Requirements

- [ ] **Clear description** of changes and motivation
- [ ] **Tests added/updated** for new functionality
- [ ] **Documentation updated** if needed
- [ ] **No breaking changes** without major version bump
- [ ] **Performance impact** considered and tested
- [ ] **Security implications** reviewed

### PR Template

```markdown
## Description
Brief description of changes and motivation.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Performance impact assessed

## Screenshots
If applicable, add screenshots of UI changes.

## Checklist
- [ ] Code follows project coding standards
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Tests added/updated
```

### Review Process

1. **Automated checks** must pass (CI/CD)
2. **Code review** by maintainer
3. **Manual testing** of functionality
4. **Approval** and merge to main

---

## Issue Management

### Bug Reports

Use the bug report template and include:

- **Clear title** summarizing the issue
- **Steps to reproduce** the problem
- **Expected behavior** vs actual behavior
- **System information** (OS, version, etc.)
- **Screenshots/videos** if applicable
- **Error logs** from Developer Tools

### Feature Requests

Include:

- **Problem description** - what need does this address?
- **Proposed solution** - how should it work?
- **Alternatives considered** - other approaches?
- **Additional context** - mockups, examples, etc.

### Issue Labels

- `bug`: Something isn't working
- `enhancement`: New feature or improvement
- `good first issue`: Suitable for newcomers
- `help wanted`: Community help needed
- `priority/high`: Critical issues
- `type/performance`: Performance-related
- `type/security`: Security-related

---

## Security Guidelines

### Reporting Security Issues

**DO NOT** create public issues for security vulnerabilities.

Instead:
1. Email: `<EMAIL>`
2. Include detailed description and reproduction steps
3. Allow 90 days for responsible disclosure

### Security Best Practices

```typescript
// ✅ Good: Input validation
function validateHexColor(hex: string): boolean {
  return /^#[0-9A-Fa-f]{6}$/.test(hex);
}

// ✅ Good: Parameterized queries
const stmt = db.prepare('SELECT * FROM colors WHERE name = ?');
const colors = stmt.all(userInput);

// ❌ Bad: SQL injection vulnerability
// const colors = db.exec(`SELECT * FROM colors WHERE name = '${userInput}'`);
```

### Code Security Checklist

- [ ] All user inputs validated and sanitized
- [ ] No secrets hardcoded in source code
- [ ] Proper error handling (no sensitive info in errors)
- [ ] IPC channels properly secured
- [ ] File system access restricted to authorized paths

---

## Release Process

### Version Management

We follow [Semantic Versioning](https://semver.org/):
- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Checklist

- [ ] All tests passing
- [ ] Documentation updated
- [ ] Changelog updated
- [ ] Version bumped in package.json
- [ ] Release notes prepared
- [ ] Security review completed
- [ ] Performance regression testing

---

## Getting Help

### Resources

- **Documentation**: All guides in this repository
- **Community**: GitHub Discussions
- **Issues**: GitHub Issues for bugs/features
- **Security**: <EMAIL>

### Development Support

- **Discord**: Join our developer community
- **Office Hours**: Weekly community calls
- **Mentorship**: Pair programming sessions for newcomers

---

## Recognition

We appreciate all contributions! Contributors are recognized through:

- **Contributors file** in the repository
- **Release notes** acknowledgments
- **Community highlights** in announcements
- **Swag** for significant contributions

Thank you for helping make ChromaSync better! 🎨