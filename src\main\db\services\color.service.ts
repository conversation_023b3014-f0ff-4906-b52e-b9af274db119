/**
 * @file color.service.ts
 * @description Service for color database operations - Updated for optimized schema
 */

import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import fs from 'node:fs';
import { ColorEntry, NewColorEntry, UpdateColorEntry } from '../../../shared/types/color.types';
import { normalizeColorName, standardizeColorCode } from '../../utils/string-utils';
import {
  validateHex,
  standardizeHex,
  validateCMYK,
  standardizeCMYK,
  areColorValuesConsistent,
  hexToCmyk,
  hexToRgb,
  hexToHsl,
  rgbToLab
} from '../../../shared/utils/color';

export class ColorService {
  // Prepared statement cache for better performance
  private static preparedStatements = new Map<string, Database.Statement>();
  
  constructor(private db: Database.Database) {}
  
  /**
   * Get or create a prepared statement with caching
   */
  private getPreparedStatement(sql: string): Database.Statement {
    if (!ColorService.preparedStatements.has(sql)) {
      ColorService.preparedStatements.set(sql, this.db.prepare(sql));
    }
    return ColorService.preparedStatements.get(sql)!;
  }
  
  /**
   * Runtime color space conversion utility
   */
  private getColorSpaces(hex: string) {
    const rgb = hexToRgb(hex);
    if (!rgb) {return { rgb: null, hsl: null, lab: null };}
    
    const hsl = hexToHsl(hex);
    const lab = rgbToLab(rgb);
    
    return { rgb, hsl, lab };
  }

  /**
   * Get all colors from the optimized schema with runtime color space conversion
   * @param organizationId - External organization ID (UUID) for multi-tenant filtering
   */
  getAll(organizationId: string): ColorEntry[] {
    try {
      // First, get the local organization ID from the external organization ID
      const localOrgStmt = this.getPreparedStatement(`
        SELECT id FROM organizations WHERE external_id = ?
      `);
      const localOrg = localOrgStmt.get(organizationId) as { id: number } | undefined;
      
      if (!localOrg) {
        console.warn(`[ColorService] Organization not found for external_id: ${organizationId}`);
        return [];
      }
      
      // Use prepared statement cache for better performance
      const stmt = this.getPreparedStatement(`
        SELECT 
          c.external_id,
          cs.code as source,
          c.code,
          c.display_name,
          c.hex,
          cm.c as cyan,
          cm.m as magenta,
          cm.y as yellow,
          cm.k as black,
          c.properties,
          c.created_at,
          c.updated_at
        FROM colors c
        LEFT JOIN color_sources cs ON c.source_id = cs.id
        LEFT JOIN color_cmyk cm ON c.id = cm.color_id
        WHERE c.deleted_at IS NULL AND c.organization_id = ?
        ORDER BY c.code ASC
      `);
      
      const rows = stmt.all(localOrg.id);
      console.log(`[ColorService] Found ${rows.length} colors for organization ${organizationId} (local ID: ${localOrg.id})`);
      return rows.map(row => this.convertToColorEntry(row));
    } catch (error) {
      console.error('[ColorService] Error getting all colors:', error);
      return [];
    }
  }

  /**
   * Get color by ID with prepared statement caching
   * @param id - Color external ID
   * @param organizationId - External organization ID (UUID) for multi-tenant filtering
   */
  getById(id: string, organizationId: string): ColorEntry | undefined {
    try {
      // First, get the local organization ID from the external organization ID
      const localOrgStmt = this.getPreparedStatement(`
        SELECT id FROM organizations WHERE external_id = ?
      `);
      const localOrg = localOrgStmt.get(organizationId) as { id: number } | undefined;
      
      if (!localOrg) {
        console.warn(`[ColorService] Organization not found for external_id: ${organizationId}`);
        return undefined;
      }
      
      // Use prepared statement cache for better performance
      const stmt = this.getPreparedStatement(`
        SELECT 
          external_id,
          source,
          code,
          display_name,
          hex,
          cyan,
          magenta,
          yellow,
          black,
          properties,
          created_at,
          updated_at
        FROM v_colors
        WHERE external_id = ? AND organization_id = ? AND is_active = 1
      `);
      
      const row = stmt.get(id, localOrg.id) as any;
      if (!row) {return undefined;}
      return this.convertToColorEntry(row);
    } catch (error) {
      console.error(`[ColorService] Error getting color ${id}:`, error);
      return undefined;
    }
  }

  /**
   * Add a new color
   * @param color - Color data to add
   * @param organizationId - Organization ID for multi-tenant support
   * @param userId - Optional user ID for audit trail
   */
  add(color: NewColorEntry, organizationId: string, userId?: string): ColorEntry {
    const id = uuidv4();
    const now = new Date().toISOString();

    console.log('[ColorService] Adding color:', {
      code: color.code,
      hex: color.hex,
      name: color.name,
      cmyk: color.cmyk
    });

    try {
      // Validate and standardize inputs
      validateHex(color.hex); // This throws if invalid
      const standardizedHex = standardizeHex(color.hex);
      
      console.log('[ColorService] Standardized hex:', standardizedHex);
      
      // Handle potential duplicate codes by making them unique
      let finalCode = standardizeColorCode(color.code);
      const sourceId = color.isLibrary ? 2 : 1;
      
      // Check if this code already exists for this source (including soft-deleted)
      const existingColor = this.db.prepare(`
        SELECT id, external_id, deleted_at FROM colors 
        WHERE source_id = ? AND code = ?
      `).get(sourceId, finalCode) as any;
      
      if (existingColor) {
        if (existingColor.deleted_at) {
          // For soft-deleted colors during import, skip reactivation and create new with unique code
          // This avoids transaction conflicts
          console.log(`[ColorService] Soft-deleted color exists with code ${finalCode}, creating with unique code`);
          const timestamp = Date.now().toString(36).toUpperCase();
          const random = Math.random().toString(36).substring(2, 6).toUpperCase();
          finalCode = `${finalCode}-${timestamp}-${random}`;
        } else {
          // Make the code unique by appending a timestamp and random suffix
          const timestamp = Date.now().toString(36).toUpperCase();
          const random = Math.random().toString(36).substring(2, 6).toUpperCase();
          finalCode = `${finalCode}-${timestamp}-${random}`;
          console.log('[ColorService] Code already exists, using unique code:', finalCode);
        }
      }
      
      // Handle gradient colors specially - they don't have valid CMYK values
      let c = 0, m = 0, y = 0, k = 0;
      let standardizedCMYK = '';
      
      if (color.gradient) {
        // For gradients, derive CMYK from the first color stop's hex value
        console.log('[ColorService] Processing gradient color, deriving CMYK from hex');
        const derivedCMYKObj = hexToCmyk(standardizedHex);
        if (derivedCMYKObj) {
          c = derivedCMYKObj.c;
          m = derivedCMYKObj.m;
          y = derivedCMYKObj.y;
          k = derivedCMYKObj.k;
          standardizedCMYK = `C:${c} M:${m} Y:${y} K:${k}`;
        }
      } else {
        // For non-gradient colors, validate and parse CMYK normally
        validateCMYK(color.cmyk); // This throws if invalid
        standardizedCMYK = standardizeCMYK(color.cmyk);

        // Parse CMYK values from standardized format "C:X M:Y Y:Z K:W"
        const cmykMatch = standardizedCMYK.match(/C:(\d+)\s*M:(\d+)\s*Y:(\d+)\s*K:(\d+)/);
        if (!cmykMatch) {
          throw new Error(`Invalid CMYK format after standardization: ${standardizedCMYK}`);
        }
        const [, cStr, mStr, yStr, kStr] = cmykMatch;
        c = parseInt(cStr, 10);
        m = parseInt(mStr, 10);
        y = parseInt(yStr, 10);
        k = parseInt(kStr, 10);
      }
      
      // Ensure color value consistency
      if (!areColorValuesConsistent(standardizedCMYK, standardizedHex)) {
        console.warn(`[ColorService] Color values not consistent for ${color.code}, using hex-derived CMYK`);
        const derivedCMYKObj = hexToCmyk(standardizedHex);
        if (!derivedCMYKObj) {
          throw new Error(`Could not derive CMYK from hex: ${standardizedHex}`);
        }
        // Use the CMYK object values directly
        const c2 = derivedCMYKObj.c;
        const m2 = derivedCMYKObj.m;
        const y2 = derivedCMYKObj.y;
        const k2 = derivedCMYKObj.k;
        
        // Insert color with derived CMYK values
        const colorInfo = this.db.prepare(`
          INSERT INTO colors (
            external_id, source_id, code, display_name, hex, 
            is_gradient, is_metallic, is_effect, properties, 
            organization_id, user_id,
            created_at, updated_at
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          id,
          sourceId,
          finalCode,
          normalizeColorName(color.name),
          standardizedHex,
          color.gradient ? 1 : 0,
          0, // is_metallic
          0, // is_effect
          JSON.stringify({
            product: color.product,
            notes: color.notes,
            gradient: color.gradient
          }),
          organizationId,
          userId || null,
          now,
          now
        );
        
        const colorId = colorInfo.lastInsertRowid as number;
        // Insert CMYK values
        this.db.prepare(`
          INSERT INTO color_cmyk (color_id, c, m, y, k)
          VALUES (?, ?, ?, ?, ?)
        `).run(colorId, c2, m2, y2, k2);
      } else {
        // Insert with provided CMYK values
        const colorInfo = this.db.prepare(`
          INSERT INTO colors (
            external_id, source_id, code, display_name, hex, 
            is_gradient, is_metallic, is_effect, properties, 
            organization_id, user_id,
            created_at, updated_at
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).run(
          id,
          sourceId,
          finalCode,
          normalizeColorName(color.name),
          standardizedHex,
          color.gradient ? 1 : 0,
          0,
          0,
          JSON.stringify({
            product: color.product,
            notes: color.notes,
            gradient: color.gradient
          }),
          organizationId,
          userId || null,
          now,
          now
        );
        
        const colorId = colorInfo.lastInsertRowid as number;
        // Insert CMYK values
        this.db.prepare(`
          INSERT INTO color_cmyk (color_id, c, m, y, k)
          VALUES (?, ?, ?, ?, ?)
        `).run(colorId, c, m, y, k);
      }

      return this.getById(id, organizationId)!;
    } catch (error) {
      console.error('[ColorService] Error adding color:', error);
      throw error;
    }
  }

  /**
   * Update an existing color
   * @param id - Color external ID
   * @param updates - Fields to update
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  update(id: string, updates: UpdateColorEntry, organizationId: string): ColorEntry | undefined {
    const existingColor = this.getById(id, organizationId);
    if (!existingColor) {return undefined;}

    const now = new Date().toISOString();
    
    // Get internal ID and verify organization
    const internalColor = this.db.prepare('SELECT id FROM colors WHERE external_id = ? AND organization_id = ?').get(id, organizationId) as { id: number } | undefined;
    if (!internalColor) {return undefined;}

    const updateTransaction = this.db.transaction(() => {
      // Update main color record
      const updateFields: string[] = [];
      const updateValues: any[] = [];

      if (updates.code !== undefined) {
        updateFields.push('code = ?');
        updateValues.push(standardizeColorCode(updates.code));
      }
      if (updates.name !== undefined) {
        updateFields.push('display_name = ?');
        updateValues.push(normalizeColorName(updates.name));
      }
      if (updates.hex !== undefined) {
        validateHex(updates.hex); // This throws if invalid
        updateFields.push('hex = ?');
        updateValues.push(standardizeHex(updates.hex));
      }
      if (updates.gradient !== undefined) {
        updateFields.push('is_gradient = ?');
        updateValues.push(updates.gradient ? 1 : 0);
      }

      // Update properties
      const currentProps = this.db.prepare('SELECT properties FROM colors WHERE id = ?').get(internalColor.id) as { properties: string } | undefined;
      const props = currentProps?.properties ? JSON.parse(currentProps.properties) : {};
      
      if (updates.product !== undefined) {props.product = updates.product;}
      if (updates.notes !== undefined) {props.notes = updates.notes;}
      if (updates.gradient !== undefined) {props.gradient = updates.gradient;}
      
      updateFields.push('properties = ?');
      updateValues.push(JSON.stringify(props));
      updateFields.push('updated_at = ?');
      updateValues.push(now);

      if (updateFields.length > 0) {
        updateValues.push(internalColor.id);
        updateValues.push(organizationId);
        this.db.prepare(`UPDATE colors SET ${updateFields.join(', ')} WHERE id = ? AND organization_id = ?`).run(...updateValues);
      }

      // Update CMYK if provided
      if (updates.cmyk !== undefined) {
        // Check if this is a gradient update - gradients might have "N/A - Gradient" as CMYK
        const isGradient = updates.gradient || (props.gradient && updates.gradient !== false);
        
        if (isGradient && updates.cmyk === 'N/A - Gradient') {
          // For gradients with N/A CMYK, derive from hex if available
          if (updates.hex) {
            const derivedCMYKObj = hexToCmyk(updates.hex);
            if (derivedCMYKObj) {
              this.db.prepare(`
                INSERT OR REPLACE INTO color_cmyk (color_id, c, m, y, k)
                VALUES (?, ?, ?, ?, ?)
              `).run(internalColor.id, derivedCMYKObj.c, derivedCMYKObj.m, derivedCMYKObj.y, derivedCMYKObj.k);
            }
          }
        } else {
          // For non-gradient colors, validate and parse CMYK normally
          validateCMYK(updates.cmyk); // This throws if invalid
          const standardizedCMYK = standardizeCMYK(updates.cmyk);
          // Parse CMYK values from standardized format "C:X M:Y Y:Z K:W"
          const cmykMatch = standardizedCMYK.match(/C:(\d+)\s*M:(\d+)\s*Y:(\d+)\s*K:(\d+)/);
          if (!cmykMatch) {
            throw new Error(`Invalid CMYK format after standardization: ${standardizedCMYK}`);
          }
          const [, cStr, mStr, yStr, kStr] = cmykMatch;
          const c = parseInt(cStr, 10);
          const m = parseInt(mStr, 10);
          const y = parseInt(yStr, 10);
          const k = parseInt(kStr, 10);
          
          this.db.prepare(`
            INSERT OR REPLACE INTO color_cmyk (color_id, c, m, y, k)
            VALUES (?, ?, ?, ?, ?)
          `).run(internalColor.id, c, m, y, k);
        }
      }

      // RGB/HSL/LAB are now computed at runtime from hex - no database updates needed

      // Update gradient stops if gradient provided
      if (updates.gradient !== undefined) {
        // Delete existing stops
        this.db.prepare('DELETE FROM gradient_stops WHERE gradient_id = ?').run(internalColor.id);
        
        // Insert new stops
        if (updates.gradient && updates.gradient.gradientStops) {
          updates.gradient.gradientStops.forEach((stop, index) => {
            this.db.prepare(`
              INSERT INTO gradient_stops (gradient_id, stop_index, position, hex)
              VALUES (?, ?, ?, ?)
            `).run(internalColor.id, index, stop.position, stop.color);
          });
        }
      }
    });

    updateTransaction();
    return this.getById(id, organizationId);
  }

  /**
   * Delete a color (soft delete in optimized schema)
   * @param id - Color external ID
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  delete(id: string, organizationId: string): boolean {
    try {
      const result = this.db.prepare(`
        UPDATE colors 
        SET deleted_at = CURRENT_TIMESTAMP 
        WHERE external_id = ? AND organization_id = ? AND deleted_at IS NULL
      `).run(id, organizationId);
      
      return result.changes > 0;
    } catch (error) {
      console.error(`[ColorService] Error deleting color ${id}:`, error);
      return false;
    }
  }

  /**
   * Import multiple colors
   * @param colors - Array of colors to import
   * @param organizationId - Organization ID for multi-tenant support
   * @param userId - Optional user ID for audit trail
   * @param options - Import options
   */
  async importColors(
    colors: NewColorEntry[],
    organizationId: string,
    userId?: string,
    options: { batchSize?: number; onProgress?: (progress: number) => void } = {}
  ): Promise<ColorEntry[]> {
    const { batchSize = 100, onProgress } = options;
    const importedColors: ColorEntry[] = [];
    const total = colors.length;

    try {
      // Process in batches
      for (let i = 0; i < colors.length; i += batchSize) {
        const batch = colors.slice(i, i + batchSize);
        
        const batchTransaction = this.db.transaction(() => {
          for (const color of batch) {
            try {
              const imported = this.add(color, organizationId, userId);
              importedColors.push(imported);
            } catch (error) {
              console.error('[ColorService] Error importing color:', error, color);
            }
          }
        });

        batchTransaction();

        // Report progress
        if (onProgress) {
          const progress = Math.min(100, Math.round(((i + batch.length) / total) * 100));
          onProgress(progress);
        }
      }

      console.log(`[ColorService] Successfully imported ${importedColors.length} colors`);
      return importedColors;
    } catch (error) {
      console.error('[ColorService] Error importing colors:', error);
      throw error;
    }
  }


  /**
   * Convert optimized schema row to ColorEntry format with runtime color space conversion
   */
  private convertToColorEntry(row: any): ColorEntry {
    const props = row.properties ? JSON.parse(row.properties) : {};
    
    // Extract CMYK values from database (stored for print workflows)
    let cmyk = '';
    if (row.cyan !== undefined && row.cyan !== null && 
        row.magenta !== undefined && row.magenta !== null && 
        row.yellow !== undefined && row.yellow !== null && 
        row.black !== undefined && row.black !== null) {
      // Use CMYK from normalized tables (optimized format)
      cmyk = `C:${row.cyan} M:${row.magenta} Y:${row.yellow} K:${row.black}`;
    } else if (props.cmyk) {
      // Use CMYK from properties (legacy format)
      if (typeof props.cmyk === 'string') {
        cmyk = props.cmyk;
      } else {
        const cmykObj = props.cmyk;
        cmyk = `C:${cmykObj.c || 0} M:${cmykObj.m || 0} Y:${cmykObj.y || 0} K:${cmykObj.k || 0}`;
      }
    } else {
      cmyk = 'C:0 M:0 Y:0 K:0';
    }
    
    // Compute RGB/HSL/LAB at runtime from hex for better performance and consistency
    const colorSpaces = this.getColorSpaces(row.hex);
    
    // Extract product name - for test data, extract from display_name
    let product = props.product || '';
    if (!product && row.display_name && row.display_name.includes(' ')) {
      // For display names like "Test Flat Color Blue", use "Test" as product
      const parts = row.display_name.split(' ');
      if (parts[0].toLowerCase() === 'test') {
        product = 'Test';
      }
    }
    
    // Process gradient data if present
    let gradient = props.gradient;
    if (gradient && gradient.stops && !gradient.gradientCSS) {
      // Generate CSS gradient string from stops
      const angle = gradient.angle || 45;
      const stops = gradient.stops.map((stop: any) => 
        `${stop.hex || stop.color} ${stop.position}%`
      ).join(', ');
      gradient = {
        ...gradient,
        gradientCSS: `linear-gradient(${angle}deg, ${stops})`,
        gradientStops: gradient.stops // Ensure we have gradientStops for compatibility
      };
    }
    
    return {
      id: row.external_id,
      product: product,
      name: row.display_name || row.code,
      code: row.code,
      hex: row.hex,
      cmyk: cmyk,
      // Runtime-computed color spaces (no database storage needed)
      rgb: colorSpaces.rgb || undefined,
      hsl: colorSpaces.hsl || undefined,
      lab: colorSpaces.lab || undefined,
      notes: props.notes,
      gradient: gradient,
      isLibrary: row.source !== 'user',
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  /**
   * Search colors by text with prepared statement caching
   */
  search(query: string): ColorEntry[] {
    try {
      const stmt = this.getPreparedStatement(`
        SELECT 
          external_id,
          source,
          code,
          display_name,
          hex,
          cyan,
          magenta,
          yellow,
          black,
          properties,
          created_at,
          updated_at
        FROM v_colors
        WHERE is_active = 1
          AND (
            code LIKE ? OR 
            display_name LIKE ? OR 
            hex LIKE ? OR
            properties LIKE ?
          )
        ORDER BY code ASC
      `);
      
      const rows = stmt.all(
        `%${query}%`,
        `%${query}%`,
        `%${query}%`,
        `%${query}%`
      );

      return rows.map(row => this.convertToColorEntry(row));
    } catch (error) {
      console.error('[ColorService] Error searching colors:', error);
      return [];
    }
  }

  /**
   * Clear all colors (soft delete by default)
   */
  clearAll(hardDelete: boolean = false): void {
    try {
      if (hardDelete) {
        // Permanently delete all colors and their relationships
        console.log('[ColorService] Hard delete: permanently removing all colors');
        
        // Delete in order to respect foreign key constraints (updated for optimized schema)
        this.db.prepare('DELETE FROM color_cmyk').run();
        this.db.prepare('DELETE FROM gradient_stops').run();
        this.db.prepare('DELETE FROM product_colors').run();
        this.db.prepare('DELETE FROM colors').run();
        
        console.log('[ColorService] All colors permanently deleted');
      } else {
        // Soft delete (default behavior)
        this.db.prepare(`
          UPDATE colors 
          SET deleted_at = CURRENT_TIMESTAMP 
          WHERE deleted_at IS NULL
        `).run();
        console.log('[ColorService] All colors soft deleted');
      }
    } catch (error) {
      console.error('[ColorService] Error clearing all colors:', error);
      throw error;
    }
  }

  /**
   * Export colors to file
   */
  async exportColors(filePath: string, format: 'json' | 'csv' = 'json'): Promise<{ exported: boolean; count?: number; message: string }> {
    try {
      const colors = this.getAll();
      
      if (colors.length === 0) {
        return {
          exported: false,
          message: 'No colors to export'
        };
      }

      if (format === 'csv') {
        // Export as CSV
        const headers = ['name', 'code', 'hex', 'cmyk', 'product', 'notes'];
        const csvContent = [
          headers.join(','),
          ...colors.map(color => {
            return [
              color.name,
              color.code,
              color.hex,
              color.cmyk,
              color.product || '',
              color.notes || ''
            ].map(val => `"${val}"`).join(',');
          })
        ].join('\n');

        fs.writeFileSync(filePath, csvContent, 'utf8');
      } else {
        // Export as JSON
        const exportData = colors.map(color => ({
          name: color.name,
          code: color.code,
          hex: color.hex,
          cmyk: color.cmyk,
          rgb: color.rgb,
          hsl: color.hsl,
          lab: color.lab,
          product: color.product,
          notes: color.notes,
          gradient: color.gradient,
          isLibrary: color.isLibrary
        }));

        fs.writeFileSync(filePath, JSON.stringify(exportData, null, 2), 'utf8');
      }

      console.log(`[ColorService] Exported ${colors.length} colors to ${filePath}`);
      return {
        exported: true,
        count: colors.length,
        message: `Successfully exported ${colors.length} colors`
      };
    } catch (error) {
      console.error('[ColorService] Error exporting colors:', error);
      return {
        exported: false,
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get color usage counts - how many products use each color code
   */
  getColorUsageCounts(): Map<string, { count: number; products: string[] }> {
    try {
      // First, get the total count of product associations for each code
      const countRows = this.db.prepare(`
        SELECT 
          c.code,
          COUNT(DISTINCT pc.product_id) as product_count,
          COUNT(*) as total_usage_count
        FROM colors c
        JOIN product_colors pc ON c.id = pc.color_id
        JOIN products p ON pc.product_id = p.id
        WHERE c.deleted_at IS NULL
          AND p.is_active = 1
        GROUP BY c.code
      `).all();

      // Then get the actual product names for each code
      const productRows = this.db.prepare(`
        SELECT DISTINCT
          c.code,
          p.name as product_name
        FROM colors c
        JOIN product_colors pc ON c.id = pc.color_id
        JOIN products p ON pc.product_id = p.id
        WHERE c.deleted_at IS NULL
          AND p.is_active = 1
        ORDER BY c.code, p.name
      `).all();

      // Build the usage map
      const usageMap = new Map<string, { count: number; products: string[] }>();
      
      // First populate with counts
      countRows.forEach((row: any) => {
        usageMap.set(row.code, {
          count: row.total_usage_count,
          products: []
        });
      });
      
      // Then add product names
      productRows.forEach((row: any) => {
        const usage = usageMap.get(row.code);
        if (usage && !usage.products.includes(row.product_name)) {
          usage.products.push(row.product_name);
        }
      });

      console.log('[ColorService] Usage counts calculated:', usageMap.size, 'unique codes');
      
      // Debug: log first few entries
      let debugCount = 0;
      usageMap.forEach((value, key) => {
        if (debugCount < 5) {
          console.log(`[ColorService] Code: ${key}, Count: ${value.count}, Products: ${value.products.join(', ')}`);
          debugCount++;
        }
      });
      
      return usageMap;
    } catch (error) {
      console.error('[ColorService] Error getting color usage counts:', error);
      throw error;
    }
  }
}
