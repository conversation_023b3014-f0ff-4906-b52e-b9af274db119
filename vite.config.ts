import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react({
      jsxRuntime: 'automatic'
    })
  ],
  
  // Resolve paths
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      'shared': resolve(__dirname, 'shared'),
      '@renderer': resolve(__dirname, 'src/renderer'),
      '@components': resolve(__dirname, 'src/renderer/components'),
      '@hooks': resolve(__dirname, 'src/renderer/hooks'),
      '@styles': resolve(__dirname, 'src/renderer/styles'),
    }
  },
  base: './',

  // Optimized build settings
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    // Generate source maps for production debugging
    sourcemap: true,
    
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      },
      external: [
        'electron',
        'electron-log',
        'electron-util',
        'electron-notarize',
        'electron-store',
        'electron-is-dev',
        'node:fs',
        'node:path',
        'node:os',
        'node:url',
        'node:crypto',
        'node:stream',
        'node:events',
        'path',
        'fs',
        'os',
        'url',
        'crypto',
        'stream',
        'events'
      ],
      output: {
        manualChunks: {
          // Split vendor libraries into separate chunks
          'react-vendor': ['react', 'react-dom'],
          'ui-components': [
            './src/renderer/components/ColorForm',
            './src/renderer/components/ColorTable',
            './src/renderer/components/ColorSwatches',
            './src/renderer/components/Header'
          ],
          'utils': ['./src/renderer/utils'],
          'stores': ['./src/renderer/store'],
        },
      },
    },
    
    // Minify output
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: false, // Keep console logs for debugging
        drop_debugger: true, // Remove debugger statements
      },
    },
    
    // Increase build performance 
    reportCompressedSize: false,
    chunkSizeWarningLimit: 1000,
  },
  
  // Development server configuration
  server: {
    port: 5173,
    strictPort: true,
    open: false,
  },
}); 