const Database = require('better-sqlite3');
const path = require('path');
const os = require('os');

// Try multiple possible database locations
const possiblePaths = [
  path.join(os.homedir(), 'Library/Application Support/chroma-sync/chromasync.db'),
  path.join(os.homedir(), 'Library/Application Support/ChromaSync/chromasync.db'),
  path.join(os.homedir(), 'Library/Application Support/chroma-sync/chroma-sync-data.db'),
  path.join(__dirname, '..', 'chromasync.db'),
  './chromasync.db'
];

let db = null;
let dbPath = null;

// Find the database
for (const path of possiblePaths) {
  try {
    console.log(`Trying: ${path}`);
    db = new Database(path, { readonly: true });
    dbPath = path;
    console.log(`✓ Found database at: ${path}\n`);
    break;
  } catch (e) {
    // Continue to next path
  }
}

if (!db) {
  console.error('Could not find database at any of the expected locations');
  process.exit(1);
}

// Check all tables
const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
console.log('Tables in database:');
tables.forEach(t => console.log(`  - ${t.name}`));
console.log('');

// Check schema for key tables
const checkTables = ['products', 'colors', 'product_colors'];

checkTables.forEach(tableName => {
  const exists = tables.some(t => t.name === tableName);
  if (!exists) {
    console.log(`\n❌ Table "${tableName}" does not exist`);
    return;
  }

  console.log(`\n📋 Schema for table "${tableName}":`);
  const columns = db.prepare(`PRAGMA table_info(${tableName})`).all();
  console.table(columns.map(col => ({
    name: col.name,
    type: col.type,
    notNull: col.notnull ? 'YES' : 'NO',
    defaultValue: col.dflt_value,
    isPK: col.pk ? 'YES' : 'NO'
  })));

  // Show sample data
  try {
    const count = db.prepare(`SELECT COUNT(*) as count FROM ${tableName}`).get();
    console.log(`  Total rows: ${count.count}`);
    
    if (count.count > 0) {
      console.log(`  Sample row:`);
      const sample = db.prepare(`SELECT * FROM ${tableName} LIMIT 1`).get();
      console.log(sample);
    }
  } catch (e) {
    console.log(`  Error reading table: ${e.message}`);
  }
});

// Check for specific columns that the code expects
console.log('\n🔍 Checking for expected columns:');
const expectedColumns = [
  { table: 'products', column: 'external_id' },
  { table: 'colors', column: 'external_id' },
  { table: 'colors', column: 'colourCode' },
  { table: 'colors', column: 'code' },
  { table: 'colors', column: 'is_library' },
  { table: 'product_colors', column: 'product_id' },
  { table: 'product_colors', column: 'color_id' }
];

expectedColumns.forEach(({ table, column }) => {
  try {
    const hasColumn = db.prepare(`
      SELECT COUNT(*) as count 
      FROM pragma_table_info('${table}') 
      WHERE name = ?
    `).get(column);
    
    console.log(`  ${table}.${column}: ${hasColumn.count > 0 ? '✓ exists' : '✗ missing'}`);
  } catch (e) {
    console.log(`  ${table}.${column}: ✗ table doesn't exist`);
  }
});

db.close();
console.log('\n✅ Schema check complete');