/**
 * Optimized database implementation using modular architecture
 * This file now coordinates between modular components for better maintainability
 */

import { v4 as uuidv4 } from 'uuid';
import type { ColorEntry } from '../../shared/types/color.types';

// Import color utilities from consolidated module
import {
  isValidHex,
  hexToCmyk,
  hexToRgb,
  standardizeCMYK
} from '../../shared/utils/color';

// Import modular database components
import {
  DatabasePool,
  getPooledConnection,
  releasePooledConnection,
  executeWithPool,
  getDatabasePath
} from './core/connection';
import {
  DatabaseInitializer,
  getDatabaseInitializer,
  initDatabase as initDatabaseCore,
  getDatabase as getDatabaseCore
} from './core/initialization';
import { loadIdMappings, getInternalId, getExternalId, addIdMapping } from './utils/id-mapping';
import { ensureGradientColumns } from './utils/gradient-columns';

// Global database instance for backward compatibility
let db: any = null;

// Export connection utilities for backward compatibility
export { getPooledConnection, releasePooledConnection, executeWithPool, getDatabasePath };

/**
 * Initialize database with optimized schema and connection pooling
 * Now uses modular architecture for better maintainability
 */
export async function initDatabase(): Promise<any | null> {
  if (db) {
    return db;
  }

  try {
    // Use the new modular database initializer
    db = await initDatabaseCore();

    if (db) {
      // Load ID mappings for backward compatibility
      loadIdMappings(db);
      console.log('[DB] Database initialized successfully using modular architecture');
    }

    return db;
  } catch (error) {
    console.error('[DB] Failed to initialize database:', error);
    db = null;
    return null;
  }
}

// Schema creation functions are now handled by the modular SchemaManager
// These functions are kept for backward compatibility but delegate to the new system

// Organization table creation is now handled by SchemaManager

// Schema verification and gradient column management are now handled by modular components

// Deprecated schema creation function removed - now handled by modular SchemaManager


/**
 * Get or create integer ID for a UUID
 */
function getOrCreateIntegerId(uuid: string, table: 'products' | 'colors'): number {
  // Check cache first
  if (idMappingCache.has(uuid)) {
    return idMappingCache.get(uuid)!;
  }

  // Check database
  const row = db!.prepare(`SELECT id FROM ${table} WHERE external_id = ?`).get(uuid) as {id: number} | undefined;
  if (row) {
    idMappingCache.set(uuid, row.id);
    reverseIdMappingCache.set(row.id, uuid);
    return row.id;
  }

  // ID doesn't exist yet, will be created during insert
  return 0;
}

/**
 * Close the database connection
 */
export function closeDatabase(): void {
  if (db) {
    // Run optimize before closing
    db.exec('PRAGMA optimize');
    db.close();
    db = null;
  }
}

// Export compatibility functions that match the old API
export function getDatabase(): any | null {
  return db;
}

// Continue in next part...
// ============================================
// Compatibility Layer - Product Operations
// ============================================

/**
 * Get all products (maintains old API)
 */
export function getAllProducts(): any[] {
  if (!db) {throw new Error('Database not initialized');}

  const products = db.prepare(`
    SELECT 
      p.external_id as id,
      p.name,
      p.metadata->>'description' as description,
      p.created_at as createdAt,
      p.updated_at as updatedAt,
      p.metadata->>'createdBy' as createdBy,
      p.metadata->>'updatedBy' as updatedBy
    FROM products p
    WHERE p.is_active = TRUE
    ORDER BY p.name
  `).all();

  return products;
}

/**
 * Get product by ID (maintains old API)
 */
export function getProductById(id: string): any | null {
  if (!db) {throw new Error('Database not initialized');}

  const product = db.prepare(`
    SELECT 
      p.external_id as id,
      p.name,
      p.metadata->>'description' as description,
      p.created_at as createdAt,
      p.updated_at as updatedAt,
      p.metadata->>'createdBy' as createdBy,
      p.metadata->>'updatedBy' as updatedBy
    FROM products p
    WHERE p.external_id = ? AND p.is_active = TRUE
  `).get(id) as any;

  return product || null;
}

/**
 * Create product (maintains old API)
 */
export function createProduct(data: { name: string; description?: string; createdBy?: string }): any {
  if (!db) {throw new Error('Database not initialized');}

  const id = uuidv4();
  const metadata = {
    description: data.description,
    createdBy: data.createdBy
  };

  db.prepare(`
    INSERT INTO products (external_id, name, metadata)
    VALUES (?, ?, json(?))
  `).run(id, data.name, JSON.stringify(metadata));

  return {
    id,
    name: data.name,
    description: data.description,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: data.createdBy
  };
}

/**
 * Update product (maintains old API)
 */
export function updateProduct(id: string, data: { name?: string; description?: string; updatedBy?: string }): any | null {
  if (!db) {throw new Error('Database not initialized');}

  const existing = getProductById(id);
  if (!existing) {return null;}

  const metadata = {
    description: data.description || existing.description,
    createdBy: existing.createdBy,
    updatedBy: data.updatedBy
  };

  db.prepare(`
    UPDATE products 
    SET name = COALESCE(?, name),
        metadata = json(?),
        updated_at = CURRENT_TIMESTAMP
    WHERE external_id = ?
  `).run(data.name, JSON.stringify(metadata), id);

  return getProductById(id);
}

/**
 * Delete product (maintains old API)
 */
export function deleteProduct(id: string): boolean {
  if (!db) {throw new Error('Database not initialized');}

  const result = db.prepare(`
    UPDATE products 
    SET is_active = FALSE,
        updated_at = CURRENT_TIMESTAMP
    WHERE external_id = ?
  `).run(id);

  return result.changes > 0;
}

// ============================================
// Compatibility Layer - Color Operations
// ============================================

/**
 * Get all colors (maintains old API)
 */
export function getAllColors(): ColorEntry[] {
  if (!db) {throw new Error('Database not initialized');}

  const colors = db.prepare(`
    SELECT 
      c.id as internal_id,
      c.external_id,
      c.source_id,
      c.code,
      c.display_name,
      c.hex,
      c.is_gradient,
      c.properties,
      c.created_at,
      c.updated_at,
      cm.c, cm.m, cm.y, cm.k,
      GROUP_CONCAT(pc.product_id) as product_ids
    FROM colors c
    LEFT JOIN color_cmyk cm ON c.id = cm.color_id
    LEFT JOIN product_colors pc ON c.id = pc.color_id
    WHERE c.is_active = TRUE
    GROUP BY c.id
    ORDER BY c.code
  `).all() as any[];

  // Convert to old format
  return colors.map(color => {
    const cmyk = color.c !== null ? `${color.c},${color.m},${color.y},${color.k}` : '0,0,0,0';
    
    // Get product name if associated
    let productName = '';
    if (color.product_ids) {
      const productId = reverseIdMappingCache.get(parseInt(color.product_ids.split(',')[0]));
      if (productId) {
        const product = getProductById(productId);
        if (product) {productName = product.name;}
      }
    }

    return {
      id: color.external_id,
      product: productName,
      name: color.display_name || color.code,
      code: color.code,
      hex: color.hex,
      cmyk: cmyk,
      notes: color.properties?.notes,
      gradient: color.is_gradient ? getGradientInfo(color.internal_id) : undefined,
      isLibrary: color.source_id !== 1,
      createdAt: color.created_at,
      updatedAt: color.updated_at
    } as ColorEntry;
  });
}

/**
 * Get color by ID (maintains old API)
 */
export function getColorById(id: string): ColorEntry | null {
  if (!db) {throw new Error('Database not initialized');}

  const color = db.prepare(`
    SELECT 
      c.id as internal_id,
      c.external_id,
      c.source_id,
      c.code,
      c.display_name,
      c.hex,
      c.is_gradient,
      c.properties,
      c.created_at,
      c.updated_at,
      cm.c, cm.m, cm.y, cm.k
    FROM colors c
    LEFT JOIN color_cmyk cm ON c.id = cm.color_id
    WHERE c.external_id = ? AND c.is_active = TRUE
  `).get(id) as any;

  if (!color) {return null;}

  // Get associated product
  const productAssoc = db.prepare(`
    SELECT p.external_id, p.name
    FROM product_colors pc
    JOIN products p ON pc.product_id = p.id
    WHERE pc.color_id = ?
    LIMIT 1
  `).get(color.internal_id) as any;

  const cmyk = color.c !== null ? `${color.c},${color.m},${color.y},${color.k}` : '0,0,0,0';

  return {
    id: color.external_id,
    product: productAssoc?.name || '',
    name: color.display_name || color.code,
    code: color.code,
    hex: color.hex,
    cmyk: cmyk,
    notes: color.properties?.notes,
    gradient: color.is_gradient ? getGradientInfo(color.internal_id) : undefined,
    isLibrary: color.source_id !== 1,
    createdAt: color.created_at,
    updatedAt: color.updated_at
  } as ColorEntry;
}

/**
 * Get gradient info for a color
 */
function getGradientInfo(colorId: number): any {
  const stops = db!.prepare(`
    SELECT stop_index, position, hex
    FROM gradient_stops
    WHERE gradient_id = ?
    ORDER BY stop_index
  `).all(colorId) as Array<{stop_index: number, position: number, hex: string}>;

  if (stops.length === 0) {return undefined;}

  const gradientStops = stops.map(stop => ({
    color: stop.hex,
    position: stop.position,
    cmyk: undefined // Can be calculated if needed
  }));

  const colorStops = gradientStops
    .map(stop => `${stop.color} ${stop.position * 100}%`)
    .join(', ');

  return {
    gradientStops,
    gradientCSS: `linear-gradient(90deg, ${colorStops})`
  };
}

/**
 * Create color (maintains old API)
 */
export function createColor(data: Partial<ColorEntry> & { createdBy?: string }): ColorEntry {
  if (!db) {throw new Error('Database not initialized');}

  const tx = db.transaction(() => {
    const externalId = data.id || uuidv4();
    const sourceId = data.isLibrary ? 2 : 1; // 2 for PANTONE, 1 for user
    
    // Validate and standardize hex
    const hex = isValidHex(data.hex!) ? data.hex!.toUpperCase() : '#000000';
    
    // Parse CMYK
    let cmykValues = [0, 0, 0, 0];
    if (data.cmyk) {
      const parsed = standardizeCMYK(data.cmyk);
      if (parsed && typeof parsed === 'string') {
        cmykValues = parsed.split(',').map(v => parseInt(v));
      }
    } else if (hex !== '#000000') {
      // Calculate CMYK from hex
      const calculated = hexToCmyk(hex);
      if (calculated) {
        cmykValues = [calculated.c, calculated.m, calculated.y, calculated.k];
      }
    }

    // Insert color
    const result = db.prepare(`
      INSERT INTO colors (external_id, source_id, code, display_name, hex, is_gradient, properties)
      VALUES (?, ?, ?, ?, ?, ?, json(?))
    `).run(
      externalId,
      sourceId,
      data.code || 'CUSTOM',
      data.name || data.code || 'Custom Color',
      hex,
      data.gradient ? 1 : 0,
      JSON.stringify({ notes: data.notes, createdBy: data.createdBy })
    );

    const colorId = result.lastInsertRowid;

    // Insert CMYK values
    db.prepare(`
      INSERT INTO color_cmyk (color_id, c, m, y, k)
      VALUES (?, ?, ?, ?, ?)
    `).run(colorId, cmykValues[0], cmykValues[1], cmykValues[2], cmykValues[3]);

    // Insert RGB values (calculated from hex)
    const rgb = hexToRgb(hex);
    if (rgb) {
      db.prepare(`
        INSERT INTO color_rgb (color_id, r, g, b)
        VALUES (?, ?, ?, ?)
      `).run(colorId, rgb.r, rgb.g, rgb.b);
    }

    // Handle gradient stops if present
    if (data.gradient && data.gradient.gradientStops) {
      data.gradient.gradientStops.forEach((stop, index) => {
        db.prepare(`
          INSERT INTO gradient_stops (gradient_id, stop_index, position, hex)
          VALUES (?, ?, ?, ?)
        `).run(colorId, index, stop.position, stop.color);
      });
    }

    // Associate with product if provided
    if (data.product) {
      const product = db.prepare('SELECT id FROM products WHERE name = ?').get(data.product) as {id: number} | undefined;
      if (product) {
        db.prepare(`
          INSERT INTO product_colors (product_id, color_id)
          VALUES (?, ?)
        `).run(product.id, colorId);
      }
    }

    // Update caches
    idMappingCache.set(externalId, colorId as number);
    reverseIdMappingCache.set(colorId as number, externalId);

    return {
      id: externalId,
      product: data.product || '',
      name: data.name || data.code || 'Custom Color',
      code: data.code || 'CUSTOM',
      hex: hex,
      cmyk: cmykValues.join(','),
      notes: data.notes,
      gradient: data.gradient,
      isLibrary: data.isLibrary || false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    } as ColorEntry;
  });

  return tx();
}
/**
 * Update color (maintains old API)
 */
export function updateColor(id: string, data: Partial<ColorEntry> & { updatedBy?: string }): ColorEntry | null {
  if (!db) {throw new Error('Database not initialized');}

  const existing = getColorById(id);
  if (!existing) {return null;}

  const tx = db.transaction(() => {
    // Get internal ID
    const internalId = getOrCreateIntegerId(id, 'colors');
    
    // Update main color record
    const updates: string[] = [];
    const params: any[] = [];

    if (data.code !== undefined) {
      updates.push('code = ?');
      params.push(data.code);
    }
    if (data.name !== undefined) {
      updates.push('display_name = ?');
      params.push(data.name);
    }
    if (data.hex !== undefined && isValidHex(data.hex)) {
      updates.push('hex = ?');
      params.push(data.hex.toUpperCase());
    }
    if (data.notes !== undefined) {
      const properties = existing.notes ? JSON.parse(existing.notes) : {};
      properties.notes = data.notes;
      properties.updatedBy = data.updatedBy;
      updates.push('properties = json(?)');
      params.push(JSON.stringify(properties));
    }

    if (updates.length > 0) {
      params.push(id);
      db.prepare(`
        UPDATE colors 
        SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE external_id = ?
      `).run(...params);
    }

    // Update CMYK if provided
    if (data.cmyk) {
      const cmykValues = standardizeCMYK(data.cmyk)?.split(',').map(v => parseInt(v));
      if (cmykValues && cmykValues.length === 4) {
        db.prepare(`
          INSERT OR REPLACE INTO color_cmyk (color_id, c, m, y, k)
          VALUES (?, ?, ?, ?, ?)
        `).run(internalId, ...cmykValues);
      }
    }

    // Update RGB if hex changed
    if (data.hex && isValidHex(data.hex)) {
      const rgb = hexToRgb(data.hex);
      if (rgb) {
        db.prepare(`
          INSERT OR REPLACE INTO color_rgb (color_id, r, g, b)
          VALUES (?, ?, ?, ?)
        `).run(internalId, rgb.r, rgb.g, rgb.b);
      }
    }

    return getColorById(id);
  });

  return tx();
}

/**
 * Delete color (maintains old API)
 */
export function deleteColor(id: string): boolean {
  if (!db) {throw new Error('Database not initialized');}

  const result = db.prepare(`
    UPDATE colors 
    SET deleted_at = CURRENT_TIMESTAMP
    WHERE external_id = ? AND is_active = TRUE
  `).run(id);

  return result.changes > 0;
}

/**
 * Get colors by product (maintains old API)
 */
export function getColorsByProduct(productId: string): ColorEntry[] {
  if (!db) {throw new Error('Database not initialized');}

  const colors = db.prepare(`
    SELECT 
      c.id as internal_id,
      c.external_id,
      c.source_id,
      c.code,
      c.display_name,
      c.hex,
      c.is_gradient,
      c.properties,
      c.created_at,
      c.updated_at,
      cm.c, cm.m, cm.y, cm.k,
      p.name as product_name
    FROM colors c
    JOIN product_colors pc ON c.id = pc.color_id
    JOIN products p ON pc.product_id = p.id
    LEFT JOIN color_cmyk cm ON c.id = cm.color_id
    WHERE p.external_id = ? AND c.is_active = TRUE
    ORDER BY pc.display_order, c.code
  `).all(productId) as any[];

  return colors.map(color => {
    const cmyk = color.c !== null ? `${color.c},${color.m},${color.y},${color.k}` : '0,0,0,0';
    
    return {
      id: color.external_id,
      product: color.product_name,
      name: color.display_name || color.code,
      code: color.code,
      hex: color.hex,
      cmyk: cmyk,
      notes: color.properties?.notes,
      gradient: color.is_gradient ? getGradientInfo(color.internal_id) : undefined,
      isLibrary: color.source_id !== 1,
      createdAt: color.created_at,
      updatedAt: color.updated_at
    } as ColorEntry;
  });
}

/**
 * Associate color with product
 */
export function associateColorWithProduct(colorId: string, productId: string): boolean {
  if (!db) {throw new Error('Database not initialized');}

  try {
    const colorInternalId = getOrCreateIntegerId(colorId, 'colors');
    const productInternalId = getOrCreateIntegerId(productId, 'products');

    if (!colorInternalId || !productInternalId) {return false;}

    db.prepare(`
      INSERT OR IGNORE INTO product_colors (product_id, color_id)
      VALUES (?, ?)
    `).run(productInternalId, colorInternalId);

    return true;
  } catch (error) {
    console.error('[DB] Failed to associate color with product:', error);
    return false;
  }
}

/**
 * Disassociate color from product
 */
export function disassociateColorFromProduct(colorId: string, productId: string): boolean {
  if (!db) {throw new Error('Database not initialized');}

  try {
    const colorInternalId = getOrCreateIntegerId(colorId, 'colors');
    const productInternalId = getOrCreateIntegerId(productId, 'products');

    if (!colorInternalId || !productInternalId) {return false;}

    const result = db.prepare(`
      DELETE FROM product_colors 
      WHERE product_id = ? AND color_id = ?
    `).run(productInternalId, colorInternalId);

    return result.changes > 0;
  } catch (error) {
    console.error('[DB] Failed to disassociate color from product:', error);
    return false;
  }
}

// ============================================
// Performance Monitoring & Maintenance
// ============================================

/**
 * Run database maintenance
 */
export function runMaintenance(): void {
  if (!db) {throw new Error('Database not initialized');}

  console.log('[DB] Running maintenance...');
  
  // Checkpoint WAL
  db.pragma('wal_checkpoint(TRUNCATE)');
  
  // Analyze tables for query optimizer
  db.exec('ANALYZE');
  
  // Run optimize
  db.pragma('optimize');
  
  console.log('[DB] Maintenance complete');
}

/**
 * Get database statistics
 */
export function getDatabaseStats(): any {
  if (!db) {throw new Error('Database not initialized');}

  const stats = {
    products: db.prepare('SELECT COUNT(*) as count FROM products WHERE is_active = TRUE').get(),
    colors: db.prepare('SELECT COUNT(*) as count FROM colors WHERE is_active = TRUE').get(),
    libraryColors: db.prepare('SELECT COUNT(*) as count FROM colors WHERE source_id != 1 AND is_active = TRUE').get(),
    userColors: db.prepare('SELECT COUNT(*) as count FROM colors WHERE source_id = 1 AND is_active = TRUE').get(),
    gradients: db.prepare('SELECT COUNT(*) as count FROM colors WHERE is_gradient = TRUE AND is_active = TRUE').get(),
    associations: db.prepare('SELECT COUNT(*) as count FROM product_colors').get(),
    walSize: 0
  };

  // Check WAL file size
  try {
    const walPath = getDatabasePath() + '-wal';
    if (fs.existsSync(walPath)) {
      stats.walSize = fs.statSync(walPath).size;
    }
  } catch (_error) {
    // Ignore
  }

  return stats;
}

// ============================================
// Search Operations
// ============================================

/**
 * Search colors by name or code
 */
export function searchColors(query: string): ColorEntry[] {
  if (!db) {throw new Error('Database not initialized');}
  
  const searchTerm = `%${query}%`;
  
  const colors = db.prepare(`
    SELECT 
      c.id as internal_id,
      c.external_id,
      c.source_id,
      c.code,
      c.display_name,
      c.hex,
      c.is_gradient,
      c.properties,
      c.created_at,
      c.updated_at,
      cm.c, cm.m, cm.y, cm.k
    FROM colors c
    LEFT JOIN color_cmyk cm ON c.id = cm.color_id
    WHERE c.is_active = TRUE
      AND (c.code LIKE ? OR c.display_name LIKE ? OR c.search_terms LIKE ?)
    ORDER BY 
      CASE 
        WHEN c.code = ? THEN 1
        WHEN c.code LIKE ? THEN 2
        ELSE 3
      END,
      c.code
    LIMIT 100
  `).all(searchTerm, searchTerm, searchTerm, query, `${query}%`) as any[];

  return colors.map(color => {
    const cmyk = color.c !== null ? `${color.c},${color.m},${color.y},${color.k}` : '0,0,0,0';
    
    return {
      id: color.external_id,
      product: '',
      name: color.display_name || color.code,
      code: color.code,
      hex: color.hex,
      cmyk: cmyk,
      notes: color.properties?.notes,
      gradient: color.is_gradient ? getGradientInfo(color.internal_id) : undefined,
      isLibrary: color.source_id !== 1,
      createdAt: color.created_at,
      updatedAt: color.updated_at
    } as ColorEntry;
  });
}

/**
 * Find similar colors by hex value
 */
export function findSimilarColors(_hex: string, _threshold: number = 50): ColorEntry[] {
  if (!db) {throw new Error('Database not initialized');}
  
  // This would use the color_distance custom function in production
  // For now, we'll do a simple hex comparison
  const colors = db.prepare(`
    SELECT 
      c.id as internal_id,
      c.external_id,
      c.source_id,
      c.code,
      c.display_name,
      c.hex,
      c.is_gradient,
      c.properties,
      c.created_at,
      c.updated_at,
      cm.c, cm.m, cm.y, cm.k
    FROM colors c
    LEFT JOIN color_cmyk cm ON c.id = cm.color_id
    WHERE c.is_active = TRUE AND c.is_gradient = FALSE
    ORDER BY c.hex
    LIMIT 20
  `).all() as any[];

  return colors.map(color => {
    const cmyk = color.c !== null ? `${color.c},${color.m},${color.y},${color.k}` : '0,0,0,0';
    
    return {
      id: color.external_id,
      product: '',
      name: color.display_name || color.code,
      code: color.code,
      hex: color.hex,
      cmyk: cmyk,
      notes: color.properties?.notes,
      gradient: undefined,
      isLibrary: color.source_id !== 1,
      createdAt: color.created_at,
      updatedAt: color.updated_at
    } as ColorEntry;
  });
}

// Export all functions
export default {
  initDatabase,
  closeDatabase,
  getDatabase,
  
  // Products
  getAllProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  
  // Colors
  getAllColors,
  getColorById,
  createColor,
  updateColor,
  deleteColor,
  getColorsByProduct,
  searchColors,
  findSimilarColors,
  
  // Associations
  associateColorWithProduct,
  disassociateColorFromProduct,
  
  // Maintenance
  runMaintenance,
  getDatabaseStats
};