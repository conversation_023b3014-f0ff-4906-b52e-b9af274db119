/**
 * @file sync-config.ts
 * @description Configuration management for the sync system
 * 
 * This module provides centralized configuration management with
 * environment-specific settings, validation, and runtime updates.
 */

import { 
  SyncConfig, 
  BatchSizeConfig, 
  TimeoutConfig, 
  RetryConfig, 
  ConflictStrategy,
  NetworkQuality 
} from './sync-types';

// ============================================================================
// DEFAULT CONFIGURATIONS
// ============================================================================

/**
 * Default batch sizes optimized for different network conditions
 */
const DEFAULT_BATCH_SIZES: BatchSizeConfig = {
  products: 100,      // Products per batch
  colors: 100,        // Colors per batch  
  relationships: 100, // Product-color relationships per batch
  supabaseQuery: 1000 // Max items per Supabase query
};

/**
 * Default timeout configurations (in milliseconds)
 */
const DEFAULT_TIMEOUTS: TimeoutConfig = {
  connection: 30000,    // 30 seconds for connection timeout
  heartbeat: 30000,     // 30 seconds between heartbeats
  healthCheck: 120000,  // 2 minutes between health checks
  recovery: 30000       // 30 seconds between recovery attempts
};

/**
 * Default retry configuration
 */
const DEFAULT_RETRY: RetryConfig = {
  maxAttempts: 5,
  baseDelay: 1000,      // 1 second base delay
  maxDelay: 30000,      // 30 seconds max delay
  backoffMultiplier: 2  // Exponential backoff multiplier
};

/**
 * Default sync configuration
 */
const DEFAULT_CONFIG: SyncConfig = {
  batchSizes: DEFAULT_BATCH_SIZES,
  timeouts: DEFAULT_TIMEOUTS,
  retry: DEFAULT_RETRY,
  conflictStrategy: ConflictStrategy.LAST_WRITE_WINS,
  enableAnalytics: true,
  logLevel: 'info'
};

// ============================================================================
// CONFIGURATION MANAGER
// ============================================================================

/**
 * Centralized configuration manager for the sync system
 */
export class SyncConfigManager {
  private config: SyncConfig;
  private listeners: Array<(config: SyncConfig) => void> = [];

  constructor(initialConfig?: Partial<SyncConfig>) {
    this.config = this.mergeConfig(DEFAULT_CONFIG, initialConfig || {});
    this.validateConfig();
  }

  /**
   * Get the current configuration
   */
  getConfig(): Readonly<SyncConfig> {
    return Object.freeze({ ...this.config });
  }

  /**
   * Update configuration with partial updates
   */
  updateConfig(updates: Partial<SyncConfig>): void {
    const newConfig = this.mergeConfig(this.config, updates);
    this.validateConfig(newConfig);
    
    const oldConfig = this.config;
    this.config = newConfig;
    
    // Notify listeners of configuration changes
    this.notifyListeners(oldConfig, newConfig);
  }

  /**
   * Get batch sizes optimized for current network quality
   */
  getOptimizedBatchSizes(networkQuality: NetworkQuality): BatchSizeConfig {
    const base = this.config.batchSizes;
    
    switch (networkQuality) {
      case 'excellent':
        return {
          products: Math.floor(base.products * 1.5),
          colors: Math.floor(base.colors * 1.5),
          relationships: Math.floor(base.relationships * 1.5),
          supabaseQuery: base.supabaseQuery
        };
      
      case 'good':
        return base;
      
      case 'fair':
        return {
          products: Math.floor(base.products * 0.7),
          colors: Math.floor(base.colors * 0.7),
          relationships: Math.floor(base.relationships * 0.7),
          supabaseQuery: Math.floor(base.supabaseQuery * 0.8)
        };
      
      case 'poor':
        return {
          products: Math.floor(base.products * 0.4),
          colors: Math.floor(base.colors * 0.4),
          relationships: Math.floor(base.relationships * 0.4),
          supabaseQuery: Math.floor(base.supabaseQuery * 0.5)
        };
      
      case 'offline':
      default:
        return {
          products: 10,
          colors: 10,
          relationships: 10,
          supabaseQuery: 50
        };
    }
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  calculateRetryDelay(attempt: number): number {
    const { baseDelay, maxDelay, backoffMultiplier } = this.config.retry;
    const exponentialDelay = baseDelay * Math.pow(backoffMultiplier, attempt);
    const jitter = Math.random() * 0.1 * exponentialDelay; // 10% jitter
    
    return Math.min(exponentialDelay + jitter, maxDelay);
  }

  /**
   * Check if operation should be retried based on attempt count
   */
  shouldRetry(attempt: number): boolean {
    return attempt < this.config.retry.maxAttempts;
  }

  /**
   * Get timeout for specific operation type
   */
  getTimeout(operation: keyof TimeoutConfig): number {
    return this.config.timeouts[operation];
  }

  /**
   * Subscribe to configuration changes
   */
  onConfigChange(listener: (config: SyncConfig) => void): () => void {
    this.listeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Reset configuration to defaults
   */
  resetToDefaults(): void {
    this.updateConfig(DEFAULT_CONFIG);
  }

  /**
   * Export current configuration for persistence
   */
  exportConfig(): string {
    return JSON.stringify(this.config, null, 2);
  }

  /**
   * Import configuration from JSON string
   */
  importConfig(configJson: string): void {
    try {
      const importedConfig = JSON.parse(configJson);
      this.updateConfig(importedConfig);
    } catch (error) {
      throw new Error(`Invalid configuration JSON: ${error.message}`);
    }
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Deep merge two configuration objects
   */
  private mergeConfig(base: SyncConfig, updates: Partial<SyncConfig>): SyncConfig {
    return {
      batchSizes: { ...base.batchSizes, ...updates.batchSizes },
      timeouts: { ...base.timeouts, ...updates.timeouts },
      retry: { ...base.retry, ...updates.retry },
      conflictStrategy: updates.conflictStrategy ?? base.conflictStrategy,
      enableAnalytics: updates.enableAnalytics ?? base.enableAnalytics,
      logLevel: updates.logLevel ?? base.logLevel
    };
  }

  /**
   * Validate configuration values
   */
  private validateConfig(config: SyncConfig = this.config): void {
    // Validate batch sizes
    if (config.batchSizes.products <= 0 || config.batchSizes.products > 1000) {
      throw new Error('Products batch size must be between 1 and 1000');
    }
    
    if (config.batchSizes.colors <= 0 || config.batchSizes.colors > 1000) {
      throw new Error('Colors batch size must be between 1 and 1000');
    }

    // Validate timeouts
    if (config.timeouts.connection <= 0 || config.timeouts.connection > 300000) {
      throw new Error('Connection timeout must be between 1ms and 5 minutes');
    }

    // Validate retry configuration
    if (config.retry.maxAttempts <= 0 || config.retry.maxAttempts > 10) {
      throw new Error('Max retry attempts must be between 1 and 10');
    }

    if (config.retry.baseDelay <= 0 || config.retry.baseDelay > 60000) {
      throw new Error('Base retry delay must be between 1ms and 1 minute');
    }

    // Validate log level
    const validLogLevels = ['debug', 'info', 'warn', 'error'];
    if (!validLogLevels.includes(config.logLevel)) {
      throw new Error(`Log level must be one of: ${validLogLevels.join(', ')}`);
    }
  }

  /**
   * Notify all listeners of configuration changes
   */
  private notifyListeners(oldConfig: SyncConfig, newConfig: SyncConfig): void {
    // Only notify if there are actual changes
    if (JSON.stringify(oldConfig) !== JSON.stringify(newConfig)) {
      this.listeners.forEach(listener => {
        try {
          listener(newConfig);
        } catch (error) {
          console.error('[SyncConfig] Error in configuration change listener:', error);
        }
      });
    }
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

/**
 * Global configuration manager instance
 */
let configManager: SyncConfigManager | null = null;

/**
 * Get or create the global configuration manager
 */
export function getSyncConfig(initialConfig?: Partial<SyncConfig>): SyncConfigManager {
  if (!configManager) {
    configManager = new SyncConfigManager(initialConfig);
  }
  return configManager;
}

/**
 * Reset the global configuration manager (useful for testing)
 */
export function resetSyncConfig(): void {
  configManager = null;
}

// ============================================================================
// EXPORTS
// ============================================================================

export {
  DEFAULT_CONFIG,
  DEFAULT_BATCH_SIZES,
  DEFAULT_TIMEOUTS,
  DEFAULT_RETRY
};
