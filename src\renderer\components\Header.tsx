/**
 * @file Header.tsx
 * @description Apple-inspired header component with app title, data controls, and window controls
 */

import { useState, useRef, useEffect } from 'react';
import { useClickOutside } from '../utils/useClickOutside';
import { useColorStore } from '../store/color.store';
import { OrganizationSwitcher } from './organization/OrganizationSwitcher';
import WindowControls from './WindowControls';
import { SyncModal } from './Sync/SyncModal';
// Import logo images
import logoNormal from '../../../assets/IVG Logo normal.svg';
import logoDarkMode from '../../../assets/IVG Logo Dark Mode.svg';

export default function Header() {
  const { importColors, exportColors, clearColors, darkMode, toggleDarkMode } = useColorStore();
  const [menuOpen, setMenuOpen] = useState(false);
  const [formatDialogOpen, setFormatDialogOpen] = useState(false);
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [clearDialogOpen, setClearDialogOpen] = useState(false);
  const [clearConfirmText, setClearConfirmText] = useState('');
  const [syncModalOpen, setSyncModalOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const formatDialogRef = useRef<HTMLDivElement>(null);
  const importDialogRef = useRef<HTMLDivElement>(null);
  const clearDialogRef = useRef<HTMLDivElement>(null);
  const [zoomFactor, setZoomFactor] = useState<number>(1.0);

  // Feature flags context (removed unused variable)

  // Sync actions and status

  // Add a separate useEffect for initialization that runs once on mount
  useEffect(() => {
    // Force immediate sync of dark mode value to ensure correct initial logo
    const isDarkMode = localStorage.getItem('theme') === 'dark' ||
      (localStorage.getItem('theme') === null &&
       window.matchMedia('(prefers-color-scheme: dark)').matches);

    if (isDarkMode !== darkMode) {
      console.log('Syncing initial dark mode state:', isDarkMode);
      // Use the store's method to ensure everything is updated properly
      toggleDarkMode();
    }
    // We only want this to run once on mount
  }, []);

  // Check if any dialog is open to keep titlebar visible
  const isAnyDialogOpen = formatDialogOpen || importDialogOpen || clearDialogOpen || menuOpen;

  // Fetch the current zoom factor on load and when it changes
  useEffect(() => {
    const getZoomFactor = async () => {
      if (window.app && window.app.zoom) {
        try {
          const factor = await window.app.zoom.getZoomFactor();
          setZoomFactor(factor);
        } catch (error) {
          console.error('Failed to get zoom factor:', error);
        }
      }
    };

    getZoomFactor();

    // Listen for zoom changes
    const zoomChangeInterval = setInterval(getZoomFactor, 1000);

    return () => {
      clearInterval(zoomChangeInterval);
    };
  }, []);

  // Handle zoom controls
  const handleZoomIn = () => {
    if (window.app && window.app.zoom) {
      window.app.zoom.zoomIn();

      // Update zoom factor with a small delay to allow the main process to update
      setTimeout(async () => {
        if (window.app && window.app.zoom) {
          const factor = await window.app.zoom.getZoomFactor();
          setZoomFactor(factor);
        }
      }, 100);
    }
  };

  const handleZoomOut = () => {
    if (window.app && window.app.zoom) {
      window.app.zoom.zoomOut();

      // Update zoom factor with a small delay to allow the main process to update
      setTimeout(async () => {
        if (window.app && window.app.zoom) {
          const factor = await window.app.zoom.getZoomFactor();
          setZoomFactor(factor);
        }
      }, 100);
    }
  };

  const handleResetZoom = () => {
    if (window.app && window.app.zoom) {
      window.app.zoom.resetZoom();

      // Update zoom factor with a small delay to allow the main process to update
      setTimeout(async () => {
        if (window.app && window.app.zoom) {
          const factor = await window.app.zoom.getZoomFactor();
          setZoomFactor(factor);
        }
      }, 100);
    }
  };

  const handleImportWithMode = async (mergeMode: 'merge' | 'replace') => {
    try {
      console.log(`Starting import process with mode: ${mergeMode}`);

      // Call the store's importColors function - don't specify a path to let the dialog open
      const result = await importColors(mergeMode);
      console.log('Import result from store:', result);

      // Display appropriate message
      if (result.success) {
        alert(`${result.message || 'Colours imported successfully'}`);
      } else if (result.message && result.message.includes('cancel')) {
        // Don't show alert for cancellations
        console.log('Import canceled by user');
      } else if (result.message) {
        // Show alert for other errors
        alert(`Import failed: ${result.message}`);
      }
    } catch (error) {
      console.error('Import failed:', error);
      alert(`Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setImportDialogOpen(false);
    }
  };

  const handleCancelImport = () => {
    console.log('Import canceled by user');
    setImportDialogOpen(false);
  };

  const handleExportWithFormat = async (format: 'json' | 'csv') => {
    try {
      console.log(`Starting export process with format: ${format}`);

      // Call the store's exportColors function which will use the API
      // No need to specify file path as the dialog will be shown to the user by the main process
      const result = await exportColors(undefined, format);
      console.log('Export result from store:', result);

      // Display appropriate message based on success
      if (result.success) {
        alert(`${result.message || `Colours exported successfully as ${format.toUpperCase()}`}`);
      } else if (result.message && result.message.includes('cancel')) {
        // Don't show alert for cancellations - user already knows they canceled
        console.log('Export canceled by user');
      } else if (result.message) {
        // Show alert for other errors
        alert(`Export failed: ${result.message}`);
      }
    } catch (error) {
      console.error('Export failed:', error);
      alert(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setFormatDialogOpen(false);
    }
  };

  const handleCancelExport = () => {
    console.log('Export canceled by user');
    setFormatDialogOpen(false);
  };

  const handleConfirmClear = async () => {
    if (clearConfirmText.toLowerCase() === 'clear all data') {
      try {
        await clearColors();
        setClearDialogOpen(false);
        setClearConfirmText('');
      } catch (error) {
        console.error('Clear all failed:', error);
      }
    }
  };

  const handleCancelClear = () => {
    setClearDialogOpen(false);
    setClearConfirmText('');
  };

  // Close menu when clicking outside
  useClickOutside(menuRef, () => setMenuOpen(false));

  // Close format dialog when clicking outside
  useClickOutside(formatDialogRef, () => setFormatDialogOpen(false), formatDialogOpen);

  // Close import dialog when clicking outside
  useClickOutside(importDialogRef, () => setImportDialogOpen(false), importDialogOpen);

  // Close clear dialog when clicking outside
  useClickOutside(clearDialogRef, () => {
    setClearDialogOpen(false);
    setClearConfirmText('');
  }, clearDialogOpen);

  // Force a re-render to ensure logo is displayed
  useEffect(() => {
    // Debug logs removed to avoid console spam in production
  }, [darkMode]);

  return (
    <header className={`flex flex-col w-full ${isAnyDialogOpen ? 'dialog-open' : ''}`} role="banner" aria-label="Application header">
      {/* Draggable title bar (Windows style) */}
      <div
        className="app-title-bar grid grid-cols-3 items-center py-[var(--spacing-2)] px-[var(--spacing-3)] bg-[var(--color-ui-background-secondary)] dark:bg-[var(--color-ui-background-secondary)] app-drag-region rounded-t-xl"
      >
        {/* Left spacer for macOS traffic lights */}
        <div className={`flex-shrink-0 ${window.navigator.userAgent.includes('Mac') ? 'w-20' : ''}`} />

        {/* Centered title and organization switcher */}
        <div className="flex items-center justify-center space-x-2 app-drag-region">
          <div className="app-title text-[var(--font-size-sm)] text-[var(--color-ui-foreground-secondary)] font-[var(--font-weight-medium)]">
            ChromaSync
          </div>
          <span className="text-[var(--color-ui-foreground-tertiary)]">•</span>
          <OrganizationSwitcher />
        </div>

        {/* Right side controls */}
        <div className="flex items-center justify-end space-x-2">
          {/* Sync button */}
          <button
            onClick={() => setSyncModalOpen(true)}
            className="text-[var(--color-ui-foreground-primary)] hover:bg-[var(--color-ui-background-hover)] rounded-[var(--radius-md)] p-[var(--spacing-2)]"
            title="Sync Data"
            aria-label="Open sync settings"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
            </svg>
          </button>
          
          {/* Zoom controls */}
          <div
            className="flex items-center space-x-1 bg-[var(--color-ui-background-tertiary)] rounded-[var(--radius-md)] px-[var(--spacing-1)]"
          >
            <button
              className="text-[var(--color-ui-foreground-primary)] hover:bg-[var(--color-ui-background-hover)] rounded-[var(--radius-sm)] p-[var(--spacing-1)]"
              onClick={handleZoomOut}
              title={window.app?.shortcuts?.zoomOut || 'Zoom Out'}
              aria-label="Zoom Out"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 12H6" />
              </svg>
            </button>

            <span
              className="text-[var(--font-size-xs)] text-[var(--color-ui-foreground-primary)] cursor-pointer"
              onClick={handleResetZoom}
              title="Reset Zoom"
            >
              {Math.round(zoomFactor * 100)}%
            </span>

            <button
              className="text-[var(--color-ui-foreground-primary)] hover:bg-[var(--color-ui-background-hover)] rounded-[var(--radius-sm)] p-[var(--spacing-1)]"
              onClick={handleZoomIn}
              title={window.app?.shortcuts?.zoomIn || 'Zoom In'}
              aria-label="Zoom In"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v12M6 12h12" />
              </svg>
            </button>
          </div>

          {/* Window controls */}
          <WindowControls />
        </div>
      </div>

      {/* Main header content */}
      <div
        className="flex items-center justify-center py-[var(--spacing-2)] px-[var(--spacing-6)] bg-[var(--color-ui-background-primary)]"
      >
        {/* Centered logo with proper sizing */}
        <div className="flex justify-center items-center w-full">
          <div className="w-28 h-28 flex-shrink-0">
            <img
              src={darkMode ? logoDarkMode : logoNormal}
              alt="IVG Logo"
              className="w-full h-full object-contain"
              key={`logo-${darkMode ? 'dark' : 'light'}-${Date.now()}`}
              onError={(e) => {
                console.error('Logo failed to load:', e);
                // Try with absolute path as fallback
                const fallbackPath = darkMode
                  ? '/assets/IVG Logo Dark Mode.svg'
                  : '/assets/IVG Logo normal.svg';
                console.log('Trying fallback path:', fallbackPath);
                e.currentTarget.src = fallbackPath;
              }}
            />
          </div>
        </div>
        {/* Sync controls moved to TableControls */}
      </div>

      {/* Format Selection Dialog */}
      {formatDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-[var(--z-modal)]">
          <div
            ref={formatDialogRef}
            className="bg-[var(--color-ui-background-primary)] rounded-[var(--radius-lg)] shadow-[var(--shadow-xl)] p-[var(--spacing-6)] max-w-md w-full mx-[var(--spacing-4)]"
          >
            <h3 className="text-[var(--font-size-lg)] font-[var(--font-weight-medium)] mb-[var(--spacing-4)] text-[var(--color-ui-foreground-primary)]">
              Choose Export Format
            </h3>
            <div className="space-y-[var(--spacing-3)] mb-[var(--spacing-6)]">
              <button
                onClick={() => handleExportWithFormat('json')}
                className="w-full py-[var(--spacing-3)] px-[var(--spacing-4)] bg-[var(--color-ui-background-tertiary)] hover:bg-[var(--color-ui-background-secondary)] text-[var(--color-ui-foreground-primary)] rounded-[var(--radius-md)] transition-colors text-left"
              >
                <span className="font-[var(--font-weight-medium)]">JSON Format</span>
                <p className="text-[var(--font-size-sm)] text-[var(--color-ui-foreground-secondary)] mt-[var(--spacing-1)]">
                  Best for data preservation and reimporting later
                </p>
              </button>

              <button
                onClick={() => handleExportWithFormat('csv')}
                className="w-full py-[var(--spacing-3)] px-[var(--spacing-4)] bg-[var(--color-ui-background-tertiary)] hover:bg-[var(--color-ui-background-secondary)] text-[var(--color-ui-foreground-primary)] rounded-[var(--radius-md)] transition-colors text-left"
              >
                <span className="font-[var(--font-weight-medium)]">CSV Format</span>
                <p className="text-[var(--font-size-sm)] text-[var(--color-ui-foreground-secondary)] mt-[var(--spacing-1)]">
                  Best for opening in Excel or other spreadsheet software
                </p>
              </button>
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleCancelExport}
                className="py-[var(--spacing-2)] px-[var(--spacing-4)] bg-[var(--color-ui-background-tertiary)] hover:bg-[var(--color-ui-background-secondary)] text-[var(--color-ui-foreground-primary)] rounded-[var(--radius-md)] transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Import Mode Selection Dialog */}
      {importDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-[var(--z-modal)]">
          <div
            ref={importDialogRef}
            className="bg-[var(--color-ui-background-primary)] rounded-[var(--radius-lg)] shadow-[var(--shadow-xl)] p-[var(--spacing-6)] max-w-md w-full mx-[var(--spacing-4)]"
          >
            <h3 className="text-[var(--font-size-lg)] font-[var(--font-weight-medium)] mb-[var(--spacing-4)] text-[var(--color-ui-foreground-primary)]">
              Import Data
            </h3>
            <div className="space-y-[var(--spacing-3)] mb-[var(--spacing-6)]">
              <button
                onClick={() => handleImportWithMode('merge')}
                className="w-full py-[var(--spacing-3)] px-[var(--spacing-4)] bg-[var(--color-ui-background-tertiary)] hover:bg-[var(--color-ui-background-secondary)] text-[var(--color-ui-foreground-primary)] rounded-[var(--radius-md)] transition-colors text-left"
              >
                <span className="font-[var(--font-weight-medium)]">Merge with Existing Data</span>
                <p className="text-[var(--font-size-sm)] text-[var(--color-ui-foreground-secondary)] mt-[var(--spacing-1)]">
                  Add new colours while preserving existing ones
                </p>
              </button>

              <button
                onClick={() => handleImportWithMode('replace')}
                className="w-full py-[var(--spacing-3)] px-[var(--spacing-4)] bg-[var(--color-ui-background-tertiary)] hover:bg-[var(--color-ui-background-secondary)] text-[var(--color-ui-foreground-primary)] rounded-[var(--radius-md)] transition-colors text-left"
              >
                <span className="font-[var(--font-weight-medium)]">Replace Existing Data</span>
                <p className="text-[var(--font-size-sm)] text-[var(--color-ui-foreground-secondary)] mt-[var(--spacing-1)]">
                  Remove all existing colours and import new ones
                </p>
              </button>
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleCancelImport}
                className="py-[var(--spacing-2)] px-[var(--spacing-4)] bg-[var(--color-ui-background-tertiary)] hover:bg-[var(--color-ui-background-secondary)] text-[var(--color-ui-foreground-primary)] rounded-[var(--radius-md)] transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Clear All Data Dialog */}
      {clearDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-[var(--z-modal)]">
          <div
            ref={clearDialogRef}
            className="bg-[var(--color-ui-background-primary)] rounded-[var(--radius-lg)] shadow-[var(--shadow-xl)] p-[var(--spacing-6)] max-w-md w-full mx-[var(--spacing-4)]"
          >
            <h3 className="text-[var(--font-size-lg)] font-[var(--font-weight-medium)] mb-[var(--spacing-4)] text-[var(--color-ui-foreground-primary)]">
              Clear All Data
            </h3>
            <div className="mb-[var(--spacing-6)]">
              <p className="text-[var(--color-ui-foreground-primary)] mb-[var(--spacing-4)]">
                Are you sure you want to clear all colour data? This cannot be undone.
              </p>
              <p className="text-[var(--color-ui-foreground-primary)] mb-[var(--spacing-4)]">
                To confirm, please type <span className="font-[var(--font-weight-bold)]">clear all data</span> below:
              </p>
              <input
                type="text"
                value={clearConfirmText}
                onChange={(e) => setClearConfirmText(e.target.value)}
                placeholder="Type 'clear all data' to confirm"
                className="w-full px-[var(--spacing-3)] py-[var(--spacing-2)] bg-[var(--color-ui-background-tertiary)] border border-[var(--color-ui-border-light)] rounded-[var(--radius-md)] text-[var(--color-ui-foreground-primary)] placeholder-[var(--color-ui-foreground-tertiary)] focus:outline-none focus:ring-2 focus:ring-[var(--color-brand-primary)]"
              />
            </div>

            <div className="flex justify-end space-x-[var(--spacing-3)]">
              <button
                onClick={handleCancelClear}
                className="py-[var(--spacing-2)] px-[var(--spacing-4)] bg-[var(--color-ui-background-tertiary)] hover:bg-[var(--color-ui-background-secondary)] text-[var(--color-ui-foreground-primary)] rounded-[var(--radius-md)] transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmClear}
                disabled={clearConfirmText.toLowerCase() !== 'clear all data'}
                className={`py-[var(--spacing-2)] px-[var(--spacing-4)] rounded-[var(--radius-md)] transition-colors ${
                  clearConfirmText.toLowerCase() === 'clear all data'
                    ? 'bg-[var(--color-feedback-error)] hover:bg-opacity-90 text-white'
                    : 'bg-opacity-50 bg-[var(--color-feedback-error)] text-white cursor-not-allowed'
                }`}
              >
                Delete All Data
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Sync Modal */}
      {syncModalOpen && (
        <SyncModal
          isOpen={syncModalOpen}
          onClose={() => setSyncModalOpen(false)}
        />
      )}
    </header>
  );
}
