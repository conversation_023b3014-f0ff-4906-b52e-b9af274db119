/**
 * @file SyncPanel.tsx
 * @description Main component for sync functionality
 */

import React, { useState } from 'react';
import { SyncStatus } from './SyncStatus';
import { SyncAuth } from './SyncAuth';
import { SyncSettings } from './SyncSettings';
import { useSyncAuth, useSyncConflicts } from '../../store/sync.store';

// Tab type
type SyncTab = 'status' | 'auth' | 'settings';

export const SyncPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState<SyncTab>('status');
  const { isAuthenticated } = useSyncAuth();
  const { conflicts } = useSyncConflicts();

  // Render the active tab content
  const renderTabContent = () => {
    switch (activeTab) {
      case 'status':
        return <SyncStatus />;
      case 'auth':
        return <SyncAuth onSuccess={() => setActiveTab('status')} />;
      case 'settings':
        return <SyncSettings />;
      default:
        return <SyncStatus />;
    }
  };

  return (
    <div className="flex flex-col space-y-4 bg-ui-background-primary dark:bg-ui-background-tertiary rounded-[var(--radius-md)]">
      <div className="flex space-x-2 px-1 pt-1">
        <button
          onClick={() => setActiveTab('status')}
          className={`px-4 py-2 rounded-t text-sm font-medium ${
            activeTab === 'status'
              ? 'bg-brand-primary text-ui-foreground-inverse'
              : 'bg-ui-background-secondary dark:bg-ui-background-secondary text-ui-foreground-primary dark:text-ui-foreground-primary hover:bg-ui-background-hover dark:hover:bg-ui-background-hover'
          }`}
        >
          Status
          {conflicts.length > 0 && (
            <span className="ml-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
              {conflicts.length}
            </span>
          )}
        </button>

        <button
          onClick={() => setActiveTab('auth')}
          className={`px-4 py-2 rounded-t text-sm font-medium ${
            activeTab === 'auth'
              ? 'bg-brand-primary text-ui-foreground-inverse'
              : 'bg-ui-background-secondary dark:bg-ui-background-secondary text-ui-foreground-primary dark:text-ui-foreground-primary hover:bg-ui-background-hover dark:hover:bg-ui-background-hover'
          }`}
        >
          {isAuthenticated ? 'Account' : 'Login'}
        </button>

        {isAuthenticated && (
          <button
            onClick={() => setActiveTab('settings')}
            className={`px-4 py-2 rounded-t text-sm font-medium ${
              activeTab === 'settings'
                ? 'bg-brand-primary text-ui-foreground-inverse'
                : 'bg-ui-background-secondary dark:bg-ui-background-secondary text-ui-foreground-primary dark:text-ui-foreground-primary hover:bg-ui-background-hover dark:hover:bg-ui-background-hover'
            }`}
          >
            Settings
          </button>
        )}
      </div>

      <div className="p-4 border border-ui-border-light dark:border-ui-border-dark rounded-[var(--radius-md)] bg-ui-background-secondary dark:bg-ui-background-secondary">
        {renderTabContent()}
      </div>
    </div>
  );
};
