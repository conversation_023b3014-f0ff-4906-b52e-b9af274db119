# ChromaSync Invitation System - Complete Guide

## 🎯 How It Works Now

The invitation system has been updated to work around email client limitations with custom protocol links.

### 1. **Sending Invitations** 📤
- Admin/Owner goes to Settings → Team
- Enters email address and role
- Clicks "Send Invitation"
- Email is sent with clear instructions

### 2. **Invitation Email** 📧
The new email template includes:
- **Clear manual instructions** (primary method)
- **Invitation code displayed prominently**
- **Step-by-step guide:**
  1. Open ChromaSync
  2. Sign in with Google
  3. Go to Settings → Team
  4. Click "Join Organization"
  5. Paste the invitation code

### 3. **Accepting Invitations** ✅

#### **Method 1: Manual Entry (Recommended)**
1. User receives email with invitation code
2. Opens ChromaSync and signs in
3. Goes to Settings → Team
4. Clicks "Join Organization" button
5. Pastes the invitation code
6. Clicks "Accept Invitation"

#### **Method 2: Deep Link (If Supported)**
- Some users may be able to click `chromasync://invite/{token}`
- This will open ChromaSync and auto-fill the invitation code
- Works on desktop if ChromaSync is installed and protocol is registered

### 4. **Technical Flow** 🔧

```
<PERSON><PERSON> → User Reads Instructions → Opens ChromaSync → 
Signs In → Settings → Team → Join Organization → 
Enter Code → Accept → Added to Organization
```

## 📝 Implementation Details

### **Frontend Components**
- `AcceptInvitation.tsx` - Modal for accepting invitations
- `TeamSettings.tsx` - Added "Join Organization" button
- `App.tsx` - Listens for deep link invitations

### **Backend Services**
- `organization.service.ts` - Handles invitation logic
- `zoho-email.service.ts` - Sends invitation emails
- `organization.ipc.ts` - IPC handlers for invitation flow

### **Database Schema**
```sql
organization_invitations (
  id, external_id, organization_id, email, 
  role, invited_by, token, expires_at, accepted_at
)
```

## 🚀 Testing the System

### **Send Test Invitation**
```bash
# In ChromaSync app:
1. Go to Settings → Team
2. Enter: <EMAIL>
3. Select role: Member
4. Click "Send Invitation"
```

### **Accept Test Invitation**
```bash
# After receiving email:
1. Open ChromaSync
2. Sign in with Google
3. Settings → Team → Join Organization
4. Paste invitation code from email
5. Click "Accept Invitation"
```

## 🛡️ Security Features
- Unique 32-byte tokens
- 7-day expiration
- One-time use only
- Role-based permissions
- Email verification

## 🔍 Troubleshooting

### **"Invalid invitation" error**
- Check if invitation has expired (7 days)
- Verify the code was copied correctly
- Ensure you're signed in with the correct Google account

### **Email not received**
- Check spam folder
- Verify email address is correct
- Check Zoho email service logs

### **Can't join organization**
- Make sure you're signed in
- Check if organization has reached member limit
- Contact the organization admin

## 📋 Summary

The updated invitation system prioritizes compatibility and user experience:
- **Clear instructions** over technical complexity
- **Manual code entry** as primary method
- **Deep links** as optional enhancement
- **Better error handling** and user feedback

Users can now reliably join organizations regardless of their email client's limitations!
