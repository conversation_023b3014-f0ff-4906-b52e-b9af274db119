@echo off
echo ===================================
echo Pantone Tracker - Windows Installer Builder
echo ===================================
echo.
echo This script will build a distributable Windows installer (.exe)
echo The installer will be created in the dist-fresh directory
echo.
echo Press any key to start the build process or Ctrl+C to cancel...
pause > nul

echo.
echo Building Windows installer...
echo This may take several minutes - Please wait
echo.

node scripts/build-windows-installer.js

if %ERRORLEVEL% NEQ 0 (
  echo.
  echo Error: Installer build process failed with error code %ERRORLEVEL%
  echo Please check the console output above for more details.
  echo.
  echo Common issues:
  echo - Make sure all dependencies are installed (npm install)
  echo - Verify electron-builder is installed
  echo - Check if the project has been built successfully before
  echo.
  echo Press any key to exit...
  pause > nul
  exit /b %ERRORLEVEL%
)

echo.
echo ===================================
echo Build process completed!
echo The installer can be found in the dist-fresh directory
echo.
echo Press any key to exit...
pause > nul
