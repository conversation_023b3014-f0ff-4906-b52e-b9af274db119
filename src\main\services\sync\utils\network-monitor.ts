/**
 * @file network-monitor.ts
 * @description Network connectivity monitoring for sync operations
 * 
 * This module provides comprehensive network monitoring including
 * connectivity checks, quality assessment, and connection events.
 */

import { net } from 'electron';
import { 
  NetworkQuality, 
  NetworkMetrics,
  SyncEventHandler 
} from '../core/sync-types';

// ============================================================================
// NETWORK MONITORING TYPES
// ============================================================================

interface NetworkTest {
  url: string;
  timeout: number;
  expectedLatency: number;
}

interface ConnectivityResult {
  isOnline: boolean;
  latency: number;
  quality: NetworkQuality;
  timestamp: number;
  error?: string;
}

// ============================================================================
// NETWORK MONITOR
// ============================================================================

/**
 * Advanced network connectivity monitor
 */
export class NetworkMonitor {
  private isOnline = true;
  private currentQuality: NetworkQuality = 'good';
  private latency = 0;
  private lastCheck = 0;
  private eventHandlers = new Map<string, SyncEventHandler[]>();
  
  private readonly CHECK_INTERVAL = 10000; // 10 seconds
  private readonly TIMEOUT = 5000; // 5 seconds
  private readonly QUALITY_HISTORY_SIZE = 10;
  
  private monitorInterval: ReturnType<typeof setInterval> | null = null;
  private qualityHistory: NetworkQuality[] = [];
  
  // Test endpoints for connectivity checks
  private readonly testEndpoints: NetworkTest[] = [
    { url: 'https://supabase.com', timeout: 5000, expectedLatency: 200 },
    { url: 'https://www.google.com', timeout: 5000, expectedLatency: 100 },
    { url: 'https://www.cloudflare.com', timeout: 5000, expectedLatency: 150 }
  ];

  constructor() {
    this.startMonitoring();
  }

  /**
   * Get current network status
   */
  getNetworkStatus(): {
    isOnline: boolean;
    quality: NetworkQuality;
    latency: number;
    lastCheck: number;
    qualityTrend: 'improving' | 'stable' | 'degrading';
  } {
    return {
      isOnline: this.isOnline,
      quality: this.currentQuality,
      latency: this.latency,
      lastCheck: this.lastCheck,
      qualityTrend: this.analyzeQualityTrend()
    };
  }

  /**
   * Get detailed network metrics
   */
  getNetworkMetrics(): NetworkMetrics {
    return {
      latency: this.latency,
      bandwidth: this.estimateBandwidth(),
      quality: this.currentQuality,
      isOnline: this.isOnline,
      lastCheck: this.lastCheck
    };
  }

  /**
   * Perform immediate connectivity check
   */
  async checkConnectivity(): Promise<ConnectivityResult> {
    const startTime = Date.now();
    
    try {
      // Test multiple endpoints and use the best result
      const results = await Promise.allSettled(
        this.testEndpoints.map(endpoint => this.testEndpoint(endpoint))
      );
      
      // Find the best successful result
      const successfulResults = results
        .filter((result): result is PromiseFulfilledResult<ConnectivityResult> => 
          result.status === 'fulfilled' && result.value.isOnline
        )
        .map(result => result.value);
      
      if (successfulResults.length === 0) {
        // All tests failed
        const result: ConnectivityResult = {
          isOnline: false,
          latency: 0,
          quality: 'offline',
          timestamp: Date.now(),
          error: 'All connectivity tests failed'
        };
        
        this.updateNetworkStatus(result);
        return result;
      }
      
      // Use the result with the best latency
      const bestResult = successfulResults.reduce((best, current) => 
        current.latency < best.latency ? current : best
      );
      
      this.updateNetworkStatus(bestResult);
      return bestResult;
      
    } catch (error) {
      console.error('[NetworkMonitor] Connectivity check failed:', error);
      
      const result: ConnectivityResult = {
        isOnline: false,
        latency: 0,
        quality: 'offline',
        timestamp: Date.now(),
        error: error.message
      };
      
      this.updateNetworkStatus(result);
      return result;
    }
  }

  /**
   * Check if network quality is sufficient for sync operations
   */
  isSyncRecommended(): boolean {
    return this.isOnline && this.currentQuality !== 'poor';
  }

  /**
   * Get recommended batch size based on network quality
   */
  getRecommendedBatchSize(baseBatchSize: number): number {
    switch (this.currentQuality) {
      case 'excellent':
        return Math.floor(baseBatchSize * 1.5);
      case 'good':
        return baseBatchSize;
      case 'fair':
        return Math.floor(baseBatchSize * 0.7);
      case 'poor':
        return Math.floor(baseBatchSize * 0.4);
      case 'offline':
        return 1;
      default:
        return baseBatchSize;
    }
  }

  /**
   * Subscribe to network events
   */
  on(event: string, handler: SyncEventHandler): () => void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    
    this.eventHandlers.get(event)!.push(handler);

    // Return unsubscribe function
    return () => {
      const handlers = this.eventHandlers.get(event);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    };
  }

  /**
   * Stop network monitoring
   */
  destroy(): void {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    
    this.eventHandlers.clear();
    console.log('[NetworkMonitor] Network monitor destroyed');
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Start continuous network monitoring
   */
  private startMonitoring(): void {
    // Initial check
    this.checkConnectivity().catch(error => {
      console.error('[NetworkMonitor] Initial connectivity check failed:', error);
    });
    
    // Start periodic monitoring
    this.monitorInterval = setInterval(() => {
      this.checkConnectivity().catch(error => {
        console.error('[NetworkMonitor] Periodic connectivity check failed:', error);
      });
    }, this.CHECK_INTERVAL);
    
    console.log('[NetworkMonitor] Started network monitoring');
  }

  /**
   * Test connectivity to a specific endpoint
   */
  private async testEndpoint(endpoint: NetworkTest): Promise<ConnectivityResult> {
    return new Promise((resolve) => {
      const startTime = Date.now();
      
      const request = net.request({
        method: 'HEAD',
        url: endpoint.url
      });

      const timeout = setTimeout(() => {
        request.abort();
        resolve({
          isOnline: false,
          latency: 0,
          quality: 'offline',
          timestamp: Date.now(),
          error: `Timeout after ${endpoint.timeout}ms`
        });
      }, endpoint.timeout);

      request.on('response', (response) => {
        clearTimeout(timeout);
        const latency = Date.now() - startTime;
        const quality = this.assessNetworkQuality(latency);
        
        resolve({
          isOnline: true,
          latency,
          quality,
          timestamp: Date.now()
        });
      });

      request.on('error', (error) => {
        clearTimeout(timeout);
        resolve({
          isOnline: false,
          latency: 0,
          quality: 'offline',
          timestamp: Date.now(),
          error: error.message
        });
      });

      request.end();
    });
  }

  /**
   * Assess network quality based on latency
   */
  private assessNetworkQuality(latency: number): NetworkQuality {
    if (latency < 100) return 'excellent';
    if (latency < 300) return 'good';
    if (latency < 1000) return 'fair';
    return 'poor';
  }

  /**
   * Update network status and emit events if changed
   */
  private updateNetworkStatus(result: ConnectivityResult): void {
    const wasOnline = this.isOnline;
    const previousQuality = this.currentQuality;
    
    this.isOnline = result.isOnline;
    this.currentQuality = result.quality;
    this.latency = result.latency;
    this.lastCheck = result.timestamp;
    
    // Update quality history
    this.qualityHistory.push(result.quality);
    if (this.qualityHistory.length > this.QUALITY_HISTORY_SIZE) {
      this.qualityHistory.shift();
    }
    
    // Emit events for status changes
    if (wasOnline !== this.isOnline) {
      const event = this.isOnline ? 'network-connected' : 'network-disconnected';
      console.log(`[NetworkMonitor] Network ${this.isOnline ? 'connected' : 'disconnected'}`);
      this.emitEvent(event, { isOnline: this.isOnline, timestamp: result.timestamp });
    }
    
    if (previousQuality !== this.currentQuality) {
      console.log(`[NetworkMonitor] Network quality changed: ${previousQuality} → ${this.currentQuality}`);
      this.emitEvent('network-quality-changed', {
        previousQuality,
        currentQuality: this.currentQuality,
        latency: this.latency,
        timestamp: result.timestamp
      });
    }
    
    // Always emit status update
    this.emitEvent('network-status-updated', this.getNetworkStatus());
  }

  /**
   * Analyze quality trend from history
   */
  private analyzeQualityTrend(): 'improving' | 'stable' | 'degrading' {
    if (this.qualityHistory.length < 3) {
      return 'stable';
    }
    
    const qualityValues = {
      'offline': 0,
      'poor': 1,
      'fair': 2,
      'good': 3,
      'excellent': 4
    };
    
    const recent = this.qualityHistory.slice(-3);
    const values = recent.map(q => qualityValues[q]);
    
    const trend = values[2] - values[0];
    
    if (trend > 0) return 'improving';
    if (trend < 0) return 'degrading';
    return 'stable';
  }

  /**
   * Estimate bandwidth based on latency and quality
   */
  private estimateBandwidth(): number {
    // Simple bandwidth estimation based on latency and quality
    // This is a rough approximation for sync planning
    switch (this.currentQuality) {
      case 'excellent':
        return 100; // Mbps
      case 'good':
        return 50;
      case 'fair':
        return 25;
      case 'poor':
        return 10;
      case 'offline':
        return 0;
      default:
        return 25;
    }
  }

  /**
   * Emit event to all registered handlers
   */
  private emitEvent(event: string, data: any): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`[NetworkMonitor] Error in event handler for ${event}:`, error);
        }
      });
    }
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Create a network monitor instance
 */
export function createNetworkMonitor(): NetworkMonitor {
  return new NetworkMonitor();
}

/**
 * Quick connectivity check (utility function)
 */
export async function checkNetworkConnectivity(): Promise<boolean> {
  const monitor = new NetworkMonitor();
  const result = await monitor.checkConnectivity();
  monitor.destroy();
  return result.isOnline;
}

/**
 * Get current network quality (utility function)
 */
export async function getNetworkQuality(): Promise<NetworkQuality> {
  const monitor = new NetworkMonitor();
  const result = await monitor.checkConnectivity();
  monitor.destroy();
  return result.quality;
}
