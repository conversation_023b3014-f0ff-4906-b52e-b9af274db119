#!/bin/bash
# Usage: ./exchange-code.sh YOUR_CODE_HERE

if [ -z "$1" ]; then
    echo "Usage: ./exchange-code.sh YOUR_AUTHORIZATION_CODE"
    echo "Example: ./exchange-code.sh 1000.abc123def456..."
    exit 1
fi

CODE="$1"
echo "🔄 Exchanging code for refresh token..."

RESPONSE=$(curl -s -X POST https://accounts.zoho.eu/oauth/v2/token \
  -d "code=${CODE}" \
  -d "client_id=1000.ZN7GG1SU13DXYH2D2JXF934HWFVJDG" \
  -d "client_secret=4ca85d5c0a70404f8778128ae3b8e1cc08bc585356" \
  -d "redirect_uri=http://localhost:8080/callback" \
  -d "grant_type=authorization_code")

# Pretty print the response
echo "$RESPONSE" | python3 -m json.tool

# Extract and highlight the refresh token
REFRESH_TOKEN=$(echo "$RESPONSE" | grep -o '"refresh_token":"[^"]*' | cut -d'"' -f4)

if [ ! -z "$REFRESH_TOKEN" ]; then
    echo ""
    echo "✅ SUCCESS! Your refresh token is:"
    echo "=================================="
    echo "$REFRESH_TOKEN"
    echo "=================================="
fi
