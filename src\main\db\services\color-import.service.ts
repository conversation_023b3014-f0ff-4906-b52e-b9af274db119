import { Database } from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import { ColorEntry, NewColorEntry } from '../../../shared/types/color.types';
import { ColorService } from './color.service';

export class ColorImportService {
  constructor(
    private db: Database,
    private colorService: ColorService
  ) {}

  /**
   * Import colors with automatic product creation and association
   */
  async importColorsWithProducts(
    colors: NewColorEntry[],
    options: { 
      batchSize?: number; 
      onProgress?: (progress: number) => void;
      createMissingProducts?: boolean;
    } = {}
  ): Promise<{ colors: ColorEntry[]; products: string[]; errors: string[] }> {
    const { 
      batchSize = 50, 
      onProgress,
      createMissingProducts = true 
    } = options;
    
    const importedColors: ColorEntry[] = [];
    const createdProducts = new Set<string>();
    const errors: string[] = [];
    const total = colors.length;

    try {
      // Step 1: Extract unique products and create them if needed
      if (createMissingProducts) {
        const uniqueProducts = new Set<string>();
        colors.forEach(color => {
          if (color.product && color.product.trim() !== '') {
            uniqueProducts.add(color.product.trim());
          }
        });

        console.log(`[ColorImportService] Found ${uniqueProducts.size} unique products`);

        // Create missing products
        const createProductsTransaction = this.db.transaction(() => {
          for (const productName of uniqueProducts) {
            try {
              // Check if product exists
              const existingProduct = this.db.prepare(`
                SELECT external_id FROM products 
                WHERE name = ? AND is_active = 1
              `).get(productName);

              if (!existingProduct) {
                // Create new product
                const productId = uuidv4();
                const sku = productName.replace(/\s+/g, '-').toUpperCase();
                
                this.db.prepare(`
                  INSERT INTO products (external_id, name, sku, is_active, created_at, updated_at)
                  VALUES (?, ?, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                `).run(productId, productName, sku);
                
                createdProducts.add(productName);
                console.log(`[ColorImportService] Created product: ${productName}`);
              }
            } catch (error) {
              console.error(`[ColorImportService] Error creating product ${productName}:`, error);
              errors.push(`Failed to create product: ${productName}`);
            }
          }
        });

        createProductsTransaction();
      }

      // Step 2: Import colors in batches with product associations
      for (let i = 0; i < colors.length; i += batchSize) {
        const batch = colors.slice(i, i + batchSize);
        
        const batchTransaction = this.db.transaction(() => {
          for (const color of batch) {
            try {
              // Import the color
              const imported = this.colorService.add(color);
              importedColors.push(imported);

              // Associate with product if specified
              if (color.product && color.product.trim() !== '') {
                const productName = color.product.trim();
                
                // Get product external_id
                const product = this.db.prepare(`
                  SELECT external_id FROM products 
                  WHERE name = ? AND is_active = 1
                `).get(productName);

                if (product) {
                  // Create product-color association
                  const productInternal = this.db.prepare(`
                    SELECT id FROM products WHERE external_id = ?
                  `).get((product as any).external_id);
                  
                  const colorInternal = this.db.prepare(`
                    SELECT id FROM colors WHERE external_id = ?
                  `).get(imported.id);

                  if (productInternal && colorInternal) {
                    this.db.prepare(`
                      INSERT OR IGNORE INTO product_colors (
                        product_id, color_id, display_order, usage_type, added_at
                      )
                      VALUES (?, ?, ?, 'standard', CURRENT_TIMESTAMP)
                    `).run(
                      (productInternal as any).id,
                      (colorInternal as any).id,
                      i
                    );
                  }
                } else {
                  errors.push(`Product not found for color ${color.code}: ${productName}`);
                }
              }
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : String(error);
              console.error('[ColorImportService] Error importing color:', errorMessage, color);
              errors.push(`Failed to import color: ${color.code} - ${errorMessage}`);
              
              // Log more details about the first few errors
              if (errors.length <= 5) {
                console.error('[ColorImportService] Detailed error for color:', {
                  color,
                  error: errorMessage,
                  stack: error instanceof Error ? error.stack : undefined
                });
              }
            }
          }
        });

        batchTransaction();

        // Report progress
        if (onProgress) {
          const progress = Math.min(100, Math.round(((i + batch.length) / total) * 100));
          onProgress(progress);
        }
      }

      console.log(`[ColorImportService] Successfully imported ${importedColors.length} colors`);
      console.log(`[ColorImportService] Created ${createdProducts.size} new products`);
      console.log(`[ColorImportService] Encountered ${errors.length} errors`);

      return {
        colors: importedColors,
        products: Array.from(createdProducts),
        errors
      };
    } catch (error) {
      console.error('[ColorImportService] Error during import:', error);
      throw error;
    }
  }

  /**
   * Get import statistics
   */
  getImportStats(): { 
    totalColors: number; 
    totalProducts: number; 
    colorsWithoutProducts: number;
    productsWithoutColors: number;
  } {
    const totalColors = this.db.prepare(`
      SELECT COUNT(*) as count FROM colors WHERE deleted_at IS NULL
    `).get() as { count: number };

    const totalProducts = this.db.prepare(`
      SELECT COUNT(*) as count FROM products WHERE is_active = 1
    `).get() as { count: number };

    const colorsWithoutProducts = this.db.prepare(`
      SELECT COUNT(*) as count 
      FROM colors c
      WHERE c.deleted_at IS NULL
      AND NOT EXISTS (
        SELECT 1 FROM product_colors pc WHERE pc.color_id = c.id
      )
    `).get() as { count: number };

    const productsWithoutColors = this.db.prepare(`
      SELECT COUNT(*) as count 
      FROM products p
      WHERE p.is_active = 1
      AND NOT EXISTS (
        SELECT 1 FROM product_colors pc WHERE pc.product_id = p.id
      )
    `).get() as { count: number };

    return {
      totalColors: totalColors.count,
      totalProducts: totalProducts.count,
      colorsWithoutProducts: colorsWithoutProducts.count,
      productsWithoutColors: productsWithoutColors.count
    };
  }
}
