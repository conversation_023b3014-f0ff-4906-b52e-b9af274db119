import { describe, test, beforeAll, afterAll, expect } from 'vitest';
import { chromium, <PERSON>, Browser } from 'playwright';
import AccessibilityTester from './utils/accessibility';

describe('Accessibility tests', () => {
  let browser: Browser;
  let page: Page;
  let accessibilityTester: AccessibilityTester;
  
  beforeAll(async () => {
    browser = await chromium.launch();
    const context = await browser.newContext();
    page = await context.newPage();
    accessibilityTester = new AccessibilityTester();
    
    // Navigate to app
    await page.goto('http://localhost:5173');
    
    // Verify the app is loaded
    await page.waitForSelector('[data-testid="app-container"]');
  });
  
  afterAll(async () => {
    await browser.close();
    
    // Generate and save accessibility report
    const report = accessibilityTester.generateReport();
    console.log(report);
  });
  
  test('Main application should have no critical accessibility violations', async () => {
    const result = await accessibilityTester.runAudit(page, 'main-app');
    
    // Filter for critical and serious violations
    const criticalViolations = result.violations.filter(
      v => v.impact === 'critical' || v.impact === 'serious'
    );
    
    expect(criticalViolations.length).toBe(0);
  });
  
  test('Key interactive elements should be keyboard accessible', async () => {
    const keyboardNavigable = await accessibilityTester.checkKeyboardNavigation(page);
    expect(keyboardNavigable).toBe(true);
  });
  
  test('Color form should be accessible', async () => {
    // Open the color form
    await page.click('[data-testid="add-color-button"]');
    await page.waitForSelector('[data-testid="color-form"]');
    
    const result = await accessibilityTester.runAudit(page, 'color-form');
    
    // Allow minor violations but no serious or critical ones
    const criticalViolations = result.violations.filter(
      v => v.impact === 'critical' || v.impact === 'serious'
    );
    
    expect(criticalViolations.length).toBe(0);
    
    // Check form controls have proper labels
    const hasLabels = await page.evaluate(() => {
      const inputs = document.querySelectorAll('[data-testid="color-form"] input, [data-testid="color-form"] select');
      let allLabeled = true;
      
      inputs.forEach(input => {
        // Check for label or aria-label
        const inputId = input.getAttribute('id');
        const hasLabel = inputId && document.querySelector(`label[for="${inputId}"]`);
        const hasAriaLabel = input.getAttribute('aria-label');
        
        if (!hasLabel && !hasAriaLabel) {
          allLabeled = false;
        }
      });
      
      return allLabeled;
    });
    
    expect(hasLabels).toBe(true);
  });
  
  test('Color table should be accessible', async () => {
    // Navigate to table view if not already there
    await page.click('[data-testid="table-view-tab"]');
    
    const result = await accessibilityTester.runAudit(page, 'color-table');
    
    // Check for table accessibility
    const tableViolations = result.violations.filter(
      v => v.id === 'table-fake-caption' || 
           v.id === 'td-has-header' || 
           v.id === 'th-has-data-cells'
    );
    
    expect(tableViolations.length).toBe(0);
  });
  
  test('Color contrast should meet WCAG AA standards', async () => {
    const hasGoodContrast = await accessibilityTester.checkColorContrast(page);
    expect(hasGoodContrast).toBe(true);
  });
  
  test('Search feature should be accessible', async () => {
    // Focus on search input
    await page.focus('[data-testid="search-input"]');
    
    // Check if the input has accessible attributes
    const searchInputAccessible = await page.evaluate(() => {
      const searchInput = document.querySelector('[data-testid="search-input"]');
      
      return {
        hasLabel: searchInput?.getAttribute('aria-label') !== null || 
                 !!document.querySelector(`label[for="${searchInput?.getAttribute('id')}"]`),
        hasRole: searchInput?.getAttribute('role') === 'searchbox' || 
                searchInput?.tagName.toLowerCase() === 'input',
        hasCorrectType: searchInput?.getAttribute('type') === 'search' || 
                      searchInput?.getAttribute('type') === 'text'
      };
    });
    
    expect(searchInputAccessible.hasLabel).toBe(true);
    expect(searchInputAccessible.hasRole || searchInputAccessible.hasCorrectType).toBe(true);
  });
}); 