/**
 * @file GradientDetailsModal.tsx
 * @description Reusable modal component for displaying gradient details with CMYK values
 */

import React, { useState, useEffect, useRef } from 'react';
import { GradientStop } from '../../shared/types/color.types';
import {} from '../hooks/useTokens';
import { useFeatureFlags } from '../context/FeatureFlagContext';
import { hexToCmyk, formatCMYKForDisplay } from '../../shared/utils/color';
import { useDesignTokens } from '../hooks/useDesignTokens';

interface GradientDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  gradient: {
    gradientCSS: string;
    gradientStops: GradientStop[];
    type: string;
  };
  product: string;
}

interface InfoTooltipProps {
  content: string;
}

const InfoTooltip: React.FC<InfoTooltipProps> = ({ content }) => {
  const designTokens = useDesignTokens();
  
  return (
    <span 
      className="relative group ml-1"
      tabIndex={0}
      role="button"
      aria-label="Information tooltip"
    >
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        width="12" 
        height="12" 
        fill="none" 
        viewBox="0 0 24 24" 
        stroke="currentColor"
      >
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <div 
        className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 p-2 rounded hidden group-hover:block group-focus:block z-50"
        style={{
          backgroundColor: designTokens.getUiColor('background.secondary'),
          border: `1px solid ${designTokens.getUiColor('border.medium')}`,
          boxShadow: designTokens.getShadow('md'),
          fontSize: designTokens.getFontSize('xs'),
          zIndex: 60,
        }}
      >
        {content}
      </div>
    </span>
  );
};

export const GradientDetailsModal: React.FC<GradientDetailsModalProps> = ({
  isOpen,
  gradient,
  product,
  onClose
}) => {
  const _featureFlags = useFeatureFlags?.() || { useNewTokenSystem: false };
  const [copiedField, setCopiedField] = useState<{index: number, field: 'hex' | 'cmyk'} | null>(null);
  const [displayStops, setDisplayStops] = useState<GradientStop[]>(
    gradient?.gradientStops?.map(stop => ({ ...stop })) || []
  );
  const [isVisible, setIsVisible] = useState<boolean>(false);
  
  // Create ref for the modal dialog to manage focus
  const modalRef = useRef<HTMLDivElement>(null);
  const initialFocusRef = useRef<HTMLButtonElement>(null);

  // Handle modal visibility with animation
  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
    } else {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 200); // Match transition duration
      return () => clearTimeout(timer);
    }
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen || !gradient || !gradient.gradientStops) {return;}
    
    // Initialize displayStops with the gradient stops, ensuring CMYK values
    setDisplayStops(gradient.gradientStops.map(stop => ({
      ...stop,
      // If CMYK doesn't exist, generate it from the hex color
      cmyk: stop.cmyk || generateCmykFromHex(stop.color)
    })));
  }, [gradient, isOpen]);
  
  // Generate CMYK string from hex color
  const generateCmykFromHex = (hexColor: string): string => {
    const cmyk = hexToCmyk(hexColor);
    if (cmyk) {
      return formatCMYKForDisplay(cmyk);
    }
    return "C0, M0, Y0, K100"; // Default to black if conversion fails
  };
  
  // Copy value to clipboard
  const copyToClipboard = (value: string, index: number, field: 'hex' | 'cmyk') => {
    navigator.clipboard.writeText(value)
      .then(() => {
        setCopiedField({ index, field });
        setTimeout(() => setCopiedField(null), 1500);
      })
      .catch(err => {
        console.error('Could not copy to clipboard: ', err);
      });
  };

  // Handle escape key press
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      
      // Set focus to the close button when modal opens
      if (initialFocusRef.current) {
        initialFocusRef.current.focus();
      }
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose]);

  // Add the focus trap effect
  useEffect(() => {
    if (!isOpen || !modalRef.current) {return;}
    
    const focusableElements = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
    const modal = modalRef.current;
    const firstFocusableElement = modal.querySelectorAll(focusableElements)[0] as HTMLElement;
    const focusableContent = modal.querySelectorAll(focusableElements);
    const lastFocusableElement = focusableContent[focusableContent.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstFocusableElement) {
            lastFocusableElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastFocusableElement) {
            firstFocusableElement.focus();
            e.preventDefault();
          }
        }
      }
    };

    document.addEventListener('keydown', handleTabKey);
    
    // Save the previously focused element
    const previouslyFocusedElement = document.activeElement as HTMLElement;
    
    // Focus the first focusable element
    initialFocusRef.current ? initialFocusRef.current.focus() : firstFocusableElement.focus();
    
    return () => {
      document.removeEventListener('keydown', handleTabKey);
      // Restore focus to the previously focused element when the modal closes
      if (previouslyFocusedElement) {
        previouslyFocusedElement.focus();
      }
    };
  }, [isOpen]);

  if (!isOpen && !isVisible) {return null;}

  // Transition classes for modal and backdrop
  const backdropClasses = `fixed inset-0 bg-black z-40 transition-opacity 
    duration-200 ease-in-out ${isOpen ? 'bg-opacity-50' : 'bg-opacity-0'}`;
  
  const modalClasses = `fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 
    bg-white dark:bg-zinc-800 z-50 rounded-lg shadow-lg w-full max-w-xl overflow-hidden
    transition-opacity duration-200 ease-in-out ${isOpen ? 'opacity-100' : 'opacity-0'}`;

  // Render with token-based styling
  return (
    <>
      <div 
        className={backdropClasses}
        onClick={onClose}
        aria-hidden="true"
        tabIndex={-1}
      />
      <div 
        ref={modalRef}
        className={modalClasses}
        onClick={(e) => e.stopPropagation()}
        role="dialog"
        aria-labelledby="gradient-details-title"
        aria-modal="true"
        tabIndex={-1}
      >
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-zinc-700 flex justify-between items-center">
          <div>
            <h3 
              id="gradient-details-title" 
              className="text-lg font-medium text-gray-900 dark:text-gray-100"
            >
              {gradient.type === 'linear' ? 'Linear' : 'Radial'} Gradient Details
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Product: {product || 'Unknown'}
            </p>
          </div>
          <button 
            ref={initialFocusRef}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none"
            onClick={onClose}
            aria-label="Close gradient details"
          >
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {/* Content */}
        <div className="px-6 py-4 overflow-y-auto max-h-[calc(80vh-120px)]">
          {/* Gradient Preview */}
          <div 
            className="h-16 w-full mb-6 rounded-md relative"
            style={{ background: gradient.gradientCSS }}
            aria-label="Gradient preview"
          >
            {/* Gradient stop indicators */}
            <div className="absolute bottom-0 left-0 right-0">
              <div className="relative h-2">
                {displayStops.map((stop, index) => (
                  <div
                    key={index}
                    className="absolute top-0 h-2 w-1 bg-white border border-gray-400 dark:border-gray-600"
                    style={{
                      left: `${stop.position}%`,
                      backgroundColor: stop.color
                    }}
                    title={`Stop at ${stop.position}%`}
                  ></div>
                ))}
              </div>
            </div>
          </div>
          
          {/* Table header */}
          <div className="grid grid-cols-12 gap-4 mb-2 px-2">
            <div className="col-span-1"></div>
            <div className="col-span-2">
              <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400">Position</h4>
            </div>
            <div className="col-span-4">
              <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400">HEX</h4>
            </div>
            <div className="col-span-5">
              <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400 flex items-center">
                CMYK
                <InfoTooltip content="CMYK values represent Cyan, Magenta, Yellow, and Key (Black) percentages used in print production." />
              </h4>
            </div>
          </div>
          
          {/* Color stops */}
          <div className="space-y-3">
            {displayStops.map((stop, index) => (
              <div 
                key={index}
                className="border border-gray-200 dark:border-zinc-700 rounded-md p-3 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700 transition-colors"
              >
                <div className="grid grid-cols-12 gap-4 items-center">
                  {/* Color swatch */}
                  <div className="col-span-1">
                    <div 
                      className="h-8 w-8 rounded-full border border-gray-300 dark:border-gray-700 flex-shrink-0"
                      style={{ backgroundColor: stop.color }}
                    ></div>
                  </div>
                  
                  {/* Position */}
                  <div className="col-span-2">
                    <div className="text-sm text-gray-600 dark:text-gray-300">
                      {stop.position}%
                    </div>
                  </div>
                  
                  {/* HEX value */}
                  <div className="col-span-4">
                    <div className="flex items-center">
                      <button 
                        className="mr-1 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-zinc-600 focus:outline-none"
                        onClick={() => copyToClipboard(stop.color, index, 'hex')}
                        title="Copy HEX value"
                        aria-label={`Copy HEX value: ${stop.color}`}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                      </button>
                      <span className="text-sm font-mono text-gray-700 dark:text-gray-300">{stop.color.toUpperCase()}</span>
                      {copiedField?.index === index && copiedField?.field === 'hex' && (
                        <span className="text-xs text-green-600 dark:text-green-500 ml-1">Copied!</span>
                      )}
                    </div>
                  </div>
                  
                  {/* CMYK value */}
                  <div className="col-span-5">
                    <div className="flex items-center">
                      <button 
                        className="mr-1 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-zinc-600 focus:outline-none"
                        onClick={() => copyToClipboard(stop.cmyk!, index, 'cmyk')}
                        title="Copy CMYK value"
                        aria-label={`Copy CMYK value: ${stop.cmyk}`}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-500 dark:text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                      </button>
                      <span className="text-sm font-mono text-gray-700 dark:text-gray-300">{stop.cmyk || "Not set"}</span>
                      {copiedField?.index === index && copiedField?.field === 'cmyk' && (
                        <span className="text-xs text-green-600 dark:text-green-500 ml-1">Copied!</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-zinc-700 bg-gray-50 dark:bg-zinc-900 flex justify-end">
          <button 
            type="button"
            className="px-4 py-2 text-sm font-medium rounded-md border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-800 hover:bg-gray-50 dark:hover:bg-zinc-700"
            onClick={onClose}
          >
            Close
          </button>
        </div>
      </div>
    </>
  );
};

export default GradientDetailsModal; 