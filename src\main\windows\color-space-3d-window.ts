import { BrowserWindow, ipcMain } from 'electron';
import path from 'path';
import { isDevelopment } from '../utils/env';
import { ColorSpace3DChannels } from '../../shared/constants/channels';

let colorSpace3DWindow: BrowserWindow | null = null;

interface ColorData {
  id: string;
  hex: string;
  name: string;
}

/**
 * Create a dedicated 3D color space visualization window
 */
export function createColorSpace3DWindow(colors: ColorData[]): BrowserWindow {
  // If window already exists, update colors and show it
  if (colorSpace3DWindow && !colorSpace3DWindow.isDestroyed()) {
    colorSpace3DWindow.webContents.send(ColorSpace3DChannels.UPDATE_COLORS, colors);
    colorSpace3DWindow.show();
    colorSpace3DWindow.focus();
    return colorSpace3DWindow;
  }

  // Create the browser window
  colorSpace3DWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    title: '3D Color Space Visualization - ChromaSync',
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
    trafficLightPosition: { x: 10, y: 14 }, // Position traffic lights on macOS
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true,
      allowRunningInsecureContent: false,
      preload: path.join(__dirname, '../preload/index.js'),
    },
    show: false, // Don't show until ready
    icon: path.join(__dirname, '../../../assets/icon.png'), // Add app icon if available
  });

  // Load the main app with a special URL parameter to indicate this is a 3D window
  // Force production mode for now since dev server might not be running
  const forceProduction = true;
  
  if (isDevelopment && !forceProduction) {
    // In development, load from the dev server
    const url = 'http://localhost:5173/?mode=color-space-3d';
    console.log('3D Window: Loading dev URL:', url);
    colorSpace3DWindow.loadURL(url);
  } else {
    // In production, load the built file with URL parameter
    const htmlPath = path.join(__dirname, '../../out/renderer/index.html');
    console.log('3D Window: Loading production file:', htmlPath, 'with search: mode=color-space-3d');
    colorSpace3DWindow.loadFile(htmlPath, { search: 'mode=color-space-3d' });
  }

  // Don't open DevTools automatically for the 3D window

  // Send initial colors when ready
  colorSpace3DWindow.webContents.once('dom-ready', () => {
    if (colorSpace3DWindow && !colorSpace3DWindow.isDestroyed()) {
      console.log('3D Window: DOM ready, sending colors:', colors.length);
      // Use a slight delay to ensure the renderer is fully loaded
      setTimeout(() => {
        if (colorSpace3DWindow && !colorSpace3DWindow.isDestroyed()) {
          console.log('3D Window: Sending colors via webContents.send');
          colorSpace3DWindow.webContents.send(ColorSpace3DChannels.UPDATE_COLORS, colors);
        }
      }, 100);
    }
  });

  // Show window when ready
  colorSpace3DWindow.once('ready-to-show', () => {
    if (colorSpace3DWindow) {
      colorSpace3DWindow.show();
      colorSpace3DWindow.focus();
    }
  });

  // Handle window close
  colorSpace3DWindow.on('closed', () => {
    colorSpace3DWindow = null;
  });

  return colorSpace3DWindow;
}

/**
 * Update colors in the 3D space window
 */
export function updateColorSpace3DColors(colors: ColorData[]): void {
  if (colorSpace3DWindow && !colorSpace3DWindow.isDestroyed()) {
    colorSpace3DWindow.webContents.send(ColorSpace3DChannels.UPDATE_COLORS, colors);
  }
}

/**
 * Close the 3D space window
 */
export function closeColorSpace3DWindow(): void {
  if (colorSpace3DWindow && !colorSpace3DWindow.isDestroyed()) {
    colorSpace3DWindow.close();
  }
}

/**
 * Initialize 3D color space window IPC handlers
 */
export function initColorSpace3DWindowHandlers(): void {
  // Open 3D color space window
  ipcMain.handle(ColorSpace3DChannels.OPEN_WINDOW, (_, colors: ColorData[]) => {
    try {
      createColorSpace3DWindow(colors);
      return { success: true };
    } catch (error) {
      console.error('Failed to create 3D color space window:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  });

  // Close 3D color space window
  ipcMain.handle(ColorSpace3DChannels.CLOSE_WINDOW, () => {
    try {
      closeColorSpace3DWindow();
      return { success: true };
    } catch (error) {
      console.error('Failed to close 3D color space window:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  });

  // Update colors in 3D space window
  ipcMain.handle(ColorSpace3DChannels.UPDATE_COLORS, (_, colors: ColorData[]) => {
    try {
      updateColorSpace3DColors(colors);
      return { success: true };
    } catch (error) {
      console.error('Failed to update 3D color space colors:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  });
}

/**
 * Get the current 3D color space window instance
 */
export function getColorSpace3DWindow(): BrowserWindow | null {
  return colorSpace3DWindow;
}