/**
 * @file product.types.ts
 * @description Type definitions for product data with compatibility layer for migration
 */

import { ColorEntry } from './color.types';

// ===== NEW OPTIMIZED TYPES =====

export interface ProductOptimized {
  id: number;
  external_id: string;
  organization_id: string; // Added for multi-tenant support
  user_id?: string; // Optional - for audit trail
  name: string;
  sku?: string;
  is_active: boolean;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface ProductColorRelation {
  product_id: number;
  color_id: number;
  display_order: number;
  usage_type: 'standard' | 'primary' | 'accent' | 'variant';
  quantity?: number;
  metadata?: Record<string, any>;
  added_at: string;
}

export interface ProductWithColorsOptimized extends ProductOptimized {
  colors: Array<{
    color_id: number;
    display_order: number;
    usage_type: string;
    quantity?: number;
    metadata?: Record<string, any>;
    added_at: string;
  }>;
}

// ===== COMPATIBILITY LAYER (OLD TYPES) =====

export interface Product {
  id: string;  // Will map to external_id
  organizationId: string;  // Added for multi-tenant support
  userId?: string;  // Optional - for audit trail
  name: string;
  description?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
}

export interface ProductWithColors extends Product {
  colors: ColorEntry[];
  datasheets: DatasheetEntry[];
}

export interface ProductWithSelections extends Product {
  selected?: boolean;
}

export interface ProductWithSelectionsAndColors extends ProductWithSelections {
  colors: ColorEntry[];
  datasheets: DatasheetEntry[];
}

export interface DatasheetEntry {
  id: string;
  productId: string;
  name: string;
  path: string;
  fileType: 'pdf' | 'docx' | 'xlsx' | 'other';
  dateAdded: string;
  createdAt?: string;
  updatedAt?: string;
}

export type NewProduct = Omit<Product, 'id' | 'createdAt' | 'updatedAt' | 'createdBy' | 'updatedBy'>;

export type UpdateProduct = Partial<Omit<Product, 'id' | 'createdAt' | 'updatedAt' | 'createdBy' | 'updatedBy'>>;

// IPC Channel names
export enum ProductChannels {
  GET_ALL = 'product:getAll',
  GET_ALL_WITH_COLORS = 'product:getAllWithColors',
  GET_BY_ID = 'product:getById',
  ADD = 'product:add',
  UPDATE = 'product:update',
  DELETE = 'product:delete',
  ADD_COLOR = 'product:addColor',
  REMOVE_COLOR = 'product:removeColor',
  GET_COLORS = 'product:getColors',
  ADD_DATASHEET = 'product:addDatasheet',
  ADD_WEB_LINK = 'product:addWebLink',
  REMOVE_DATASHEET = 'product:removeDatasheet',
  OPEN_DATASHEETS = 'product:openDatasheets',
  OPEN_DATASHEET = 'product:openDatasheet',
  DELETE_MULTIPLE_PRODUCTS = 'product:deleteMultiple',
  RUN_CLEANUP = 'product:runCleanup',
  INFER_COLORS = 'product:infer-colors'
}

// ===== CONVERSION HELPERS =====

// Helper type to convert between old and new formats
export interface ProductConversion {
  toOptimized(product: Product): ProductOptimized;
  fromOptimized(product: ProductOptimized): Product;
}
