/**
 * @file DebugTeamSettings.tsx
 * @description Debug component to help identify team settings issues
 * TEMPORARY - Remove after debugging
 */

import React from 'react';
import { useOrganizationStoreWithAliases } from '../store/organization.store';

export const DebugTeamSettings: React.FC = () => {
  const { currentOrg, members } = useOrganizationStoreWithAliases();
  
  const currentUserMember = members.find(m => m.isCurrentUser);
  const currentUserRole = currentUserMember?.role || 'member';
  const canManageTeam = currentUserRole === 'owner' || currentUserRole === 'admin';
  
  const getSeatsForPlan = (plan: string) => {
    switch (plan) {
      case 'free': return 3;
      case 'team': return 10;  
      case 'enterprise': return 'Unlimited';
      default: return 3;
    }
  };
  
  const maxSeats = currentOrg ? getSeatsForPlan(currentOrg.plan) : 0;
  const seatsUsed = members.length;
  const canInviteMore = maxSeats === 'Unlimited' || seatsUsed < maxSeats;
  const shouldShowInvite = canManageTeam && canInviteMore;

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 m-4">
      <h3 className="font-bold text-yellow-800 mb-3">🔍 Team Settings Debug Info</h3>
      
      <div className="space-y-2 text-sm">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <strong>Current Organization:</strong>
            <pre className="text-xs bg-white p-2 rounded mt-1 overflow-auto">
              {JSON.stringify(currentOrg, null, 2)}
            </pre>
          </div>
          
          <div>
            <strong>Members ({members.length}):</strong>
            <pre className="text-xs bg-white p-2 rounded mt-1 overflow-auto">
              {JSON.stringify(members, null, 2)}
            </pre>
          </div>
        </div>
        
        <div className="bg-white p-3 rounded border">
          <h4 className="font-semibold mb-2">Conditions Check:</h4>
          <div className="space-y-1">
            <div className={`flex items-center ${currentOrg ? 'text-green-600' : 'text-red-600'}`}>
              {currentOrg ? '✅' : '❌'} Current Organization: {currentOrg ? currentOrg.name : 'NOT_LOADED'}
            </div>
            
            <div className={`flex items-center ${currentUserMember ? 'text-green-600' : 'text-red-600'}`}>
              {currentUserMember ? '✅' : '❌'} Current User Member: {currentUserMember ? `${currentUserRole}` : 'NOT_FOUND'}
            </div>
            
            <div className={`flex items-center ${canManageTeam ? 'text-green-600' : 'text-red-600'}`}>
              {canManageTeam ? '✅' : '❌'} Can Manage Team: {canManageTeam ? 'YES' : `NO (role: ${currentUserRole})`}
            </div>
            
            <div className={`flex items-center ${canInviteMore ? 'text-green-600' : 'text-red-600'}`}>
              {canInviteMore ? '✅' : '❌'} Can Invite More: {canInviteMore ? 'YES' : 'NO'} ({seatsUsed}/{maxSeats} seats used)
            </div>
            
            <div className={`flex items-center font-bold ${shouldShowInvite ? 'text-green-600' : 'text-red-600'}`}>
              {shouldShowInvite ? '✅' : '❌'} SHOULD SHOW INVITE: {shouldShowInvite ? 'YES' : 'NO'}
            </div>
          </div>
        </div>
        
        {!shouldShowInvite && (
          <div className="bg-red-50 border border-red-200 p-3 rounded">
            <h4 className="font-semibold text-red-800 mb-2">🚨 Issues Preventing Invite Option:</h4>
            <ul className="list-disc pl-5 space-y-1 text-red-700">
              {!currentOrg && <li>No organization loaded</li>}
              {!currentUserMember && <li>Current user not found in members list</li>}
              {!canManageTeam && <li>Insufficient permissions (need admin or owner role, current: {currentUserRole})</li>}
              {!canInviteMore && <li>Seat limit reached ({seatsUsed}/{maxSeats})</li>}
            </ul>
          </div>
        )}
        
        <div className="bg-blue-50 border border-blue-200 p-3 rounded">
          <h4 className="font-semibold text-blue-800 mb-2">💡 Troubleshooting Steps:</h4>
          <ol className="list-decimal pl-5 space-y-1 text-blue-700 text-xs">
            <li>If no organization: Create or select an organization</li>
            <li>If not in members: Check if you're authenticated properly</li>
            <li>If wrong role: Ask organization owner to promote you to admin/owner</li>
            <li>If seat limit: Upgrade plan or remove inactive members</li>
          </ol>
        </div>
      </div>
    </div>
  );
};