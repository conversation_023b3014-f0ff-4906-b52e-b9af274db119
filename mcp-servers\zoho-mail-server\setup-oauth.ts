import axios from 'axios';
import * as readline from 'readline';
import { createServer } from 'http';
import { URL } from 'url';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

const question = (query: string): Promise<string> => {
  return new Promise((resolve) => {
    rl.question(query, resolve);
  });
};

async function getOAuthCredentials() {
  console.log('🔧 Zoho OAuth Setup for ChromaSync\n');
  
  const clientId = await question('Enter your Client ID: ');
  const clientSecret = await question('Enter your Client Secret: ');
  
  const scopes = 'ZohoMail.messages.CREATE,ZohoMail.messages.READ,ZohoMail.accounts.READ';
  const redirectUri = 'http://localhost:8080/callback';
  
  // Generate authorization URL
  const authUrl = `https://accounts.zoho.com/oauth/v2/auth?` +
    `client_id=${clientId}&` +
    `response_type=code&` +
    `redirect_uri=${encodeURIComponent(redirectUri)}&` +
    `scope=${encodeURIComponent(scopes)}&` +
    `access_type=offline&` +
    `prompt=consent`;
  
  console.log('\n📋 Open this URL in your browser:');
  console.log(authUrl);
  console.log('\n⏳ Waiting for authorization...');
  
  // Start local server to receive callback
  const code = await new Promise<string>((resolve) => {
    const server = createServer((req, res) => {
      const url = new URL(req.url!, `http://localhost:8080`);
      const code = url.searchParams.get('code');
      
      if (code) {
        res.writeHead(200, { 'Content-Type': 'text/html' });
        res.end('<h1>✅ Authorization successful!</h1><p>You can close this window.</p>');
        server.close();
        resolve(code);
      }
    });
    
    server.listen(8080);
  });
  
  console.log('\n✅ Authorization code received!');
  console.log('🔄 Exchanging for refresh token...');
  
  // Exchange code for tokens
  try {
    const tokenResponse = await axios.post(
      'https://accounts.zoho.com/oauth/v2/token',
      null,
      {
        params: {
          code,
          client_id: clientId,
          client_secret: clientSecret,
          redirect_uri: redirectUri,
          grant_type: 'authorization_code',
        },
      }
    );
    
    console.log('\n✅ Success! Here are your credentials:\n');
    console.log('ZOHO_CLIENT_ID=' + clientId);
    console.log('ZOHO_CLIENT_SECRET=' + clientSecret);
    console.log('ZOHO_REFRESH_TOKEN=' + tokenResponse.data.refresh_token);
    console.log('\n📝 You still need to get your Account ID from:');
    console.log('https://mail.zoho.com → Settings → Mail Accounts');
    
  } catch (error) {
    console.error('\n❌ Failed to get tokens:', error.response?.data || error);
  }
  
  rl.close();
}

getOAuthCredentials().catch(console.error);
