#!/bin/bash

# ChromaSync Domain Setup Script
# This script helps set up chromasync.app with Cloudflare and Zoho

echo "🚀 ChromaSync Domain Configuration Helper"
echo "========================================"
echo ""

# Check if .env exists
if [ ! -f .env ]; then
    echo "Creating .env file from .env.example..."
    cp .env.example .env
    echo "✅ Created .env file"
else
    echo "⚠️  .env file already exists"
fi

echo ""
echo "📋 Next Steps:"
echo ""
echo "1. CLOUDFLARE SETUP"
echo "   - Log in to Cloudflare dashboard"
echo "   - Add DNS records from CLOUDFLARE_DNS_CONFIG.md"
echo "   - Enable SSL/TLS Full (strict) mode"
echo ""
echo "2. ZOHO MAIL SETUP"
echo "   - Go to https://mail.zoho.com"
echo "   - Add chromasync.app domain"
echo "   - Create <EMAIL> email"
echo "   - Generate app-specific password"
echo ""
echo "3. UPDATE .env FILE"
echo "   Edit .env and add:"
echo "   - ZOHO_EMAIL=<EMAIL>"
echo "   - ZOHO_PASSWORD=your-app-password"
echo "   - SUPABASE_URL=your-supabase-url"
echo "   - SUPABASE_ANON_KEY=your-anon-key"
echo ""
echo "4. SUPABASE CONFIGURATION"
echo "   - Add https://auth.chromasync.app/callback to redirect URLs"
echo "   - Update email templates with chromasync.app domain"
echo ""
echo "5. TEST CONFIGURATION"
echo "   npm run test:email  # Test email sending"
echo "   npm run test:auth   # Test OAuth flow"
echo ""

# Make auth callback HTML file
mkdir -p auth-callback
cat > auth-callback/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>ChromaSync Authentication</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background: #f8fafc;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 400px;
        }
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            background: #3b82f6;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: bold;
        }
        h2 {
            color: #1e293b;
            margin-bottom: 0.5rem;
        }
        p {
            color: #64748b;
            margin-bottom: 1.5rem;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #e2e8f0;
            border-top-color: #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">C</div>
        <h2>Authentication Successful</h2>
        <p>Redirecting to ChromaSync...</p>
        <div class="spinner"></div>
    </div>
    <script>
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        
        if (code) {
            // Redirect to ChromaSync app
            window.location.href = 'chromasync://auth?code=' + code;
            
            // Update message after redirect attempt
            setTimeout(() => {
                document.querySelector('p').textContent = 'You can now close this window.';
                document.querySelector('.spinner').style.display = 'none';
            }, 2000);
        } else {
            document.querySelector('p').textContent = 'Authentication failed. Please try again.';
            document.querySelector('.spinner').style.display = 'none';
        }
    </script>
</body>
</html>
EOF

echo "✅ Created auth callback HTML at auth-callback/index.html"
echo ""
echo "📁 Upload auth-callback/index.html to:"
echo "   - Cloudflare Pages"
echo "   - Or any static hosting at auth.chromasync.app"
echo ""
echo "Need help? Check DOMAIN_EMAIL_SETUP.md for detailed instructions."