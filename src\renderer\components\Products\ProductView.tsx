/**
 * @file ProductView.tsx
 * @description Component to display and manage colors in a product
 */

import React, { useState, useEffect } from 'react';
import { useProductStore } from '../../store/product.store';
import {} from '../../store/color.store';
import { Plus, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Folder } from 'lucide-react';
import ColorSwatch from '../ColorSwatches/ColorSwatch';
import AddToProductModal from './AddToProductModal';
import { useTokens } from '../../hooks/useTokens';
import Modal from '../ui/Modal';
import DatasheetList from '../Datasheets/DatasheetList';

interface ProductViewProps {
  activeProductId: string | null;
}

const ProductView: React.FC<ProductViewProps> = ({ activeProductId }) => {
  const tokens = useTokens();

  const {
    products,
    removeColorFromProduct
  } = useProductStore();

  const [isLoading, setIsLoading] = useState(false);
  const [showAddColorModal, setShowAddColorModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  // Find the active product
  const activeProduct = products.find(p => p.id === activeProductId);

  // Load colors when active product changes
  useEffect(() => {
    if (activeProductId) {
      setIsLoading(true);
      // Simulate loading delay
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [activeProductId]);

  // Handle removing color from product
  const handleRemoveColor = async (colorId: string) => {
    if (!activeProductId) {return;}

    try {
      await removeColorFromProduct(activeProductId, colorId);
      setShowDeleteConfirm(null);
    } catch (err) {
      console.error('Failed to remove color:', err);
    }
  };

  // If no product is selected
  if (!activeProductId) {
    return (
      <div className="h-full flex flex-col items-center justify-center p-8 text-center">
        <Folder className="h-12 w-12 text-ui-muted dark:text-gray-500 mb-4" />
        <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-2">No Product Selected</h3>
        <p className="text-ui-muted dark:text-gray-400 max-w-md">
          Select a product from the sidebar to view its colors or create a new product to get started.
        </p>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="h-full flex flex-col items-center justify-center p-8">
        <div className="animate-pulse flex flex-col items-center">
          <div className="rounded-full bg-ui-background-tertiary dark:bg-zinc-700 h-12 w-12 mb-4"></div>
          <div className="h-4 bg-ui-background-tertiary dark:bg-zinc-700 rounded w-48 mb-2"></div>
          <div className="h-3 bg-ui-background-tertiary dark:bg-zinc-700 rounded w-32"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Product header */}
      <div className="border-b border-ui-border-light dark:border-zinc-700 bg-ui-background-secondary dark:bg-zinc-800">
        <div className="px-5 py-3.5 flex items-center justify-between">
          <div>
            <h2 className="text-lg font-medium text-ui-foreground-primary dark:text-white leading-tight">
              {activeProduct?.name || 'Product'}
            </h2>
            {activeProduct?.description && (
              <p className="text-ui-foreground-secondary dark:text-gray-300 text-xs mt-1 max-w-md">
                {activeProduct.description}
              </p>
            )}
          </div>

          <button
            onClick={() => setShowAddColorModal(true)}
            className="flex items-center px-3.5 py-1.5 text-xs font-medium rounded-md bg-brand-primary text-ui-inverse hover:bg-brand-primary-dark transition-colors"
            style={{
              transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
            }}
          >
            <Plus size={14} className="mr-1" />
            Add Color
          </button>
        </div>
      </div>

      {/* Datasheets section */}
      {activeProduct && (
        <DatasheetList
          productId={activeProductId}
          productName={activeProduct.name}
        />
      )}

      {/* Product content */}
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-3">Colors</h3>

          {activeProduct && (!activeProduct.colors || activeProduct.colors.length === 0) ? (
            <div className="py-12 flex flex-col items-center justify-center text-center border border-ui-border-light dark:border-zinc-700 rounded-md bg-ui-background-secondary dark:bg-zinc-800">
              <AlertTriangle className="h-12 w-12 text-ui-muted dark:text-gray-500 mb-4" />
              <h3 className="text-lg font-medium text-ui-foreground-primary dark:text-white mb-2">
                This product has no colors
              </h3>
              <p className="text-ui-muted dark:text-gray-400 max-w-md mb-6">
                Start adding colors to this product
              </p>
              <button
                onClick={() => setShowAddColorModal(true)}
                className="flex items-center px-4 py-2 text-sm font-medium rounded-md bg-brand-primary text-ui-inverse hover:bg-brand-primary-dark transition-colors"
                style={{
                  transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.out}`
                }}
              >
                <Plus size={16} className="mr-1.5" />
                Add Color
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-6 gap-4">
              {activeProduct?.colors?.map(color => (
                <div key={color.id} className="relative group">
                  <ColorSwatch entry={color} />
                  <button
                    onClick={() => setShowDeleteConfirm(color.id)}
                    className="absolute top-2 right-2 bg-ui-background-primary/90 dark:bg-zinc-800/90 text-ui-foreground-primary dark:text-white rounded-full p-1.5 opacity-0 group-hover:opacity-100 transition-opacity"
                    title="Remove from product"
                  >
                    <Trash2 size={14} />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Add to product modal */}
      {activeProductId && (
        <AddToProductModal
          isOpen={showAddColorModal}
          onClose={() => setShowAddColorModal(false)}
          productId={activeProductId}
        />
      )}

      {/* Delete color confirmation modal */}
      {showDeleteConfirm && (
        <Modal
          isOpen={Boolean(showDeleteConfirm)}
          onClose={() => setShowDeleteConfirm(null)}
          title="Remove Color"
        >
          <div className="p-5">
            <p className="text-ui-foreground-secondary dark:text-gray-300 text-sm mb-5">
              Are you sure you want to remove this color from the product?
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(null)}
                className="px-3.5 py-1.5 text-xs font-medium text-ui-foreground-primary dark:text-white bg-ui-background-tertiary dark:bg-zinc-700 rounded-md hover:bg-ui-background-tertiary-hover dark:hover:bg-zinc-600 transition-colors"
                style={{
                  transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                }}
              >
                Cancel
              </button>
              <button
                onClick={() => handleRemoveColor(showDeleteConfirm)}
                className="px-3.5 py-1.5 text-xs font-medium bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                style={{
                  transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                }}
              >
                Remove
              </button>
            </div>
          </div>
        </Modal>
      )}

    </div>
  );
};

export default ProductView;