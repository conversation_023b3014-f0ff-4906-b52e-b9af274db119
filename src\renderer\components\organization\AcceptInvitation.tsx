/**
 * @file AcceptInvitation.tsx
 * @description Modal component for accepting organization invitations
 */

import React, { useState, useEffect } from 'react';
import { Loader2, CheckCircle, XCircle, Users } from 'lucide-react';
import { useOrganizationStore } from '../../store/organization.store';

interface AcceptInvitationModalProps {
  token?: string;
  onClose: () => void;
}

export const AcceptInvitationModal: React.FC<AcceptInvitationModalProps> = ({ token: initialToken, onClose }) => {
  const [token, setToken] = useState(initialToken || '');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [invitationDetails, setInvitationDetails] = useState<any>(null);
  
  const { acceptInvitation, fetchOrganizations } = useOrganizationStore();

  const handleAccept = async () => {
    if (!token.trim()) {
      setError('Please enter an invitation code');
      return;
    }

    setIsLoading(true);
    setError(null);
    
    try {
      const result = await window.organizationAPI.acceptInvitation(token.trim());
      
      if (result.success) {
        setSuccess(true);
        setInvitationDetails(result);
        
        // Refresh organizations list
        await fetchOrganizations();
        
        // Close modal after a delay
        setTimeout(() => {
          onClose();
          // Refresh the app to show new organization
          window.location.reload();
        }, 2000);
      } else {
        setError(result.error || 'Failed to accept invitation');
      }
    } catch (err) {
      console.error('Error accepting invitation:', err);
      setError('Failed to accept invitation. Please check your invitation code.');
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-accept if token provided
  useEffect(() => {
    if (initialToken) {
      handleAccept();
    }
  }, [initialToken]);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <Users className="w-6 h-6 text-indigo-500" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Join Organization
            </h2>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {success ? (
            <div className="text-center space-y-4">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Invitation Accepted!
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Welcome to {invitationDetails?.organizationName || 'the organization'}. 
                Redirecting you now...
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {!initialToken && (
                <>
                  <p className="text-gray-600 dark:text-gray-400">
                    Enter your invitation code to join an organization.
                  </p>
                  
                  <div>
                    <label htmlFor="invitation-token" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Invitation Code
                    </label>
                    <input
                      id="invitation-token"
                      type="text"
                      value={token}
                      onChange={(e) => setToken(e.target.value)}
                      placeholder="Enter your invitation code"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg 
                               bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                               focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                      disabled={isLoading}
                    />
                  </div>
                </>
              )}

              {error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <XCircle className="w-5 h-5 text-red-500" />
                  <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
                </div>
              )}

              {isLoading && (
                <div className="flex items-center justify-center gap-2 py-4">
                  <Loader2 className="w-5 h-5 animate-spin text-indigo-500" />
                  <span className="text-gray-600 dark:text-gray-400">
                    Processing invitation...
                  </span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        {!success && (
          <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 
                       dark:hover:bg-gray-700 rounded-lg transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              onClick={handleAccept}
              disabled={isLoading || !token.trim()}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 
                       disabled:opacity-50 disabled:cursor-not-allowed transition-colors
                       flex items-center gap-2"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Accepting...
                </>
              ) : (
                'Accept Invitation'
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

// Standalone function to show the modal
export const showAcceptInvitationModal = (token?: string) => {
  const modalRoot = document.getElementById('modal-root');
  if (!modalRoot) {
    const root = document.createElement('div');
    root.id = 'modal-root';
    document.body.appendChild(root);
  }

  const container = document.createElement('div');
  document.getElementById('modal-root')!.appendChild(container);

  const closeModal = () => {
    container.remove();
  };

  import('react-dom/client').then(({ createRoot }) => {
    const root = createRoot(container);
    root.render(
      <AcceptInvitationModal
        token={token}
        onClose={closeModal}
      />
    );
  });
};