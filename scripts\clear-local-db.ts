/**
 * Clear all data from the local ChromaSync database
 * Run with: npx ts-node scripts/clear-local-db.ts
 */

import Database from 'better-sqlite3';
import * as path from 'path';
import * as os from 'os';
import * as fs from 'fs';

// Get local database path
function getDatabasePath(): string {
  const appName = 'chromasync';
  let dbPath: string;

  switch (process.platform) {
    case 'darwin': // macOS
      dbPath = path.join(os.homedir(), 'Library', 'Application Support', appName, 'chromasync.db');
      break;
    case 'win32': // Windows
      dbPath = path.join(process.env.APPDATA || '', appName, 'chromasync.db');
      break;
    default: // Linux and others
      dbPath = path.join(os.homedir(), '.config', appName, 'chromasync.db');
      break;
  }

  return dbPath;
}

function clearLocalDatabase(): void {
  console.log('🗑️  Clearing local ChromaSync database...\n');
  
  const dbPath = getDatabasePath();
  console.log(`📁 Database path: ${dbPath}\n`);
  
  // Check if database exists
  if (!fs.existsSync(dbPath)) {
    console.log('❌ Database file not found. Nothing to clear.');
    return;
  }
  
  try {
    const db = new Database(dbPath);
    
    // Begin transaction
    db.exec('BEGIN TRANSACTION');
    
    try {
      // Clear tables in order to respect foreign key constraints
      const tables = [
        { name: 'gradient_stops', description: 'gradient stops' },
        { name: 'product_colors', description: 'product-color relationships' },
        { name: 'color_cmyk', description: 'CMYK color data' },
        { name: 'color_rgb', description: 'RGB color data' },
        { name: 'color_lab', description: 'LAB color data' },
        { name: 'color_hsl', description: 'HSL color data' },
        { name: 'colors', description: 'colors' },
        { name: 'products', description: 'products' },
        { name: 'datasheets', description: 'datasheets' },
        { name: 'product_datasheets', description: 'product-datasheet links' }
      ];
      
      for (const table of tables) {
        try {
          const result = db.prepare(`DELETE FROM ${table.name}`).run();
          console.log(`  ✓ Cleared ${result.changes} ${table.description}`);
        } catch (error: any) {
          // Table might not exist in all databases
          if (!error.message.includes('no such table')) {
            console.error(`  ⚠️  Error clearing ${table.name}:`, error.message);
          }
        }
      }
      
      // Reset autoincrement sequences
      console.log('\n🔄 Resetting autoincrement sequences...');
      db.exec(`
        DELETE FROM sqlite_sequence WHERE name IN (
          'products', 'colors', 'datasheets', 
          'gradient_stops', 'color_cmyk', 'color_rgb', 
          'color_lab', 'color_hsl'
        );
      `);
      
      // Commit transaction
      db.exec('COMMIT');
      
      // Run cleanup operations
      console.log('\n🧹 Running cleanup operations...');
      db.exec('VACUUM');
      db.exec('ANALYZE');
      
      // Get final stats
      const stats = {
        products: (db.prepare('SELECT COUNT(*) as count FROM products').get() as any).count,
        colors: (db.prepare('SELECT COUNT(*) as count FROM colors').get() as any).count,
        relationships: (db.prepare('SELECT COUNT(*) as count FROM product_colors').get() as any).count
      };
      
      console.log('\n📊 Final database stats:');
      console.log(`  - Products: ${stats.products}`);
      console.log(`  - Colors: ${stats.colors}`);
      console.log(`  - Relationships: ${stats.relationships}`);
      
      db.close();
      
      console.log('\n✅ Local database cleared successfully!');
      
    } catch (error) {
      // Rollback on error
      db.exec('ROLLBACK');
      throw error;
    }
    
  } catch (error: any) {
    console.error('❌ Error clearing local database:', error.message);
    process.exit(1);
  }
}

// Run the script
clearLocalDatabase();
