const { app } = require('electron');
const Database = require('better-sqlite3');
const path = require('path');

app.whenReady().then(() => {
  // Get the database path from environment or use default
  const dbPath = process.env.DATABASE_PATH || path.join(app.getPath('userData'), 'chromasync.db');
  
  console.log(`Checking database at: ${dbPath}`);
  
  try {
    const db = new Database(dbPath, { readonly: true });
    
    // Check if product_colors table exists
    const tableCheck = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='product_colors'
    `).get();
    
    if (!tableCheck) {
      console.log('❌ product_colors table does not exist!');
      app.quit();
      return;
    }
    
    console.log('✅ product_colors table exists');
    
    // Count total relationships
    const totalCount = db.prepare('SELECT COUNT(*) as count FROM product_colors').get();
    console.log(`\nTotal product-color relationships: ${totalCount.count}`);
    
    // Get products with colors
    const productsWithColors = db.prepare(`
      SELECT 
        p.id,
        p.name,
        p.external_id,
        p.user_id,
        COUNT(pc.color_id) as color_count
      FROM products p
      LEFT JOIN product_colors pc ON p.id = pc.product_id
      WHERE p.is_active = 1
      GROUP BY p.id
      ORDER BY color_count DESC
      LIMIT 10
    `).all();
    
    console.log('\nTop 10 products by color count:');
    productsWithColors.forEach(p => {
      console.log(`- ${p.name} (ID: ${p.id}, External: ${p.external_id}): ${p.color_count} colors, User: ${p.user_id || 'NULL'}`);
    });
    
    // Check for any user_id values
    const userCheck = db.prepare(`
      SELECT DISTINCT user_id FROM products WHERE user_id IS NOT NULL
    `).all();
    
    console.log(`\nUnique user IDs in products: ${userCheck.length}`);
    userCheck.forEach(u => console.log(`- ${u.user_id}`));
    
    // Sample product_colors data
    const sampleRelations = db.prepare(`
      SELECT 
        pc.*,
        p.name as product_name,
        c.code as color_code,
        c.hex
      FROM product_colors pc
      JOIN products p ON pc.product_id = p.id
      JOIN colors c ON pc.color_id = c.id
      LIMIT 5
    `).all();
    
    console.log('\nSample product-color relationships:');
    sampleRelations.forEach(r => {
      console.log(`- Product: ${r.product_name} → Color: ${r.color_code} (${r.hex}), Order: ${r.display_order}`);
    });
    
    db.close();
  } catch (error) {
    console.error('Error:', error.message);
  }
  
  app.quit();
});

// Prevent default behavior
app.on('window-all-closed', () => {
  // Do nothing - we'll quit manually
});