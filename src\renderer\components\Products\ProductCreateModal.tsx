/**
 * @file ProductCreateModal.tsx
 * @description Modal for creating new products
 */

import React, { useState, useEffect } from 'react';
import { useTokens } from '../../hooks/useTokens';
import { useProductStore } from '../../store/product.store';
import { X } from 'lucide-react';

interface ProductCreateModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ProductCreateModal: React.FC<ProductCreateModalProps> = ({
  isOpen,
  onClose
}) => {
  const tokens = useTokens();
  const { createProduct } = useProductStore();

  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setName('');
      setDescription('');
      setError('');
    }
  }, [isOpen]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!name.trim()) {
      setError('Product name is required');
      return;
    }

    try {
      setIsSubmitting(true);
      setError('');

      const product = await createProduct(name.trim(), {
        description: description.trim() || undefined
      });

      if (product) {
        // Success - close modal
        onClose();
      } else {
        setError('Failed to create product');
      }
    } catch (err) {
      console.error('Error creating product:', err);
      setError(err instanceof Error ? err.message : 'Failed to create product');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) {return null;}

  return (
    <div
      className="fixed inset-0 bg-black/25 dark:bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
      onClick={(e) => {
        if (e.target === e.currentTarget) {onClose();}
      }}
    >
      <div
        className="bg-ui-background-primary dark:bg-zinc-900 rounded-xl shadow-2xl max-w-md w-full mx-4 border border-ui-border-light dark:border-zinc-700"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-ui-border-light dark:border-zinc-700">
          <h2 className="text-lg font-medium text-ui-foreground-primary dark:text-white">
            Create New Product
          </h2>
          <button
            onClick={onClose}
            className="p-1 text-ui-muted dark:text-gray-400 hover:text-ui-foreground-primary dark:hover:text-white rounded-md hover:bg-ui-background-tertiary dark:hover:bg-zinc-800 transition-colors"
            style={{
              transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
            }}
          >
            <X size={20} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6">
          {/* Name field */}
          <div className="mb-5">
            <label
              htmlFor="name"
              className="block text-sm font-medium text-ui-foreground-primary dark:text-white mb-2"
            >
              Product Name <span className="text-red-500">*</span>
            </label>
            <input
              id="name"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter product name"
              className="w-full px-3 py-2 bg-ui-background-secondary dark:bg-zinc-800 border border-ui-border-light dark:border-zinc-700 rounded-md text-ui-foreground-primary dark:text-white placeholder-ui-foreground-tertiary dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-primary dark:focus:ring-brand-primary focus:border-transparent"
              autoFocus
            />
          </div>

          {/* Description field */}
          <div className="mb-6">
            <label
              htmlFor="description"
              className="block text-sm font-medium text-ui-foreground-primary dark:text-white mb-2"
            >
              Description
            </label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Optional description"
              rows={3}
              className="w-full px-3 py-2 bg-ui-background-secondary dark:bg-zinc-800 border border-ui-border-light dark:border-zinc-700 rounded-md text-ui-foreground-primary dark:text-white placeholder-ui-foreground-tertiary dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-brand-primary dark:focus:ring-brand-primary focus:border-transparent resize-none"
            />
          </div>

          {/* Error message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-ui-foreground-primary dark:text-white bg-ui-background-tertiary dark:bg-zinc-800 rounded-md hover:bg-ui-background-tertiary-hover dark:hover:bg-zinc-700 transition-colors"
              disabled={isSubmitting}
              style={{
                transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium bg-brand-primary text-white rounded-md hover:bg-brand-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting || !name.trim()}
              style={{
                transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
              }}
            >
              {isSubmitting ? 'Creating...' : 'Create Product'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProductCreateModal;