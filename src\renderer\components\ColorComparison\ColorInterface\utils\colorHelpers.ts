/**
 * Color helper utilities
 * Common functions used across color interface components
 */

import { parseCMYK, formatCMYKForDisplay } from '../../../../../shared/utils/color';

export const calculateInkCoverage = (cmykString: string): number => {
  const cmyk = parseCMYK(cmykString);
  if (!cmyk) {return 0;}
  
  return cmyk.c + cmyk.m + cmyk.y + cmyk.k;
};

export const getDominantInk = (cmykString: string): string => {
  const cmyk = parseCMYK(cmykString);
  if (!cmyk) {return 'None';}
  
  const { c, m, y, k } = cmyk;
  const max = Math.max(c, m, y, k);
  
  if (max === 0) {return 'None';}
  if (max === c) {return 'Cyan';}
  if (max === m) {return 'Magenta';}
  if (max === y) {return 'Yellow';}
  return 'Black';
};

export const getNearestNamedColor = (_hex: string): string => {
  // This would normally check against a database of named colors
  // For now, return a placeholder
  return 'Custom Color';
};

export const getColorFamily = (hue: number): string => {
  if (hue < 30) {return 'Red';}
  if (hue < 60) {return 'Orange';}
  if (hue < 90) {return 'Yellow';}
  if (hue < 150) {return 'Green';}
  if (hue < 210) {return 'Cyan';}
  if (hue < 270) {return 'Blue';}
  if (hue < 330) {return 'Purple';}
  return 'Red';
};

export const getColorTemperature = (hsl: { h: number; s: number; l: number }): 'warm' | 'cool' | 'neutral' => {
  if (hsl.s < 10) {return 'neutral';}
  
  // Warm colors: red, orange, yellow (0-60, 300-360)
  // Cool colors: green, blue, purple (60-300)
  if ((hsl.h >= 0 && hsl.h <= 60) || (hsl.h >= 300 && hsl.h <= 360)) {
    return 'warm';
  } else if (hsl.h > 60 && hsl.h < 300) {
    return 'cool';
  }
  
  return 'neutral';
};

export const isColorPrintable = (cmykString: string): boolean => {
  const coverage = calculateInkCoverage(cmykString);
  return coverage <= 280; // Standard print limit
};

export const formatColorValue = (value: string, format: string): string => {
  // Format color values for display
  switch (format) {
    case 'hex':
      return value.toUpperCase();
    case 'cmyk':
      return formatCMYKForDisplay(parseCMYK(value));
    default:
      return value;
  }
};