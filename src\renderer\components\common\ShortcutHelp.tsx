import React, { useEffect } from 'react';
import useKeyboardShortcuts from '../../hooks/useKeyboardShortcuts';
import { useTokens } from '../../hooks/useTokens';

interface ShortcutHelpProps {
  isOpen: boolean;
  onClose: () => void;
}

export const ShortcutHelp: React.FC<ShortcutHelpProps> = ({ isOpen, onClose }) => {
  const { shortcuts } = useKeyboardShortcuts();
  const tokens = useTokens();

  // Close on Escape key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onClose]);

  if (!isOpen) {return null;}

  return (
    <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
      <div className="bg-ui-background-primary dark:bg-ui-background-primary rounded-lg shadow-xl max-w-md w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-ui-foreground-primary dark:text-ui-foreground-primary">Keyboard Shortcuts</h2>
          <button
            onClick={onClose}
            className="text-ui-foreground-secondary dark:text-ui-foreground-secondary hover:text-ui-foreground-primary dark:hover:text-ui-foreground-primary transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        <div className="space-y-2">
          {shortcuts.map((shortcut, index) => (
            <div key={index} className="flex justify-between py-2 border-b border-ui-border-light dark:border-ui-border-medium">
              <span className="text-ui-foreground-primary dark:text-ui-foreground-primary">{shortcut.description}</span>
              <kbd className="bg-ui-background-secondary dark:bg-ui-background-tertiary px-2 py-1 rounded text-sm text-ui-foreground-primary dark:text-ui-foreground-primary border border-ui-border-light dark:border-ui-border-medium">
                {shortcut.ctrlKey && 'Ctrl+'}
                {shortcut.altKey && 'Alt+'}
                {shortcut.shiftKey && 'Shift+'}
                {shortcut.key}
              </kbd>
            </div>
          ))}
        </div>

        <div className="mt-6 text-center">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-brand-primary text-white rounded hover:bg-brand-primary/90 transition-colors"
            style={{
              transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.out}`
            }}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ShortcutHelp;