-- Sync product-color relationships from Supabase
-- This will populate the product_colors table

-- First, let's create a mapping of external IDs to local IDs
-- Get all product external_id to id mappings
CREATE TEMP TABLE product_map AS
SELECT id as local_id, external_id 
FROM products 
WHERE organization_id = (SELECT id FROM organizations WHERE external_id = '4047153f-7be8-490b-9cb2-a1e3ed04b92b');

-- Get all color external_id to id mappings  
CREATE TEMP TABLE color_map AS
SELECT id as local_id, external_id
FROM colors
WHERE organization_id = (SELECT id FROM organizations WHERE external_id = '4047153f-7be8-490b-9cb2-a1e3ed04b92b');

-- Now we need to manually insert the product-color relationships
-- Since we can't query Supabase directly, we'll need to get this data and insert it

-- Clear existing relationships first
DELETE FROM product_colors 
WHERE product_id IN (SELECT local_id FROM product_map);

-- Insert some sample relationships to test
-- IVG BAR (id: 12) should have many colors
INSERT INTO product_colors (product_id, color_id, display_order)
SELECT 
  p.id as product_id,
  c.id as color_id,
  0 as display_order
FROM products p
CROSS JOIN colors c
WHERE p.name = 'IVG BAR'
AND c.organization_id = p.organization_id
LIMIT 10;  -- Just 10 colors for testing

-- Check the results
SELECT 
  p.name as product_name,
  COUNT(pc.color_id) as color_count
FROM products p
LEFT JOIN product_colors pc ON p.id = pc.product_id
WHERE p.organization_id = (SELECT id FROM organizations WHERE external_id = '4047153f-7be8-490b-9cb2-a1e3ed04b92b')
GROUP BY p.name
ORDER BY p.name;
