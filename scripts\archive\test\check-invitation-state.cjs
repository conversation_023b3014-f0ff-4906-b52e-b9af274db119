const Database = require('better-sqlite3');
const path = require('path');
const os = require('os');

// Get the database path
const dbPath = path.join(os.homedir(), 'Library', 'Application Support', 'ChromaSync', 'chromasync.db');
console.log('📁 Database path:', dbPath);

try {
  const db = new Database(dbPath, { readonly: true });
  
  // Check if users table exists
  const usersTable = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='users'").get();
  console.log('\n👥 Users table exists:', !!usersTable);
  
  if (usersTable) {
    const userCount = db.prepare("SELECT COUNT(*) as count FROM users").get();
    console.log('   Total users:', userCount.count);
    
    const users = db.prepare("SELECT id, email FROM users LIMIT 5").all();
    console.log('   Sample users:');
    users.forEach(u => console.log(`     - ${u.id.substring(0, 8)}... ${u.email}`));
  }
  
  // Check organization_invitations table
  const invitationsTable = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='organization_invitations'").get();
  console.log('\n📨 Invitations table exists:', !!invitationsTable);
  
  if (invitationsTable) {
    const invitationCount = db.prepare("SELECT COUNT(*) as count FROM organization_invitations").get();
    console.log('   Total invitations:', invitationCount.count);
    
    const invitations = db.prepare("SELECT email, role, created_at FROM organization_invitations ORDER BY created_at DESC LIMIT 5").all();
    if (invitations.length > 0) {
      console.log('   Recent invitations:');
      invitations.forEach(i => console.log(`     - ${i.email} (${i.role}) - ${i.created_at}`));
    }
  }
  
  // Check foreign key constraints
  console.log('\n🔗 Foreign key constraints on organization_invitations:');
  const fks = db.prepare("PRAGMA foreign_key_list(organization_invitations)").all();
  fks.forEach(fk => {
    console.log(`   - ${fk.from} → ${fk.table}(${fk.to})`);
  });
  
  // Check current user from organization_members
  console.log('\n👤 Current organization members:');
  const members = db.prepare(`
    SELECT om.user_id, om.role, o.name as org_name 
    FROM organization_members om
    JOIN organizations o ON om.organization_id = o.id
    LIMIT 5
  `).all();
  members.forEach(m => {
    console.log(`   - User ${m.user_id.substring(0, 8)}... is ${m.role} of ${m.org_name}`);
  });
  
  db.close();
} catch (error) {
  console.error('❌ Error:', error.message);
}