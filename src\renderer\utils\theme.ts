/**
 * @file theme.ts
 * @description Utility functions to work with design tokens
 */

import tokens from '../styles/tokens';

/**
 * Get a specific color from the color palette
 * @param path - Dot notation path to the color (e.g., 'brand.primary')
 * @returns The color value or undefined if not found
 */
export function getColor(path: string): string | undefined {
  return getNestedValue(tokens.colors, path);
}

/**
 * Get a specific typography value
 * @param path - Dot notation path to the typography value (e.g., 'fontSize.base')
 * @returns The typography value or undefined if not found
 */
export function getTypography(path: string): string | string[] | undefined {
  return getNestedValue(tokens.typography, path);
}

/**
 * Get a specific spacing value
 * @param key - The spacing key (e.g., '4' for 1rem)
 * @returns The spacing value or undefined if not found
 */
export function getSpacing(key: string | number): string | undefined {
  return tokens.spacing[key.toString()];
}

/**
 * Get a nested value using a dot notation path
 * @param obj - The object to search in
 * @param path - Dot notation path (e.g., 'ui.background.primary')
 * @returns The value or undefined if not found
 */
function getNestedValue(obj: any, path: string): any {
  const keys = path.split('.');
  return keys.reduce((o, key) => (o && o[key] !== undefined) ? o[key] : undefined, obj);
}

/**
 * Generate CSS variables based on the design tokens
 * @returns A string of CSS variables
 */
export function generateCssVariables(): string {
  const variables: string[] = [];

  // Process colors
  flattenObject(tokens.colors, 'color').forEach(({ key, value }) => {
    variables.push(`--${key}: ${value};`);
  });

  // Process typography
  flattenObject(tokens.typography.fontSize, 'font-size').forEach(({ key, value }) => {
    variables.push(`--${key}: ${value};`);
  });

  flattenObject(tokens.typography.fontWeight, 'font-weight').forEach(({ key, value }) => {
    variables.push(`--${key}: ${value};`);
  });

  // Process spacing
  Object.entries(tokens.spacing).forEach(([key, value]) => {
    variables.push(`--spacing-${key}: ${value};`);
  });

  // Process shadows
  Object.entries(tokens.shadows).forEach(([key, value]) => {
    const shadowKey = key === 'DEFAULT' ? 'shadow' : `shadow-${key}`;
    variables.push(`--${shadowKey}: ${value};`);
  });

  return variables.join('\n');
}

/**
 * Flatten a nested object into key-value pairs with namespaced keys
 * @param obj - The object to flatten
 * @param prefix - Prefix for the keys
 * @returns Array of key-value pairs
 */
function flattenObject(obj: any, prefix: string): Array<{ key: string; value: any }> {
  const result: Array<{ key: string; value: any }> = [];

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];
      const newKey = key === 'DEFAULT' ? prefix : `${prefix}-${key}`;

      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        result.push(...flattenObject(value, newKey));
      } else {
        result.push({ key: newKey, value });
      }
    }
  }

  return result;
}

export default {
  getColor,
  getTypography,
  getSpacing,
  generateCssVariables,
}; 