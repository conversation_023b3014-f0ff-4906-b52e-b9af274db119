import { describe, test, beforeAll, afterAll, expect } from 'vitest';
import { Page } from 'playwright';
import BrowserCompatibilityTester, { BrowserName } from './utils/browser-compatibility';

const BROWSERS: BrowserName[] = ['chrome', 'firefox', 'safari', 'edge'];

// Components to test for cross-browser compatibility
const COMPONENTS_TO_TEST = [
  { name: 'Head<PERSON>', selector: 'header', screenshot: true },
  { name: 'SearchBar', selector: '[data-testid="search-bar"]', screenshot: true },
  { name: 'ViewTabs', selector: '[data-testid="view-tabs"]', screenshot: true },
  { name: 'ColorForm', selector: '[data-testid="color-form"]', screenshot: true },
  { name: 'ColorTable', selector: '[data-testid="color-table"]', screenshot: true },
  { name: 'ColorSwatches', selector: '[data-testid="color-swatches"]', screenshot: true },
];

// Key user flows to test
const USER_FLOWS = [
  {
    name: 'Add color entry',
    steps: async (page: Page) => {
      await page.click('[data-testid="add-color-button"]');
      await page.fill('[data-testid="color-name-input"]', 'Test Color');
      await page.fill('[data-testid="pantone-code-input"]', 'PMS 123');
      await page.fill('[data-testid="hex-value-input"]', '#FFCC00');
      await page.click('[data-testid="submit-color-button"]');
      const newColorExists = await page.isVisible('text="Test Color"');
      expect(newColorExists).toBe(true);
    }
  },
  {
    name: 'Search for color',
    steps: async (page: Page) => {
      await page.fill('[data-testid="search-input"]', 'Test');
      await page.press('[data-testid="search-input"]', 'Enter');
      // Verify search results contain the term
      const searchResults = await page.$$eval('[data-testid="color-row"]',
        (rows) => rows.some(row => row.textContent?.includes('Test')));
      expect(searchResults).toBe(true);
    }
  },
  {
    name: 'Switch views',
    steps: async (page: Page) => {
      // Switch to flavours view (formerly swatches)
      await page.click('[data-testid="swatches-view-tab"]');
      const swatchesVisible = await page.isVisible('[data-testid="color-swatches"]');
      expect(swatchesVisible).toBe(true);

      // Switch back to details view (formerly table)
      await page.click('[data-testid="table-view-tab"]');
      const tableVisible = await page.isVisible('[data-testid="color-table"]');
      expect(tableVisible).toBe(true);
    }
  }
];

describe('Cross-browser compatibility tests', () => {
  for (const browser of BROWSERS) {
    describe(`Testing in ${browser}`, () => {
      const tester = new BrowserCompatibilityTester();
      let page: Page;

      beforeAll(async () => {
        // Skip Edge tests in CI environment if not on Windows
        if (browser === 'edge' && process.env.CI && process.platform !== 'win32') {
          console.log('Skipping Edge tests in CI on non-Windows platform');
          return;
        }

        try {
          page = await tester.setup(browser);
        } catch (error) {
          console.error(`Failed to set up ${browser}: ${error}`);
          throw error;
        }
      });

      afterAll(async () => {
        await tester.teardown();
      });

      // Component rendering tests
      COMPONENTS_TO_TEST.forEach(component => {
        test(`${component.name} renders correctly`, async () => {
          if (!page) {
            console.log(`Skipping test on ${browser}`);
            return;
          }

          const isVisible = await page.isVisible(component.selector);
          expect(isVisible).toBe(true);

          if (component.screenshot) {
            await tester.takeScreenshot(`${browser}-${component.name}`);
          }
        });
      });

      // User flow tests
      USER_FLOWS.forEach(flow => {
        test(`User flow: ${flow.name}`, async () => {
          if (!page) {
            console.log(`Skipping test on ${browser}`);
            return;
          }

          await tester.executeTest(flow.steps);
        });
      });
    });
  }
});