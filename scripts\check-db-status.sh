#!/bin/bash

# ChromaSync Database Status Check
# This script checks which databases exist and their status

echo "🔍 ChromaSync Database Status"
echo "============================="
echo ""

# Function to get database path based on OS
get_db_path() {
    case "$OSTYPE" in
        darwin*)  # macOS
            echo "$HOME/Library/Application Support/chromasync"
            ;;
        msys*|cygwin*)  # Windows
            echo "$APPDATA/chromasync"
            ;;
        *)  # Linux and others
            echo "$HOME/.config/chromasync"
            ;;
    esac
}

# Function to check database
check_db() {
    local db_file="$1"
    local db_name="$2"
    
    if [ -f "$db_file" ]; then
        local size=$(ls -lh "$db_file" | awk '{print $5}')
        echo "✅ $db_name: Found (Size: $size)"
        
        # Check if it's SQLite
        if command -v sqlite3 >/dev/null 2>&1; then
            # Get table count and row counts
            local table_count=$(sqlite3 "$db_file" "SELECT COUNT(*) FROM sqlite_master WHERE type='table';" 2>/dev/null)
            if [ $? -eq 0 ]; then
                echo "   Tables: $table_count"
                
                # Check specific tables if they exist
                for table in products colors product_colors; do
                    local count=$(sqlite3 "$db_file" "SELECT COUNT(*) FROM $table;" 2>/dev/null)
                    if [ $? -eq 0 ]; then
                        echo "   - $table: $count rows"
                    fi
                done
            fi
        fi
    else
        echo "❌ $db_name: Not found"
    fi
    echo ""
}

# Get the database directory
DB_DIR=$(get_db_path)
echo "📁 Database directory: $DB_DIR"
echo ""

# Check if directory exists
if [ ! -d "$DB_DIR" ]; then
    echo "❌ Database directory not found. ChromaSync may not be installed."
    exit 1
fi

# Navigate to database directory
cd "$DB_DIR" || exit 1

echo "📊 Database Status:"
echo ""

# Check each possible database
check_db "chromasync.db" "ChromaSync DB (Optimized)"
check_db "database.sqlite" "Database SQLite (Legacy)"
check_db "chroma-sync-data.db" "Chroma Sync Data (Old Format)"

# Check for WAL files
echo "📄 WAL Files (Write-Ahead Logging):"
for wal in *.db-wal *.sqlite-wal; do
    if [ -f "$wal" ]; then
        size=$(ls -lh "$wal" | awk '{print $5}')
        echo "  - $wal (Size: $size)"
    fi
done
echo ""

# Check legacy databases folder
if [ -d "legacy-databases" ]; then
    echo "🗄️  Legacy Databases Backup:"
    count=$(ls -1 legacy-databases/ | wc -l)
    echo "  Found $count backup files in legacy-databases/"
fi
