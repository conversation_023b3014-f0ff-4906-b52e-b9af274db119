/**
 * @file circuit-breaker.ts
 * @description Circuit breaker pattern implementation for sync operations
 * 
 * This module provides a robust circuit breaker implementation to prevent
 * cascade failures and provide automatic recovery capabilities.
 */

import { SyncEventHandler } from '../core/sync-types';

// ============================================================================
// CIRCUIT BREAKER TYPES
// ============================================================================

export type CircuitBreakerState = 'CLOSED' | 'OPEN' | 'HALF_OPEN';

interface CircuitBreakerOptions {
  failureThreshold?: number;
  recoveryTimeout?: number;
  monitoringPeriod?: number;
  expectedFailureRate?: number;
  onStateChange?: (state: CircuitBreakerState, reason: string) => void;
}

interface CircuitBreakerMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  failureRate: number;
  consecutiveFailures: number;
  lastFailureTime: number;
  lastSuccessTime: number;
  stateChanges: number;
}

// ============================================================================
// CIRCUIT BREAKER IMPLEMENTATION
// ============================================================================

/**
 * Advanced circuit breaker for sync operations
 */
export class CircuitBreaker {
  private state: CircuitBreakerState = 'CLOSED';
  private failureCount = 0;
  private consecutiveFailures = 0;
  private lastFailureTime = 0;
  private lastSuccessTime = 0;
  private stateChanges = 0;
  private eventHandlers = new Map<string, SyncEventHandler[]>();
  
  // Configuration
  private readonly failureThreshold: number;
  private readonly recoveryTimeout: number;
  private readonly monitoringPeriod: number;
  private readonly expectedFailureRate: number;
  private readonly onStateChange?: (state: CircuitBreakerState, reason: string) => void;
  
  // Metrics tracking
  private totalRequests = 0;
  private successfulRequests = 0;
  private requestHistory: Array<{ timestamp: number; success: boolean }> = [];
  private readonly MAX_HISTORY_SIZE = 1000;

  constructor(options: CircuitBreakerOptions = {}) {
    this.failureThreshold = options.failureThreshold || 5;
    this.recoveryTimeout = options.recoveryTimeout || 60000; // 1 minute
    this.monitoringPeriod = options.monitoringPeriod || 300000; // 5 minutes
    this.expectedFailureRate = options.expectedFailureRate || 0.1; // 10%
    this.onStateChange = options.onStateChange;
  }

  /**
   * Execute a function with circuit breaker protection
   */
  async execute<T>(operation: () => Promise<T>, operationName?: string): Promise<T> {
    // Check if circuit breaker allows the request
    if (!this.allowRequest()) {
      const error = new Error(`Circuit breaker is ${this.state} - operation rejected`);
      this.emitEvent('request-rejected', { 
        state: this.state, 
        operation: operationName,
        reason: 'Circuit breaker protection'
      });
      throw error;
    }

    const startTime = Date.now();
    this.totalRequests++;

    try {
      // Execute the operation
      const result = await operation();
      
      // Record success
      this.recordSuccess();
      
      this.emitEvent('request-succeeded', {
        operation: operationName,
        duration: Date.now() - startTime,
        state: this.state
      });
      
      return result;
      
    } catch (error) {
      // Record failure
      this.recordFailure();
      
      this.emitEvent('request-failed', {
        operation: operationName,
        error: error.message,
        duration: Date.now() - startTime,
        state: this.state
      });
      
      throw error;
    }
  }

  /**
   * Get current circuit breaker state
   */
  getState(): CircuitBreakerState {
    return this.state;
  }

  /**
   * Get circuit breaker metrics
   */
  getMetrics(): CircuitBreakerMetrics {
    return {
      totalRequests: this.totalRequests,
      successfulRequests: this.successfulRequests,
      failedRequests: this.totalRequests - this.successfulRequests,
      failureRate: this.calculateFailureRate(),
      consecutiveFailures: this.consecutiveFailures,
      lastFailureTime: this.lastFailureTime,
      lastSuccessTime: this.lastSuccessTime,
      stateChanges: this.stateChanges
    };
  }

  /**
   * Check if circuit breaker is healthy
   */
  isHealthy(): boolean {
    const failureRate = this.calculateFailureRate();
    return this.state === 'CLOSED' && failureRate <= this.expectedFailureRate;
  }

  /**
   * Manually reset the circuit breaker
   */
  reset(): void {
    this.setState('CLOSED', 'Manual reset');
    this.failureCount = 0;
    this.consecutiveFailures = 0;
    this.lastFailureTime = 0;
    
    console.log('[CircuitBreaker] Manually reset to CLOSED state');
    this.emitEvent('circuit-reset', { timestamp: Date.now() });
  }

  /**
   * Force circuit breaker to open (for testing or emergency)
   */
  forceOpen(reason: string = 'Forced open'): void {
    this.setState('OPEN', reason);
    console.log(`[CircuitBreaker] Forced to OPEN state: ${reason}`);
  }

  /**
   * Subscribe to circuit breaker events
   */
  on(event: string, handler: SyncEventHandler): () => void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    
    this.eventHandlers.get(event)!.push(handler);

    // Return unsubscribe function
    return () => {
      const handlers = this.eventHandlers.get(event);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    };
  }

  /**
   * Get failure rate analysis
   */
  getFailureAnalysis(): {
    currentFailureRate: number;
    recentFailureRate: number;
    trend: 'improving' | 'stable' | 'degrading';
    recommendation: string;
  } {
    const currentFailureRate = this.calculateFailureRate();
    const recentFailureRate = this.calculateRecentFailureRate();
    
    let trend: 'improving' | 'stable' | 'degrading' = 'stable';
    if (recentFailureRate < currentFailureRate * 0.8) {
      trend = 'improving';
    } else if (recentFailureRate > currentFailureRate * 1.2) {
      trend = 'degrading';
    }
    
    let recommendation = 'System operating normally';
    if (currentFailureRate > this.expectedFailureRate) {
      recommendation = 'High failure rate detected - investigate underlying issues';
    } else if (this.state === 'OPEN') {
      recommendation = 'Circuit breaker is open - wait for automatic recovery or investigate failures';
    } else if (this.state === 'HALF_OPEN') {
      recommendation = 'Circuit breaker is testing recovery - monitor next few operations';
    }
    
    return {
      currentFailureRate,
      recentFailureRate,
      trend,
      recommendation
    };
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Check if a request should be allowed
   */
  private allowRequest(): boolean {
    switch (this.state) {
      case 'CLOSED':
        return true;
        
      case 'OPEN':
        // Check if recovery timeout has passed
        if (Date.now() - this.lastFailureTime >= this.recoveryTimeout) {
          this.setState('HALF_OPEN', 'Recovery timeout reached');
          return true;
        }
        return false;
        
      case 'HALF_OPEN':
        // Allow limited requests to test recovery
        return true;
        
      default:
        return false;
    }
  }

  /**
   * Record a successful operation
   */
  private recordSuccess(): void {
    this.successfulRequests++;
    this.consecutiveFailures = 0;
    this.lastSuccessTime = Date.now();
    
    // Add to history
    this.addToHistory(true);
    
    // If we're in HALF_OPEN state, consider closing the circuit
    if (this.state === 'HALF_OPEN') {
      this.setState('CLOSED', 'Successful operation in HALF_OPEN state');
    }
  }

  /**
   * Record a failed operation
   */
  private recordFailure(): void {
    this.failureCount++;
    this.consecutiveFailures++;
    this.lastFailureTime = Date.now();
    
    // Add to history
    this.addToHistory(false);
    
    // Check if we should open the circuit
    if (this.state === 'CLOSED' && this.consecutiveFailures >= this.failureThreshold) {
      this.setState('OPEN', `Failure threshold reached: ${this.consecutiveFailures} consecutive failures`);
    } else if (this.state === 'HALF_OPEN') {
      // If we fail in HALF_OPEN state, go back to OPEN
      this.setState('OPEN', 'Failure during recovery test');
    }
  }

  /**
   * Add request result to history
   */
  private addToHistory(success: boolean): void {
    this.requestHistory.push({
      timestamp: Date.now(),
      success
    });
    
    // Maintain history size
    if (this.requestHistory.length > this.MAX_HISTORY_SIZE) {
      this.requestHistory.shift();
    }
    
    // Clean old entries outside monitoring period
    const cutoff = Date.now() - this.monitoringPeriod;
    this.requestHistory = this.requestHistory.filter(entry => entry.timestamp > cutoff);
  }

  /**
   * Calculate overall failure rate
   */
  private calculateFailureRate(): number {
    if (this.totalRequests === 0) return 0;
    return (this.totalRequests - this.successfulRequests) / this.totalRequests;
  }

  /**
   * Calculate recent failure rate (within monitoring period)
   */
  private calculateRecentFailureRate(): number {
    const cutoff = Date.now() - this.monitoringPeriod;
    const recentRequests = this.requestHistory.filter(entry => entry.timestamp > cutoff);
    
    if (recentRequests.length === 0) return 0;
    
    const recentFailures = recentRequests.filter(entry => !entry.success).length;
    return recentFailures / recentRequests.length;
  }

  /**
   * Change circuit breaker state
   */
  private setState(newState: CircuitBreakerState, reason: string): void {
    if (this.state !== newState) {
      const oldState = this.state;
      this.state = newState;
      this.stateChanges++;
      
      console.log(`[CircuitBreaker] State changed: ${oldState} → ${newState} (${reason})`);
      
      // Call state change callback
      if (this.onStateChange) {
        try {
          this.onStateChange(newState, reason);
        } catch (error) {
          console.error('[CircuitBreaker] Error in state change callback:', error);
        }
      }
      
      // Emit state change event
      this.emitEvent('state-changed', {
        oldState,
        newState,
        reason,
        timestamp: Date.now(),
        metrics: this.getMetrics()
      });
    }
  }

  /**
   * Emit event to all registered handlers
   */
  private emitEvent(event: string, data: any): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`[CircuitBreaker] Error in event handler for ${event}:`, error);
        }
      });
    }
  }
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Create a circuit breaker with default configuration
 */
export function createCircuitBreaker(options: CircuitBreakerOptions = {}): CircuitBreaker {
  return new CircuitBreaker(options);
}

/**
 * Create a circuit breaker specifically configured for sync operations
 */
export function createSyncCircuitBreaker(): CircuitBreaker {
  return new CircuitBreaker({
    failureThreshold: 3,        // Open after 3 consecutive failures
    recoveryTimeout: 30000,     // Try recovery after 30 seconds
    monitoringPeriod: 300000,   // Monitor last 5 minutes
    expectedFailureRate: 0.05,  // Expect 5% failure rate
    onStateChange: (state, reason) => {
      console.log(`[SyncCircuitBreaker] ${state}: ${reason}`);
    }
  });
}
