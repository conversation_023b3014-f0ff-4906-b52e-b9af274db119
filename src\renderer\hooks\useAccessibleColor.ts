import { useMemo } from 'react';
import { useColorBlindness } from '../context/ColorBlindnessContext';
import { simulateColorBlindness, getContrastRatio, suggestAccessibleColor } from '../../shared/utils/color/colorBlindness';

/**
 * Hook to get accessible color values based on color blindness mode
 */
export function useAccessibleColor(hex: string, background: string = '#FFFFFF') {
  const { mode } = useColorBlindness();

  const accessibleColor = useMemo(() => {
    // Get the simulated color
    const simulated = simulateColorBlindness(hex, mode);
    
    // Get a suggested accessible version if needed
    const suggested = suggestAccessibleColor(hex, background, mode);
    
    // Calculate contrast ratios
    const originalContrast = getContrastRatio(hex, background);
    const simulatedContrast = getContrastRatio(simulated, background);
    const suggestedContrast = getContrastRatio(suggested, background);

    return {
      original: hex,
      simulated,
      suggested,
      useAlternative: simulatedContrast < 4.5 && suggestedContrast > simulatedContrast,
      originalContrast,
      simulatedContrast,
      suggestedContrast,
      display: mode === 'normal' ? hex : simulated
    };
  }, [hex, background, mode]);

  return accessibleColor;
}

/**
 * Hook to get multiple accessible colors at once
 */
export function useAccessibleColors(colors: string[], background: string = '#FFFFFF') {
  const { mode } = useColorBlindness();

  return useMemo(() => {
    return colors.map(hex => {
      const simulated = simulateColorBlindness(hex, mode);
      const suggested = suggestAccessibleColor(hex, background, mode);
      const simulatedContrast = getContrastRatio(simulated, background);
      const suggestedContrast = getContrastRatio(suggested, background);

      return {
        original: hex,
        simulated,
        suggested,
        useAlternative: simulatedContrast < 4.5 && suggestedContrast > simulatedContrast,
        display: mode === 'normal' ? hex : simulated
      };
    });
  }, [colors, background, mode]);
}