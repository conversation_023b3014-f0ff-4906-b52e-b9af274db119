-- Migration: Production-ready sync compatibility (SAFE VERSION)
-- Ensures local database can handle all sync scenarios
-- This version includes proper existence checks

-- Note: This migration should be executed through SafeMigrationRunner
-- which will handle column existence checks automatically

-- Add device_id to track which device made changes
ALTER TABLE products ADD COLUMN device_id TEXT;
ALTER TABLE colors ADD COLUMN device_id TEXT;

-- Add conflict resolution columns
ALTER TABLE products ADD COLUMN conflict_resolved_at TEXT;
ALTER TABLE colors ADD COLUMN conflict_resolved_at TEXT;

-- Create sync health monitoring table
CREATE TABLE IF NOT EXISTS sync_health (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  sync_type TEXT NOT NULL, -- 'initial', 'incremental', 'manual'
  sync_status TEXT NOT NULL, -- 'started', 'completed', 'failed'
  organization_id INTEGER,
  started_at TEXT NOT NULL,
  completed_at TEXT,
  error_message TEXT,
  items_synced_products INTEGER DEFAULT 0,
  items_synced_colors INTEGER DEFAULT 0,
  items_synced_relationships INTEGER DEFAULT 0,
  retry_count INTEGER DEFAULT 0,
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (organization_id) REFERENCES organizations(id)
);

-- Create table for tracking sync conflicts
CREATE TABLE IF NOT EXISTS sync_conflicts (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  table_name TEXT NOT NULL,
  external_id TEXT NOT NULL,
  local_data TEXT,
  remote_data TEXT,
  conflict_type TEXT, -- 'update-update', 'delete-update', etc.
  resolved BOOLEAN DEFAULT FALSE,
  resolution_type TEXT, -- 'local', 'remote', 'merged'
  created_at TEXT DEFAULT CURRENT_TIMESTAMP,
  resolved_at TEXT
);

-- Add indexes for better sync performance
CREATE INDEX IF NOT EXISTS idx_sync_health_org ON sync_health(organization_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_products_device ON products(device_id);
CREATE INDEX IF NOT EXISTS idx_colors_device ON colors(device_id);
CREATE INDEX IF NOT EXISTS idx_sync_conflicts_unresolved ON sync_conflicts(resolved, created_at);

-- Update existing data to have device_id if missing
UPDATE products SET device_id = 'legacy' WHERE device_id IS NULL;
UPDATE colors SET device_id = 'legacy' WHERE device_id IS NULL;