-- Create test product with flat and gradient colors

-- Begin transaction
BEGIN TRANSACTION;

-- 1. Create test product
INSERT INTO products (external_id, name, metadata, created_at, updated_at)
VALUES (
  lower(hex(randomblob(16))), 
  'Test Product ' || date('now'),
  '{"description": "Test product with flat and gradient colors", "testData": true}',
  datetime('now'),
  datetime('now')
);

-- Get the product ID we just created
-- Store product ID for later use
WITH new_product AS (
  SELECT id, external_id FROM products 
  WHERE name = 'Test Product ' || date('now')
  ORDER BY id DESC LIMIT 1
)

-- 2. Create flat color
INSERT INTO colors (external_id, source_id, code, display_name, hex, is_gradient, created_at, updated_at)
SELECT 
  lower(hex(randomblob(16))),
  1, -- User source
  'TEST-FLAT-' || strftime('%s', 'now'),
  'Test Blue',
  '#2563EB',
  0,
  datetime('now'),
  datetime('now')
FROM new_product;

-- Get the flat color ID
WITH new_color AS (
  SELECT id, external_id FROM colors 
  WHERE code LIKE 'TEST-FLAT-%'
  ORDER BY id DESC LIMIT 1
)
-- Add CMYK values for flat color
INSERT INTO color_cmyk (color_id, cyan, magenta, yellow, black)
SELECT id, 85, 60, 0, 8 FROM new_color;

-- Add RGB values for flat color
WITH new_color AS (
  SELECT id FROM colors 
  WHERE code LIKE 'TEST-FLAT-%'
  ORDER BY id DESC LIMIT 1
)
INSERT INTO color_rgb (color_id, red, green, blue)
SELECT id, 37, 99, 235 FROM new_color;

-- 3. Create gradient color
WITH new_product AS (
  SELECT id, external_id FROM products 
  WHERE name = 'Test Product ' || date('now')
  ORDER BY id DESC LIMIT 1
)
INSERT INTO colors (external_id, source_id, code, display_name, hex, is_gradient, properties, created_at, updated_at)
SELECT 
  lower(hex(randomblob(16))),
  1, -- User source
  'TEST-GRADIENT-' || strftime('%s', 'now'),
  'Test Gradient (Blue to Purple)',
  '#2563EB',
  1,
  '{"gradient": {"type": "linear", "angle": 45, "stops": [{"position": 0, "color": "#2563EB"}, {"position": 0.5, "color": "#7C3AED"}, {"position": 1, "color": "#DC2626"}]}}',
  datetime('now'),
  datetime('now')
FROM new_product;

-- Get the gradient color ID
WITH new_gradient AS (
  SELECT id FROM colors 
  WHERE code LIKE 'TEST-GRADIENT-%'
  ORDER BY id DESC LIMIT 1
)
-- Add CMYK values for gradient color
INSERT INTO color_cmyk (color_id, cyan, magenta, yellow, black)
SELECT id, 85, 60, 0, 8 FROM new_gradient;

-- Add RGB values for gradient color
WITH new_gradient AS (
  SELECT id FROM colors 
  WHERE code LIKE 'TEST-GRADIENT-%'
  ORDER BY id DESC LIMIT 1
)
INSERT INTO color_rgb (color_id, red, green, blue)
SELECT id, 37, 99, 235 FROM new_gradient;

-- 4. Associate colors with product
-- Associate flat color
INSERT INTO product_colors (product_id, color_id, display_order)
SELECT 
  p.id,
  c.id,
  1
FROM products p
CROSS JOIN colors c
WHERE p.name = 'Test Product ' || date('now')
AND c.code LIKE 'TEST-FLAT-%'
ORDER BY p.id DESC, c.id DESC
LIMIT 1;

-- Associate gradient color
INSERT INTO product_colors (product_id, color_id, display_order)
SELECT 
  p.id,
  c.id,
  2
FROM products p
CROSS JOIN colors c
WHERE p.name = 'Test Product ' || date('now')
AND c.code LIKE 'TEST-GRADIENT-%'
ORDER BY p.id DESC, c.id DESC
LIMIT 1;

-- Commit transaction
COMMIT;

-- Show what was created
SELECT 
  p.name as product_name,
  c.code as color_code,
  c.display_name as color_name,
  c.hex,
  CASE WHEN c.is_gradient = 1 THEN 'Gradient' ELSE 'Flat' END as color_type
FROM products p
JOIN product_colors pc ON p.id = pc.product_id
JOIN colors c ON pc.color_id = c.id
WHERE p.name = 'Test Product ' || date('now')
ORDER BY pc.display_order;