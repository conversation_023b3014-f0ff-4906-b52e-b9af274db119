/**
 * IPC handlers for loading sample data
 */

import { ipcMain } from 'electron';
import { ProductService } from '../db/services/product.service';
import { ColorService } from '../db/services/color.service';
import { getCurrentOrganizationId } from './organization.ipc';

// Sample data for 10 products
const sampleProducts = [
  {name: "Summer Collection 2024",
    sku: "SUMM-2024",
    description: "Vibrant summer fashion line",
    colors: [
      { code: "PANTONE 17-1463", name: "Tangerine Tango", hex: "#DD4124", library: "code" },
      {code: "PANTONE 14-4318", name: "Aqua Sky", hex: "#7BC4C4", library: "code" },
      {code: "PANTONE 13-0647", name: "Illuminating Yellow", hex: "#F5DF4D", library: "code" },
      {code: "PANTONE 18-3838", name: "Ultra Violet", hex: "#5F4B8B", library: "code" },
      {code: "PANTONE 15-5519", name: "Turquoise", hex: "#45B8AC", library: "code" }
    ]
  },
  {
    name: "Urban Office Furniture",
    sku: "URB-OFF-001",
    description: "Modern office furniture collection",
    colors: [
      { code: "RAL 7016", name: "Anthracite Grey", hex: "#293133", library: "ral" },
      { code: "RAL 9003", name: "Signal White", hex: "#F4F4F4", library: "ral" },
      { code: "RAL 5024", name: "Pastel Blue", hex: "#5D9B9B", library: "ral" },
      { code: "RAL 7035", name: "Light Grey", hex: "#D7D7D7", library: "ral" },
      { code: "CUSTOM-001", name: "Executive Brown", hex: "#3E2723", library: "user" }
    ]
  },
  {name: "Kids Room Essentials",
    sku: "KIDS-ESS-001",
    description: "Colorful and safe furniture for children",
    colors: [
      { code: "PANTONE 13-4411", name: "Baby Blue", hex: "#B5D3E7", library: "code" },
      {code: "PANTONE 12-0752", name: "Buttercup", hex: "#FCE883", library: "code" },
      {code: "PANTONE 14-4318", name: "Mint Green", hex: "#98D982", library: "code" },
      {code: "PANTONE 14-2311", name: "Pink Lavender", hex: "#E8B5CE", library: "code" },
      {code: "PANTONE 16-1441", name: "Peach", hex: "#FFBE98", library: "code" }
    ]
  },
  {
    name: "Automotive Paint Series",
    sku: "AUTO-PAINT-001",
    description: "Professional automotive paint collection",
    colors: [
      { code: "RAL 3020", name: "Traffic Red", hex: "#CC0605", library: "ral" },
      { code: "RAL 9005", name: "Jet Black", hex: "#0A0A0A", library: "ral" },
      { code: "METAL-001", name: "Metallic Silver", hex: "#C0C0C0", library: "user", metallic: true },
      { code: "RAL 5002", name: "Ultramarine Blue", hex: "#1E3A8A", library: "ral", metallic: true },
      { code: "PEARL-001", name: "Pearl White", hex: "#F8F8FF", library: "user", metallic: true }
    ]
  },
  {
    name: "Garden Furniture Set",
    sku: "GARD-FURN-001",
    description: "Weather-resistant outdoor furniture",
    colors: [
      { code: "RAL 6005", name: "Moss Green", hex: "#0F4336", library: "ral" },
      { code: "RAL 8017", name: "Chocolate Brown", hex: "#45322E", library: "ral" },
      { code: "RAL 7032", name: "Pebble Grey", hex: "#B8B799", library: "ral" },
      { code: "RAL 1015", name: "Light Ivory", hex: "#E6D690", library: "ral" },
      {code: "PANTONE 19-0509", name: "Forest Night", hex: "#1B3B36", library: "code" }
    ]
  },
  {
    name: "Tech Accessories Line",
    sku: "TECH-ACC-001",
    description: "Modern tech accessories with style",
    colors: [
      { code: "TECH-001", name: "Space Grey", hex: "#4A4A4A", library: "user", metallic: true },
      { code: "TECH-002", name: "Rose Gold", hex: "#E0BFB8", library: "user", metallic: true },
      {code: "PANTONE 2735", name: "Electric Blue", hex: "#3F00FF", library: "code" },
      { code: "TECH-003", name: "Midnight Black", hex: "#000000", library: "user" },
      { code: "TECH-004", name: "Arctic White", hex: "#FFFFFF", library: "user" }
    ]
  },
  {
    name: "Bathroom Fixtures",
    sku: "BATH-FIX-001",  
    description: "Premium bathroom fixtures and fittings",
    colors: [
      { code: "RAL 9010", name: "Pure White", hex: "#FFFFFF", library: "ral" },
      { code: "CHROME-001", name: "Chrome", hex: "#D4D4D4", library: "user", metallic: true },
      { code: "BRASS-001", name: "Brushed Brass", hex: "#B8860B", library: "user", metallic: true },
      { code: "RAL 7001", name: "Silver Grey", hex: "#8D948D", library: "ral" },
      { code: "COPPER-001", name: "Aged Copper", hex: "#B87333", library: "user", metallic: true }
    ]
  },
  {
    name: "Kitchen Cabinet Series",
    sku: "KITCH-CAB-001",
    description: "Contemporary kitchen cabinet designs",
    colors: [
      { code: "RAL 9016", name: "Traffic White", hex: "#F6F6F6", library: "ral" },
      { code: "NCS S 7500-N", name: "Charcoal", hex: "#3A3A3A", library: "ncs" },
      { code: "RAL 7047", name: "Telegraph Grey", hex: "#D0D0D0", library: "ral" },
      { code: "WOOD-001", name: "Natural Oak", hex: "#BA8C63", library: "user" },
      { code: "NCS S 4010-B30G", name: "Sage Green", hex: "#87A96B", library: "ncs" }
    ]
  },
  {name: "Sports Equipment Range",
    sku: "SPORT-EQ-001",
    description: "Professional sports equipment",
    colors: [
      { code: "PANTONE 18-1664", name: "Fiery Red", hex: "#FF4040", library: "code" },
      {code: "PANTONE 2728", name: "Royal Blue", hex: "#004CFF", library: "code" },
      {code: "PANTONE 375", name: "Lime Green", hex: "#A6CE39", library: "code" },
      { code: "RAL 2004", name: "Pure Orange", hex: "#F44611", library: "ral" },
      { code: "NEON-001", name: "Neon Yellow", hex: "#FFFF00", library: "user" }
    ]
  },
  {name: "Living Room Textiles",
    sku: "LIV-TEX-001",
    description: "Luxury textiles for modern living",
    colors: [
      { code: "PANTONE 18-1441", name: "Marsala", hex: "#955251", library: "code" },
      {code: "PANTONE 14-4107", name: "Harbor Mist", hex: "#B4B8B0", library: "code" },
      {code: "PANTONE 16-1546", name: "Coral Reef", hex: "#FD6F68", library: "code" },
      {code: "PANTONE 17-3938", name: "Very Peri", hex: "#6667AB", library: "code" },
      {code: "PANTONE 13-0647", name: "Mimosa", hex: "#F0C05A", library: "code" }
    ]
  }
];

// Library name to source ID mapping
// const _sourceMap: Record<string, number> = { // Unused
/*'user': 1,
  'code': 2,
  'ral': 3,
  'ncs': 4
}; */

export function registerSampleDataHandlers(
  productService: ProductService,
  colorService: ColorService
): void {
  console.log('[SampleData] Registering sample data handlers...');

  // Create all sample products
  ipcMain.removeHandler('sampleData:createAll');
  ipcMain.handle('sampleData:createAll', async () => {
    try {
      console.log('[SampleData] Creating all sample products...');
      
      const results = [];
      let successCount = 0;
      let errorCount = 0;

      for (const productData of sampleProducts) {
        try {
          // Get current organization ID
          const organizationId = getCurrentOrganizationId();
          if (!organizationId) {
            throw new Error('No organization selected');
          }
          
          // Create product
          const product = productService.create({
            name: productData.name,
            metadata: {
              description: productData.description,
              sku: productData.sku,
              sampleData: true
            }
          }, organizationId);

          console.log(`[SampleData] Created product: ${product.name}`);

          // Create and associate colors
          for (const colorData of productData.colors) {
            try {
              // Create color
              const color = colorService.add({
                product: product.name,
                organizationId,
                name: colorData.name,
                code: colorData.code,
                hex: colorData.hex,
                cmyk: '0,0,0,0', // Will be calculated from hex
                notes: `Sample data from ${colorData.library}`,
                isLibrary: colorData.library !== 'user'
              }, organizationId);

              // Associate with product
              productService.addColorToProduct(product.id, color.id, organizationId);
              console.log(`[SampleData] Added color ${color.code} to ${product.name}`);
            } catch (colorError) {
              console.error(`[SampleData] Error creating color ${colorData.code}:`, colorError);
              errorCount++;
            }
          }

          results.push(product);
          successCount++;
        } catch (productError) {
          console.error(`[SampleData] Error creating product ${productData.name}:`, productError);
          errorCount++;
        }
      }

      return {
        success: true,
        message: `Created ${successCount} products with their colors. Errors: ${errorCount}`,
        products: results
      };
    } catch (error) {
      console.error('[SampleData] Error creating sample data:', error);
      throw error;
    }
  });

  // Remove all sample data
  ipcMain.removeHandler('sampleData:removeAll');
  ipcMain.handle('sampleData:removeAll', async () => {
    try {
      console.log('[SampleData] Removing all sample data...');
      
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      
      const allProducts = productService.getAll(organizationId);
      const sampleProducts = allProducts.filter(p => 
        p.metadata && 
        typeof p.metadata === 'object' && 
        'sampleData' in p.metadata
      );

      let removedCount = 0;
      
      for (const product of sampleProducts) {
        productService.delete(product.id, organizationId);
        removedCount++;
      }

      return {
        success: true,
        removedCount,
        message: `Removed ${removedCount} sample products and their associated colors`
      };
    } catch (error) {
      console.error('[SampleData] Error removing sample data:', error);
      throw error;
    }
  });

  console.log('[SampleData] Sample data handlers registered');
}
