/**
 * Color Analysis Utilities
 * Functions for color contrast, harmony, and accessibility analysis
 */

import { colord, extend } from 'colord';
import labPlugin from 'colord/plugins/lab';
import a11yPlugin from 'colord/plugins/a11y';
import { RGB, HSL, LAB, ContrastResult, WCAGCompliance, DeltaEResult, APCAResult } from './types';
import { hexToRgb } from './conversion';

// Extend colord with plugins
extend([labPlugin, a11yPlugin]);

/**
 * Contrast Calculations
 */

// Calculate relative luminance of a color (WCAG formula)
export function calculateRelativeLuminance(rgb: RGB): number {
  // Use colord's built-in luminance calculation which follows WCAG 2.1
  return colord({ r: rgb.r, g: rgb.g, b: rgb.b }).luminance();
}

// Calculate contrast ratio between two colors
export function calculateContrastRatio(rgb1: RGB, rgb2: RGB): number {
  // Use colord's contrast calculation which follows WCAG guidelines
  const color1 = colord({ r: rgb1.r, g: rgb1.g, b: rgb1.b });
  const color2 = colord({ r: rgb2.r, g: rgb2.g, b: rgb2.b });
  return color1.contrast(color2);
}

// Get WCAG compliance levels for a contrast ratio
export function getWcagCompliance(ratio: number): WCAGCompliance {
  return {
    aa: {
      normal: ratio >= 4.5,
      large: ratio >= 3
    },
    aaa: {
      normal: ratio >= 7,
      large: ratio >= 4.5
    }
  };
}

// Check contrast and get full results
export function checkContrast(rgb1: RGB, rgb2: RGB): ContrastResult {
  const ratio = calculateContrastRatio(rgb1, rgb2);
  const wcag = getWcagCompliance(ratio);
  
  return { ratio, wcag };
}

// Check contrast from hex colors
export function checkContrastHex(hex1: string, hex2: string): ContrastResult | null {
  const rgb1 = hexToRgb(hex1);
  const rgb2 = hexToRgb(hex2);
  
  if (!rgb1 || !rgb2) {return null;}
  
  return checkContrast(rgb1, rgb2);
}

/**
 * Color Difference (Delta E) Calculations
 */

// Calculate Delta E (CIE2000) - Most accurate perceptual color difference
export function calculateDeltaE(lab1: LAB, lab2: LAB): number {
  // Use colord's delta method which implements Delta E 2000
  const color1 = colord({ l: lab1.l, a: lab1.a, b: lab1.b });
  const color2 = colord({ l: lab2.l, a: lab2.a, b: lab2.b });
  return color1.delta(color2) * 100; // colord returns 0-1, convert to 0-100 scale
}

// Calculate Delta E from RGB colors
export function calculateDeltaERgb(rgb1: RGB, rgb2: RGB): number {
  // Use colord's delta method directly with RGB colors
  const color1 = colord({ r: rgb1.r, g: rgb1.g, b: rgb1.b });
  const color2 = colord({ r: rgb2.r, g: rgb2.g, b: rgb2.b });
  return color1.delta(color2) * 100; // colord returns 0-1, convert to 0-100 scale
}

// Interpret Delta E value
export function interpretDeltaE(deltaE: number): DeltaEResult {
  let interpretation: string;
  let description: string;
  
  if (deltaE < 1) {
    interpretation = 'Imperceptible';
    description = 'Not perceptible by human eyes';
  } else if (deltaE < 2) {
    interpretation = 'Perceptible through close observation';
    description = 'Only perceived by trained eyes';
  } else if (deltaE < 3.5) {
    interpretation = 'Perceptible at a glance';
    description = 'Noticeable difference';
  } else if (deltaE < 5) {
    interpretation = 'Noticeable difference';
    description = 'Clear difference in color';
  } else {
    interpretation = 'Different colors';
    description = 'Colors are distinctly different';
  }
  
  return {
    value: deltaE,
    interpretation,
    description
  };
}

/**
 * Accessibility Helpers
 */

// Check if text color is accessible on background
export function checkAccessibility(
  textRgb: RGB,
  bgRgb: RGB,
  level: 'AA' | 'AAA' = 'AA',
  size: 'normal' | 'large' = 'normal'
): boolean {
  // Use colord's isReadable method which checks WCAG compliance
  const textColor = colord({ r: textRgb.r, g: textRgb.g, b: textRgb.b });
  const bgColor = colord({ r: bgRgb.r, g: bgRgb.g, b: bgRgb.b });
  return textColor.isReadable(bgColor, { level, size });
}

// Get recommended text color (black or white) for a background
export function getAccessibleTextColor(bgRgb: RGB): 'black' | 'white' {
  const white = { r: 255, g: 255, b: 255 };
  const black = { r: 0, g: 0, b: 0 };
  
  const whiteContrast = calculateContrastRatio(bgRgb, white);
  const blackContrast = calculateContrastRatio(bgRgb, black);
  
  return blackContrast > whiteContrast ? 'black' : 'white';
}

/**
 * Color Harmony Analysis
 */

// Get complementary color
export function getComplementaryColor(hsl: HSL): HSL {
  return {
    h: (hsl.h + 180) % 360,
    s: hsl.s,
    l: hsl.l
  };
}

// Get analogous colors (adjacent on color wheel)
export function getAnalogousColors(hsl: HSL, angle: number = 30): [HSL, HSL] {
  return [
    { h: (hsl.h + angle) % 360, s: hsl.s, l: hsl.l },
    { h: (hsl.h - angle + 360) % 360, s: hsl.s, l: hsl.l }
  ];
}

// Get triadic colors (evenly spaced on color wheel)
export function getTriadicColors(hsl: HSL): [HSL, HSL] {
  return [
    { h: (hsl.h + 120) % 360, s: hsl.s, l: hsl.l },
    { h: (hsl.h + 240) % 360, s: hsl.s, l: hsl.l }
  ];
}

// Get tetradic colors (square on color wheel)
export function getTetradicColors(hsl: HSL): [HSL, HSL, HSL] {
  return [
    { h: (hsl.h + 90) % 360, s: hsl.s, l: hsl.l },
    { h: (hsl.h + 180) % 360, s: hsl.s, l: hsl.l },
    { h: (hsl.h + 270) % 360, s: hsl.s, l: hsl.l }
  ];
}

// Get split complementary colors
export function getSplitComplementaryColors(hsl: HSL, angle: number = 30): [HSL, HSL] {
  const complement = (hsl.h + 180) % 360;
  return [
    { h: (complement + angle) % 360, s: hsl.s, l: hsl.l },
    { h: (complement - angle + 360) % 360, s: hsl.s, l: hsl.l }
  ];
}

// Get monochromatic colors (variations in lightness)
export function getMonochromaticColors(hsl: HSL, steps: number = 5): HSL[] {
  const colors: HSL[] = [];
  const lightnessStep = 80 / (steps - 1); // Range from 10% to 90%
  
  for (let i = 0; i < steps; i++) {
    colors.push({
      h: hsl.h,
      s: hsl.s,
      l: Math.round(10 + i * lightnessStep)
    });
  }
  
  return colors;
}

/**
 * Color Analysis Utilities
 */

// Check if color is grayscale
export function isGrayscale(rgb: RGB): boolean {
  return rgb.r === rgb.g && rgb.g === rgb.b;
}

// Get color temperature (warm/cool/neutral)
export function getColorTemperature(hsl: HSL): 'warm' | 'cool' | 'neutral' {
  if (hsl.s < 10) {return 'neutral';} // Low saturation = neutral
  
  // Warm colors: red, orange, yellow (0-60, 300-360)
  // Cool colors: green, blue, purple (60-300)
  if ((hsl.h >= 0 && hsl.h <= 60) || (hsl.h >= 300 && hsl.h <= 360)) {
    return 'warm';
  } else if (hsl.h > 60 && hsl.h < 300) {
    return 'cool';
  }
  
  return 'neutral';
}

// Calculate color similarity score (0-100)
export function calculateColorSimilarity(rgb1: RGB, rgb2: RGB): number {
  const deltaE = calculateDeltaERgb(rgb1, rgb2);
  
  // Convert Delta E to a 0-100 scale
  // Delta E of 0 = 100% similar, Delta E of 100+ = 0% similar
  const similarity = Math.max(0, 100 - deltaE);
  
  return Math.round(similarity);
}

/**
 * APCA (Advanced Perceptual Contrast Algorithm) Implementation
 * Based on WCAG 3.0 draft specifications
 * More perceptually accurate than WCAG 2.1 contrast ratios
 */

// APCA constants
const APCA_Y_THRESHOLD = 0.022;
const APCA_W3_MAGIC_NUMBER = 1.14;
const APCA_G = 2.4;
const APCA_NORM_TXT_Y = 0.4;
const APCA_NORM_BG_Y = 0.56;
const APCA_REV_TXT_Y = 0.62;
const APCA_REV_BG_Y = 0.65;

// Convert RGB to Y (luminance) for APCA calculations
function rgbToY(rgb: RGB): number {
  // Normalize RGB values to 0-1
  let { r, g, b } = rgb;
  r = r / 255;
  g = g / 255;
  b = b / 255;

  // Apply gamma correction
  r = r <= 0.04045 ? r / 12.92 : Math.pow((r + 0.055) / 1.055, APCA_G);
  g = g <= 0.04045 ? g / 12.92 : Math.pow((g + 0.055) / 1.055, APCA_G);
  b = b <= 0.04045 ? b / 12.92 : Math.pow((b + 0.055) / 1.055, APCA_G);

  // Calculate Y using sRGB/Rec.709 coefficients
  return 0.2126729 * r + 0.7151522 * g + 0.0721750 * b;
}

// Calculate APCA contrast
export function calculateAPCAContrast(textRgb: RGB, bgRgb: RGB): number {
  const txtY = rgbToY(textRgb);
  const bgY = rgbToY(bgRgb);

  // Determine if this is normal or reverse polarity
  const isReverse = bgY > txtY;

  // Apply clipping thresholds
  let txtYclamp = txtY;
  let bgYclamp = bgY;

  if (isReverse) {
    txtYclamp = txtY < APCA_Y_THRESHOLD ? 0 : txtY;
    bgYclamp = bgY < APCA_Y_THRESHOLD ? 0 : bgY;
  } else {
    txtYclamp = txtY < APCA_Y_THRESHOLD ? 0 : txtY;
    bgYclamp = bgY < APCA_Y_THRESHOLD ? 0 : bgY;
  }

  // Calculate contrast
  let contrast = 0;
  
  if (isReverse) {
    // Reverse polarity (light text on dark background)
    if (bgYclamp > txtYclamp) {
      contrast = (Math.pow(bgYclamp, APCA_REV_BG_Y) - Math.pow(txtYclamp, APCA_REV_TXT_Y)) * APCA_W3_MAGIC_NUMBER;
    }
  } else {
    // Normal polarity (dark text on light background)
    if (bgYclamp > txtYclamp) {
      contrast = (Math.pow(bgYclamp, APCA_NORM_BG_Y) - Math.pow(txtYclamp, APCA_NORM_TXT_Y)) * APCA_W3_MAGIC_NUMBER;
    }
  }

  // Return absolute value as percentage
  return Math.abs(contrast * 100);
}

// Evaluate APCA contrast result
export function evaluateAPCAContrast(
  textRgb: RGB, 
  bgRgb: RGB, 
  fontSize: number = 16, 
  fontWeight: number = 400
): APCAResult {
  const contrast = calculateAPCAContrast(textRgb, bgRgb);
  
  // APCA thresholds (simplified)
  // These are rough approximations - actual APCA uses complex lookup tables
  let level: APCAResult['level'];
  let passes: boolean;
  let description: string;
  let minFontWeight: number | undefined;
  let minFontSize: number | undefined;

  if (contrast >= 90) {
    level = 'Gold';
    passes = true;
    description = 'Excellent contrast - suitable for all text sizes and weights';
  } else if (contrast >= 75) {
    level = 'Silver';
    passes = true;
    description = 'Good contrast - suitable for most text';
    minFontSize = fontSize < 14 ? 14 : undefined;
  } else if (contrast >= 60) {
    level = 'Bronze';
    passes = fontSize >= 18 || fontWeight >= 600;
    description = 'Adequate contrast - suitable for large or bold text only';
    minFontWeight = fontWeight < 600 ? 600 : undefined;
    minFontSize = fontSize < 18 ? 18 : undefined;
  } else {
    level = 'Fail';
    passes = false;
    description = 'Insufficient contrast - not recommended for text';
    minFontWeight = 700;
    minFontSize = 24;
  }

  return {
    contrast,
    level,
    passes,
    description,
    minFontWeight,
    minFontSize
  };
}