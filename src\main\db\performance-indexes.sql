-- Performance Indexes for ChromaSync Database
-- Optimizes common query patterns for enterprise-level performance

-- =============================================================================
-- Colors Table Indexes
-- =============================================================================

-- Primary lookup indexes
CREATE INDEX IF NOT EXISTS idx_colors_external_id ON colors(external_id);
CREATE INDEX IF NOT EXISTS idx_colors_hex ON colors(hex);
CREATE INDEX IF NOT EXISTS idx_colors_code ON colors(code);
CREATE INDEX IF NOT EXISTS idx_colors_name ON colors(name);

-- Product association indexes
CREATE INDEX IF NOT EXISTS idx_colors_product_name ON colors(product, name);
CREATE INDEX IF NOT EXISTS idx_colors_source_id ON colors(source_id);

-- Soft delete and active state indexes
CREATE INDEX IF NOT EXISTS idx_colors_deleted_at ON colors(deleted_at);
CREATE INDEX IF NOT EXISTS idx_colors_active ON colors(deleted_at) WHERE deleted_at IS NULL;

-- Library vs user colors
CREATE INDEX IF NOT EXISTS idx_colors_library ON colors(is_library);
CREATE INDEX IF NOT EXISTS idx_colors_library_active ON colors(is_library, deleted_at) WHERE deleted_at IS NULL;

-- Search and filtering indexes
CREATE INDEX IF NOT EXISTS idx_colors_name_search ON colors(LOWER(name)) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_colors_code_search ON colors(LOWER(code)) WHERE deleted_at IS NULL;

-- Timestamp indexes for sync and ordering
CREATE INDEX IF NOT EXISTS idx_colors_created_at ON colors(created_at);
CREATE INDEX IF NOT EXISTS idx_colors_updated_at ON colors(updated_at);
CREATE INDEX IF NOT EXISTS idx_colors_sync_timestamp ON colors(updated_at, deleted_at);

-- Composite index for common queries
CREATE INDEX IF NOT EXISTS idx_colors_active_updated ON colors(deleted_at, updated_at) WHERE deleted_at IS NULL;

-- =============================================================================
-- Products Table Indexes
-- =============================================================================

-- Primary lookup indexes
CREATE INDEX IF NOT EXISTS idx_products_external_id ON products(external_id);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);

-- Soft delete indexes
CREATE INDEX IF NOT EXISTS idx_products_deleted_at ON products(deleted_at);
CREATE INDEX IF NOT EXISTS idx_products_active ON products(deleted_at) WHERE deleted_at IS NULL;

-- Search indexes
CREATE INDEX IF NOT EXISTS idx_products_name_search ON products(LOWER(name)) WHERE deleted_at IS NULL;

-- Timestamp indexes
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at);
CREATE INDEX IF NOT EXISTS idx_products_updated_at ON products(updated_at);

-- =============================================================================
-- Product Colors Junction Table Indexes
-- =============================================================================

-- Primary relationship indexes
CREATE INDEX IF NOT EXISTS idx_product_colors_product_id ON product_colors(product_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_color_id ON product_colors(color_id);

-- Composite indexes for efficient joins
CREATE INDEX IF NOT EXISTS idx_product_colors_relationship ON product_colors(product_id, color_id);
CREATE INDEX IF NOT EXISTS idx_product_colors_color_product ON product_colors(color_id, product_id);

-- Timestamp indexes for sync
CREATE INDEX IF NOT EXISTS idx_product_colors_created_at ON product_colors(created_at);

-- =============================================================================
-- Color Sources Table Indexes
-- =============================================================================

-- Primary lookup indexes
CREATE INDEX IF NOT EXISTS idx_color_sources_external_id ON color_sources(external_id);
CREATE INDEX IF NOT EXISTS idx_color_sources_name ON color_sources(name);
CREATE INDEX IF NOT EXISTS idx_color_sources_type ON color_sources(type);

-- Active state indexes
CREATE INDEX IF NOT EXISTS idx_color_sources_deleted_at ON color_sources(deleted_at);
CREATE INDEX IF NOT EXISTS idx_color_sources_active ON color_sources(deleted_at) WHERE deleted_at IS NULL;

-- =============================================================================
-- CMYK Table Indexes (Performance Critical)
-- =============================================================================

-- Color relationship index
CREATE INDEX IF NOT EXISTS idx_color_cmyk_color_id ON color_cmyk(color_id);

-- CMYK value searches
CREATE INDEX IF NOT EXISTS idx_color_cmyk_values ON color_cmyk(c, m, y, k);
CREATE INDEX IF NOT EXISTS idx_color_cmyk_c ON color_cmyk(c);
CREATE INDEX IF NOT EXISTS idx_color_cmyk_m ON color_cmyk(m);
CREATE INDEX IF NOT EXISTS idx_color_cmyk_y ON color_cmyk(y);
CREATE INDEX IF NOT EXISTS idx_color_cmyk_k ON color_cmyk(k);

-- =============================================================================
-- Datasheets Table Indexes
-- =============================================================================

-- Primary lookup indexes
CREATE INDEX IF NOT EXISTS idx_datasheets_external_id ON datasheets(external_id);
CREATE INDEX IF NOT EXISTS idx_datasheets_product_id ON datasheets(product_id);

-- File and URL lookups
CREATE INDEX IF NOT EXISTS idx_datasheets_file_path ON datasheets(file_path);
CREATE INDEX IF NOT EXISTS idx_datasheets_web_url ON datasheets(web_url);

-- Soft delete indexes
CREATE INDEX IF NOT EXISTS idx_datasheets_deleted_at ON datasheets(deleted_at);
CREATE INDEX IF NOT EXISTS idx_datasheets_active ON datasheets(deleted_at) WHERE deleted_at IS NULL;

-- Search indexes
CREATE INDEX IF NOT EXISTS idx_datasheets_name_search ON datasheets(LOWER(name)) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_datasheets_description_search ON datasheets(LOWER(description)) WHERE deleted_at IS NULL;

-- Timestamp indexes
CREATE INDEX IF NOT EXISTS idx_datasheets_created_at ON datasheets(created_at);
CREATE INDEX IF NOT EXISTS idx_datasheets_updated_at ON datasheets(updated_at);

-- =============================================================================
-- Gradient Stops Table Indexes
-- =============================================================================

-- Gradient relationship index
CREATE INDEX IF NOT EXISTS idx_gradient_stops_color_id ON gradient_stops(color_id);

-- Position and ordering
CREATE INDEX IF NOT EXISTS idx_gradient_stops_position ON gradient_stops(position);
CREATE INDEX IF NOT EXISTS idx_gradient_stops_color_position ON gradient_stops(color_id, position);

-- =============================================================================
-- Complex Composite Indexes for Advanced Queries
-- =============================================================================

-- Colors with product association (most common query)
CREATE INDEX IF NOT EXISTS idx_colors_comprehensive ON colors(deleted_at, is_library, updated_at, external_id) WHERE deleted_at IS NULL;

-- Product colors join optimization
CREATE INDEX IF NOT EXISTS idx_product_colors_join ON product_colors(product_id, color_id, created_at);

-- Search optimization across colors and products
CREATE INDEX IF NOT EXISTS idx_colors_search_comprehensive ON colors(LOWER(name), LOWER(code), hex, deleted_at) WHERE deleted_at IS NULL;

-- Sync and replication optimization
CREATE INDEX IF NOT EXISTS idx_colors_sync_comprehensive ON colors(updated_at, external_id, deleted_at);
CREATE INDEX IF NOT EXISTS idx_products_sync_comprehensive ON products(updated_at, external_id, deleted_at);

-- =============================================================================
-- Full-Text Search Indexes (if supported)
-- =============================================================================

-- Note: SQLite FTS requires separate FTS tables
-- These would need to be implemented separately if full-text search is needed

-- =============================================================================
-- Performance Analysis Queries
-- =============================================================================

-- Use these queries to analyze index usage:
-- 
-- Check index usage:
-- PRAGMA index_info('index_name');
-- 
-- Analyze query plans:
-- EXPLAIN QUERY PLAN SELECT * FROM colors WHERE deleted_at IS NULL ORDER BY updated_at DESC;
-- 
-- Get table statistics:
-- PRAGMA table_info('colors');
-- 
-- Analyze database for better query planning:
-- ANALYZE;

-- =============================================================================
-- Index Maintenance Commands
-- =============================================================================

-- Rebuild all indexes (use sparingly in production):
-- REINDEX;

-- Rebuild specific index:
-- REINDEX idx_colors_comprehensive;

-- Update statistics for query planner:
-- ANALYZE colors;
-- ANALYZE products;
-- ANALYZE product_colors;