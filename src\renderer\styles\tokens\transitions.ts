/**
 * @file transitions.ts
 * @description Transition tokens for the design system
 */

import { TransitionTokens } from './types';

export const transitions: TransitionTokens = {
  duration: {
    75: '75ms',
    100: '100ms',
    150: '150ms',
    200: '200ms',
    300: '300ms',
    500: '500ms',
    700: '700ms',
    1000: '1000ms',
  },
  easing: {
    linear: 'linear',
    in: 'cubic-bezier(0.4, 0, 1, 1)',
    out: 'cubic-bezier(0, 0, 0.2, 1)',
    inOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    // Apple's easing curve
    apple: 'cubic-bezier(0.25, 0.1, 0.25, 1.0)',
  },
}; 