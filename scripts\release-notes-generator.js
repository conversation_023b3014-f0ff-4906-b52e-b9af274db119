#!/usr/bin/env node

/**
 * @file release-notes-generator.js
 * @description Automated release notes generator for ChromaSync
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class ReleaseNotesGenerator {
  constructor() {
    this.packageJson = this.loadPackageJson();
    this.currentVersion = this.packageJson.version;
    this.gitCommits = this.getGitCommits();
    this.releaseNotesDir = path.join(__dirname, '../docs/release-notes');
  }

  loadPackageJson() {
    const packagePath = path.join(__dirname, '../package.json');
    return JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  }

  getGitCommits(since = null) {
    try {
      const sinceFlag = since ? `--since="${since}"` : '--all';
      const command = `git log ${sinceFlag} --pretty=format:"%h|%s|%an|%ad" --date=short`;
      const output = execSync(command, { encoding: 'utf8' });
      
      return output.split('\n').filter(line => line.trim()).map(line => {
        const [hash, subject, author, date] = line.split('|');
        return { hash, subject, author, date };
      });
    } catch (error) {
      console.warn('Could not retrieve git commits:', error.message);
      return [];
    }
  }

  categorizeCommits(commits) {
    const categories = {
      features: [],
      improvements: [],
      fixes: [],
      breaking: [],
      docs: [],
      tests: [],
      chore: []
    };

    commits.forEach(commit => {
      const subject = commit.subject.toLowerCase();
      
      if (subject.startsWith('feat:') || subject.includes('add ') || subject.includes('implement ')) {
        categories.features.push(commit);
      } else if (subject.startsWith('fix:') || subject.includes('fix ') || subject.includes('resolve ')) {
        categories.fixes.push(commit);
      } else if (subject.startsWith('perf:') || subject.includes('improve ') || subject.includes('optimize ')) {
        categories.improvements.push(commit);
      } else if (subject.includes('breaking') || subject.startsWith('break:')) {
        categories.breaking.push(commit);
      } else if (subject.startsWith('docs:') || subject.includes('documentation')) {
        categories.docs.push(commit);
      } else if (subject.startsWith('test:') || subject.includes('test ')) {
        categories.tests.push(commit);
      } else {
        categories.chore.push(commit);
      }
    });

    return categories;
  }

  generateReleaseNotes(version, commits, previousVersion = null) {
    const categories = this.categorizeCommits(commits);
    const releaseDate = new Date().toISOString().split('T')[0];
    const versionType = this.getVersionType(version, previousVersion);

    let notes = `# ChromaSync v${version} Release Notes

**Release Date:** ${releaseDate}  
**Type:** ${versionType}  

`;

    // New Features
    if (categories.features.length > 0) {
      notes += `## 🎉 New Features

`;
      categories.features.forEach(commit => {
        notes += `- **${this.formatCommitSubject(commit.subject)}** (${commit.hash})
`;
      });
      notes += '\n';
    }

    // Improvements
    if (categories.improvements.length > 0) {
      notes += `## 🛠️ Improvements

`;
      categories.improvements.forEach(commit => {
        notes += `- ${this.formatCommitSubject(commit.subject)} (${commit.hash})
`;
      });
      notes += '\n';
    }

    // Bug Fixes
    if (categories.fixes.length > 0) {
      notes += `## 🐛 Bug Fixes

`;
      categories.fixes.forEach(commit => {
        notes += `- ${this.formatCommitSubject(commit.subject)} (${commit.hash})
`;
      });
      notes += '\n';
    }

    // Breaking Changes
    if (categories.breaking.length > 0) {
      notes += `## ⚠️ Breaking Changes

`;
      categories.breaking.forEach(commit => {
        notes += `- **${this.formatCommitSubject(commit.subject)}** (${commit.hash})
`;
      });
      notes += '\n';
    }

    // Documentation
    if (categories.docs.length > 0) {
      notes += `## 📚 Documentation

`;
      categories.docs.forEach(commit => {
        notes += `- ${this.formatCommitSubject(commit.subject)} (${commit.hash})
`;
      });
      notes += '\n';
    }

    // Statistics
    notes += `## 📊 Release Statistics

- **Total Commits:** ${commits.length}
- **Contributors:** ${this.getUniqueContributors(commits).length}
- **Files Changed:** ${this.getFilesChanged()}
- **Release Type:** ${versionType}

`;

    // What's Next
    notes += `## 🔮 What's Next

See our [roadmap](../roadmap.md) for upcoming features and improvements.

## 🙏 Contributors

Special thanks to all contributors for this release:

`;
    
    this.getUniqueContributors(commits).forEach(contributor => {
      notes += `- ${contributor}
`;
    });

    notes += `
---

**Download:**
- [Release Assets](https://github.com/your-org/chromasync/releases/tag/v${version})

For installation instructions, see our [setup guide](../development.md).
`;

    return notes;
  }

  formatCommitSubject(subject) {
    // Remove conventional commit prefixes and capitalize
    let formatted = subject
      .replace(/^(feat|fix|docs|style|refactor|test|chore|perf):\s*/, '')
      .replace(/^(add|implement|fix|resolve|update|improve)\s+/i, '');
    
    return formatted.charAt(0).toUpperCase() + formatted.slice(1);
  }

  getVersionType(version, previousVersion) {
    if (!previousVersion) return 'MAJOR';
    
    const [currentMajor, currentMinor, currentPatch] = version.split('.').map(Number);
    const [prevMajor, prevMinor, prevPatch] = previousVersion.split('.').map(Number);
    
    if (currentMajor > prevMajor) return 'MAJOR';
    if (currentMinor > prevMinor) return 'MINOR';
    if (currentPatch > prevPatch) return 'PATCH';
    
    return 'PATCH';
  }

  getUniqueContributors(commits) {
    const contributors = new Set();
    commits.forEach(commit => contributors.add(commit.author));
    return Array.from(contributors).sort();
  }

  getFilesChanged() {
    try {
      const output = execSync('git diff --name-only HEAD~10..HEAD', { encoding: 'utf8' });
      return output.split('\n').filter(line => line.trim()).length;
    } catch (error) {
      return 'N/A';
    }
  }

  getPreviousVersion() {
    try {
      const tags = execSync('git tag --sort=-version:refname', { encoding: 'utf8' });
      const tagList = tags.split('\n').filter(tag => tag.trim().match(/^v?\d+\.\d+\.\d+$/));
      return tagList[1]?.replace(/^v/, '') || null;
    } catch (error) {
      return null;
    }
  }

  saveReleaseNotes(version, content) {
    if (!fs.existsSync(this.releaseNotesDir)) {
      fs.mkdirSync(this.releaseNotesDir, { recursive: true });
    }

    const filePath = path.join(this.releaseNotesDir, `v${version}.md`);
    fs.writeFileSync(filePath, content);
    
    console.log(`✅ Release notes saved to: ${filePath}`);
    return filePath;
  }

  generateAndSave(version = null, since = null) {
    version = version || this.currentVersion;
    const previousVersion = this.getPreviousVersion();
    
    console.log(`📝 Generating release notes for v${version}...`);
    console.log(`📅 Previous version: ${previousVersion || 'None'}`);
    
    const commits = since ? this.getGitCommits(since) : this.getGitCommits();
    console.log(`📊 Found ${commits.length} commits to analyze`);
    
    const releaseNotes = this.generateReleaseNotes(version, commits, previousVersion);
    const filePath = this.saveReleaseNotes(version, releaseNotes);
    
    return filePath;
  }

  updateChangelog(version = null) {
    version = version || this.currentVersion;
    const changelogPath = path.join(__dirname, '../CHANGELOG.md');
    const releaseNotesPath = path.join(this.releaseNotesDir, `v${version}.md`);
    
    if (!fs.existsSync(releaseNotesPath)) {
      console.error(`❌ Release notes not found for v${version}`);
      return;
    }
    
    const releaseNotes = fs.readFileSync(releaseNotesPath, 'utf8');
    let changelog = '';
    
    if (fs.existsSync(changelogPath)) {
      changelog = fs.readFileSync(changelogPath, 'utf8');
    } else {
      changelog = `# Changelog

All notable changes to ChromaSync will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

`;
    }
    
    // Insert the new release notes at the top (after the header)
    const lines = changelog.split('\n');
    const insertIndex = lines.findIndex(line => line.startsWith('## ')) || lines.length;
    
    lines.splice(insertIndex, 0, '', releaseNotes.replace(/^# /, '## '), '');
    
    fs.writeFileSync(changelogPath, lines.join('\n'));
    console.log(`✅ Updated CHANGELOG.md with v${version}`);
  }
}

// CLI Interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const generator = new ReleaseNotesGenerator();
  
  const version = args.find(arg => arg.match(/^\d+\.\d+\.\d+$/));
  const since = args.find(arg => arg.startsWith('--since='))?.replace('--since=', '');
  const updateChangelog = args.includes('--changelog');
  
  try {
    const filePath = generator.generateAndSave(version, since);
    
    if (updateChangelog) {
      generator.updateChangelog(version);
    }
    
    console.log(`\n🎉 Release notes generation complete!`);
    console.log(`📄 File: ${filePath}`);
    console.log(`\nNext steps:`);
    console.log(`1. Review the generated release notes`);
    console.log(`2. Edit manually if needed`);
    console.log(`3. Create a git tag: git tag v${version || generator.currentVersion}`);
    console.log(`4. Push with tags: git push origin --tags`);
    
  } catch (error) {
    console.error(`❌ Error generating release notes:`, error.message);
    process.exit(1);
  }
}

module.exports = ReleaseNotesGenerator;