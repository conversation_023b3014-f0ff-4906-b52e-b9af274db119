/**
 * @file lazyComponents.tsx
 * @description Lazy-loaded components for improved startup performance
 */

import React, { Suspense, lazy } from 'react';
import { Skeleton } from '../components/ui/Skeleton';

// Lazy load heavy components
export const LazyColorTable = lazy(() => import('../components/ColorTable'));
export const LazyColorSwatches = lazy(() => import('../components/ColorSwatches'));
export const LazyProductsPanel = lazy(() => import('../components/Products/ProductsPanel'));
export const LazySettingsModal = lazy(() => import('../components/Settings/SettingsModal'));
export const LazyColorComparison = lazy(() => import('../components/ColorComparison/ColorComparisonModal'));
export const LazyDebugMonitoringPanel = lazy(() => import('../components/DebugMonitoringPanel'));

// Loading fallback components
const TableSkeleton = () => (
  <div className="space-y-4">
    <Skeleton className="h-12 w-full" />
    <Skeleton className="h-8 w-full" />
    <Skeleton className="h-8 w-full" />
    <Skeleton className="h-8 w-full" />
    <Skeleton className="h-8 w-full" />
  </div>
);

const SwatchesSkeleton = () => (
  <div className="grid grid-cols-6 gap-4">
    {Array.from({ length: 24 }).map((_, i) => (
      <Skeleton key={i} className="h-20 w-full rounded-lg" />
    ))}
  </div>
);

const ProductsSkeleton = () => (
  <div className="space-y-4">
    <Skeleton className="h-10 w-full" />
    <div className="space-y-2">
      <Skeleton className="h-16 w-full" />
      <Skeleton className="h-16 w-full" />
      <Skeleton className="h-16 w-full" />
    </div>
  </div>
);

const ModalSkeleton = () => (
  <div className="space-y-4 p-6">
    <Skeleton className="h-6 w-32" />
    <Skeleton className="h-10 w-full" />
    <Skeleton className="h-10 w-full" />
    <Skeleton className="h-10 w-full" />
  </div>
);

// Wrapper components with suspense
export const ColorTableWithSuspense: React.FC<any> = (props) => (
  <Suspense fallback={<TableSkeleton />}>
    <LazyColorTable {...props} />
  </Suspense>
);

export const ColorSwatchesWithSuspense: React.FC<any> = (props) => (
  <Suspense fallback={<SwatchesSkeleton />}>
    <LazyColorSwatches {...props} />
  </Suspense>
);

export const ProductsPanelWithSuspense: React.FC<any> = (props) => (
  <Suspense fallback={<ProductsSkeleton />}>
    <LazyProductsPanel {...props} />
  </Suspense>
);

export const SettingsModalWithSuspense: React.FC<any> = (props) => (
  <Suspense fallback={<ModalSkeleton />}>
    <LazySettingsModal {...props} />
  </Suspense>
);

export const ColorComparisonWithSuspense: React.FC<any> = (props) => (
  <Suspense fallback={<ModalSkeleton />}>
    <LazyColorComparison {...props} />
  </Suspense>
);

export const DebugMonitoringPanelWithSuspense: React.FC<any> = (props) => (
  <Suspense fallback={<div>Loading debug tools...</div>}>
    <LazyDebugMonitoringPanel {...props} />
  </Suspense>
);

// Preloader for critical components
export const preloadCriticalComponents = async () => {
  // Preload the most commonly used components
  const promises = [
    import('../components/ColorTable'),
    import('../components/ColorSwatches'),
    // Don't preload products panel as it's less commonly used initially
  ];
  
  await Promise.allSettled(promises);
  console.log('[Startup] Critical components preloaded');
};

// Progressive loading utility
export const useProgressiveLoading = () => {
  const [loadedComponents, setLoadedComponents] = React.useState<Set<string>>(new Set());
  
  const markComponentLoaded = React.useCallback((componentName: string) => {
    setLoadedComponents(prev => new Set(prev).add(componentName));
  }, []);
  
  const isComponentLoaded = React.useCallback((componentName: string) => {
    return loadedComponents.has(componentName);
  }, [loadedComponents]);
  
  return { markComponentLoaded, isComponentLoaded };
};