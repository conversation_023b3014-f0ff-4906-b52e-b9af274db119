#!/bin/bash

# Reset local ChromaSync database for testing fresh installs

echo "Resetting local ChromaSync database..."

# Get the database path
DB_PATH="$HOME/Library/Application Support/chroma-sync/chromasync.db"

if [ -f "$DB_PATH" ]; then
    echo "Removing existing database at: $DB_PATH"
    rm "$DB_PATH"
    echo "Database removed successfully"
else
    echo "No existing database found at: $DB_PATH"
fi

# Also remove any backup files
if [ -f "$DB_PATH-shm" ]; then
    rm "$DB_PATH-shm"
fi

if [ -f "$DB_PATH-wal" ]; then
    rm "$DB_PATH-wal"
fi

echo "Local database reset complete. The app will create a fresh database on next launch."