/**
 * @file product-sync.strategy.ts
 * @description Product-specific synchronization strategy
 * 
 * This strategy handles synchronization of product data between local SQLite
 * and Supabase, including product-color relationships.
 */

import { getSupabaseClient } from '../../supabase-client';
import { getDatabase } from '../../../db/database';
import { 
  ISyncStrategy, 
  SyncOperation, 
  SyncQueueItem, 
  SyncResult, 
  SyncError 
} from '../core/sync-types';

// ============================================================================
// PRODUCT SYNC STRATEGY
// ============================================================================

/**
 * Strategy for synchronizing product data
 */
export class ProductSyncStrategy implements ISyncStrategy {
  readonly name = 'product-sync';
  readonly priority = 3; // Higher priority than colors

  private userId: string | null = null;
  private organizationId: string | null = null;

  constructor(userId: string, organizationId: string) {
    this.userId = userId;
    this.organizationId = organizationId;
  }

  /**
   * Check if this strategy can handle the given table and operation
   */
  canHandle(table: string, operation: SyncOperation): boolean {
    return table === 'products' || table === 'product_colors';
  }

  /**
   * Execute product synchronization
   */
  async execute(items: SyncQueueItem[]): Promise<SyncResult> {
    const startTime = Date.now();
    const errors: SyncError[] = [];
    let itemsSucceeded = 0;
    let itemsFailed = 0;

    try {
      console.log(`[ProductSync] Processing ${items.length} product items`);

      // Separate products and relationships
      const productItems = items.filter(item => item.table === 'products');
      const relationshipItems = items.filter(item => item.table === 'product_colors');

      // Process products first (relationships depend on products)
      if (productItems.length > 0) {
        const productResult = await this.processProducts(productItems);
        itemsSucceeded += productResult.succeeded;
        itemsFailed += productResult.failed;
        errors.push(...productResult.errors);
      }

      // Process relationships after products
      if (relationshipItems.length > 0) {
        const relationshipResult = await this.processRelationships(relationshipItems);
        itemsSucceeded += relationshipResult.succeeded;
        itemsFailed += relationshipResult.failed;
        errors.push(...relationshipResult.errors);
      }

      return {
        success: itemsFailed === 0,
        itemsProcessed: items.length,
        itemsSucceeded,
        itemsFailed,
        errors,
        duration: Date.now() - startTime
      };

    } catch (error) {
      console.error('[ProductSync] Strategy execution failed:', error);
      
      return {
        success: false,
        itemsProcessed: items.length,
        itemsSucceeded: 0,
        itemsFailed: items.length,
        errors: [{
          id: `product-sync-error-${Date.now()}`,
          timestamp: Date.now(),
          operation: 'product sync',
          category: 'database',
          severity: 'high',
          message: error.message,
          originalError: error,
          recoverable: true
        }],
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * Validate product data structure
   */
  validateData(data: any): boolean {
    if (!data) return false;
    
    // Required fields for product data
    const requiredFields = ['external_id', 'name'];
    
    return requiredFields.every(field => 
      data.hasOwnProperty(field) && data[field] !== null && data[field] !== undefined
    );
  }

  /**
   * Transform product data for Supabase sync
   */
  transformData(localProduct: any): any {
    // Handle metadata transformation
    let metadata = {};
    if (localProduct.metadata) {
      try {
        metadata = typeof localProduct.metadata === 'string' 
          ? JSON.parse(localProduct.metadata) 
          : localProduct.metadata;
      } catch (error) {
        console.warn('[ProductSync] Failed to parse product metadata:', error);
      }
    }

    return {
      external_id: localProduct.external_id,
      name: localProduct.name,
      description: localProduct.description,
      sku: metadata.sku || null, // Extract SKU from metadata
      metadata: metadata,
      user_id: this.userId,
      organization_id: this.organizationId,
      created_by: localProduct.created_by || this.userId,
      is_active: localProduct.is_active !== 0,
      created_at: localProduct.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Process product operations
   */
  private async processProducts(items: SyncQueueItem[]): Promise<{
    succeeded: number;
    failed: number;
    errors: SyncError[];
  }> {
    const errors: SyncError[] = [];
    let succeeded = 0;
    let failed = 0;

    // Group by action
    const upsertItems = items.filter(item => item.action === 'upsert');
    const deleteItems = items.filter(item => item.action === 'delete');

    // Process upserts
    if (upsertItems.length > 0) {
      const upsertResult = await this.processProductUpserts(upsertItems);
      succeeded += upsertResult.succeeded;
      failed += upsertResult.failed;
      errors.push(...upsertResult.errors);
    }

    // Process deletes
    if (deleteItems.length > 0) {
      const deleteResult = await this.processProductDeletes(deleteItems);
      succeeded += deleteResult.succeeded;
      failed += deleteResult.failed;
      errors.push(...deleteResult.errors);
    }

    return { succeeded, failed, errors };
  }

  /**
   * Process product upsert operations
   */
  private async processProductUpserts(items: SyncQueueItem[]): Promise<{
    succeeded: number;
    failed: number;
    errors: SyncError[];
  }> {
    const errors: SyncError[] = [];
    let succeeded = 0;
    let failed = 0;

    try {
      const supabase = getSupabaseClient();
      const db = await getDatabase();

      // Get local product data
      const productData: any[] = [];
      
      for (const item of items) {
        try {
          let localProduct;
          
          if (item.data) {
            // Data provided in queue item
            localProduct = item.data;
          } else {
            // Fetch from local database
            const stmt = db.prepare(`
              SELECT * FROM products 
              WHERE external_id = ? AND organization_id = (
                SELECT id FROM organizations WHERE external_id = ?
              )
            `);
            localProduct = stmt.get(item.id, this.organizationId);
          }

          if (!localProduct) {
            console.warn(`[ProductSync] Product not found: ${item.id}`);
            failed++;
            continue;
          }

          if (!this.validateData(localProduct)) {
            console.warn(`[ProductSync] Invalid product data: ${item.id}`);
            failed++;
            continue;
          }

          const transformedProduct = this.transformData(localProduct);
          productData.push(transformedProduct);
          
        } catch (error) {
          console.error(`[ProductSync] Error processing product ${item.id}:`, error);
          failed++;
          
          errors.push({
            id: `product-process-error-${Date.now()}-${item.id}`,
            timestamp: Date.now(),
            operation: `process product ${item.id}`,
            category: 'validation',
            severity: 'medium',
            message: `Failed to process product: ${error.message}`,
            originalError: error,
            recoverable: true
          });
        }
      }

      if (productData.length === 0) {
        return { succeeded, failed, errors };
      }

      // Batch upsert to Supabase
      console.log(`[ProductSync] Upserting ${productData.length} products to Supabase`);
      
      const { data, error } = await supabase
        .from('products')
        .upsert(productData, {
          onConflict: 'external_id'
        })
        .select();

      if (error) {
        console.error('[ProductSync] Supabase upsert error:', error);
        failed += productData.length;
        
        errors.push({
          id: `product-upsert-error-${Date.now()}`,
          timestamp: Date.now(),
          operation: 'product batch upsert',
          category: 'database',
          severity: 'high',
          message: `Supabase upsert failed: ${error.message}`,
          originalError: error,
          recoverable: true
        });
      } else {
        succeeded += productData.length;
        console.log(`[ProductSync] Successfully upserted ${productData.length} products`);
        console.log(`[ProductSync] Supabase returned ${data?.length || 0} records`);
      }

    } catch (error) {
      console.error('[ProductSync] Upsert processing failed:', error);
      failed += items.length;
      
      errors.push({
        id: `product-upsert-batch-error-${Date.now()}`,
        timestamp: Date.now(),
        operation: 'product upsert batch',
        category: 'database',
        severity: 'high',
        message: `Batch upsert failed: ${error.message}`,
        originalError: error,
        recoverable: true
      });
    }

    return { succeeded, failed, errors };
  }

  /**
   * Process product delete operations
   */
  private async processProductDeletes(items: SyncQueueItem[]): Promise<{
    succeeded: number;
    failed: number;
    errors: SyncError[];
  }> {
    const errors: SyncError[] = [];
    let succeeded = 0;
    let failed = 0;

    try {
      const supabase = getSupabaseClient();
      
      // Extract external IDs for deletion
      const externalIds = items.map(item => item.id);
      
      console.log(`[ProductSync] Deleting ${externalIds.length} products from Supabase`);
      
      const { error } = await supabase
        .from('products')
        .delete()
        .in('external_id', externalIds)
        .eq('user_id', this.userId)
        .eq('organization_id', this.organizationId);

      if (error) {
        console.error('[ProductSync] Supabase delete error:', error);
        failed += items.length;
        
        errors.push({
          id: `product-delete-error-${Date.now()}`,
          timestamp: Date.now(),
          operation: 'product batch delete',
          category: 'database',
          severity: 'high',
          message: `Supabase delete failed: ${error.message}`,
          originalError: error,
          recoverable: true
        });
      } else {
        succeeded += items.length;
        console.log(`[ProductSync] Successfully deleted ${items.length} products`);
      }

    } catch (error) {
      console.error('[ProductSync] Delete processing failed:', error);
      failed += items.length;
      
      errors.push({
        id: `product-delete-batch-error-${Date.now()}`,
        timestamp: Date.now(),
        operation: 'product delete batch',
        category: 'database',
        severity: 'high',
        message: `Batch delete failed: ${error.message}`,
        originalError: error,
        recoverable: true
      });
    }

    return { succeeded, failed, errors };
  }

  /**
   * Process product-color relationship operations
   */
  private async processRelationships(items: SyncQueueItem[]): Promise<{
    succeeded: number;
    failed: number;
    errors: SyncError[];
  }> {
    // For now, return success - relationships will be handled in a separate strategy
    // This is a placeholder for future relationship sync implementation
    console.log(`[ProductSync] Relationship sync not yet implemented for ${items.length} items`);
    
    return {
      succeeded: items.length,
      failed: 0,
      errors: []
    };
  }
}
