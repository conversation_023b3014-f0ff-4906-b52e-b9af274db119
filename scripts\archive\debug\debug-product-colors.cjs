/**
 * Debug script to test product-color relationships
 * Run with: node debug-product-colors.cjs
 */

const Database = require('better-sqlite3');
const path = require('path');

// Function to get database path (simplified version)
function getDatabasePath() {
  // Use the actual user data path where Electron stores the database
  const userDataPath = path.join(require('os').homedir(), 'Library/Application Support/chroma-sync');
  const dbPath = path.join(userDataPath, 'chromasync.db');
  return dbPath;
}

async function debugProductColors() {
  console.log('[Debug] Starting product-color relationship debug...');
  
  const dbPath = getDatabasePath();
  console.log('[Debug] Database path:', dbPath);
  
  let db;
  try {
    db = new Database(dbPath);
    console.log('[Debug] Database connected successfully');
  } catch (error) {
    console.error('[Debug] Failed to connect to database:', error);
    return;
  }

  try {
    // Check if tables exist
    console.log('\n=== Checking table existence ===');
    const tables = db.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name IN ('products', 'colors', 'product_colors', 'organizations', 'schema_migrations')
    `).all();
    console.log('[Debug] Existing tables:', tables.map(t => t.name));

    // Check which migrations have been applied
    console.log('\n=== Checking applied migrations ===');
    const migrationTableExists = tables.some(t => t.name === 'schema_migrations');
    if (migrationTableExists) {
      const appliedMigrations = db.prepare('SELECT version, name, applied_at FROM schema_migrations ORDER BY version').all();
      console.log('[Debug] Applied migrations:', appliedMigrations);
    } else {
      console.log('[Debug] schema_migrations table does not exist - no migration tracking');
    }

    if (tables.length === 0) {
      console.log('[Debug] No tables found. Database might be empty.');
      return;
    }

    // Check product_colors table schema
    console.log('\n=== Checking product_colors table schema ===');
    const productColorsSchema = db.prepare(`
      SELECT sql FROM sqlite_master WHERE type='table' AND name='product_colors'
    `).get();
    
    if (productColorsSchema) {
      console.log('[Debug] product_colors table schema:');
      console.log(productColorsSchema.sql);
      
      // Check columns specifically
      const columns = db.prepare(`
        PRAGMA table_info(product_colors)
      `).all();
      console.log('[Debug] product_colors columns:', columns.map(c => `${c.name} (${c.type})`));
      
      const hasOrgId = columns.some(c => c.name === 'organization_id');
      console.log('[Debug] Has organization_id column:', hasOrgId);
    } else {
      console.log('[Debug] product_colors table does not exist');
    }

    // Check organizations table
    console.log('\n=== Checking organizations ===');
    const orgCount = db.prepare('SELECT COUNT(*) as count FROM organizations').get();
    console.log('[Debug] Organizations count:', orgCount.count);
    
    if (orgCount.count > 0) {
      const orgs = db.prepare('SELECT external_id, name FROM organizations LIMIT 5').all();
      console.log('[Debug] Sample organizations:', orgs);
    }

    // Check products
    console.log('\n=== Checking products ===');
    const productCount = db.prepare('SELECT COUNT(*) as count FROM products WHERE is_active = 1').get();
    console.log('[Debug] Active products count:', productCount.count);
    
    if (productCount.count > 0) {
      const products = db.prepare('SELECT external_id, name, organization_id FROM products WHERE is_active = 1 LIMIT 5').all();
      console.log('[Debug] Sample products:', products);
    }

    // Check colors
    console.log('\n=== Checking colors ===');
    const colorCount = db.prepare('SELECT COUNT(*) as count FROM colors WHERE deleted_at IS NULL').get();
    console.log('[Debug] Active colors count:', colorCount.count);
    
    if (colorCount.count > 0) {
      const colors = db.prepare('SELECT external_id, code, organization_id FROM colors WHERE deleted_at IS NULL LIMIT 5').all();
      console.log('[Debug] Sample colors:', colors);
    }

    // Check product_colors relationships
    console.log('\n=== Checking product_colors relationships ===');
    const relationshipCount = db.prepare('SELECT COUNT(*) as count FROM product_colors').get();
    console.log('[Debug] Product-color relationships count:', relationshipCount.count);
    
    if (relationshipCount.count > 0) {
      const relationships = db.prepare(`
        SELECT 
          pc.product_id, 
          pc.color_id, 
          p.external_id as product_external_id,
          c.external_id as color_external_id
        FROM product_colors pc
        LEFT JOIN products p ON pc.product_id = p.id
        LEFT JOIN colors c ON pc.color_id = c.id
        LIMIT 5
      `).all();
      console.log('[Debug] Sample relationships:', relationships);
    }

    // Test the specific query that's failing
    console.log('\n=== Testing product-color query ===');
    if (productCount.count > 0 && orgCount.count > 0) {
      const firstOrg = db.prepare('SELECT id, external_id FROM organizations LIMIT 1').get();
      const firstProduct = db.prepare('SELECT id, external_id FROM products WHERE organization_id = ? LIMIT 1').get(firstOrg.id);
      
      if (firstProduct) {
        console.log('[Debug] Testing with org:', firstOrg.external_id, 'product:', firstProduct.external_id);
        
        const colorIds = db.prepare(`
          SELECT c.external_id
          FROM product_colors pc
          JOIN colors c ON pc.color_id = c.id
          WHERE pc.product_id = ? AND c.deleted_at IS NULL
          ORDER BY pc.display_order
        `).all(firstProduct.id);
        
        console.log('[Debug] Colors found for product:', colorIds.length);
        console.log('[Debug] Color external_ids:', colorIds.map(c => c.external_id));
      }
    }

  } catch (error) {
    console.error('[Debug] Error during debug:', error);
  } finally {
    if (db) {
      db.close();
      console.log('[Debug] Database connection closed');
    }
  }
}

// Run the debug
debugProductColors().catch(console.error);