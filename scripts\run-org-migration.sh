#!/bin/bash

# Run organization migration for ChromaSync

# Set database path
DB_PATH="$HOME/Library/Application Support/chroma-sync/chromasync.db"

# Check if database exists
if [ ! -f "$DB_PATH" ]; then
    echo "Database not found at: $DB_PATH"
    exit 1
fi

# Run migration
echo "Running organization migration..."
sqlite3 "$DB_PATH" < scripts/migrations/add-organizations.sql

if [ $? -eq 0 ]; then
    echo "Migration completed successfully!"
    echo "Organization tables added to database."
else
    echo "Migration failed!"
    exit 1
fi
