# ChromaSync Multi-Tenant Schema Design Documentation

## Table of Contents
1. [Overview](#overview)
2. [Design Principles](#design-principles)
3. [Architecture Decisions](#architecture-decisions)
4. [Schema Design](#schema-design)
5. [Security Model](#security-model)
6. [Performance Optimizations](#performance-optimizations)
7. [Migration Strategy](#migration-strategy)
8. [Trade-offs and Considerations](#trade-offs-and-considerations)
9. [Implementation Details](#implementation-details)
10. [Best Practices](#best-practices)

## Overview

ChromaSync implements a multi-tenant architecture that enables organizations to collaborate on color and product management while maintaining complete data isolation. This document details the design decisions, implementation choices, and rationale behind our multi-tenant schema.

### Key Requirements
- **Data Isolation**: Complete separation of data between organizations
- **Performance**: Fast queries even with millions of records across thousands of organizations
- **Flexibility**: Support for both single-user personal use and large enterprise teams
- **Security**: Row-level security ensuring users only access their organization's data
- **Scalability**: Ability to grow from individual users to enterprise deployments

## Design Principles

### 1. Organization-First Architecture
Every piece of data is scoped to an organization, not individual users. This enables:
- Natural team collaboration
- Simplified permission management
- Clear data ownership boundaries
- Easier compliance with data regulations

### 2. Hybrid Storage Model
- **Local SQLite**: Primary data store for offline-first functionality
- **Supabase PostgreSQL**: Cloud synchronization and team collaboration
- **Bidirectional Sync**: Seamless data flow between local and cloud

### 3. Backward Compatibility
Support for legacy user-based data while encouraging migration to organization model:
```sql
-- Legacy support in RLS policies
OR (organization_id IS NULL AND user_id = auth.uid())
```

## Architecture Decisions

### Why Organizations Over User-Based Model?

**Decision**: Use organizations as the primary tenant boundary instead of individual users.

**Rationale**:
1. **Natural Collaboration**: Teams work together on color palettes and products
2. **Enterprise Requirements**: B2B customers expect organization-level management
3. **Permission Simplicity**: Role-based access control (RBAC) maps naturally to organizations
4. **Billing Model**: Organization-based plans align with business model

### Database Choice

**Local**: SQLite
- Embedded, zero-configuration database
- Perfect for offline-first desktop applications
- Excellent performance for local queries
- Limitations handled through application logic

**Cloud**: PostgreSQL (Supabase)
- Row-Level Security (RLS) for data isolation
- JSONB support for flexible metadata
- Excellent multi-tenant performance
- Built-in authentication with Supabase Auth

### Schema Design Pattern

**Decision**: Shared schema with discriminator column (`organization_id`)

**Alternatives Considered**:
1. **Separate Databases**: Too complex for management
2. **Schema per Tenant**: PostgreSQL schema overhead
3. **Shared Tables**: ✓ Chosen approach

**Benefits**:
- Simple backup and maintenance
- Easy cross-organization queries (for admins)
- Efficient resource utilization
- Standard approach for SaaS applications

## Schema Design

### Core Tables

#### Organizations Table
```sql
CREATE TABLE organizations (
    id INTEGER PRIMARY KEY,                    -- Local SQLite
    external_id TEXT UNIQUE NOT NULL,         -- UUID for sync
    name TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,                -- URL-friendly identifier
    plan TEXT DEFAULT 'free',                 -- Subscription tier
    settings JSON DEFAULT '{}',               -- Flexible configuration
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Design Decisions**:
- `slug`: Human-readable URL component for better UX
- `plan`: Embedded tier information for quick limit checks
- `settings`: JSON for flexibility without schema changes

#### Organization Members Table
```sql
CREATE TABLE organization_members (
    organization_id INTEGER NOT NULL,
    user_id TEXT NOT NULL,                    -- UUID as TEXT in SQLite
    role TEXT NOT NULL DEFAULT 'member',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    invited_by TEXT,
    PRIMARY KEY (organization_id, user_id)
);
```

**Design Decisions**:
- Composite primary key prevents duplicate memberships
- Three-tier role system: owner, admin, member
- Audit trail with `joined_at` and `invited_by`

#### Multi-Tenant Data Tables
All data tables include `organization_id`:
```sql
ALTER TABLE colors ADD COLUMN organization_id INTEGER;
ALTER TABLE products ADD COLUMN organization_id INTEGER;
ALTER TABLE product_colors ADD COLUMN organization_id INTEGER;
```

### Indexing Strategy

#### Organization-Scoped Indexes
```sql
-- Composite indexes for common query patterns
CREATE INDEX idx_colors_org_hex ON colors(organization_id, hex);
CREATE INDEX idx_products_org_name ON products(organization_id, name);
CREATE INDEX idx_colors_org_updated ON colors(organization_id, updated_at DESC);
```

**Rationale**:
- Lead with `organization_id` for partition pruning
- Include commonly queried columns for covering indexes
- Separate indexes for different access patterns

#### Performance-Critical Indexes
```sql
-- Member lookup - frequent operation
CREATE INDEX idx_org_members_user ON organization_members(user_id);

-- Slug lookup for URL routing
CREATE INDEX idx_org_slug ON organizations(slug);
```

## Security Model

### Row-Level Security (PostgreSQL/Supabase)

#### Policy Design Pattern
```sql
CREATE POLICY "Organization members can view colors" ON colors
    FOR SELECT TO authenticated
    USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid()
        )
    );
```

**Key Principles**:
1. **Membership Check**: Every policy verifies organization membership
2. **Role-Based Access**: Different operations require different roles
3. **No Direct Access**: All data access goes through organization context

#### Security Layers
1. **Authentication**: Supabase Auth / OAuth
2. **Authorization**: RLS policies based on organization membership
3. **Application Logic**: Additional permission checks in code
4. **API Layer**: Request validation and sanitization

### Local Security (SQLite)

Since SQLite lacks built-in RLS, security is implemented in the application layer:

```typescript
// Every query includes organization context
const colors = await db.colors.findMany({
  where: { organization_id: currentOrgId }
});
```

## Performance Optimizations

### 1. Intelligent Indexing
```sql
-- Covering index for color lookups
CREATE INDEX idx_colors_org_hex_name 
ON colors(organization_id, hex, name);

-- Partial index for active records
CREATE INDEX idx_products_org_active 
ON products(organization_id, created_at DESC) 
WHERE deleted_at IS NULL;
```

### 2. Query Optimization Patterns

#### Efficient Membership Checks
```sql
-- Materialized membership view (PostgreSQL)
CREATE MATERIALIZED VIEW user_organizations AS
SELECT user_id, array_agg(organization_id) as org_ids
FROM organization_members
GROUP BY user_id;

-- Refresh periodically
REFRESH MATERIALIZED VIEW CONCURRENTLY user_organizations;
```

#### Batch Operations
```sql
-- Batch upsert function for colors
CREATE FUNCTION batch_upsert_colors(
    p_organization_id UUID,
    p_colors JSONB
) RETURNS VOID
```

### 3. Caching Strategy
- Organization membership cached in application memory
- Current organization stored in electron-store
- Frequently accessed organizations in LRU cache

## Migration Strategy

### From User-Based to Organization-Based

#### Automatic Migration
```sql
-- Function to migrate user data to organization
CREATE FUNCTION migrate_user_to_organization(
    p_user_id UUID,
    p_organization_name TEXT DEFAULT NULL
) RETURNS UUID AS $$
-- Creates organization and migrates all user data
```

#### Migration Steps:
1. Create default organization for user
2. Generate unique slug from email/name
3. Assign user as organization owner
4. Update all records with organization_id
5. Maintain backward compatibility

### Data Migration Patterns
```sql
-- Preserve existing data while adding organization support
UPDATE products 
SET organization_id = NEW_ORG_ID 
WHERE user_id = USER_ID 
  AND organization_id IS NULL;
```

## Trade-offs and Considerations

### Advantages of Our Approach

1. **Simplicity**: Single schema, standard patterns
2. **Performance**: Optimized indexes, efficient queries
3. **Flexibility**: JSON fields for extensibility
4. **Compatibility**: Works with existing tools and ORMs

### Limitations and Mitigations

1. **Index Overhead**
   - **Issue**: More indexes mean slower writes
   - **Mitigation**: Carefully chosen indexes, periodic maintenance

2. **Query Complexity**
   - **Issue**: All queries need organization_id filter
   - **Mitigation**: Repository pattern abstracts complexity

3. **Migration Complexity**
   - **Issue**: Moving from user to organization model
   - **Mitigation**: Automated migration, backward compatibility

4. **SQLite Limitations**
   - **Issue**: No built-in RLS, limited concurrent writes
   - **Mitigation**: Application-layer security, write queuing

## Implementation Details

### Repository Pattern
```typescript
class ColorRepository {
  async findByOrganization(orgId: number): Promise<Color[]> {
    return this.db.colors.findMany({
      where: { organization_id: orgId },
      orderBy: { created_at: 'desc' }
    });
  }
}
```

### Context Management
```typescript
// Organization context provider
const OrganizationContext = createContext<{
  currentOrg: Organization;
  switchOrg: (orgId: string) => void;
}>();
```

### Sync Considerations
```typescript
// All sync operations include organization context
await syncService.syncColors({
  organization_id: currentOrg.id,
  since: lastSyncTimestamp
});
```

## Best Practices

### 1. Always Include Organization Context
```typescript
// ❌ Bad: No organization context
const colors = await db.colors.findAll();

// ✅ Good: Explicit organization context
const colors = await db.colors.findAll({
  where: { organization_id: currentOrgId }
});
```

### 2. Validate Organization Access
```typescript
// Check membership before operations
const canAccess = await checkOrgMembership(userId, orgId);
if (!canAccess) throw new ForbiddenError();
```

### 3. Use Transactions for Data Integrity
```typescript
await db.transaction(async (tx) => {
  const org = await tx.organizations.create({ name, slug });
  await tx.organizationMembers.create({
    organization_id: org.id,
    user_id: userId,
    role: 'owner'
  });
});
```

### 4. Plan-Based Feature Flags
```typescript
// Check plan limits before operations
if (org.plan === 'free' && memberCount >= 3) {
  throw new PlanLimitError('Free plan limited to 3 members');
}
```

### 5. Efficient Batch Operations
```typescript
// Use batch operations for better performance
await batchUpsertColors(orgId, colors);
```

## Future Considerations

### Potential Enhancements
1. **Organization Hierarchies**: Parent-child organization relationships
2. **Cross-Organization Sharing**: Controlled data sharing between orgs
3. **Advanced Permissions**: Granular resource-level permissions
4. **Audit Logging**: Comprehensive activity tracking
5. **Data Residency**: Region-specific data storage

### Scalability Path
1. **Database Sharding**: Shard by organization_id
2. **Read Replicas**: Distribute read load
3. **Caching Layer**: Redis for hot data
4. **Search Infrastructure**: Elasticsearch for full-text search

## Conclusion

The ChromaSync multi-tenant schema design provides a robust foundation for team collaboration while maintaining security, performance, and flexibility. The organization-first approach aligns with business requirements and user expectations, while the careful implementation ensures scalability and maintainability.

Key success factors:
- Clear separation of concerns
- Consistent patterns throughout the codebase
- Performance optimization from the start
- Security as a primary consideration
- Flexibility for future enhancements

This architecture supports ChromaSync's growth from individual users to large enterprise deployments while maintaining a excellent user experience.