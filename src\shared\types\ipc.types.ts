import { ColorEntry } from './color.types';

export interface IpcChannels {
  // Color operations
  'color:getAll': () => Promise<ColorEntry[]>;
  'color:add': (entry: Omit<ColorEntry, 'id' | 'createdAt' | 'updatedAt'>) => Promise<ColorEntry>;
  'color:update': (id: string, updates: Partial<ColorEntry>) => Promise<ColorEntry>;
  'color:delete': (id: string) => Promise<boolean>;
  
  // Import/Export operations
  'data:import': (mergeMode?: 'replace' | 'merge') => Promise<{ added: number; errors: string[] }>;
  'data:export': (filePath: string, format: 'csv' | 'json') => Promise<boolean>;
  'data:clear': () => Promise<boolean>;
} 