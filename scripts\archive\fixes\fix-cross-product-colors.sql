-- Fix colors that are incorrectly shared across multiple products
-- Each product should have its own color instances

-- Step 1: Create a backup of current state
CREATE TABLE IF NOT EXISTS product_colors_backup AS 
SELECT * FROM product_colors;

-- Step 2: Find colors that are shared across multiple products
CREATE TEMPORARY TABLE shared_colors AS
SELECT 
    c.id as original_color_id,
    c.external_id,
    c.name,
    c.display_name,
    c.code,
    c.hex,
    c.source_id,
    c.properties,
    c.created_at,
    c.updated_at,
    c.version,
    c.organization_id,
    c.created_by,
    c.user_id,
    c.deleted_at,
    c.device_id,
    c.sync_version,
    COUNT(DISTINCT pc.product_id) as product_count
FROM colors c
JOIN product_colors pc ON c.id = pc.color_id
JOIN products p ON pc.product_id = p.id
GROUP BY c.id
HAVING product_count > 1;

-- Step 3: Show what we're about to fix
SELECT 'COLORS SHARED ACROSS MULTIPLE PRODUCTS:' as status;
SELECT 
    sc.name,
    sc.hex,
    sc.product_count,
    GROUP_CONCAT(DISTINCT p.name) as shared_products
FROM shared_colors sc
JOIN product_colors pc ON sc.original_color_id = pc.color_id
JOIN products p ON pc.product_id = p.id
GROUP BY sc.original_color_id
ORDER BY sc.product_count DESC, sc.name
LIMIT 20;

-- Step 4: For each shared color, create separate instances for each product
-- We'll do this by updating the color records to be product-specific

-- First, let's see how many colors need to be duplicated
SELECT 'WILL CREATE ' || (SUM(product_count) - COUNT(*)) || ' NEW COLOR INSTANCES' as action
FROM shared_colors;

-- Step 5: Create new color instances for shared colors
-- For each shared color, keep one instance for the first product and create new ones for others

CREATE TEMPORARY TABLE new_color_assignments AS
SELECT 
    pc.product_id,
    pc.color_id as old_color_id,
    sc.original_color_id,
    p.name as product_name,
    ROW_NUMBER() OVER (PARTITION BY sc.original_color_id ORDER BY pc.product_id) as instance_number
FROM product_colors pc
JOIN shared_colors sc ON pc.color_id = sc.original_color_id  
JOIN products p ON pc.product_id = p.id;

-- Show the plan
SELECT 'ASSIGNMENT PLAN:' as status;
SELECT 
    product_name,
    COUNT(*) as colors_to_separate
FROM new_color_assignments nca
JOIN shared_colors sc ON nca.original_color_id = sc.original_color_id
GROUP BY product_name
ORDER BY colors_to_separate DESC;