/**
 * @file sync-engine.ts
 * @description Main orchestrator for the sync system
 * 
 * This module coordinates all sync operations, manages strategies,
 * and provides the main interface for sync functionality.
 */

import { machineIdSync } from 'node-machine-id';
import { 
  SyncStatus, 
  SyncOperation, 
  SyncProgress, 
  SyncResult, 
  ISyncStrategy,
  SyncQue<PERSON>Item,
  SyncEventHandler,
  SyncEvents
} from './sync-types';
import { SyncConfigManager, getSyncConfig } from './sync-config';
import { SyncQueueManager } from './sync-queue';

// ============================================================================
// SYNC ENGINE
// ============================================================================

/**
 * Main sync engine that orchestrates all sync operations
 */
export class SyncEngine {
  private status: SyncStatus = 'idle';
  private userId: string | null = null;
  private organizationId: string | null = null;
  private deviceId = machineIdSync();
  private isInitialized = false;
  private isSyncing = false;

  // Core components
  private configManager: SyncConfigManager;
  private queueManager: SyncQueueManager;
  private strategies = new Map<string, ISyncStrategy>();
  private eventHandlers = new Map<keyof SyncEvents, SyncEventHandler[]>();

  // Current sync operation tracking
  private currentProgress: SyncProgress | null = null;
  private currentOperation: SyncOperation | null = null;
  private syncStartTime: number = 0;

  constructor() {
    this.configManager = getSyncConfig();
    this.queueManager = new SyncQueueManager();
    
    // Subscribe to queue events
    this.queueManager.on('queue:updated', (stats) => {
      this.emitEvent('queue:updated', stats);
    });
  }

  /**
   * Initialize the sync engine
   */
  async initialize(userId: string, organizationId: string): Promise<void> {
    if (this.isInitialized) {
      throw new Error('Sync engine already initialized');
    }

    try {
      this.setStatus('initializing');
      
      this.userId = userId;
      this.organizationId = organizationId;
      
      // Initialize queue manager
      this.queueManager.initialize(userId, organizationId);
      
      // Validate required components
      await this.validateInitialization();
      
      this.isInitialized = true;
      this.setStatus('idle');
      
      console.log(`[SyncEngine] Initialized for user ${userId} in organization ${organizationId}`);
      
    } catch (error) {
      this.setStatus('error');
      throw new Error(`Sync engine initialization failed: ${error.message}`);
    }
  }

  /**
   * Register a sync strategy
   */
  registerStrategy(strategy: ISyncStrategy): void {
    this.strategies.set(strategy.name, strategy);
    console.log(`[SyncEngine] Registered strategy: ${strategy.name}`);
  }

  /**
   * Unregister a sync strategy
   */
  unregisterStrategy(strategyName: string): void {
    this.strategies.delete(strategyName);
    console.log(`[SyncEngine] Unregistered strategy: ${strategyName}`);
  }

  /**
   * Start a sync operation
   */
  async startSync(operation: SyncOperation = 'manual'): Promise<SyncResult> {
    if (!this.isInitialized) {
      throw new Error('Sync engine not initialized');
    }

    if (this.isSyncing) {
      throw new Error('Sync operation already in progress');
    }

    try {
      this.isSyncing = true;
      this.currentOperation = operation;
      this.syncStartTime = Date.now();
      this.setStatus('syncing');

      console.log(`[SyncEngine] Starting ${operation} sync operation`);

      // Initialize progress tracking
      this.currentProgress = {
        phase: 'initializing',
        progress: 0,
        currentOperation: 'Preparing sync',
        itemsProcessed: 0,
        totalItems: 0,
        estimatedTimeRemaining: 0,
        errors: [],
        warnings: []
      };

      this.emitEvent('sync:started', this.currentProgress);

      // Execute the sync operation
      const result = await this.executeSyncOperation(operation);

      // Update final progress
      this.currentProgress = {
        ...this.currentProgress,
        phase: result.success ? 'complete' : 'error',
        progress: 100,
        currentOperation: result.success ? 'Sync completed' : 'Sync failed'
      };

      this.emitEvent('sync:completed', result);
      this.setStatus(result.success ? 'completed' : 'error');

      return result;

    } catch (error) {
      const errorResult: SyncResult = {
        success: false,
        itemsProcessed: 0,
        itemsSucceeded: 0,
        itemsFailed: 0,
        errors: [{
          id: `sync-error-${Date.now()}`,
          timestamp: Date.now(),
          operation: operation,
          category: 'database',
          severity: 'high',
          message: error.message,
          originalError: error,
          recoverable: true
        }],
        duration: Date.now() - this.syncStartTime
      };

      this.emitEvent('sync:error', errorResult.errors[0]);
      this.setStatus('error');
      
      return errorResult;

    } finally {
      this.isSyncing = false;
      this.currentOperation = null;
      this.currentProgress = null;
    }
  }

  /**
   * Add item to sync queue
   */
  queueItem(item: Omit<SyncQueueItem, 'metadata'>): void {
    this.queueManager.enqueue(item);
  }

  /**
   * Get current sync status
   */
  getStatus(): SyncStatus {
    return this.status;
  }

  /**
   * Get current sync progress
   */
  getProgress(): SyncProgress | null {
    return this.currentProgress;
  }

  /**
   * Check if sync is currently running
   */
  isSyncInProgress(): boolean {
    return this.isSyncing;
  }

  /**
   * Get queue statistics
   */
  getQueueStats() {
    return this.queueManager.getStats();
  }

  /**
   * Subscribe to sync events
   */
  on<K extends keyof SyncEvents>(event: K, handler: SyncEventHandler<SyncEvents[K]>): () => void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    
    this.eventHandlers.get(event)!.push(handler);

    // Return unsubscribe function
    return () => {
      const handlers = this.eventHandlers.get(event);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    };
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    console.log('[SyncEngine] Cleaning up resources');
    
    this.isSyncing = false;
    this.isInitialized = false;
    this.setStatus('idle');
    
    // Cleanup queue manager
    this.queueManager.destroy();
    
    // Clear event handlers
    this.eventHandlers.clear();
    
    // Clear strategies
    this.strategies.clear();
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Execute the actual sync operation
   */
  private async executeSyncOperation(operation: SyncOperation): Promise<SyncResult> {
    const config = this.configManager.getConfig();
    const batchSize = config.batchSizes.products; // Use products batch size as default
    
    // Get items from queue
    const queueItems = this.queueManager.dequeue(batchSize);
    
    if (queueItems.length === 0) {
      return {
        success: true,
        itemsProcessed: 0,
        itemsSucceeded: 0,
        itemsFailed: 0,
        errors: [],
        duration: Date.now() - this.syncStartTime
      };
    }

    // Update progress
    this.updateProgress({
      phase: 'pushing',
      progress: 10,
      currentOperation: `Processing ${queueItems.length} items`,
      totalItems: queueItems.length
    });

    // Group items by table and find appropriate strategies
    const strategyGroups = this.groupItemsByStrategy(queueItems, operation);
    
    let totalProcessed = 0;
    let totalSucceeded = 0;
    let totalFailed = 0;
    const allErrors: any[] = [];

    // Process each strategy group
    for (const [strategy, items] of strategyGroups.entries()) {
      try {
        console.log(`[SyncEngine] Processing ${items.length} items with strategy: ${strategy.name}`);
        
        this.updateProgress({
          currentOperation: `Syncing ${items[0].table} (${items.length} items)`,
          progress: Math.floor((totalProcessed / queueItems.length) * 80) + 10
        });

        const result = await strategy.execute(items);
        
        totalProcessed += result.itemsProcessed;
        totalSucceeded += result.itemsSucceeded;
        totalFailed += result.itemsFailed;
        allErrors.push(...result.errors);

        // Remove successfully processed items from queue
        if (result.itemsSucceeded > 0) {
          const successfulIds = items
            .slice(0, result.itemsSucceeded)
            .map(item => item.id);
          this.queueManager.removeItems(successfulIds);
        }

        // Mark failed items for retry
        if (result.itemsFailed > 0) {
          const failedIds = items
            .slice(result.itemsSucceeded)
            .map(item => item.id);
          this.queueManager.markItemsFailed(failedIds);
        }

      } catch (error) {
        console.error(`[SyncEngine] Strategy ${strategy.name} failed:`, error);
        
        // Mark all items in this group as failed
        const itemIds = items.map(item => item.id);
        this.queueManager.markItemsFailed(itemIds);
        
        totalFailed += items.length;
        allErrors.push({
          id: `strategy-error-${Date.now()}`,
          timestamp: Date.now(),
          operation: `${strategy.name} execution`,
          category: 'database',
          severity: 'high',
          message: error.message,
          originalError: error,
          recoverable: true
        });
      }
    }

    // Final progress update
    this.updateProgress({
      phase: 'complete',
      progress: 100,
      currentOperation: 'Sync completed',
      itemsProcessed: totalProcessed
    });

    return {
      success: totalFailed === 0,
      itemsProcessed: totalProcessed,
      itemsSucceeded: totalSucceeded,
      itemsFailed: totalFailed,
      errors: allErrors,
      duration: Date.now() - this.syncStartTime
    };
  }

  /**
   * Group queue items by appropriate sync strategy
   */
  private groupItemsByStrategy(items: SyncQueueItem[], operation: SyncOperation): Map<ISyncStrategy, SyncQueueItem[]> {
    const groups = new Map<ISyncStrategy, SyncQueueItem[]>();

    for (const item of items) {
      const strategy = this.findStrategyForItem(item, operation);
      
      if (strategy) {
        if (!groups.has(strategy)) {
          groups.set(strategy, []);
        }
        groups.get(strategy)!.push(item);
      } else {
        console.warn(`[SyncEngine] No strategy found for item: ${item.table}/${item.action}`);
      }
    }

    return groups;
  }

  /**
   * Find the best strategy for a queue item
   */
  private findStrategyForItem(item: SyncQueueItem, operation: SyncOperation): ISyncStrategy | null {
    const candidates = Array.from(this.strategies.values())
      .filter(strategy => strategy.canHandle(item.table, operation))
      .sort((a, b) => b.priority - a.priority); // Higher priority first

    return candidates[0] || null;
  }

  /**
   * Update sync progress and emit event
   */
  private updateProgress(updates: Partial<SyncProgress>): void {
    if (this.currentProgress) {
      this.currentProgress = { ...this.currentProgress, ...updates };
      this.emitEvent('sync:progress', this.currentProgress);
    }
  }

  /**
   * Set sync status and emit event if changed
   */
  private setStatus(newStatus: SyncStatus): void {
    if (this.status !== newStatus) {
      this.status = newStatus;
      console.log(`[SyncEngine] Status changed to: ${newStatus}`);
    }
  }

  /**
   * Emit event to all registered handlers
   */
  private emitEvent<K extends keyof SyncEvents>(event: K, data: SyncEvents[K]): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`[SyncEngine] Error in event handler for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Validate initialization requirements
   */
  private async validateInitialization(): Promise<void> {
    if (!this.userId) {
      throw new Error('User ID is required for initialization');
    }

    if (!this.organizationId) {
      throw new Error('Organization ID is required for initialization');
    }

    if (this.strategies.size === 0) {
      console.warn('[SyncEngine] No sync strategies registered');
    }
  }
}
