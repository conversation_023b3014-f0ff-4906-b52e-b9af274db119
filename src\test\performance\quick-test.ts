/**
 * Quick Performance Test
 * Verify the performance testing setup works
 */

import { generateTestColors } from './generateTestData.js';
import { assessPerformance } from './index.js';

console.log('🧪 ChromaSync Quick Performance Test\n');

// Test 1: Generate 10k colors
console.log('Test 1: Generating 10,000 colors...');
const startGen = performance.now();
const colors = generateTestColors({
  count: 10000,
  includeGradients: true,
  includeAllColorSpaces: true,
  realWorldDistribution: true
});
const genTime = performance.now() - startGen;
console.log(`✓ Generated in ${genTime.toFixed(2)}ms ${assessPerformance(genTime, 'export')}\n`);

// Test 2: Search performance
console.log('Test 2: Testing search across 10,000 colors...');
const searchTerms = ['blue', 'PANTONE', '123'];
const startSearch = performance.now();
searchTerms.forEach(term => {
  const results = colors.filter(c => 
    c.name?.toLowerCase().includes(term.toLowerCase()) ||
    c.code?.toLowerCase().includes(term.toLowerCase())
  );
  console.log(`  Found ${results.length} matches for "${term}"`);
});
const searchTime = performance.now() - startSearch;
console.log(`✓ Search completed in ${searchTime.toFixed(2)}ms ${assessPerformance(searchTime, 'search')}\n`);

// Test 3: Memory usage
console.log('Test 3: Memory usage...');
const memoryMB = process.memoryUsage().heapUsed / 1024 / 1024;
console.log(`✓ Current heap: ${memoryMB.toFixed(2)}MB ${assessPerformance(memoryMB, 'memory')}\n`);

// Summary
console.log('📊 Quick Test Summary:');
console.log('━'.repeat(40));
console.log(`Colors: ${colors.length.toLocaleString()}`);
console.log(`Generation: ${genTime.toFixed(2)}ms`);
console.log(`Search: ${searchTime.toFixed(2)}ms`);
console.log(`Memory: ${memoryMB.toFixed(2)}MB`);
console.log('━'.repeat(40));

if (genTime < 1000 && searchTime < 100 && memoryMB < 300) {
  console.log('\n✅ All quick tests passed! Ready for full performance validation.');
} else {
  console.log('\n⚠️ Some tests are slower than expected. Run full test suite for details.');
}

console.log('\nRun `npm run test:perf:full` for comprehensive 100k+ color testing.');
