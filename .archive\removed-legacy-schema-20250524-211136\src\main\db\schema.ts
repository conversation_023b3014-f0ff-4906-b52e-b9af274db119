/**
 * @file schema.ts
 * @description DEPRECATED - Database schema reference file
 * 
 * NOTE: This file is now deprecated. All database initialization and schema
 * definitions have been consolidated in database.ts to eliminate redundancy
 * and ensure consistency.
 * 
 * This file is kept for reference only and may be removed in a future version.
 * Please use database.ts for all database operations.
 */

import { initDatabase } from './database';

/**
 * @deprecated Use initDatabase from database.ts instead
 */
export async function initializeDatabase() {
  console.warn('[DEPRECATED] schema.ts: initializeDatabase is deprecated. Use initDatabase from database.ts instead.');
  return await initDatabase();
}