/**
 * Add User as Organization Owner
 * Directly adds the authenticated user as owner of the IVG organization
 */

const Database = require('better-sqlite3');

function addUserAsOwner() {
  const dbPath = '/Users/<USER>/Library/Application Support/ChromaSync/chromasync.db';
  console.log('Opening database:', dbPath);
  
  const db = new Database(dbPath);
  
  try {
    // Your authenticated user ID from the app logs
    const userId = 'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf';
    const userEmail = '<EMAIL>';
    
    // Get the IVG organization
    const org = db.prepare(`
      SELECT id, external_id, name FROM organizations 
      WHERE external_id = '4047153f-7be8-490b-9cb2-a1e3ed04b92b'
      LIMIT 1
    `).get();
    
    if (!org) {
      console.error('❌ IVG organization not found');
      return;
    }
    
    console.log('✅ Found organization:', org);
    
    // First, ensure the user exists in the users table
    const existingUser = db.prepare('SELECT * FROM users WHERE id = ?').get(userId);
    
    if (!existingUser) {
      console.log('📝 Creating user record...');
      db.prepare(`
        INSERT INTO users (id, email, name, created_at, updated_at)
        VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `).run(userId, userEmail, 'Michael');
      
      console.log('✅ User record created');
    } else {
      console.log('✅ User record already exists:', existingUser.email);
    }
    
    // Check if user is already a member
    const existingMember = db.prepare(`
      SELECT * FROM organization_members 
      WHERE organization_id = ? AND user_id = ?
    `).get(org.id, userId);
    
    if (!existingMember) {
      console.log('🔧 Adding user as organization owner...');
      
      db.prepare(`
        INSERT INTO organization_members (organization_id, user_id, role, joined_at, invited_by)
        VALUES (?, ?, 'owner', CURRENT_TIMESTAMP, ?)
      `).run(org.id, userId, userId);
      
      console.log('✅ Successfully added user as organization owner');
    } else {
      console.log('✅ User is already a member with role:', existingMember.role);
      
      // If not owner, promote to owner
      if (existingMember.role !== 'owner') {
        console.log('🔧 Promoting user to owner...');
        
        db.prepare(`
          UPDATE organization_members 
          SET role = 'owner' 
          WHERE organization_id = ? AND user_id = ?
        `).run(org.id, userId);
        
        console.log('✅ User promoted to owner');
      }
    }
    
    // Verify the final state
    const finalMembers = db.prepare(`
      SELECT om.*, u.email 
      FROM organization_members om 
      LEFT JOIN users u ON om.user_id = u.id
      WHERE om.organization_id = ?
    `).all(org.id);
    
    console.log('\n🎉 Final organization members:');
    finalMembers.forEach(member => {
      console.log(`  - ${member.email || member.user_id} (${member.role})`);
    });
    
    console.log('\n✅ Migration complete!');
    console.log('   Restart ChromaSync and go to Settings → Team');
    console.log('   You should now see the invite option');
    
  } catch (error) {
    console.error('❌ Error during migration:', error);
  } finally {
    db.close();
  }
}

addUserAsOwner();