/**
 * @file ConflictResolution.tsx
 * @description Component for resolving sync conflicts
 */

import React, { useState } from 'react';
import { useSyncConflicts } from '../../store/sync.store';
import { SyncConflict } from '../../../shared/types/sync.types';

export const ConflictResolution: React.FC = () => {
  const { conflicts, resolveConflicts } = useSyncConflicts();
  const [isResolving, setIsResolving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [resolutions, setResolutions] = useState<Record<string, 'local' | 'remote' | 'merged'>>({});
  
  // No conflicts to resolve
  if (conflicts.length === 0) {
    return (
      <div className="text-sm text-gray-500">
        No conflicts to resolve.
      </div>
    );
  }
  
  // Handle resolution selection
  const handleResolutionChange = (conflictId: string, resolution: 'local' | 'remote' | 'merged') => {
    setResolutions(prev => ({
      ...prev,
      [conflictId]: resolution
    }));
  };
  
  // Handle resolve button click
  const handleResolve = async () => {
    // Check if all conflicts have resolutions
    const unresolvedConflicts = conflicts.filter(
      conflict => !resolutions[conflict.recordId]
    );
    
    if (unresolvedConflicts.length > 0) {
      setError('Please select a resolution for all conflicts');
      return;
    }
    
    setIsResolving(true);
    setError(null);
    
    try {
      // Format resolutions for the API
      const resolutionArray = conflicts.map(conflict => ({
        conflictId: conflict.recordId,
        resolution: resolutions[conflict.recordId]
      }));
      
      const success = await resolveConflicts(resolutionArray);
      
      if (!success) {
        setError('Failed to resolve conflicts');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to resolve conflicts');
    } finally {
      setIsResolving(false);
    }
  };
  
  // Render a single conflict
  const renderConflict = (conflict: SyncConflict) => {
    const { recordId, table, localData, remoteData } = conflict;
    
    return (
      <div key={recordId} className="p-4 border border-gray-200 rounded mb-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm font-semibold">
            Conflict in {table} (ID: {recordId.substring(0, 8)}...)
          </h3>
        </div>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="p-2 bg-gray-100 rounded">
            <h4 className="text-xs font-semibold mb-1">Local Version</h4>
            <pre className="text-xs overflow-auto max-h-40">
              {JSON.stringify(localData, null, 2)}
            </pre>
          </div>
          
          <div className="p-2 bg-gray-100 rounded">
            <h4 className="text-xs font-semibold mb-1">Remote Version</h4>
            <pre className="text-xs overflow-auto max-h-40">
              {JSON.stringify(remoteData, null, 2)}
            </pre>
          </div>
        </div>
        
        <div className="flex space-x-4">
          <label className="flex items-center space-x-2">
            <input
              type="radio"
              name={`resolution-${recordId}`}
              value="local"
              checked={resolutions[recordId] === 'local'}
              onChange={() => handleResolutionChange(recordId, 'local')}
              className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300"
              disabled={isResolving}
            />
            <span className="text-sm">Keep Local</span>
          </label>
          
          <label className="flex items-center space-x-2">
            <input
              type="radio"
              name={`resolution-${recordId}`}
              value="remote"
              checked={resolutions[recordId] === 'remote'}
              onChange={() => handleResolutionChange(recordId, 'remote')}
              className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300"
              disabled={isResolving}
            />
            <span className="text-sm">Use Remote</span>
          </label>
          
          <label className="flex items-center space-x-2">
            <input
              type="radio"
              name={`resolution-${recordId}`}
              value="merged"
              checked={resolutions[recordId] === 'merged'}
              onChange={() => handleResolutionChange(recordId, 'merged')}
              className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300"
              disabled={isResolving}
            />
            <span className="text-sm">Merge</span>
          </label>
        </div>
      </div>
    );
  };
  
  return (
    <div className="flex flex-col space-y-4">
      <h2 className="text-lg font-semibold">
        Resolve Conflicts ({conflicts.length})
      </h2>
      
      {error && (
        <div className="text-sm text-red-500 bg-red-100 p-2 rounded">
          {error}
        </div>
      )}
      
      <div className="space-y-4">
        {conflicts.map(renderConflict)}
      </div>
      
      <button
        onClick={handleResolve}
        disabled={isResolving}
        className={`px-4 py-2 rounded ${
          isResolving
            ? 'bg-gray-300 cursor-not-allowed'
            : 'bg-blue-500 hover:bg-blue-600 text-white'
        }`}
      >
        {isResolving ? 'Resolving...' : 'Resolve Conflicts'}
      </button>
    </div>
  );
};
