/**
 * @file AddColorDemo.tsx
 * @description Demonstration component for the new two-path Add Color workflow
 */

import React from 'react';
import AddColorButtons from './AddColorButtons';

const AddColorDemo: React.FC = () => {
  return (
    <div className="p-6 bg-ui-background-secondary dark:bg-zinc-900 rounded-lg">
      <div className="mb-6 text-center">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-2">
          New Color Workflow
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Choose which type of color to add to your collection
        </p>
      </div>
      
      <div className="flex justify-center">
        <AddColorButtons />
      </div>
      
      <div className="mt-8 border-t border-gray-200 dark:border-zinc-700 pt-4">
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">About this workflow:</h3>
        <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-2">
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>The <strong>Add Flat Color</strong> button opens a form with fields for product, flavor, HEX, Pantone, and CMYK.</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>The <strong>Add Gradient</strong> button opens a specialized form for creating gradient colors with product and flavor fields.</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>This creates a cleaner separation between the two color types from the beginning of the workflow.</span>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default AddColorDemo; 