/**
 * @file run-cleanup.ts
 * @description Utility to run the cleanup process from the main process
 */

import { getDatabase } from '../db/database';

/**
 * Run the cleanup process
 * @returns Promise<{merged: number, deleted: number, normalized: number}>
 */
export async function runCleanup(): Promise<{merged: number, deleted: number, normalized: number}> {
  console.log('[Cleanup] Starting cleanup process...');

  try {
    // Get the database instance
    const db = getDatabase();

    // Verify that the database is properly initialized
    if (!db) {
      console.error('[Cleanup] Database object is undefined');
      throw new Error('Database not initialized. Please restart the application.');
    }

    // Cleanup functions have been removed as they're no longer needed
    // in the optimized schema
    console.log('[Cleanup] Skipping cleanup - not needed with optimized schema');

    return {
      merged: 0,
      deleted: 0,
      normalized: 0
    };
  } catch (error) {
    console.error('[Cleanup] Error during cleanup:', error);
    throw error;
  }
}
