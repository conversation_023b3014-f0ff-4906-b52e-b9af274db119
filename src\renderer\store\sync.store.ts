/**
 * @file sync.store.ts
 * @description Zustand store for sync state management
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
  SyncStatus,
  SyncConfig,
  SyncAuthState,
  SyncConflict,
  ConflictResolutionStrategy
} from '../../shared/types/sync.types';

interface SyncState {
  // Status
  status: SyncStatus;
  lastSyncTime: number | null;
  message: string | null;
  error: string | null;

  // Sync statistics
  syncStats: {
    colors: number;
    products: number;
    
    datasheets: number;
  };

  // Configuration
  config: SyncConfig;

  // Authentication
  authState: SyncAuthState;
  
  // Organization context
  organizationStatus?: 'needs_organization_setup' | 'needs_organization_selection' | 'authenticated';
  organizations?: any[];

  // Conflicts
  conflicts: SyncConflict[];

  // Actions
  initialize: () => Promise<void>;
  login: (email: string, password: string) => Promise<boolean>;
  signup: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  syncData: () => Promise<boolean>;
  updateConfig: (config: Partial<SyncConfig>) => Promise<void>;
  resolveConflicts: (resolutions: Array<{
    conflictId: string;
    resolution: 'local' | 'remote' | 'merged';
    mergedData?: unknown;
  }>) => Promise<boolean>;
  refreshAuthState: () => Promise<void>;
}

// Default sync configuration
const DEFAULT_SYNC_CONFIG: SyncConfig = {
  autoSync: true,
  syncInterval: 30, // 30 minutes
  conflictResolution: ConflictResolutionStrategy.LAST_WRITE_WINS,
  maxStorageVersions: 5
};

// Default auth state
const DEFAULT_AUTH_STATE: SyncAuthState = {
  isAuthenticated: false
};

export const useSyncStore = create<SyncState>()(
  devtools(
    (set, _get) => ({
      // Initial state
      status: SyncStatus.IDLE,
      lastSyncTime: null,
      message: null,
      error: null,
      syncStats: {
        colors: 0,
        products: 0,
        datasheets: 0
      },
      config: DEFAULT_SYNC_CONFIG,
      authState: DEFAULT_AUTH_STATE,
      conflicts: [],

      // Initialize the store
      initialize: async (): Promise<void> => {
        try {
          // Check if syncAPI is available
          if (typeof window === 'undefined' || !window.syncAPI) {
            console.warn('syncAPI not available for sync store initialization');
            return;
          }

          // Set up listeners for sync events
// @ts-ignore - Sync API type mismatch
          const _removeStatusListener = window.syncAPI.onStatusUpdate((status: { status: string; message?: string }) => {
            set({
              status: status.status as SyncStatus,
              message: status.message || null,
              error: status.status === 'error' ? status.message || 'Sync failed' : null
            });

            if (status.status === 'success') {
              set({ lastSyncTime: Date.now() });
            }
          });

// @ts-ignore - Sync API type mismatch
          const _removeConflictsListener = window.syncAPI.onConflicts((conflicts) => {
            set({ conflicts });
          });

          // Get sync configuration
// @ts-ignore - Sync API type mismatch
          const configResponse = await window.syncAPI.getConfig();
          if (configResponse.success && configResponse.data) {
            set({ config: configResponse.data });
          }

          // Get auth state - this will restore session if one exists
          const authResponse = await window.syncAPI.getAuthState();
          console.log('[SyncStore] Auth response on initialize:', authResponse);
          
          if (authResponse.isAuthenticated) {
            set({ 
              authState: {
                isAuthenticated: true,
                user: authResponse.user,
                session: authResponse.session
              },
              organizationStatus: authResponse.status,
              organizations: authResponse.organizations || []
            });
          }

          // Store cleanup functions for later use if needed
          // Note: These would need to be called separately if cleanup is required
        } catch (error) {
          console.error('Error initializing sync store:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to initialize sync'
          });
        }
      },

      // Login with Google
      login: async () => {
        try {
          set({ status: SyncStatus.SYNCING, message: 'Signing in with Google...', error: null });

          const response = await window.syncAPI.login();

          if (response.success) {
            // Handle organization status
            if (response.status) {
              set({
                organizationStatus: response.status,
                organizations: response.organizations
              });
            }
            
            // Get updated auth state
            const authResponse = await window.syncAPI.getAuthState();
            if (authResponse.isAuthenticated) {
              set({
                authState: {
                  isAuthenticated: true,
                  user: authResponse.user,
                  session: authResponse.session
                },
                status: SyncStatus.SUCCESS,
                message: 'Signed in successfully'
              });
            }
            
            // Return an object with all needed information
            return {
              success: true,
              requiresConsent: response.requiresConsent || false,
              status: response.status,
              organizations: response.organizations
            };
          } else {
            set({
              status: SyncStatus.ERROR,
              error: response.error || 'Sign in failed'
            });
            return { success: false };
          }
        } catch (error) {
          console.error('Error signing in:', error);
          set({
            status: SyncStatus.ERROR,
            error: error instanceof Error ? error.message : 'Sign in failed'
          });
          return { success: false };
        }
      },

      // Refresh auth state
      refreshAuthState: async () => {
        try {
          const authResponse = await window.syncAPI.getAuthState();
          console.log('[SyncStore] Auth response on refresh:', authResponse);
          
          // Update auth state and organization info in one call
          set({
            authState: {
              isAuthenticated: authResponse.isAuthenticated || false,
              user: authResponse.user,
              session: authResponse.session
            },
            organizationStatus: authResponse.status,
            organizations: authResponse.organizations || []
          });
        } catch (error) {
          console.error('Error refreshing auth state:', error);
        }
      },

      // Signup (not supported with Google-only auth)
      signup: async () => {
        set({
          status: SyncStatus.ERROR,
          error: 'Please use Google sign-in'
        });
        return false;
      },

      // Logout
      logout: async () => {
        try {
          set({ status: SyncStatus.SYNCING, message: 'Logging out...', error: null });

// @ts-ignore - Sync API type mismatch
          const response = await window.syncAPI.logout();

          if (response.success) {
            set({
              authState: DEFAULT_AUTH_STATE,
              status: SyncStatus.SUCCESS,
              message: 'Logged out successfully'
            });
          } else {
            set({
              status: SyncStatus.ERROR,
              error: response.error || 'Logout failed'
            });
          }
        } catch (error) {
          console.error('Error logging out:', error);
          set({
            status: SyncStatus.ERROR,
            error: error instanceof Error ? error.message : 'Logout failed'
          });
        }
      },

      // Sync data
      syncData: async () => {
        try {
          // Set syncing status immediately
          set({ status: SyncStatus.SYNCING, message: 'Syncing data...', error: null });

          // Call the sync API
// @ts-ignore - Sync API type mismatch
          const response = await window.syncAPI.syncData();

          if (response.success) {
            // Update sync stats with counts from response
            const updateState: any = {
              status: SyncStatus.SUCCESS,
              message: response.message || 'Data synced successfully',
              lastSyncTime: Date.now()
            };

            // If response includes counts, update syncStats
            if (response.counts) {
              updateState.syncStats = {
                colors: response.counts.colors || 0,
                products: response.counts.products || 0,
                datasheets: response.counts.datasheets || 0
              };
            }

            set(updateState);
            return true;
          } else {
            // Handle sync failure
            {
              set({
                status: SyncStatus.ERROR,
                error: response.error || 'Sync failed'
              });
            }
            return false;
          }
        } catch (error) {
          console.error('Error syncing data:', error);
          set({
            status: SyncStatus.ERROR,
            error: error instanceof Error ? error.message : 'Sync failed'
          });
          return false;
        }
      },

      // Update config
      updateConfig: async (config: Partial<SyncConfig>) => {
        try {
// @ts-ignore - Sync API type mismatch
          const response = await window.syncAPI.updateConfig(config);

          if (response.success && response.data) {
            set({ config: response.data });
          } else {
            set({
              error: response.error || 'Failed to update config'
            });
          }
        } catch (error) {
          console.error('Error updating config:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to update config'
          });
        }
      },

      // Resolve conflicts
      resolveConflicts: async (resolutions: Array<{
        conflictId: string;
        resolution: 'local' | 'remote' | 'merged';
        mergedData?: unknown;
      }>) => {
        try {
          set({ status: SyncStatus.SYNCING, message: 'Resolving conflicts...', error: null });

// @ts-ignore - Sync API type mismatch
          const response = await window.syncAPI.resolveConflicts(resolutions);

          if (response.success) {
            set({
              status: SyncStatus.SUCCESS,
              message: 'Conflicts resolved successfully',
              conflicts: [],
              lastSyncTime: Date.now()
            });
            return true;
          } else {
            set({
              status: SyncStatus.ERROR,
              error: response.error || 'Failed to resolve conflicts'
            });
            return false;
          }
        } catch (error) {
          console.error('Error resolving conflicts:', error);
          set({
            status: SyncStatus.ERROR,
            error: error instanceof Error ? error.message : 'Failed to resolve conflicts'
          });
          return false;
        }
      }
    }),
    { name: 'sync-store' }
  )
);

// Initialize the store when the module is loaded
// Check if window.syncAPI exists before initializing
if (typeof window !== 'undefined' && window.syncAPI) {
  useSyncStore.getState().initialize();
} else {
  console.warn('syncAPI not available, sync store initialization deferred');
}

// Export selectors for common operations
export const useSyncStatus = () => useSyncStore(state => ({
  status: state.status,
  message: state.message,
  error: state.error,
  lastSyncTime: state.lastSyncTime,
  syncStats: state.syncStats
}));

export const useSyncAuth = () => useSyncStore(state => ({
  isAuthenticated: state.authState.isAuthenticated,
  userId: state.authState.userId,
  user: state.authState.user,
  organizationStatus: state.organizationStatus,
  organizations: state.organizations,
  login: state.login,
  signup: state.signup,
  logout: state.logout
}));

export const useSyncConfig = () => useSyncStore(state => ({
  config: state.config,
  updateConfig: state.updateConfig
}));

export const useSyncConflicts = () => useSyncStore(state => ({
  conflicts: state.conflicts,
  resolveConflicts: state.resolveConflicts
}));

export const useSyncActions = () => useSyncStore(state => ({
  syncData: state.syncData
}));
