import { generateCssVariables } from './generateCssVariables';
import { describe, test, expect } from 'vitest';

describe('generateCssVariables', () => {
  test('should generate CSS variables with proper formatting', () => {
    const cssVariables = generateCssVariables();
    
    // Check that it's a non-empty string
    expect(typeof cssVariables).toBe('string');
    expect(cssVariables.length).toBeGreaterThan(0);
    
    // Check that it includes root element
    expect(cssVariables).toContain(':root {');
    
    // Check for some essential token categories
    expect(cssVariables).toContain('--color-brand-primary');
    expect(cssVariables).toContain('--color-ui-background-primary');
    expect(cssVariables).toContain('--font-size-base');
    expect(cssVariables).toContain('--spacing-4');
    expect(cssVariables).toContain('--radius-md');
    
    // Check for dark mode section
    expect(cssVariables).toContain('.dark {');
  });
  
  test('should generate valid CSS that can be parsed', () => {
    const cssVariables = generateCssVariables();
    
    // Create a style element and try to insert the CSS
    const style = document.createElement('style');
    style.textContent = cssVariables;
    
    // If this doesn't throw, the CSS is valid
    document.head.appendChild(style);
    document.head.removeChild(style);
    
    // This test passes if no exception was thrown
    expect(true).toBe(true);
  });
}); 