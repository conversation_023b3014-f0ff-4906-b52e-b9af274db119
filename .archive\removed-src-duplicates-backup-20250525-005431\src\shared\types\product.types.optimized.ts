/**
 * @file product.types.optimized.ts
 * @description Updated type definitions for the optimized product schema
 */

export interface Product {
  id: number;
  external_id: string;
  name: string;
  sku?: string;
  is_active: boolean;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface ProductColor {
  product_id: number;
  color_id: number;
  display_order: number;
  usage_type: 'standard' | 'primary' | 'accent' | 'variant';
  quantity?: number;
  metadata?: Record<string, any>;
  added_at: string;
}

export interface ProductWithColors extends Product {
  colors: Array<{
    color_id: number;
    display_order: number;
    usage_type: string;
    added_at: string;
    // Include denormalized color data
    code: string;
    hex: string;
    display_name?: string;
  }>;
}

// Create/Update types
export type NewProduct = Omit<Product, 'id' | 'created_at' | 'updated_at' | 'is_active'>;
export type UpdateProduct = Partial<NewProduct>;

// IPC Channel names
export enum ProductChannels {
  GET_ALL = 'product:getAll',
  GET_BY_ID = 'product:getById',
  GET_WITH_COLORS = 'product:getWithColors',
  ADD = 'product:add',
  UPDATE = 'product:update',
  DELETE = 'product:delete',
  ADD_COLOR = 'product:addColor',
  REMOVE_COLOR = 'product:removeColor',
  UPDATE_COLOR_ORDER = 'product:updateColorOrder',
  DELETE_MULTIPLE = 'product:deleteMultiple'
}