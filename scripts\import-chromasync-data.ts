import { app } from 'electron';
import Database from 'better-sqlite3';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

interface ImportColor {
  id: string;
  product: string;
  name: string;
  code: string;
  hex: string;
  cmyk: string;
  notes?: string;
  isLibrary: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CMYKValues {
  c: number;
  m: number;
  y: number;
  k: number;
}

function parseCMYK(cmykString: string): CMYKValues {
  // Parse "C:96 M:69 Y:0 K:0" format
  const matches = cmykString.match(/C:(\d+)\s*M:(\d+)\s*Y:(\d+)\s*K:(\d+)/);
  if (!matches) {
    throw new Error(`Invalid CMYK format: ${cmykString}`);
  }
  
  return {
    c: parseInt(matches[1], 10),
    m: parseInt(matches[2], 10),
    y: parseInt(matches[3], 10),
    k: parseInt(matches[4], 10)
  };
}

function hexToRGB(hex: string): { r: number; g: number; b: number } {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (!result) {
    throw new Error(`Invalid hex color: ${hex}`);
  }
  
  return {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  };
}

async function importData() {
  // Wait for app to be ready
  await app.whenReady();
  
  const dbPath = path.join(app.getPath('userData'), 'chromasync.db');
  const jsonPath = path.join(process.cwd(), 'chromasync-import-fixed.json');
  
  console.log(`Database path: ${dbPath}`);
  console.log(`JSON path: ${jsonPath}`);
  
  if (!fs.existsSync(jsonPath)) {
    console.error('Import file not found:', jsonPath);
    app.quit();
    return;
  }
  
  const importData: ImportColor[] = JSON.parse(fs.readFileSync(jsonPath, 'utf-8'));
  console.log(`Found ${importData.length} colors to import`);
  
  const db = new Database(dbPath);
  
  try {
    // Enable foreign keys
    db.exec('PRAGMA foreign_keys = ON');
    
    // Start transaction
    db.exec('BEGIN');
    
    // Track product mappings
    const productMap = new Map<string, number>();
    
    // First, ensure all unique products exist
    const uniqueProducts = [...new Set(importData.map(c => c.product))];
    console.log(`Found ${uniqueProducts.length} unique products`);
    
    const insertProduct = db.prepare(`
      INSERT OR IGNORE INTO products (external_id, name, sku, is_active, metadata)
      VALUES (?, ?, ?, 1, ?)
    `);
    
    const getProduct = db.prepare(`
      SELECT id FROM products WHERE name = ?
    `);
    
    for (const productName of uniqueProducts) {
      const external_id = uuidv4();
      const metadata = JSON.stringify({ source: 'import', importDate: new Date().toISOString() });
      
      insertProduct.run(external_id, productName, productName, metadata);
      
      const product = getProduct.get(productName);
      if (product) {
        productMap.set(productName, product.id);
        console.log(`✓ Product: ${productName} (ID: ${product.id})`);
      }
    }
    
    // Insert colors and create relationships
    const insertColor = db.prepare(`
      INSERT OR IGNORE INTO colors (
        external_id, source_id, code, display_name, hex, 
        properties, search_terms, created_at, updated_at
      ) VALUES (?, 1, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const getColor = db.prepare(`
      SELECT id FROM colors WHERE external_id = ?
    `);
    
    const insertCMYK = db.prepare(`
      INSERT OR REPLACE INTO color_cmyk (color_id, c, m, y, k)
      VALUES (?, ?, ?, ?, ?)
    `);
    
    const insertRGB = db.prepare(`
      INSERT OR REPLACE INTO color_rgb (color_id, r, g, b)
      VALUES (?, ?, ?, ?)
    `);
    
    const insertProductColor = db.prepare(`
      INSERT OR IGNORE INTO product_colors (product_id, color_id, display_order)
      VALUES (?, ?, ?)
    `);
    
    let colorCount = 0;
    let relationCount = 0;
    
    for (const [index, color] of importData.entries()) {
      try {
        // Insert color
        const properties = {
          notes: color.notes,
          isLibrary: color.isLibrary,
          originalId: color.id
        };
        
        const searchTerms = [color.name, color.code, color.product].join(' ');
        
        insertColor.run(
          color.id, // Use existing ID as external_id
          color.code,
          color.name,
          color.hex,
          JSON.stringify(properties),
          searchTerms,
          color.createdAt,
          color.updatedAt
        );
        
        const colorRow = getColor.get(color.id);
        if (colorRow) {
          colorCount++;
          
          // Insert CMYK values
          try {
            const cmyk = parseCMYK(color.cmyk);
            insertCMYK.run(colorRow.id, cmyk.c, cmyk.m, cmyk.y, cmyk.k);
          } catch (e) {
            console.warn(`Failed to parse CMYK for ${color.code}: ${e.message}`);
          }
          
          // Insert RGB values
          try {
            const rgb = hexToRGB(color.hex);
            insertRGB.run(colorRow.id, rgb.r, rgb.g, rgb.b);
          } catch (e) {
            console.warn(`Failed to parse RGB for ${color.code}: ${e.message}`);
          }
          
          // Create product-color relationship
          const productId = productMap.get(color.product);
          if (productId) {
            insertProductColor.run(productId, colorRow.id, index);
            relationCount++;
          }
        }
        
        if (colorCount % 100 === 0) {
          console.log(`Progress: ${colorCount}/${importData.length} colors`);
        }
      } catch (error) {
        console.error(`Failed to import color ${color.code}:`, error);
      }
    }
    
    // Commit transaction
    db.exec('COMMIT');
    
    console.log('\n✅ Import completed successfully!');
    console.log(`- Imported ${colorCount} colors`);
    console.log(`- Created ${relationCount} product-color relationships`);
    
    // Verify the import
    const verifyProducts = db.prepare('SELECT COUNT(*) as count FROM products WHERE is_active = 1').get();
    const verifyColors = db.prepare('SELECT COUNT(*) as count FROM colors WHERE deleted_at IS NULL').get();
    const verifyRelations = db.prepare('SELECT COUNT(*) as count FROM product_colors').get();
    
    console.log('\nDatabase verification:');
    console.log(`- Total products: ${verifyProducts.count}`);
    console.log(`- Total colors: ${verifyColors.count}`);
    console.log(`- Total relationships: ${verifyRelations.count}`);
    
  } catch (error) {
    console.error('Import failed:', error);
    db.exec('ROLLBACK');
  } finally {
    db.close();
    app.quit();
  }
}

// Start the import
importData().catch(error => {
  console.error('Import error:', error);
  app.quit();
});

// Prevent Electron from opening windows
app.on('window-all-closed', () => {
  // Do nothing
});