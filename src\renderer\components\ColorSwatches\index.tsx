/**
 * @file ColorSwatches/index.tsx
 * @description Grid component for displaying color entries as swatches
 */

import { useMemo } from 'react';
import { useColorStore } from '../../store/color.store';
import ColorSwatch from './ColorSwatch';
import { useTokens } from '../../hooks/useTokens';
import { useFeatureFlags } from '../../context/FeatureFlagContext';
import { SwatchSkeleton } from '../ui/Skeleton';

export default function ColorSwatches() {
  const { colors, searchQuery, isLoading, error } = useColorStore();
  const tokens = useTokens();
  const { useNewTokenSystem } = useFeatureFlags?.() || { useNewTokenSystem: false };

  // Filter colors based on search query - updated to support product filter
  const filteredColors = useMemo(() => {
    if (!searchQuery) {return colors;}
    
    // Check if it's a product filter query
    if (searchQuery.toLowerCase().startsWith('product:')) {
      const productName = searchQuery.substring(8).replace(/"/g, '').trim();
      return colors.filter(color => 
        color.product.toLowerCase() === productName.toLowerCase()
      );
    }
    
    // Regular search query
    const query = searchQuery.toLowerCase();
    return colors.filter((color) => {
      return (
        color.product.toLowerCase().includes(query) ||
        color.name.toLowerCase().includes(query) ||
        color.code.toLowerCase().includes(query) ||
        color.hex.toLowerCase().includes(query) ||
        (color.notes && color.notes.toLowerCase().includes(query))
      );
    });
  }, [colors, searchQuery]);

  // Style classes with improved layout and spacing
  const containerClasses = "relative table-view-container w-full h-full";
  const contentContainerClasses = "bg-ui-background-primary shadow-md h-full flex flex-col";
  const scrollContainerClasses = "overflow-y-auto flex-1 scrollable-content";
  
  // Updated grid layout with consistent spacing and better alignment - always 5-6 columns on desktop
  const gridClasses = useNewTokenSystem
    ? `grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-6 gap-[var(--spacing-4)] p-[var(--spacing-4)]`
    : "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-6 gap-4 pt-1 pb-5 px-4";
  
  const textClasses = useNewTokenSystem
    ? "text-ui-foreground-secondary p-[var(--spacing-4)] text-center"
    : "text-ui-foreground-secondary dark:text-ui-foreground-secondary text-center p-[var(--spacing-4)]";
  
  const errorClasses = useNewTokenSystem
    ? "text-feedback-error p-[var(--spacing-4)] text-center"
    : "text-feedback-error dark:text-feedback-error text-center p-[var(--spacing-4)]";
  
  const _loadingContainerClasses = "flex justify-center py-8";
  const _spinnerClasses = "animate-spin rounded-full h-8 w-8 border-b-2 border-ui-border-dark dark:border-ui-border-dark";
  const emptyStateClasses = "text-center py-[var(--spacing-8)]";
  const helperTextClasses = useNewTokenSystem
    ? "text-xs text-ui-foreground-tertiary py-[var(--spacing-3)] px-[var(--spacing-5)] text-center border-t border-ui-border-light"
    : "text-xs text-ui-foreground-tertiary py-3 px-5 text-center border-t border-ui-border-light dark:border-ui-border-dark";

  // Check if we're in product filter mode
  const isProductMode = searchQuery && searchQuery.toLowerCase().startsWith('product:');
  const productName = isProductMode ? searchQuery.substring(8).replace(/"/g, '').trim() : null;

  // Use consistent styling across all swatches - optimized for 5-6 columns on desktop
  const gridContainerStyle = useNewTokenSystem ? {
    padding: tokens.spacing[4],
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fill, minmax(180px, 1fr))',
    gap: tokens.spacing[4],
    '@media (min-width: 1024px)': {
      gridTemplateColumns: 'repeat(5, 1fr)',
    },
    '@media (min-width: 1280px)': {
      gridTemplateColumns: 'repeat(6, 1fr)',
    }
  } : {};

  return (
    <div className={`${containerClasses} h-full flex flex-col`}>
      {isLoading ? (
        <div className={`${contentContainerClasses} flex flex-col h-full`}>
          <div className={scrollContainerClasses}>
            <div className={gridClasses} style={gridContainerStyle}>
              {Array.from({ length: 12 }).map((_, i) => (
                <SwatchSkeleton key={i} />
              ))}
            </div>
          </div>
        </div>
      ) : error ? (
        <div className={`${contentContainerClasses} flex flex-col h-full`}>
          <p className={errorClasses}>Error loading colors: {error}</p>
        </div>
      ) : filteredColors.length === 0 ? (
        <div className={`${contentContainerClasses} flex flex-col h-full`}>
          <div className={emptyStateClasses}>
            <p className={textClasses}>
              {isProductMode 
? `No colours found in product "${productName}".`
                : searchQuery 
? "No colours match your search query."
: "No colours found. Add your first colour using the form."
              }
            </p>
          </div>
        </div>
      ) : (
        <div className={`${contentContainerClasses} flex flex-col h-full`} style={{ marginTop: 0, padding: 0 }}>
          {isProductMode && (
            <div className={useNewTokenSystem 
              ? "py-[var(--spacing-3)] px-[var(--spacing-4)] bg-ui-background-secondary border-b border-ui-border-light"
              : "py-3 px-4 bg-ui-background-secondary dark:bg-zinc-800 border-b border-ui-border-light dark:border-gray-700"
            }>
              <h2 className="text-sm font-medium text-ui-foreground-primary dark:text-gray-100 flex items-center">
                <svg className="w-4 h-4 mr-1.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path>
                </svg>
                Product: {productName} ({filteredColors.length} colors)
              </h2>
            </div>
          )}
          <div className={scrollContainerClasses}>
            <div className="h-full">
              {useNewTokenSystem ? (
                <div className={gridClasses}>
                  {filteredColors.map((color) => (
                    <ColorSwatch key={color.id} entry={color} />
                  ))}
                </div>
              ) : (
                <div className={gridClasses}>
                  {filteredColors.map((color) => (
                    <ColorSwatch key={color.id} entry={color} />
                  ))}
                </div>
              )}
            </div>
          </div>
          <div className={helperTextClasses}>
            Click on color values to copy • Click on gradients to view details
            {isProductMode && (
              <span className="ml-2">• <button 
                className="text-brand-primary hover:text-brand-secondary underline focus:outline-none" 
                onClick={() => useColorStore.getState().setSearchQuery('')}
              >
                Clear product filter
              </button></span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
