import React, { useState, useMemo } from 'react';
import { 
  hexToHsl, 
  hslToHex, 
  hexToRgb, 
  rgbToHex
} from '../../../../../../shared/utils/color/conversion';
import { useColorStore } from '../../../../../store/color.store';
import { useToast } from '../../../../../hooks/useToast';
import { useTokens } from '../../../../../hooks/useTokens';
import { 
  Download, 
  Copy, 
  Palette,
  Sun,
  Moon,
  Contrast,
  Shuffle,
  Eye,
  EyeOff,
  Info
} from 'lucide-react';
import Tooltip from '../../../../../components/Tooltip';

interface BatchOperationsProps {
  colors: Array<{
    id: string;
    hex: string;
    name: string;
  }>;
}

interface ProcessedColor {
  id: string;
  hex: string;
  name: string;
  originalHex?: string;
}

type OperationType = 'lighten' | 'darken' | 'saturate' | 'desaturate' | 'hueShift' | 'invert';

interface OperationConfig {
  type: OperationType;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  hasValue: boolean;
  defaultValue: number;
  min?: number;
  max?: number;
  step?: number;
}

const operations: OperationConfig[] = [
  {
    type: 'lighten',
    label: 'Lighten',
    icon: Sun,
    description: 'Increase lightness',
    hasValue: true,
    defaultValue: 10,
    min: 0,
    max: 50,
    step: 5
  },
  {
    type: 'darken',
    label: 'Darken',
    icon: Moon,
    description: 'Decrease lightness',
    hasValue: true,
    defaultValue: 10,
    min: 0,
    max: 50,
    step: 5
  },
  {
    type: 'saturate',
    label: 'Saturate',
    icon: Contrast,
    description: 'Increase saturation',
    hasValue: true,
    defaultValue: 20,
    min: 0,
    max: 100,
    step: 10
  },
  {
    type: 'desaturate',
    label: 'Desaturate',
    icon: Contrast,
    description: 'Decrease saturation',
    hasValue: true,
    defaultValue: 20,
    min: 0,
    max: 100,
    step: 10
  },
  {
    type: 'hueShift',
    label: 'Hue Shift',
    icon: Shuffle,
    description: 'Rotate hue',
    hasValue: true,
    defaultValue: 30,
    min: -180,
    max: 180,
    step: 15
  },
  {
    type: 'invert',
    label: 'Invert',
    icon: Palette,
    description: 'Invert colors',
    hasValue: false,
    defaultValue: 0
  }
];

export default function BatchOperations({ colors }: BatchOperationsProps) {
  const { toast } = useToast();
  const _tokens = useTokens();
  const { addColor } = useColorStore();
  
  const [selectedOperation, setSelectedOperation] = useState<OperationType>('lighten');
  const [operationValue, setOperationValue] = useState<number>(10);
  const [previewMode, setPreviewMode] = useState(true);

  const operation = operations.find(op => op.type === selectedOperation)!;

  const processedColors = useMemo((): ProcessedColor[] => {
    if (!previewMode) {return colors;}

    return colors.map(color => {
      const processed = applyOperation(color.hex, selectedOperation, operationValue);
      return {
        ...color,
        originalHex: color.hex,
        hex: processed
      };
    });
  }, [colors, selectedOperation, operationValue, previewMode]);

  const handleApply = async () => {
    try {
      const newColors = colors.map(color => {
        const processedHex = applyOperation(color.hex, selectedOperation, operationValue);
        if (!processedHex) {
          return null;
        }
        return {
          name: `${color.name} (${operation.label})`,
          hex: processedHex,
          rgb: hexToRgb(processedHex),
          product_id: null
        };
      }).filter(Boolean);

      // Add colors to store
      for (const color of newColors) {
        if (color && color.rgb) {
          await addColor({
            product: 'Custom',
            name: color.name,
            code: color.hex.toUpperCase(),
            hex: color.hex,
            cmyk: '0,0,0,0', // Default CMYK
            rgb: color.rgb
          });
        }
      }

      toast({
        title: 'Colors created',
        description: `Successfully created ${newColors.length} new colors`
      });
    } catch (_error) {
      toast({
        title: 'Error',
        description: 'Failed to create colors'
      });
    }
  };

  const handleExport = () => {
    const exportData = processedColors.map(color => ({
      name: color.name,
      original: color.originalHex || color.hex,
      modified: color.hex,
      operation: `${operation.label} ${operation.hasValue ? operationValue : ''}`
    }));

    const csv = [
      'Name,Original,Modified,Operation',
      ...exportData.map(row => 
        `"${row.name}","${row.original}","${row.modified}","${row.operation}"`
      )
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `batch-colors-${selectedOperation}-${Date.now()}.csv`;
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="h-full overflow-y-auto p-4 space-y-4">
      {/* Color Preview at top */}
      <div className="bg-ui-background-secondary rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-medium">
            {previewMode ? 'Preview' : 'Original Colors'}
          </h4>
          <button
            onClick={() => setPreviewMode(!previewMode)}
            className="flex items-center gap-1 px-2 py-1 text-xs rounded-md border border-ui-border-light hover:bg-ui-background-tertiary transition-colors"
          >
            {previewMode ? <Eye className="w-3 h-3" /> : <EyeOff className="w-3 h-3" />}
            <span>{previewMode ? 'Preview On' : 'Preview Off'}</span>
          </button>
        </div>
        
        {previewMode ? (
          <div className="space-y-4">
            {/* Original colors row */}
            <div>
              <p className="text-xs font-medium text-ui-foreground-secondary mb-2">Original</p>
              <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-3">
                {colors.slice(0, 10).map((color) => (
                  <div key={`orig-${color.id}`} className="space-y-1">
                    <div
                      className="w-full h-12 rounded-md border-2 border-ui-border-light hover:border-ui-border-medium transition-colors cursor-pointer"
                      style={{ backgroundColor: color.hex }}
                    />
                    <p className="text-xs text-center text-ui-foreground-primary font-medium truncate">
                      {color.name ? color.name.split('-')[0].trim() : color.hex}
                    </p>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Arrow indicator */}
            <div className="flex justify-center py-1">
              <svg className="w-5 h-5 text-ui-foreground-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
            </div>
            
            {/* Modified colors row */}
            <div>
              <p className="text-xs font-medium text-ui-foreground-secondary mb-2">Modified ({operation.label})</p>
              <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-3">
                {processedColors.slice(0, 10).map((color) => (
                  <div key={`mod-${color.id}`} className="space-y-1">
                    <div
                      className="w-full h-12 rounded-md border-2 border-ui-border-light hover:border-brand-primary transition-colors cursor-pointer"
                      style={{ backgroundColor: color.hex }}
                    />
                    <p className="text-xs text-center text-ui-foreground-primary font-medium truncate">
                      {color.name ? color.name.split('-')[0].trim() : color.hex}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ) : (
          /* Just show original colors when preview is off */
          <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 gap-3">
            {colors.slice(0, 15).map((color) => (
              <div key={color.id} className="space-y-1">
                <div
                  className="w-full h-12 rounded-md border-2 border-ui-border-light hover:border-ui-border-medium transition-colors cursor-pointer"
                  style={{ backgroundColor: color.hex }}
                />
                <p className="text-xs text-center text-ui-foreground-primary font-medium truncate">
                  {color.name ? color.name.split('-')[0].trim() : color.hex}
                </p>
              </div>
            ))}
          </div>
        )}
        
        {colors.length > 10 && previewMode && (
          <p className="text-xs text-ui-foreground-secondary mt-2 text-center">
            Showing {Math.min(10, colors.length)} of {colors.length} colors
          </p>
        )}
      </div>

      {/* Operation Selection and Controls in one row */}
      <div className="flex gap-3 bg-ui-background-secondary rounded-lg p-3">
        {/* Left side - Operation buttons (50%) */}
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="text-sm font-medium">Operations</h3>
            <Tooltip content="Select an operation to apply to all colors" position="right">
              <Info className="w-3 h-3 text-ui-foreground-secondary" />
            </Tooltip>
          </div>
          <div className="flex flex-wrap gap-1">
            {operations.map(op => {
              const Icon = op.icon;
              return (
                <Tooltip key={op.type} content={op.description} position="bottom">
                  <button
                    onClick={() => {
                      setSelectedOperation(op.type);
                      setOperationValue(op.defaultValue);
                    }}
                    className={`
                      px-2 py-1.5 rounded-md border transition-all flex items-center gap-1
                      ${selectedOperation === op.type
                        ? 'border-brand-primary bg-brand-primary/10'
                        : 'border-ui-border-light hover:border-ui-border-medium'
                      }
                    `}
                  >
                    <Icon className="w-3 h-3" />
                    <span className="text-xs">{op.label}</span>
                  </button>
                </Tooltip>
              );
            })}
          </div>
        </div>

        {/* Right side - Slider controls (50%) */}
        <div className="flex-1 border-l border-ui-border-light pl-3">
          {operation.hasValue ? (
            <>
              <label className="text-sm font-medium flex items-center gap-1 mb-2">
                {operation.label}
                <span className="text-xs text-ui-foreground-secondary">
                  ({operationValue}{operation.type === 'hueShift' ? '°' : '%'})
                </span>
              </label>
              <div className="relative">
                <input
                  type="range"
                  min={operation.min}
                  max={operation.max}
                  step={operation.step}
                  value={operationValue}
                  onChange={(e) => setOperationValue(Number(e.target.value))}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-ui-foreground-secondary mt-1">
                  <span>{operation.min}{operation.type === 'hueShift' ? '°' : '%'}</span>
                  <span>{operation.max}{operation.type === 'hueShift' ? '°' : '%'}</span>
                </div>
              </div>
            </>
          ) : (
            <div className="text-sm text-ui-foreground-secondary">
              {operation.description}
            </div>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="flex gap-2 pt-2">
        <button
          onClick={handleApply}
          disabled={!previewMode}
          className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-brand-primary text-white rounded-lg hover:bg-brand-primary-dark disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-sm"
        >
          <Copy className="w-4 h-4" />
          <span>Create Colors</span>
        </button>
        <Tooltip content="Export processed colors as CSV" position="top">
          <button
            onClick={handleExport}
            className="px-3 py-2 border border-ui-border-light rounded-lg hover:bg-ui-background-secondary transition-colors"
          >
            <Download className="w-4 h-4" />
          </button>
        </Tooltip>
      </div>
    </div>
  );
}

function applyOperation(hex: string, operation: OperationType, value: number): string {
  const hsl = hexToHsl(hex);
  const rgb = hexToRgb(hex);

  if (!hsl) {
    // If HSL conversion fails, return original color
    return hex;
  }

  switch (operation) {
    case 'lighten':
      hsl.l = Math.min(100, hsl.l + value);
      return hslToHex(hsl);

    case 'darken':
      hsl.l = Math.max(0, hsl.l - value);
      return hslToHex(hsl);

    case 'saturate':
      hsl.s = Math.min(100, hsl.s + value);
      return hslToHex(hsl);

    case 'desaturate':
      hsl.s = Math.max(0, hsl.s - value);
      return hslToHex(hsl);

    case 'hueShift':
      hsl.h = (hsl.h + value + 360) % 360;
      return hslToHex(hsl);

    case 'invert':
      if (rgb) {
        const inverted = {
          r: 255 - rgb.r,
          g: 255 - rgb.g,
          b: 255 - rgb.b
        };
        return rgbToHex(inverted);
      }
      return hex;

    default:
      return hex;
  }
}