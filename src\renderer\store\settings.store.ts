/**
 * @file settings.store.ts
 * @description Zustand store for managing application settings
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type Theme = 'light' | 'dark' | 'system' | 'new-york';
type LightThemeOption = 'light' | 'system-light';
type DarkThemeOption = 'dark' | 'new-york';
type ImportExportFormat = 'json' | 'csv';
type ConflictStrategy = 'local' | 'remote' | 'newest';

interface SettingsState {
  // Theme settings
  theme: Theme;
  setTheme: (theme: Theme) => void;
  preferredLightTheme: LightThemeOption;
  setPreferredLightTheme: (theme: LightThemeOption) => void;
  preferredDarkTheme: DarkThemeOption;
  setPreferredDarkTheme: (theme: DarkThemeOption) => void;

  // Logo settings
  logoPath: string | null;
  setLogoPath: (path: string | null) => void;

  // Import/Export settings
  defaultImportFormat: ImportExportFormat;
  defaultExportFormat: ImportExportFormat;
  setDefaultImportFormat: (format: ImportExportFormat) => void;
  setDefaultExportFormat: (format: ImportExportFormat) => void;

  // Sync settings
  autoSyncInterval: number; // in minutes, 0 = disabled
  autoSyncOnStartup: boolean;
  syncConflictStrategy: ConflictStrategy;
  dataRetentionDays: number; // 0 = keep forever
  enableRealtimeSync: boolean;
  setAutoSyncInterval: (minutes: number) => void;
  setAutoSyncOnStartup: (enabled: boolean) => void;
  setSyncConflictStrategy: (strategy: ConflictStrategy) => void;
  setDataRetentionDays: (days: number) => void;
  setEnableRealtimeSync: (enabled: boolean) => void;

  // Advanced settings
  showDevTools: boolean;
  setShowDevTools: (show: boolean) => void;

  // Reset all settings
  resetSettings: () => void;
}

const defaultSettings = {
  theme: 'light' as Theme,
  preferredLightTheme: 'light' as LightThemeOption,
  preferredDarkTheme: 'dark' as DarkThemeOption,
  logoPath: null,
  defaultImportFormat: 'json' as ImportExportFormat,
  defaultExportFormat: 'json' as ImportExportFormat,
  autoSyncInterval: 60, // 1 hour
  autoSyncOnStartup: true,
  syncConflictStrategy: 'newest' as ConflictStrategy,
  dataRetentionDays: 0, // keep forever
  enableRealtimeSync: false,
  showDevTools: false,
};

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set) => ({
      ...defaultSettings,

      // Theme settings
      setTheme: (theme) => set({ theme }),
      setPreferredLightTheme: (preferredLightTheme) => set({ preferredLightTheme }),
      setPreferredDarkTheme: (preferredDarkTheme) => set({ preferredDarkTheme }),

      // Logo settings
      setLogoPath: (logoPath) => set({ logoPath }),

      // Import/Export settings
      setDefaultImportFormat: (defaultImportFormat) => set({ defaultImportFormat }),
      setDefaultExportFormat: (defaultExportFormat) => set({ defaultExportFormat }),

      // Sync settings
      setAutoSyncInterval: (autoSyncInterval) => set({ autoSyncInterval }),
      setAutoSyncOnStartup: (autoSyncOnStartup) => set({ autoSyncOnStartup }),
      setSyncConflictStrategy: (syncConflictStrategy) => set({ syncConflictStrategy }),
      setDataRetentionDays: (dataRetentionDays) => set({ dataRetentionDays }),
      setEnableRealtimeSync: (enableRealtimeSync) => set({ enableRealtimeSync }),

      // Advanced settings
      setShowDevTools: (showDevTools) => set({ showDevTools }),

      // Reset all settings
      resetSettings: () => set(defaultSettings),
    }),
    {
      name: 'settings-storage',
    }
  )
);
