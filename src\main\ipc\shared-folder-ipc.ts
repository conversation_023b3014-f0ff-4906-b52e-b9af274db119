import { ipcMain } from 'electron';
import { SharedFolderManager } from '../shared-folder';

export function setupSharedFolderIPC(sharedFolderManager: SharedFolderManager): void {
  // Get shared folder path
  ipcMain.handle('shared-folder:get-path', async () => {
    return sharedFolderManager.getPath();
  });

  // Ensure shared folder exists
  ipcMain.handle('shared-folder:ensure-exists', async () => {
    return await sharedFolderManager.ensureExists();
  });

  // Read file from shared folder
  ipcMain.handle('shared-folder:read-file', async (_, fileName: string) => {
    try {
      return await sharedFolderManager.readFile(fileName);
    } catch (error) {
      console.error(`IPC: Failed to read file ${fileName}:`, error);
      throw error;
    }
  });

  // Write file to shared folder
  ipcMain.handle('shared-folder:write-file', async (_, fileName: string, content: string) => {
    try {
      return await sharedFolderManager.writeFile(fileName, content);
    } catch (error) {
      console.error(`IPC: Failed to write file ${fileName}:`, error);
      return false;
    }
  });

  // List files in shared folder
  ipcMain.handle('shared-folder:list-files', async () => {
    try {
      return await sharedFolderManager.listFiles();
    } catch (error) {
      console.error('IPC: Failed to list files:', error);
      return [];
    }
  });
} 