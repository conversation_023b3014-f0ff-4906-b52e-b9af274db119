CREATE TABLE products (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL
);
CREATE TABLE product_selections (
  id TEXT PRIMARY KEY,
  product_id TEXT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (product_id) REFERENCES products(id)
);
CREATE TABLE colors (
  id TEXT PRIMARY KEY,
  product TEXT NOT NULL,
  flavour TEXT NOT NULL,
  pantone TEXT NOT NULL,
  hex TEXT NOT NULL,
  cmyk TEXT NOT NULL,
  notes TEXT,
  gradient TEXT,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL
);
CREATE TABLE selection_colors (
  id TEXT PRIMARY KEY,
  selection_id TEXT NOT NULL,
  color_id TEXT NOT NULL,
  created_at TEXT NOT NULL,
  updated_at TEXT NOT NULL,
  FOREIGN KEY (selection_id) REFERENCES product_selections(id),
  FOREIGN KEY (color_id) REFERENCES colors(id)
);
CREATE INDEX idx_product_selections_product_id ON product_selections(product_id);
CREATE INDEX idx_selection_colors_selection_id ON selection_colors(selection_id);
CREATE INDEX idx_selection_colors_color_id ON selection_colors(color_id);
