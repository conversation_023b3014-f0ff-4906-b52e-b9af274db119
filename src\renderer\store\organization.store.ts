/**
 * @file organization.store.ts
 * @description Zustand store for organization state management
 */

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { Organization, OrganizationMember } from '@shared/types/organization.types';

interface OrganizationState {
  // Current organization context
  currentOrganization: Organization | null;
  organizations: Organization[];
  
  // Team members
  members: OrganizationMember[];
  
  // Loading states
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setCurrentOrganization: (org: Organization | null) => void;
  setOrganizations: (orgs: Organization[]) => void;
  loadOrganizations: () => Promise<void>;
  loadCurrentOrganization: () => Promise<void>;
  createOrganization: (name: string) => Promise<{ success: boolean; data?: Organization; error?: string }>;
  switchOrganization: (orgId: string) => Promise<{ success: boolean; error?: string }>;
  loadMembers: () => Promise<void>;
  inviteMember: (email: string, role?: string) => Promise<{ success: boolean; error?: string }>;
  removeMember: (userId: string) => Promise<{ success: boolean; error?: string }>;
  updateMemberRole: (userId: string, role: string) => Promise<{ success: boolean; error?: string }>;
  deleteOrganization: (orgId: string) => Promise<{ success: boolean; error?: string }>;
  reset: () => void;
}

export const useOrganizationStore = create<OrganizationState>()(
  devtools(
    (set, get) => ({
      // Initial state
      currentOrganization: null,
      organizations: [],
      members: [],
      isLoading: false,
      error: null,
      
      // Set current organization
      setCurrentOrganization: (org) => {
        set({ currentOrganization: org, error: null });
        
        // Persist the selected organization
        if (org) {
          localStorage.setItem('chromasync:lastOrganization', org.external_id);
          // This will trigger color and product stores to reload with new org context
          window.organizationAPI.setCurrentOrganization(org.external_id);
        } else {
          localStorage.removeItem('chromasync:lastOrganization');
        }
      },
      
      // Set organizations list
      setOrganizations: (orgs) => {
        set({ organizations: orgs });
      },
      
      // Load user's organizations
      loadOrganizations: async () => {
        set({ isLoading: true, error: null });
        
        try {
          const result = await window.organizationAPI.getOrganizations();
          
          if (result.success) {
            set({ 
              organizations: result.data, 
              isLoading: false 
            });
            
            // If there's only one organization, auto-select it
            if (result.data.length === 1 && !get().currentOrganization) {
              get().setCurrentOrganization(result.data[0]);
            }
          } else {
            set({ 
              error: result.error || 'Failed to load organizations', 
              isLoading: false 
            });
          }
        } catch (error) {
          set({ 
            error: 'Failed to load organizations', 
            isLoading: false 
          });
        }
      },
      
      // Load current organization from backend
      loadCurrentOrganization: async () => {
        try {
          const result = await window.organizationAPI.getCurrentOrganization();
          
          if (result.success && result.data) {
            set({ 
              currentOrganization: result.data,
              error: null 
            });
            
            // Persist the organization
            if (result.data.external_id) {
              localStorage.setItem('chromasync:lastOrganization', result.data.external_id);
            }
            
            // Load members for the organization
            await get().loadMembers();
          }
        } catch (error) {
          console.error('Failed to load current organization:', error);
        }
      },
      
      // Create new organization
      createOrganization: async (name: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const result = await window.organizationAPI.createOrganization({ name });
          
          if (result.success && result.data) {
            const newOrg = result.data;
            
            // Add to organizations list
            set({ 
              organizations: [...get().organizations, newOrg],
              currentOrganization: newOrg,
              isLoading: false 
            });
            
            // Reload other stores with new organization context
            await window.organizationAPI.setCurrentOrganization(newOrg.external_id);
            
            // Force reload of colors and products with the new organization context
            try {
              const { fetchColors } = await import('./color.store').then(m => m.useColorStore.getState());
              const { fetchProducts } = await import('./product.store').then(m => m.useProductStore.getState());
              
              await Promise.all([
                fetchColors(),
                fetchProducts()
              ]);
            } catch (error) {
              console.error('[OrganizationStore] Error reloading data:', error);
            }
            
            // Initialize sync for the new organization
            try {
              await window.syncAPI.initialize();
              console.log('[OrganizationStore] Sync initialized for new organization:', newOrg.external_id);
            } catch (syncError) {
              console.warn('[OrganizationStore] Failed to initialize sync for new organization:', syncError);
            }
            
            return { success: true, data: newOrg };
          } else {
            set({ 
              error: result.error || 'Failed to create organization', 
              isLoading: false 
            });
            return { success: false, error: result.error };
          }
        } catch (error) {
          const errorMsg = 'Failed to create organization';
          set({ error: errorMsg, isLoading: false });
          return { success: false, error: errorMsg };
        }
      },
      
      // Switch to different organization
      switchOrganization: async (orgId: string) => {
        // First try to find in our local organizations
        let org = get().organizations.find(o => o.external_id === orgId);
        
        // If not found locally, try to fetch from backend
        if (!org) {
          try {
            const orgResult = await window.organizationAPI.getOrganizations();
            if (orgResult.success && orgResult.data) {
              // Update our local store with fresh data
              set({ organizations: orgResult.data });
              // Try to find again
              org = orgResult.data.find((o: Organization) => o.external_id === orgId);
            }
          } catch (error) {
            console.error('Failed to fetch organizations:', error);
          }
        }
        
        if (!org) {
          set({ error: 'Organization not found' });
          return { success: false, error: 'Organization not found' };
        }
        
        set({ isLoading: true, error: null });
        
        try {
          await window.organizationAPI.setCurrentOrganization(orgId);
          set({ 
            currentOrganization: org, 
            isLoading: false 
          });
          
          // Reload members for new organization
          await get().loadMembers();
          
          // Trigger other stores to reload with new context
          const { fetchColors } = await import('./color.store').then(m => m.useColorStore.getState());
          const { fetchProductsWithColors } = await import('./product.store').then(m => m.useProductStore.getState());
          const { syncData } = await import('./sync.store').then(m => m.useSyncStore.getState());
          
          // Initialize sync for the new organization and trigger full sync
          try {
            await window.syncAPI.initialize();
            console.log('[OrganizationStore] Sync initialized for organization:', orgId);
            
            // Trigger a full sync to get all data from Supabase
            console.log('[OrganizationStore] Triggering full sync for new organization...');
            await syncData();
            console.log('[OrganizationStore] Sync completed');
          } catch (syncError) {
            console.warn('[OrganizationStore] Failed to initialize/sync:', syncError);
          }
          
          // Then fetch the synced data
          await Promise.all([
            fetchColors(),
            fetchProductsWithColors()
          ]);
          
          return { success: true };
        } catch (error) {
          const errorMsg = 'Failed to switch organization';
          set({ 
            error: errorMsg, 
            isLoading: false 
          });
          return { success: false, error: errorMsg };
        }
      },
      
      // Load organization members
      loadMembers: async () => {
        const currentOrg = get().currentOrganization;
        if (!currentOrg) {return;}
        
        set({ isLoading: true, error: null });
        
        try {
          const result = await window.organizationAPI.getMembers(currentOrg.external_id);
          
          if (result.success) {
            set({ 
              members: result.data, 
              isLoading: false 
            });
          } else {
            set({ 
              error: result.error || 'Failed to load members', 
              isLoading: false 
            });
          }
        } catch (error) {
          set({ 
            error: 'Failed to load members', 
            isLoading: false 
          });
        }
      },
      
      // Invite new member
      inviteMember: async (email: string, role: string = 'member') => {
        const currentOrg = get().currentOrganization;
        if (!currentOrg) {
          set({ error: 'No organization selected' });
          return { success: false, error: 'No organization selected' };
        }
        
        set({ isLoading: true, error: null });
        
        try {
          const result = await window.organizationAPI.inviteMember({
            organizationId: currentOrg.external_id,
            email,
            role
          });
          
          if (result.success) {
            await get().loadMembers();
            set({ isLoading: false });
            return { success: true };
          } else {
            const errorMsg = result.error || 'Failed to invite member';
            set({ 
              error: errorMsg, 
              isLoading: false 
            });
            return { success: false, error: errorMsg };
          }
        } catch (error) {
          const errorMsg = 'Failed to invite member';
          set({ 
            error: errorMsg, 
            isLoading: false 
          });
          return { success: false, error: errorMsg };
        }
      },
      
      // Remove member
      removeMember: async (userId: string) => {
        const currentOrg = get().currentOrganization;
        if (!currentOrg) {
          set({ error: 'No organization selected' });
          return { success: false, error: 'No organization selected' };
        }
        
        set({ isLoading: true, error: null });
        
        try {
          const result = await window.organizationAPI.removeMember({
            organizationId: currentOrg.external_id,
            userId
          });
          
          if (result.success) {
            await get().loadMembers();
            set({ isLoading: false });
            return { success: true };
          } else {
            const errorMsg = result.error || 'Failed to remove member';
            set({ 
              error: errorMsg, 
              isLoading: false 
            });
            return { success: false, error: errorMsg };
          }
        } catch (error) {
          const errorMsg = 'Failed to remove member';
          set({ 
            error: errorMsg, 
            isLoading: false 
          });
          return { success: false, error: errorMsg };
        }
      },
      
      // Update member role
      updateMemberRole: async (userId: string, role: string) => {
        const currentOrg = get().currentOrganization;
        if (!currentOrg) {
          set({ error: 'No organization selected' });
          return { success: false, error: 'No organization selected' };
        }
        
        set({ isLoading: true, error: null });
        
        try {
          const result = await window.organizationAPI.updateMemberRole({
            organizationId: currentOrg.external_id,
            userId,
            role
          });
          
          if (result.success) {
            await get().loadMembers();
            set({ isLoading: false });
            return { success: true };
          } else {
            const errorMsg = result.error || 'Failed to update member role';
            set({ 
              error: errorMsg, 
              isLoading: false 
            });
            return { success: false, error: errorMsg };
          }
        } catch (error) {
          const errorMsg = 'Failed to update member role';
          set({ 
            error: errorMsg, 
            isLoading: false 
          });
          return { success: false, error: errorMsg };
        }
      },
      
      // Update organization
      updateOrganization: async (orgId: string, updates: Partial<Organization>) => {
        set({ isLoading: true, error: null });
        
        try {
          const result = await window.organizationAPI.updateSettings({
            organizationId: orgId,
            settings: updates
          });
          
          if (result.success) {
            // Update in local state
            const updatedOrgs = get().organizations.map(org => 
              org.external_id === orgId 
                ? { ...org, ...updates }
                : org
            );
            
            set({ 
              organizations: updatedOrgs,
              isLoading: false 
            });
            
            // Update current organization if it's the one being updated
            if (get().currentOrganization?.external_id === orgId) {
              set({ 
                currentOrganization: { 
                  ...get().currentOrganization!, 
                  ...updates 
                } 
              });
            }
            
            return { success: true };
          } else {
            const errorMsg = result.error || 'Failed to update organization';
            set({ 
              error: errorMsg, 
              isLoading: false 
            });
            return { success: false, error: errorMsg };
          }
        } catch (error) {
          const errorMsg = 'Failed to update organization';
          set({ 
            error: errorMsg, 
            isLoading: false 
          });
          return { success: false, error: errorMsg };
        }
      },
      
      // Delete organization
      deleteOrganization: async (orgId: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const result = await window.organizationAPI.deleteOrganization(orgId);
          
          if (result.success) {
            // Remove from organizations list
            const orgs = get().organizations.filter(org => org.external_id !== orgId);
            set({ 
              organizations: orgs,
              isLoading: false 
            });
            
            // If this was the current organization, clear it
            if (get().currentOrganization?.external_id === orgId) {
              set({ currentOrganization: null });
              localStorage.removeItem('chromasync:lastOrganization');
            }
            
            return { success: true };
          } else {
            const errorMsg = result.error || 'Failed to delete organization';
            set({ 
              error: errorMsg, 
              isLoading: false 
            });
            return { success: false, error: errorMsg };
          }
        } catch (error) {
          const errorMsg = 'Failed to delete organization';
          set({ 
            error: errorMsg, 
            isLoading: false 
          });
          return { success: false, error: errorMsg };
        }
      },
      
      // Reset store
      reset: () => {
        set({
          currentOrganization: null,
          organizations: [],
          members: [],
          isLoading: false,
          error: null
        });
      }
    }),
    {
      name: 'organization-store'
    }
  )
);
// Selectors for backward compatibility and convenience
export const useCurrentOrg = () => useOrganizationStore(state => state.currentOrganization);
export const useOrganizations = () => useOrganizationStore(state => state.organizations);
export const useOrganizationMembers = () => useOrganizationStore(state => state.members);

// Export the store with both naming conventions
export const useOrganizationStoreWithAliases = () => {
  const store = useOrganizationStore();
  return {
    ...store,
    currentOrg: store.currentOrganization, // Alias for components expecting currentOrg
    // Add joinOrganization method
    joinOrganization: async (inviteCode: string) => {
      // TODO: Implement join by invite code
      console.log('Join organization with code:', inviteCode);
      return { success: false, error: 'Not implemented yet' };
    }
  };
};
