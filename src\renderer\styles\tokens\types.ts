/**
 * @file types.ts
 * @description TypeScript interfaces for the design token system
 */

export interface ColorTokens {
  brand: {
    primary: string;
    secondary: string;
    accent: string;
  };
  ui: {
    background: {
      primary: string;
      secondary: string;
      tertiary: string;
    };
    foreground: {
      primary: string;
      secondary: string;
      tertiary: string;
      inverse: string;
    };
    border: {
      light: string;
      medium: string;
      dark: string;
    };
    focus: string;
  };
  feedback: {
    success: string;
    warning: string;
    error: string;
    info: string;
  };
}

export interface TypographyTokens {
  fontFamily: {
    sans: string[];
    mono: string[];
  };
  fontSize: {
    xs: string;
    sm: string;
    base: string;
    lg: string;
    xl: string;
    '2xl': string;
    '3xl': string;
    '4xl': string;
  };
  fontWeight: {
    normal: string;
    medium: string;
    semibold: string;
    bold: string;
  };
  lineHeight: {
    none: string;
    tight: string;
    snug: string;
    normal: string;
    relaxed: string;
    loose: string;
  };
}

export interface SpacingTokens {
  px: string;
  0: string;
  0.5: string;
  1: string;
  1.5: string;
  2: string;
  2.5: string;
  3: string;
  4: string;
  5: string;
  6: string;
  8: string;
  10: string;
  12: string;
  16: string;
  20: string;
  24: string;
  [key: string]: string;
}

export interface BorderRadiusTokens {
  none: string;
  sm: string;
  DEFAULT: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
  full: string;
}

export interface ShadowTokens {
  sm: string;
  DEFAULT: string;
  md: string;
  lg: string;
  xl: string;
}

export interface TransitionTokens {
  duration: {
    75: string;
    100: string;
    150: string;
    200: string;
    300: string;
    500: string;
    700: string;
    1000: string;
  };
  easing: {
    linear: string;
    in: string;
    out: string;
    inOut: string;
    apple: string;
  };
}

export interface ZIndexTokens {
  0: string;
  10: string;
  20: string;
  30: string;
  40: string;
  50: string;
  auto: string;
  dropdown: string;
  modal: string;
  tooltip: string;
}

export interface BreakpointTokens {
  sm: string;
  md: string;
  lg: string;
  xl: string;
  '2xl': string;
}

// Main tokens interface that combines all token categories
export interface DesignTokens {
  colors: ColorTokens;
  typography: TypographyTokens;
  spacing: SpacingTokens;
  borderRadius: BorderRadiusTokens;
  shadows: ShadowTokens;
  transitions: TransitionTokens;
  zIndex: ZIndexTokens;
  breakpoints: BreakpointTokens;
} 