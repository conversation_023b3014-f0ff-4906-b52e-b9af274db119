#!/usr/bin/env node

// Test script for Zoho Email OAuth integration
const axios = require('axios');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

// Load environment variables
const CLIENT_ID = process.env.ZOHO_CLIENT_ID;
const CLIENT_SECRET = process.env.ZOHO_CLIENT_SECRET;
const ACCOUNT_ID = process.env.ZOHO_ACCOUNT_ID;
const REFRESH_TOKEN = process.env.ZOHO_REFRESH_TOKEN;
const REGION = process.env.ZOHO_REGION || 'COM';
const SUPPORT_ALIAS = process.env.ZOHO_SUPPORT_ALIAS || '<EMAIL>';

// Determine Zoho domains based on region
const authDomain = REGION === 'EU' ? 'accounts.zoho.eu' : 'accounts.zoho.com';
const apiDomain = REGION === 'EU' ? 'mail.zoho.eu' : 'mail.zoho.com';

console.log('🔧 Zoho Email OAuth Test\n');
console.log('Configuration:');
console.log(`- Region: ${REGION}`);
console.log(`- Auth Domain: ${authDomain}`);
console.log(`- API Domain: ${apiDomain}`);
console.log(`- Account ID: ${ACCOUNT_ID}`);
console.log(`- Support Alias: ${SUPPORT_ALIAS}\n`);

let accessToken = null;

async function refreshAccessToken() {
  console.log('🔄 Refreshing access token...');
  
  try {
    const response = await axios.post(
      `https://${authDomain}/oauth/v2/token`,
      null,
      {
        params: {
          refresh_token: REFRESH_TOKEN,
          client_id: CLIENT_ID,
          client_secret: CLIENT_SECRET,
          grant_type: 'refresh_token'
        }
      }
    );

    accessToken = response.data.access_token;
    console.log('✅ Access token refreshed successfully');
    console.log(`   Token expires in: ${response.data.expires_in} seconds\n`);
    
    return accessToken;
  } catch (error) {
    console.error('❌ Failed to refresh token:', error.response?.data || error.message);
    throw error;
  }
}

async function sendTestEmail() {
  console.log('📧 Sending test email...');
  
  const emailData = {
    fromAddress: SUPPORT_ALIAS,
    toAddress: '<EMAIL>',
    subject: '🎨 ChromaSync Email Test - OAuth Integration',
    content: `<html>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; text-align: center; border-radius: 8px 8px 0 0;">
    <h1 style="color: white; margin: 0;">🎨 ChromaSync</h1>
    <p style="color: white; margin: 5px 0;">Email Service Test</p>
  </div>
  
  <div style="padding: 30px; background: #f8f9fa;">
    <h2>✅ Email Configuration Working!</h2>
    
    <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h3>Configuration Details:</h3>
      <ul>
        <li><strong>Region:</strong> ${REGION}</li>
        <li><strong>API Domain:</strong> ${apiDomain}</li>
        <li><strong>Account ID:</strong> ${ACCOUNT_ID}</li>
        <li><strong>From Address:</strong> ${SUPPORT_ALIAS}</li>
        <li><strong>Timestamp:</strong> ${new Date().toLocaleString()}</li>
      </ul>
    </div>
    
    <p>This test email confirms that your Zoho Mail OAuth integration is working correctly. You can now send team invitation emails through ChromaSync!</p>
    
    <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">
    
    <p style="text-align: center; color: #666; font-size: 14px;">
      ChromaSync - Professional Color Management<br>
      <a href="mailto:<EMAIL>" style="color: #667eea;"><EMAIL></a>
    </p>
  </div>
</body>
</html>`,
    mailFormat: 'html'
  };

  try {
    const response = await axios.post(
      `https://${apiDomain}/api/accounts/${ACCOUNT_ID}/messages`,
      emailData,
      {
        headers: {
          'Authorization': `Zoho-oauthtoken ${accessToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('✅ Email sent successfully!');
    console.log(`   Message ID: ${response.data.data?.messageId || 'N/A'}`);
    console.log(`   To: ${emailData.toAddress}`);
    console.log(`   Subject: ${emailData.subject}\n`);
  } catch (error) {
    console.error('❌ Failed to send email:', error.response?.data || error.message);
    
    if (error.response?.status === 401) {
      console.error('\n⚠️  Authentication error - token may be invalid');
    } else if (error.response?.status === 400) {
      console.error('\n⚠️  Bad request - check if the support alias is configured in Zoho');
    }
    
    throw error;
  }
}

async function testEmailService() {
  try {
    // Check configuration
    if (!CLIENT_ID || !CLIENT_SECRET || !ACCOUNT_ID || !REFRESH_TOKEN) {
      console.error('❌ Missing required environment variables:');
      if (!CLIENT_ID) console.error('   - ZOHO_CLIENT_ID');
      if (!CLIENT_SECRET) console.error('   - ZOHO_CLIENT_SECRET');
      if (!ACCOUNT_ID) console.error('   - ZOHO_ACCOUNT_ID');
      if (!REFRESH_TOKEN) console.error('   - ZOHO_REFRESH_TOKEN');
      process.exit(1);
    }

    // Refresh token and send test email
    await refreshAccessToken();
    await sendTestEmail();
    
    console.log('🎉 Email service test completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Check your inbox for the test email');
    console.log('2. If you haven\'t already, <NAME_EMAIL> alias in Zoho Mail');
    console.log('3. Run the ChromaSync app to test team invitation emails\n');
    
  } catch (error) {
    console.error('\n❌ Email service test failed');
    process.exit(1);
  }
}

// Run the test
testEmailService();
