/**
 * @file AddColorButtons.tsx
 * @description Component displaying buttons to create either a flat color or gradient, and compare colors
 */

import React, { useState } from 'react';
import { useTokens } from '../hooks/useTokens';
import ColorForm from './ColorForm';
import GradientPickerModal from './GradientPickerModal';
import { ColorCompareButton } from './ColorComparison';
import { Plus, Droplet, Palette } from 'lucide-react';

interface AddColorButtonsProps {
  className?: string;
}

const AddColorButtons: React.FC<AddColorButtonsProps> = ({ className = '' }) => {
  const tokens = useTokens();
  const [showProductTypeModal, setShowProductTypeModal] = useState<boolean>(false);
  const [showFlatColorModal, setShowFlatColorModal] = useState<boolean>(false);
  const [showGradientModal, setShowGradientModal] = useState<boolean>(false);

  const openProductTypeModal = () => {
    setShowProductTypeModal(true);
  };

  const openFlatColorModal = () => {
    setShowProductTypeModal(false);
    setShowFlatColorModal(true);
  };

  const openGradientModal = () => {
    setShowProductTypeModal(false);
    setShowGradientModal(true);
  };

  // Handle successful color creation
  const handleSuccess = () => {
    // Both modals will call this when they successfully create a color
    // Could add additional logic here like showing a notification
  };

  // Common button styling for consistency
  const buttonBaseClass = "px-[var(--spacing-4)] py-[var(--spacing-2)] text-sm font-medium rounded-[var(--radius-md)] shadow-[var(--shadow-sm)] transition-colors w-full flex items-center justify-center gap-1.5";
  const primaryButtonClass = `${buttonBaseClass} bg-brand-primary text-ui-inverse hover:bg-brand-primary-dark`;
  const secondaryButtonClass = `${buttonBaseClass} bg-ui-background-tertiary text-ui-foreground-primary hover:bg-ui-background-secondary`;
  const optionButtonClass = "flex items-center gap-3 px-[var(--spacing-4)] py-[var(--spacing-3)] w-full text-left text-ui-foreground-primary hover:bg-ui-background-secondary transition-colors rounded-[var(--radius-md)]";

  return (
    <>
      <div className={`flex ${className.includes('flex-row') ? 'flex-row' : 'flex-col'} ${className.includes('space-') ? '' : 'space-y-3'} ${className}`}>
        <button
          type="button"
          className={primaryButtonClass}
          onClick={openProductTypeModal}
          data-testid="add-product-button"
          style={{
            transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.out}`
          }}
        >
          <Plus size={16} />
          <span>Add New Product</span>
        </button>
        
        <ColorCompareButton className={secondaryButtonClass} />
      </div>

      {/* Product Type Selection Modal */}
      {showProductTypeModal && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center p-[var(--spacing-4)] z-50">
          <div className="bg-ui-background-primary rounded-[var(--radius-lg)] shadow-[var(--shadow-xl)] max-w-md w-full p-[var(--spacing-6)]"
               style={{
                 animation: `fadeIn ${tokens.transitions.duration[300]} ${tokens.transitions.easing.apple}`
               }}>
            <div className="flex justify-between items-center mb-[var(--spacing-4)]">
              <h3 className="text-lg font-medium text-ui-foreground-primary">Select Product Type</h3>
              <button
                onClick={() => setShowProductTypeModal(false)}
                className="text-ui-muted hover:text-ui-foreground-primary focus:outline-none"
                aria-label="Close"
              >
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="space-y-3 mb-6">
              <button 
                className={optionButtonClass} 
                onClick={openFlatColorModal}
              >
                <div className="w-10 h-10 rounded-md bg-brand-primary flex items-center justify-center text-white">
                  <Droplet size={20} />
                </div>
                <div>
                  <div className="font-medium">Flat Color</div>
                  <div className="text-xs text-ui-muted">Single-color products with Pantone, HEX, and CMYK values</div>
                </div>
              </button>
              
              <button 
                className={optionButtonClass} 
                onClick={openGradientModal}
              >
                <div className="w-10 h-10 rounded-md bg-gradient-to-r from-brand-primary to-brand-secondary flex items-center justify-center text-white">
                  <Palette size={20} />
                </div>
                <div>
                  <div className="font-medium">Gradient</div>
                  <div className="text-xs text-ui-muted">Multi-color products with gradient effects</div>
                </div>
              </button>
            </div>
            
            <div className="text-center">
              <button
                onClick={() => setShowProductTypeModal(false)}
                className="px-4 py-2 text-ui-muted hover:text-ui-foreground-primary transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Flat Color Modal */}
      {showFlatColorModal && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center p-4 z-50">
          <div className="bg-ui-background-primary rounded-lg shadow-xl w-full max-w-xl overflow-hidden">
            <div className="px-6 py-4 border-b border-ui-border-light">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-ui-foreground-primary">Add Flat Color</h3>
                <button
                  onClick={() => setShowFlatColorModal(false)}
                  className="text-ui-muted hover:text-ui-foreground-primary focus:outline-none"
                  aria-label="Close"
                >
                  <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            <div className="p-6">
              <ColorForm 
                isModal={true}
                onSuccess={() => {
                  handleSuccess();
                  setShowFlatColorModal(false);
                }}
                onCancel={() => setShowFlatColorModal(false)}
              />
            </div>
          </div>
        </div>
      )}

      {/* Gradient Modal */}
      <GradientPickerModal
        isOpen={showGradientModal}
        onClose={() => setShowGradientModal(false)}
        onSuccess={() => {
          handleSuccess();
          setShowGradientModal(false);
        }}
      />
    </>
  );
};

export default AddColorButtons; 