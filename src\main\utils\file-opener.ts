/**
 * @file file-opener.ts
 * @description Utility functions for opening files and URLs
 */

import { shell, BrowserWindow } from 'electron';
import * as fs from 'fs';
import { openUrlInDefaultBrowser, isSharePointOrOneDriveUrl } from './browser-opener';

/**
 * Determines if a path is a web URL
 * @param path The path to check
 * @returns True if the path is a web URL
 */
export function isWebUrl(path: string): boolean {
  return path.startsWith('http://') ||
         path.startsWith('https://') ||
         path.startsWith('www.') ||
         path.includes('sharepoint.com') ||
         path.includes('onedrive.com');
}

/**
 * Opens a file or URL with the appropriate application
 * @param path The file path or URL to open
 * @returns Promise resolving to true if successful, false otherwise
 */
export async function openFileOrUrl(path: string): Promise<boolean> {
  try {
    // Get the current focused window
    const focusedWindow = BrowserWindow.getFocusedWindow();

    if (isWebUrl(path)) {
      // It's a web URL, open in browser
      console.log(`Opening URL in browser: ${path}`);

      // For SharePoint and OneDrive URLs, use our specialized handler
      if (isSharePointOrOneDriveUrl(path)) {
        console.log(`Using specialized SharePoint URL handler for: ${path}`);
        return await openUrlInDefaultBrowser(path); // This will use the specialized method
      }

      // For other URLs, use the standard approach
      return await openUrlInDefaultBrowser(path);
    } else {
      // Check if the file exists before trying to open it
      if (fs.existsSync(path)) {
        // It's a local file, open with default application
        console.log(`Opening local file: ${path}`);
        await shell.openPath(path);

        // Restore focus to the app window after a short delay
        setTimeout(() => {
          if (focusedWindow && !focusedWindow.isDestroyed()) {
            if (focusedWindow.isMinimized()) {
              focusedWindow.restore();
            }
            focusedWindow.focus();
            console.log('Restored focus to application window');
          }
        }, 1000); // 1 second delay

        return true;
      } else {
        console.error(`File does not exist: ${path}`);
        return false;
      }
    }
  } catch (error) {
    console.error(`Error opening file or URL: ${path}`, error);

    // As a fallback, try multiple methods
    try {
      if (isWebUrl(path)) {
        // Try the direct shell method first
        try {
          await shell.openExternal(path, { activate: true });

          // Restore focus to the app window after a short delay
          const focusedWindow = BrowserWindow.getFocusedWindow();
          setTimeout(() => {
            if (focusedWindow && !focusedWindow.isDestroyed()) {
              if (focusedWindow.isMinimized()) {
                focusedWindow.restore();
              }
              focusedWindow.focus();
              console.log('Restored focus to application window (fallback)');
            }
          }, 1500); // 1.5 second delay

          return true;
        } catch (shellError) {
          console.error(`Shell method failed: ${shellError}`);
        }

        // Try the platform-specific command as a last resort
        const success = await openUrlInDefaultBrowser(path);
        if (success) {
          return true;
        }
      }
    } catch (fallbackError) {
      console.error(`All fallback methods failed: ${fallbackError}`);
    }

    return false;
  }
}
