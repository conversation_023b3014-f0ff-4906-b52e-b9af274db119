const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Find database
function findDatabase() {
  const dbPath = path.join(
    process.env.HOME || process.env.USERPROFILE,
    'Library',
    'Application Support',
    'chroma-sync',
    'chromasync.db'
  );
  
  if (fs.existsSync(dbPath)) {
    return dbPath;
  }
  throw new Error('Database not found');
}

function analyzeSharedColors() {
  try {
    const dbPath = findDatabase();
    const db = new Database(dbPath, { readonly: true });
    
    console.log('╔══════════════════════════════════════════════════════════════════════════════╗');
    console.log('║                         SHARED COLORS ANALYSIS                               ║');
    console.log('╚══════════════════════════════════════════════════════════════════════════════╝');
    console.log();
    
    // Find colors shared between multiple products
    const sharedColorsQuery = `
      SELECT 
        c.id,
        c.name as color_name,
        c.pantone_code,
        c.hex_value,
        GROUP_CONCAT(p.name, ' | ') as products,
        COUNT(DISTINCT p.id) as product_count
      FROM colors c
      JOIN product_colors pc ON c.id = pc.color_id
      JOIN products p ON pc.product_id = p.id
      GROUP BY c.id
      HAVING COUNT(DISTINCT p.id) > 1
      ORDER BY product_count DESC, c.name
    `;
    
    const sharedColors = db.prepare(sharedColorsQuery).all();
    
    console.log(`Found ${sharedColors.length} colors shared between multiple products:`);
    console.log();
    
    // Group by number of products sharing
    const groupedByCount = {};
    sharedColors.forEach(color => {
      if (!groupedByCount[color.product_count]) {
        groupedByCount[color.product_count] = [];
      }
      groupedByCount[color.product_count].push(color);
    });
    
    // Display results grouped by share count
    Object.keys(groupedByCount).sort((a, b) => b - a).forEach(count => {
      console.log(`┌─────────────────────────────────────────────────────────────────────────────┐`);
      console.log(`│ Colors shared between ${count} products:                                           │`);
      console.log(`├─────────────────────────────────────────────────────────────────────────────┤`);
      
      groupedByCount[count].forEach(color => {
        console.log(`│ ${color.color_name || 'Unnamed'} (${color.pantone_code || 'No Pantone'})                                              │`);
        console.log(`│   Hex: ${color.hex_value}                                                    │`);
        console.log(`│   Products: ${color.products.substring(0, 60)}...                          │`);
        console.log(`├─────────────────────────────────────────────────────────────────────────────┤`);
      });
    });
    
    // Analyze specific problematic products
    console.log();
    console.log('┌─────────────────────────────────────────────────────────────────────────────┐');
    console.log('│                    DETAILED ANALYSIS OF PROBLEM PRODUCTS                     │');
    console.log('├─────────────────────────────────────────────────────────────────────────────┤');
    
    const problemProducts = [
      ['5500 SMART', 'Smart 5500'],
      ['IVG Beyond CLK 6000', 'IVG Beyond Cybr 6000'],
      ['Blue Raspberry Ice', 'Classic Mint', 'Double Apple', 'Mango Ice', 'Strawberry Watermelon']
    ];
    
    problemProducts.forEach(group => {
      console.log(`│ Analyzing: ${group.join(' vs ')}                                             │`);
      
      // Get colors for each product in the group
      const productColors = {};
      group.forEach(productName => {
        const query = `
          SELECT c.id, c.name, c.pantone_code, c.hex_value
          FROM colors c
          JOIN product_colors pc ON c.id = pc.color_id
          JOIN products p ON pc.product_id = p.id
          WHERE p.name = ?
          ORDER BY c.name
        `;
        productColors[productName] = db.prepare(query).all(productName);
      });
      
      // Find shared colors within this group
      const colorSets = group.map(p => new Set(productColors[p].map(c => c.id)));
      const sharedInGroup = productColors[group[0]].filter(color => 
        group.every(p => productColors[p].some(c => c.id === color.id))
      );
      
      console.log(`│   Shared colors: ${sharedInGroup.length}                                              │`);
      if (sharedInGroup.length > 0 && sharedInGroup.length <= 5) {
        sharedInGroup.forEach(color => {
          console.log(`│     • ${color.name || 'Unnamed'} (${color.pantone_code || 'No code'})                                    │`);
        });
      }
      
      // Show unique colors for each
      group.forEach(productName => {
        const unique = productColors[productName].filter(color =>
          !group.some(otherProduct => 
            otherProduct !== productName && 
            productColors[otherProduct].some(c => c.id === color.id)
          )
        );
        console.log(`│   ${productName} unique colors: ${unique.length}                                     │`);
      });
      
      console.log(`├─────────────────────────────────────────────────────────────────────────────┤`);
    });
    
    // Check orphaned colors
    const orphanedQuery = `
      SELECT id, name, pantone_code, hex_value
      FROM colors c
      WHERE NOT EXISTS (
        SELECT 1 FROM product_colors pc WHERE pc.color_id = c.id
      )
      LIMIT 10
    `;
    
    const orphaned = db.prepare(orphanedQuery).all();
    
    console.log();
    console.log('┌─────────────────────────────────────────────────────────────────────────────┐');
    console.log('│                          ORPHANED COLORS (First 10)                          │');
    console.log('├─────────────────────────────────────────────────────────────────────────────┤');
    
    orphaned.forEach(color => {
      console.log(`│ ${(color.name || 'Unnamed').padEnd(30)} │ ${(color.pantone_code || 'No code').padEnd(10)} │ ${color.hex_value} │`);
    });
    
    console.log('└─────────────────────────────────────────────────────────────────────────────┘');
    
    db.close();
    
  } catch (error) {
    console.error('Error analyzing shared colors:', error);
  }
}

analyzeSharedColors();