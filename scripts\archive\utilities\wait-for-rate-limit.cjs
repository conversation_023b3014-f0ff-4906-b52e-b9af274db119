#!/usr/bin/env node

// Wait and retry email sending after rate limit
console.log('⏰ Zoho Rate Limit Recovery\n');
console.log('<PERSON>oh<PERSON> has rate limited our requests. We need to wait before trying again.');
console.log('Recommended wait time: 60-120 seconds\n');

const waitTime = 90; // 90 seconds
console.log(`Waiting ${waitTime} seconds before refreshing token...`);

let remaining = waitTime;
const interval = setInterval(() => {
  remaining -= 10;
  if (remaining > 0) {
    process.stdout.write(`\r${remaining} seconds remaining...    `);
  } else {
    clearInterval(interval);
    console.log('\n\n✅ Wait complete! You can now:');
    console.log('1. Run: node refresh-zoho-token.cjs');
    console.log('2. Then run ChromaSync again');
    console.log('\nThe email service should work without rate limits.');
    process.exit(0);
  }
}, 10000);

// Show initial countdown
process.stdout.write(`\r${remaining} seconds remaining...    `);
