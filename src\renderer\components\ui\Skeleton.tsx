import React from 'react';

interface SkeletonProps {
  className?: string;
  variant?: 'text' | 'rectangular' | 'circular';
  width?: string | number;
  height?: string | number;
  animation?: 'pulse' | 'wave' | 'none';
}

export const Skeleton: React.FC<SkeletonProps> = ({
  className = '',
  variant = 'text',
  width,
  height,
  animation = 'pulse'
}) => {
  const baseClasses = 'bg-ui-background-tertiary dark:bg-zinc-700';
  
  const variantClasses = {
    text: 'rounded',
    rectangular: 'rounded-md',
    circular: 'rounded-full'
  };

  const animationClasses = {
    pulse: 'animate-pulse',
    wave: 'animate-shimmer',
    none: ''
  };

  const style: React.CSSProperties = {
    width: width || (variant === 'text' ? '100%' : undefined),
    height: height || (variant === 'text' ? '1em' : undefined)
  };

  return (
    <div
      className={`${baseClasses} ${variantClasses[variant]} ${animationClasses[animation]} ${className}`}
      style={style}
    />
  );
};

// Table skeleton component
export const TableSkeleton: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 6 
}) => {
  return (
    <div className="w-full">
      <table className="w-full">
        <thead>
          <tr>
            {Array.from({ length: columns }).map((_, i) => (
              <th key={i} className="py-3 px-4">
                <Skeleton height={20} />
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {Array.from({ length: rows }).map((_, rowIndex) => (
            <tr key={rowIndex} className="border-b border-ui-border-light dark:border-ui-border-dark">
              {Array.from({ length: columns }).map((_, colIndex) => (
                <td key={colIndex} className="py-3 px-4">
                  <Skeleton height={16} width={colIndex === 0 ? '60%' : '80%'} />
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

// Card skeleton component
export const CardSkeleton: React.FC = () => {
  return (
    <div className="bg-ui-background-primary dark:bg-ui-background-tertiary rounded-lg p-4 space-y-3">
      <Skeleton variant="rectangular" height={120} />
      <Skeleton width="60%" />
      <Skeleton width="80%" />
      <div className="flex justify-between items-center pt-2">
        <Skeleton width={60} height={24} />
        <Skeleton variant="circular" width={32} height={32} />
      </div>
    </div>
  );
};

// Color swatch skeleton
export const SwatchSkeleton: React.FC = () => {
  return (
    <div className="flex flex-col items-center space-y-2">
      <Skeleton variant="rectangular" width={80} height={80} className="rounded-lg" />
      <Skeleton width={60} height={14} />
      <Skeleton width={40} height={12} />
    </div>
  );
};

// Product card skeleton
export const ProductCardSkeleton: React.FC = () => {
  return (
    <div className="bg-ui-background-secondary dark:bg-zinc-800 rounded-lg p-4 space-y-3">
      <div className="flex items-center justify-between">
        <Skeleton width="40%" height={24} />
        <Skeleton variant="circular" width={24} height={24} />
      </div>
      <div className="grid grid-cols-4 gap-2">
        {Array.from({ length: 8 }).map((_, i) => (
          <Skeleton key={i} variant="rectangular" height={40} className="rounded" />
        ))}
      </div>
      <div className="flex justify-between items-center pt-2">
        <Skeleton width={100} height={16} />
        <Skeleton width={80} height={32} className="rounded-md" />
      </div>
    </div>
  );
};