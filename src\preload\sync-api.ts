// Typed API for sync operations
contextBridge.exposeInMainWorld('syncAPI', {
  // Get sync configuration
  getConfig: async () => {
    console.log('Preload: calling syncAPI.getConfig');
    return ipcRenderer.invoke('sync:get-config');
  },

  // Check for unsynced local changes
  hasUnsyncedLocalChanges: async () => {
    console.log('Preload: calling syncAPI.hasUnsyncedLocalChanges');
    return ipcRenderer.invoke('sync:has-unsynced-local-changes');
  },

  // Update sync configuration
  updateConfig: async (config: Partial<SyncConfig>) => {
    console.log('Preload: calling syncAPI.updateConfig');
    return ipcRenderer.invoke('sync:update-config', config);
  },

  // Get sync state
  getState: async () => {
    console.log('Preload: calling syncAPI.getState');
    return ipcRenderer.invoke('sync:get-state');
  },

  // Get authentication state
  getAuthState: async () => {
    console.log('Preload: calling syncAPI.getAuthState');
    return ipcRenderer.invoke('sync:get-auth-state');
  },

  // Login with Google (new Supabase implementation)
  login: async () => {
    console.log('Preload: calling syncAPI.login (Google OAuth)');
    return ipcRenderer.invoke('sync:login');
  },

  // Accept GDPR consent
  acceptGDPR: async (ip?: string) => {
    console.log('Preload: calling syncAPI.acceptGDPR');
    return ipcRenderer.invoke('sync:accept-gdpr', ip);
  },

  // Export user data (GDPR)
  exportData: async () => {
    console.log('Preload: calling syncAPI.exportData');
    return ipcRenderer.invoke('sync:export-data');
  },

  // Delete account and data (GDPR)
  deleteAccount: async () => {
    console.log('Preload: calling syncAPI.deleteAccount');
    return ipcRenderer.invoke('sync:delete-account');
  },

  // Logout
  logout: async () => {
    console.log('Preload: calling syncAPI.logout');
    return ipcRenderer.invoke('sync:logout');
  },

  // Sync data (manual sync)
  sync: async () => {
    console.log('Preload: calling syncAPI.sync');
    return ipcRenderer.invoke('sync:sync');
  },

  // Initialize sync
  initialize: async () => {
    console.log('Preload: calling syncAPI.initialize');
    return ipcRenderer.invoke('sync:initialize');
  },

  // Legacy compatibility methods (for backward compatibility)
  signup: async (email: string, password: string) => {
    console.log('Preload: signup not supported in Google-only mode');
    return { success: false, error: 'Please use Google sign-in' };
  },

  syncData: async () => {
    console.log('Preload: calling syncAPI.syncData (legacy)');
    return ipcRenderer.invoke('sync:sync');
  },

  testConnection: async () => {
    console.log('Preload: calling syncAPI.testConnection');
    const authState = await ipcRenderer.invoke('sync:get-auth-state');
    return { success: authState.isAuthenticated };
  },

  subscribe: async () => {
    console.log('Preload: subscribe handled automatically by real-time sync');
    return { success: true };
  },

  unsubscribe: async () => {
    console.log('Preload: unsubscribe handled automatically by real-time sync');
    return { success: true };
  },

  // Resolve conflicts (using last-write-wins strategy)
  resolveConflicts: async (resolutions: Array<{
    conflictId: string;
    resolution: 'local' | 'remote' | 'merged';
    mergedData?: unknown;
  }>) => {
    console.log('Preload: conflicts resolved automatically with last-write-wins');
    return { success: true, resolutions };
  },
});
