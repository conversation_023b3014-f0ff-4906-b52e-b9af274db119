/**
 * @file product.service.ts
 * @description Service for product database operations - Updated for optimized schema
 */

import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import { getOrCreateDeviceId } from '../../utils/deviceId';
import {
  Product,
  NewProduct,
  UpdateProduct
} from '../../../shared/types/product.types';
// import { Selection, SelectionWithColors } from '../../../shared/types/selection.types'; // DISABLED
import { ColorEntry } from '../../../shared/types/color.types';
// import { SelectionService } from './selection.service'; // DISABLED
import { ColorService } from './color.service';

export class ProductService {
  constructor(private db: Database.Database, private colorService?: ColorService) {
    // Tables already exist in optimized schema
  }

  /**
   * Get all products
   * @param organizationId - External organization ID (UUID) for multi-tenant filtering
   */
  getAll(organizationId: string): Product[] {
    try {
      console.log('[ProductService] Getting all products for organization:', organizationId);
      
      // First, get the local organization ID from the external organization ID
      const localOrgStmt = this.db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `);
      const localOrg = localOrgStmt.get(organizationId) as { id: number } | undefined;
      
      if (!localOrg) {
        console.warn(`[ProductService] Organization not found for external_id: ${organizationId}`);
        
        // Debug: Check what organizations exist
        const allOrgs = this.db.prepare('SELECT external_id, name FROM organizations').all();
        console.log('[ProductService] Available organizations:', allOrgs);
        
        return [];
      }
      
      const rows = this.db.prepare(`
        SELECT 
          external_id as id,
          name,
          description,
          metadata,
          created_by,
          user_id,
          created_at as createdAt,
          updated_at as updatedAt
        FROM products
        WHERE is_active = 1 AND organization_id = ?
        ORDER BY name ASC
      `).all(localOrg.id);

      console.log(`[ProductService] Found ${rows.length} products for organization ${organizationId} (local ID: ${localOrg.id})`);
      console.log('[ProductService] First product:', rows[0]);
      
      // Parse metadata for each product and merge with column data
      return rows.map((row: any) => {
        const metadata = row.metadata ? JSON.parse(row.metadata) : {};
        return {
          id: row.id,
          name: row.name,
          description: row.description || metadata.description || undefined,
          organizationId,
          createdAt: row.createdAt,
          updatedAt: row.updatedAt,
          createdBy: row.created_by || metadata.createdBy || undefined,
          updatedBy: metadata.updatedBy || undefined
        };
      }) as Product[];
    } catch (error) {
      console.error('[ProductService] Error getting all products:', error);
      return [];
    }
  }

  /**
   * Get product by ID
   * @param id - Product external ID
   * @param organizationId - External organization ID (UUID) for multi-tenant filtering
   */
  getById(id: string, organizationId: string): Product | undefined {
    try {
      // First, get the local organization ID from the external organization ID
      const localOrgStmt = this.db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `);
      const localOrg = localOrgStmt.get(organizationId) as { id: number } | undefined;
      
      if (!localOrg) {
        console.warn(`[ProductService] Organization not found for external_id: ${organizationId}`);
        return undefined;
      }
      
      const row = this.db.prepare(`
        SELECT 
          external_id as id,
          name,
          description,
          metadata,
          created_by,
          user_id,
          created_at as createdAt,
          updated_at as updatedAt
        FROM products
        WHERE external_id = ? AND organization_id = ? AND is_active = 1
      `).get(id, localOrg.id) as any;

      if (!row) {return undefined;}

      const metadata = row.metadata ? JSON.parse(row.metadata) : {};
      return {
        id: row.id,
        name: row.name,
        description: row.description || metadata.description || undefined,
        organizationId,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt,
        createdBy: row.created_by || metadata.createdBy || undefined,
        updatedBy: metadata.updatedBy || undefined
      };
    } catch (error) {
      console.error(`[ProductService] Error getting product ${id}:`, error);
      return undefined;
    }
  }

  /**
   * Create a new product
   * @param product - Product data to add
   * @param organizationId - Organization ID for multi-tenant support
   * @param userId - Optional user ID for audit trail
   */
  add(product: NewProduct, organizationId: string, userId?: string): Product {
    const id = uuidv4();
    const now = new Date().toISOString();
    const deviceId = getOrCreateDeviceId();

    try {
      // First, get the local organization ID from the external organization ID
      const localOrgStmt = this.db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `);
      const localOrg = localOrgStmt.get(organizationId) as { id: number } | undefined;
      
      if (!localOrg) {
        throw new Error(`[ProductService] Organization not found for external_id: ${organizationId}`);
      }

      this.db.prepare(`
        INSERT INTO products (external_id, name, metadata, organization_id, user_id, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `).run(
        id,
        product.name,
        JSON.stringify({
          description: product.description,
          createdBy: userId || deviceId,
          updatedBy: userId || deviceId
        }),
        localOrg.id,
        userId || null,
        now,
        now
      );

      return {
        id,
        name: product.name,
        description: product.description,
        organizationId,
        userId,
        createdAt: now,
        updatedAt: now,
        createdBy: userId || deviceId,
        updatedBy: userId || deviceId
      };
    } catch (error) {
      console.error('[ProductService] Error adding product:', error);
      throw error;
    }
  }

  /**
   * Update a product
   * @param id - Product external ID
   * @param updates - Fields to update
   * @param organizationId - Organization ID for multi-tenant filtering
   * @param userId - Optional user ID for audit trail
   */
  update(id: string, updates: UpdateProduct, organizationId: string, userId?: string): Product | undefined {
    const deviceId = getOrCreateDeviceId();
    const now = new Date().toISOString();

    try {
      // First, get the local organization ID from the external organization ID
      const localOrgStmt = this.db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `);
      const localOrg = localOrgStmt.get(organizationId) as { id: number } | undefined;
      
      if (!localOrg) {
        console.warn(`[ProductService] Organization not found for external_id: ${organizationId}`);
        return undefined;
      }

      // Get current product to preserve metadata and verify organization
      const current = this.db.prepare(`
        SELECT metadata FROM products WHERE external_id = ? AND organization_id = ?
      `).get(id, localOrg.id) as { metadata: string } | undefined;

      if (!current) {return undefined;}

      const currentMetadata = current.metadata ? JSON.parse(current.metadata) : {};
      const updatedMetadata = {
        ...currentMetadata,
        description: updates.description !== undefined ? updates.description : currentMetadata.description,
        updatedBy: userId || deviceId
      };

      const updateFields: string[] = [];
      const updateValues: any[] = [];

      if (updates.name !== undefined) {
        updateFields.push('name = ?');
        updateValues.push(updates.name);
      }

      updateFields.push('metadata = ?');
      updateValues.push(JSON.stringify(updatedMetadata));
      updateFields.push('updated_at = ?');
      updateValues.push(now);
      updateValues.push(id);
      updateValues.push(localOrg.id);

      this.db.prepare(`
        UPDATE products 
        SET ${updateFields.join(', ')} 
        WHERE external_id = ? AND organization_id = ?
      `).run(...updateValues);

      return this.getById(id, organizationId);
    } catch (error) {
      console.error(`[ProductService] Error updating product ${id}:`, error);
      return undefined;
    }
  }

  /**
   * Delete a product (soft delete)
   * @param id - Product external ID
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  delete(id: string, organizationId: string): boolean {
    try {
      // First, get the local organization ID from the external organization ID
      const localOrgStmt = this.db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `);
      const localOrg = localOrgStmt.get(organizationId) as { id: number } | undefined;
      
      if (!localOrg) {
        console.warn(`[ProductService] Organization not found for external_id: ${organizationId}`);
        return false;
      }

      const result = this.db.prepare(`
        UPDATE products 
        SET is_active = 0, updated_at = CURRENT_TIMESTAMP 
        WHERE external_id = ? AND organization_id = ? AND is_active = 1
      `).run(id, localOrg.id);
      
      return result.changes > 0;
    } catch (error) {
      console.error(`[ProductService] Error deleting product ${id}:`, error);
      return false;
    }
  }

  /**
   * Delete multiple products
   * @param ids - Array of product external IDs to delete
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  deleteMultiple(ids: string[], organizationId: string): { success: boolean; deletedIds: string[] } {
    const deletedIds: string[] = [];

    try {
      const deleteTransaction = this.db.transaction(() => {
        for (const id of ids) {
          const result = this.db.prepare(`
            UPDATE products 
            SET is_active = 0, updated_at = CURRENT_TIMESTAMP 
            WHERE external_id = ? AND organization_id = (SELECT id FROM organizations WHERE external_id = ?) AND is_active = 1
          `).run(id, organizationId);
          
          if (result.changes > 0) {
            deletedIds.push(id);
          }
        }
      });

      deleteTransaction();
      
      return {
        success: deletedIds.length > 0,
        deletedIds
      };
    } catch (error) {
      console.error('[ProductService] Error deleting multiple products:', error);
      return {
        success: false,
        deletedIds: []
      };
    }
  }

  /**
   * Get product with its selections
   * DISABLED: This method uses SelectionService which has been removed
   */
  /*
  getWithSelections(id: string, selectionService: SelectionService): ProductWithSelections | undefined {
    const product = this.getById(id);
    if (!product) return undefined;

    // Get internal product ID
    const internalProduct = this.db.prepare('SELECT id FROM products WHERE external_id = ?').get(id) as any;
    if (!internalProduct) return { ...product, selections: [] };

    // Get selections associated with this product through product_selections mapping
    // Note: This assumes a product_selections table exists in the optimized schema
    // If not, we'll need to add it or adjust the logic
    const selections = selectionService.getAll().filter(selection => {
      // For now, return all selections since we don't have product-selection mapping
      // This would need to be implemented based on your business logic
      return true;
    });

    return {
      ...product,
      selections
    };
  }
  */

  /**
   * Get product with selections and their colors
   * DISABLED: This method uses SelectionService which has been removed
   */
  /*
  getWithSelectionsAndColors(
    id: string,
    selectionService: SelectionService,
    _colorService: ColorService
  ): ProductWithSelectionsAndColors | undefined {
    const product = this.getById(id);
    if (!product) return undefined;

    // Get selections with colors
    const selections = selectionService.getAll()
      .map(selection => selectionService.getWithColors(selection.id, colorService))
      .filter((sel): sel is SelectionWithColors => !!sel);

    return {
      ...product,
      selections
    };
  }
  */

  /**
   * Get all colors associated with a product
   * @param id - Product external ID
   * @param organizationId - Organization ID for multi-tenant filtering
   * @param __colorService - Color service instance (legacy parameter)
   */
  getColors(id: string, organizationId: string, __colorService?: ColorService): ColorEntry[] {
    try {
      // First, get the local organization ID from the external organization ID
      const localOrgStmt = this.db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `);
      const localOrg = localOrgStmt.get(organizationId) as { id: number } | undefined;
      
      if (!localOrg) {
        console.warn(`[ProductService] Organization not found for external_id: ${organizationId}`);
        return [];
      }
      
      // Get internal product ID and verify organization
      const internalProduct = this.db.prepare('SELECT id FROM products WHERE external_id = ? AND organization_id = ?').get(id, localOrg.id) as any;
      if (!internalProduct) {return [];}

      // Get colors directly associated with the product
      const colorIds = this.db.prepare(`
        SELECT c.external_id
        FROM product_colors pc
        JOIN colors c ON pc.color_id = c.id
        WHERE pc.product_id = ? AND c.deleted_at IS NULL
        ORDER BY pc.display_order
      `).all((internalProduct as any).id) as Array<{ external_id: string }>;

      return this.colorService ? colorIds
        .map(row => this.colorService!.getById(row.external_id, organizationId))
        .filter((color): color is ColorEntry => !!color) : [];
    } catch (error) {
      console.error(`[ProductService] Error getting colors for product ${id}:`, error);
      return [];
    }
  }

  /**
   * Associate a color directly with a product
   * @param productId - Product external ID
   * @param colorId - Color external ID
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  addColor(productId: string, colorId: string, organizationId: string): boolean {
    try {
      // First, get the local organization ID from the external organization ID
      const localOrgStmt = this.db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `);
      const localOrg = localOrgStmt.get(organizationId) as { id: number } | undefined;
      
      if (!localOrg) {
        console.warn(`[ProductService] Organization not found for external_id: ${organizationId}`);
        return false;
      }
      
      // Get internal IDs and verify organization
      const product = this.db.prepare('SELECT id FROM products WHERE external_id = ? AND organization_id = ?').get(productId, localOrg.id) as { id: number } | undefined;
      const color = this.db.prepare('SELECT id FROM colors WHERE external_id = ? AND organization_id = ?').get(colorId, localOrg.id) as { id: number } | undefined;

      if (!product || !color) {return false;}

      // Get next display order
      const maxOrder = this.db.prepare(`
        SELECT MAX(display_order) as max_order 
        FROM product_colors 
        WHERE product_id = ?
      `).get(product.id) as { max_order: number | null } | undefined;

      const displayOrder = (maxOrder?.max_order || 0) + 1;

      // Check if organization_id column exists in product_colors table
      const hasOrgIdColumn = this.db.prepare(`
        SELECT COUNT(*) as count 
        FROM pragma_table_info('product_colors') 
        WHERE name = 'organization_id'
      `).get() as { count: number };

      if (hasOrgIdColumn.count > 0) {
        this.db.prepare(`
          INSERT OR IGNORE INTO product_colors (product_id, color_id, display_order, organization_id)
          VALUES (?, ?, ?, ?)
        `).run(product.id, color.id, displayOrder, localOrg.id);
      } else {
        // Fallback for databases without organization_id column
        this.db.prepare(`
          INSERT OR IGNORE INTO product_colors (product_id, color_id, display_order)
          VALUES (?, ?, ?)
        `).run(product.id, color.id, displayOrder);
      }

      return true;
    } catch (error) {
      console.error(`[ProductService] Error adding color ${colorId} to product ${productId}:`, error);
      return false;
    }
  }

  /**
   * Remove a color from a product
   * @param productId - Product external ID
   * @param colorId - Color external ID
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  removeColor(productId: string, colorId: string, organizationId: string): boolean {
    try {
      // First, get the local organization ID from the external organization ID
      const localOrgStmt = this.db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `);
      const localOrg = localOrgStmt.get(organizationId) as { id: number } | undefined;
      
      if (!localOrg) {
        console.warn(`[ProductService] Organization not found for external_id: ${organizationId}`);
        return false;
      }
      
      // Get internal IDs and verify organization
      const product = this.db.prepare('SELECT id FROM products WHERE external_id = ? AND organization_id = ?').get(productId, localOrg.id) as { id: number } | undefined;
      const color = this.db.prepare('SELECT id FROM colors WHERE external_id = ? AND organization_id = ?').get(colorId, localOrg.id) as { id: number } | undefined;

      if (!product || !color) {return false;}

      // Check if organization_id column exists in product_colors table
      const hasOrgIdColumn = this.db.prepare(`
        SELECT COUNT(*) as count 
        FROM pragma_table_info('product_colors') 
        WHERE name = 'organization_id'
      `).get() as { count: number };

      const result = hasOrgIdColumn.count > 0 
        ? this.db.prepare(`
            DELETE FROM product_colors 
            WHERE product_id = ? AND color_id = ? AND organization_id = ?
          `).run(product.id, color.id, localOrg.id)
        : this.db.prepare(`
            DELETE FROM product_colors 
            WHERE product_id = ? AND color_id = ?
          `).run(product.id, color.id);

      return result.changes > 0;
    } catch (error) {
      console.error(`[ProductService] Error removing color ${colorId} from product ${productId}:`, error);
      return false;
    }
  }

  /**
   * Get all products with their colors
   * @param organizationId - External organization ID (UUID) for multi-tenant filtering
   */
  getAllProductsWithColors(organizationId: string): any[] {
    try {
      console.log('[ProductService] Getting all products with colors...');
      
      if (!organizationId) {
        throw new Error('[ProductService] Organization ID is required');
      }
      
      const products = this.getAll(organizationId);
      console.log('[ProductService] Found products:', products.length, products);
      
      // First, get the local organization ID from the external organization ID
      const localOrgStmt = this.db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `);
      const localOrg = localOrgStmt.get(organizationId) as { id: number } | undefined;
      
      if (!localOrg) {
        console.warn(`[ProductService] Organization not found for external_id: ${organizationId}`);
        return [];
      }

      return products.map(product => {
        // Get internal product ID and verify organization
        const internalProduct = this.db.prepare('SELECT id FROM products WHERE external_id = ? AND organization_id = ?').get(product.id, localOrg.id);
        if (!internalProduct) {return { ...product, colors: [] };}

        // Get colors directly from database to avoid ColorService recursion
        const colors = this.db.prepare(`
          SELECT 
            c.external_id as id,
            c.display_name,
            c.code,
            c.hex,
            c.source_id,
            pc.display_order
          FROM product_colors pc
          JOIN colors c ON pc.color_id = c.id
          WHERE pc.product_id = ? AND pc.organization_id = ? AND c.deleted_at IS NULL
          ORDER BY pc.display_order
        `).all((internalProduct as any).id, localOrg.id) as Array<{
          id: string;
          display_name: string;
          code: string;
          hex: string;
          source_id: number;
          display_order: number;
        }>;

        // Format colors to match ColorEntry interface
        const formattedColors = colors.map(color => ({
          id: color.id,
          name: color.display_name || color.code || 'Unnamed Color',
          code: color.code,
          hex: color.hex,
          source: color.source_id === 2 ? 'pantone' : color.source_id === 3 ? 'ral' : 'user',
          displayOrder: color.display_order
        }));

        return {
          ...product,
          colors: formattedColors
        };
      });
    } catch (error) {
      console.error('[ProductService] Error getting all products with colors:', error);
      return [];
    }
  }

  /**
   * Get a single product with its colors
   * @param id - Product external ID
   * @param organizationId - External organization ID (UUID) for multi-tenant filtering
   */
  getProductWithColors(id: string, organizationId: string): any {
    try {
      if (!organizationId) {
        throw new Error('[ProductService] Organization ID is required');
      }
      
      const product = this.getById(id, organizationId);
      if (!product) {return undefined;}

      // First, get the local organization ID from the external organization ID
      const localOrgStmt = this.db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `);
      const localOrg = localOrgStmt.get(organizationId) as { id: number } | undefined;
      
      if (!localOrg) {
        console.warn(`[ProductService] Organization not found for external_id: ${organizationId}`);
        return { ...product, colors: [] };
      }

      // Get internal product ID and verify organization
      const internalProduct = this.db.prepare('SELECT id FROM products WHERE external_id = ? AND organization_id = ?').get(id, localOrg.id) as any;
      if (!internalProduct) {return { ...product, colors: [] };}

      // Get colors for this product using ColorService to ensure proper formatting
      const colorIds = this.db.prepare(`
        SELECT c.external_id
        FROM product_colors pc
        JOIN colors c ON pc.color_id = c.id
        WHERE pc.product_id = ? AND c.deleted_at IS NULL
        ORDER BY pc.display_order
      `).all((internalProduct as any).id) as Array<{ external_id: string }>;

      const colors = this.colorService ? colorIds
        .map(row => this.colorService!.getById(row.external_id, organizationId))
        .filter((color): color is ColorEntry => !!color) : [];

      return {
        ...product,
        colors
      };
    } catch (error) {
      console.error(`[ProductService] Error getting product with colors ${id}:`, error);
      return undefined;
    }
  }

  /**
   * Add color to product
   * @param productId - Product external ID
   * @param colorId - Color external ID
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  addColorToProduct(productId: string, colorId: string, organizationId: string): boolean {
    return this.addColor(productId, colorId, organizationId);
  }

  /**
   * Remove color from product
   * @param productId - Product external ID
   * @param colorId - Color external ID
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  removeColorFromProduct(productId: string, colorId: string, organizationId: string): boolean {
    return this.removeColor(productId, colorId, organizationId);
  }

  /**
   * Get colors for a product
   * @param productId - Product external ID
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  getProductColors(productId: string, organizationId: string): any[] {
    try {
      // Get internal product ID and verify organization
      const internalProduct = this.db.prepare('SELECT id FROM products WHERE external_id = ? AND organization_id = (SELECT id FROM organizations WHERE external_id = ?)').get(productId, organizationId);
      if (!internalProduct) {return [];}

      // Get colors for this product
      // Get colors for this product using ColorService to ensure proper formatting
      const colorIds = this.db.prepare(`
        SELECT c.external_id
        FROM product_colors pc
        JOIN colors c ON pc.color_id = c.id
        WHERE pc.product_id = ? AND c.deleted_at IS NULL
        ORDER BY pc.display_order
      `).all((internalProduct as any).id) as Array<{ external_id: string }>;

      return this.colorService ? colorIds
        .map(row => this.colorService!.getById(row.external_id, organizationId))
        .filter((color): color is ColorEntry => !!color) : [];
    } catch (error) {
      console.error(`[ProductService] Error getting colors for product ${productId}:`, error);
      return [];
    }
  }

  /**
   * Create a new product
   * @param data - Product data
   * @param organizationId - Organization ID for multi-tenant support
   * @param userId - Optional user ID for audit trail
   */
  create(data: { name: string; metadata?: any }, organizationId: string, userId?: string): Product {
    return this.add({ name: data.name, description: data.metadata?.description, organizationId }, organizationId, userId);
  }

  /**
   * Search products by name
   * @param query - Search query
   * @param organizationId - Organization ID for multi-tenant filtering
   */
  search(query: string, organizationId: string): Product[] {
    try {
      // First, get the local organization ID from the external organization ID
      const localOrgStmt = this.db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `);
      const localOrg = localOrgStmt.get(organizationId) as { id: number } | undefined;
      
      if (!localOrg) {
        console.warn(`[ProductService] Organization not found for external_id: ${organizationId}`);
        return [];
      }
      
      const rows = this.db.prepare(`
        SELECT 
          external_id as id,
          name,
          metadata,
          created_at as createdAt,
          updated_at as updatedAt
        FROM products
        WHERE is_active = 1 AND organization_id = ? AND name LIKE ?
        ORDER BY name ASC
      `).all(localOrg.id, `%${query}%`);

      // Parse metadata for each product
      return rows.map((row: any) => {
        const metadata = row.metadata ? JSON.parse(row.metadata) : {};
        return {
          id: row.id,
          name: row.name,
          description: metadata.description || undefined,
          createdAt: row.createdAt,
          updatedAt: row.updatedAt,
          createdBy: metadata.createdBy || undefined,
          updatedBy: metadata.updatedBy || undefined
        };
      }) as Product[];
    } catch (error) {
      console.error('[ProductService] Error searching products:', error);
      return [];
    }
  }
}
