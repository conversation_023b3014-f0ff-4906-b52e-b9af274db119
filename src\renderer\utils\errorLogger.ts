/**
 * Comprehensive error logging system for ChromaSync
 */

export interface ErrorLog {
  id: string;
  timestamp: number;
  level: 'error' | 'warning' | 'info';
  message: string;
  stack?: string;
  context?: Record<string, any>;
  source: 'renderer' | 'main' | 'preload';
  category: 'ui' | 'database' | 'sync' | 'import' | 'export' | 'general';
  userId?: string;
  sessionId: string;
  version: string;
  platform: string;
}

export interface PerformanceMetric {
  id: string;
  timestamp: number;
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count' | 'percent';
  context?: Record<string, any>;
}

class ErrorLogger {
  private static instance: ErrorLogger;
  private logs: ErrorLog[] = [];
  private metrics: PerformanceMetric[] = [];
  private sessionId: string;
  private maxLogs = 1000;
  private maxMetrics = 500;

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.initializeErrorHandlers();
  }

  static getInstance(): ErrorLogger {
    if (!ErrorLogger.instance) {
      ErrorLogger.instance = new ErrorLogger();
    }
    return ErrorLogger.instance;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  private initializeErrorHandlers(): void {
    // Global error handler
    window.addEventListener('error', (event) => {
      this.logError({
        message: event.message,
        stack: event.error?.stack,
        source: 'renderer',
        category: 'general',
        context: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        },
      });
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.logError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        source: 'renderer',
        category: 'general',
        context: {
          reason: event.reason,
        },
      });
    });

    // Performance observer for monitoring
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.logMetric({
              name: entry.name,
              value: entry.duration || entry.transferSize || 0,
              unit: entry.duration ? 'ms' : 'bytes',
              context: {
                entryType: entry.entryType,
                startTime: entry.startTime,
              },
            });
          }
        });

        observer.observe({ entryTypes: ['navigation', 'resource', 'measure'] });
      } catch (error) {
        console.warn('Performance observer not supported:', error);
      }
    }
  }

  logError(error: Partial<ErrorLog> & { message: string }): string {
    const errorLog: ErrorLog = {
      id: this.generateId(),
      timestamp: Date.now(),
      level: error.level || 'error',
      message: error.message,
      stack: error.stack,
      context: error.context,
      source: error.source || 'renderer',
      category: error.category || 'general',
      sessionId: this.sessionId,
      version: '1.0.0', // TODO: Get from app version
      platform: navigator.platform,
      ...error,
    };

    this.logs.push(errorLog);
    this.trimLogs();

    // Console logging for development
    if (process.env.NODE_ENV === 'development') {
      console.error(`[${errorLog.category}] ${errorLog.message}`, errorLog);
    }

    // Send to main process for potential file logging
    if (window.ipc) {
      window.ipc.invoke('log-error', errorLog).catch(() => {
        // Ignore IPC errors to prevent infinite loops
      });
    }

    return errorLog.id;
  }

  logWarning(message: string, context?: Record<string, any>): string {
    return this.logError({
      message,
      level: 'warning',
      context,
    });
  }

  logInfo(message: string, context?: Record<string, any>): string {
    return this.logError({
      message,
      level: 'info',
      context,
    });
  }

  logMetric(metric: Partial<PerformanceMetric> & { name: string; value: number }): string {
    const performanceMetric: PerformanceMetric = {
      id: this.generateId(),
      timestamp: Date.now(),
      name: metric.name,
      value: metric.value,
      unit: metric.unit || 'count',
      context: metric.context,
      ...metric,
    };

    this.metrics.push(performanceMetric);
    this.trimMetrics();

    return performanceMetric.id;
  }

  // Measure function execution time
  async measureAsync<T>(name: string, fn: () => Promise<T>, context?: Record<string, any>): Promise<T> {
    const start = performance.now();
    try {
      const result = await fn();
      const duration = performance.now() - start;
      this.logMetric({
        name,
        value: duration,
        unit: 'ms',
        context: { ...context, success: true },
      });
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      this.logMetric({
        name,
        value: duration,
        unit: 'ms',
        context: { ...context, success: false, error: String(error) },
      });
      this.logError({
        message: `Error in ${name}: ${String(error)}`,
        stack: error instanceof Error ? error.stack : undefined,
        category: 'general',
        context,
      });
      throw error;
    }
  }

  measureSync<T>(name: string, fn: () => T, context?: Record<string, any>): T {
    const start = performance.now();
    try {
      const result = fn();
      const duration = performance.now() - start;
      this.logMetric({
        name,
        value: duration,
        unit: 'ms',
        context: { ...context, success: true },
      });
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      this.logMetric({
        name,
        value: duration,
        unit: 'ms',
        context: { ...context, success: false, error: String(error) },
      });
      this.logError({
        message: `Error in ${name}: ${String(error)}`,
        stack: error instanceof Error ? error.stack : undefined,
        category: 'general',
        context,
      });
      throw error;
    }
  }

  // Get logs and metrics
  getLogs(level?: ErrorLog['level'], limit = 100): ErrorLog[] {
    let filteredLogs = this.logs;
    if (level) {
      filteredLogs = this.logs.filter(log => log.level === level);
    }
    return filteredLogs.slice(-limit);
  }

  getMetrics(name?: string, limit = 100): PerformanceMetric[] {
    let filteredMetrics = this.metrics;
    if (name) {
      filteredMetrics = this.metrics.filter(metric => metric.name.includes(name));
    }
    return filteredMetrics.slice(-limit);
  }

  // Export logs for debugging
  exportLogs(): string {
    return JSON.stringify({
      sessionId: this.sessionId,
      timestamp: Date.now(),
      logs: this.logs,
      metrics: this.metrics,
    }, null, 2);
  }

  // Clear logs (useful for testing)
  clearLogs(): void {
    this.logs = [];
    this.metrics = [];
  }

  // Get application health status
  getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    errors: number;
    warnings: number;
    recentErrors: ErrorLog[];
    memoryUsage?: number;
  } {
    const recentTime = Date.now() - (5 * 60 * 1000); // Last 5 minutes
    const recentLogs = this.logs.filter(log => log.timestamp > recentTime);
    const errors = recentLogs.filter(log => log.level === 'error').length;
    const warnings = recentLogs.filter(log => log.level === 'warning').length;

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (errors > 10) {
      status = 'critical';
    } else if (errors > 3 || warnings > 10) {
      status = 'warning';
    }

    return {
      status,
      errors,
      warnings,
      recentErrors: recentLogs.filter(log => log.level === 'error').slice(-5),
      memoryUsage: (performance as any).memory?.usedJSHeapSize,
    };
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  private trimLogs(): void {
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }
  }

  private trimMetrics(): void {
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }
}

// Export singleton instance
export const errorLogger = ErrorLogger.getInstance();

// Convenience functions
export const logError = (message: string, context?: Record<string, any>) => 
  errorLogger.logError({ message, context });

export const logWarning = (message: string, context?: Record<string, any>) => 
  errorLogger.logWarning(message, context);

export const logInfo = (message: string, context?: Record<string, any>) => 
  errorLogger.logInfo(message, context);

export const measureAsync = <T>(name: string, fn: () => Promise<T>, context?: Record<string, any>) =>
  errorLogger.measureAsync(name, fn, context);

export const measureSync = <T>(name: string, fn: () => T, context?: Record<string, any>) =>
  errorLogger.measureSync(name, fn, context);