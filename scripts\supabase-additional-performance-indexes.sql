-- Additional Supabase Performance Indexes
-- These supplement the organization indexes already created in supabase-multi-tenant-migration.sql
-- Run this in Supabase SQL Editor for additional performance optimizations

-- User-based indexes for RLS performance (these may not exist yet)
CREATE INDEX IF NOT EXISTS idx_colors_user_id 
ON colors USING btree (user_id);

CREATE INDEX IF NOT EXISTS idx_products_user_id 
ON products USING btree (user_id);

-- External ID indexes for sync performance (critical for upsert operations)
CREATE INDEX IF NOT EXISTS idx_colors_external_id 
ON colors USING btree (external_id);

CREATE INDEX IF NOT EXISTS idx_products_external_id 
ON products USING btree (external_id);

-- Organization member lookup optimization
CREATE INDEX IF NOT EXISTS idx_org_members_user_role 
ON organization_members USING btree (user_id, role);

-- Invitation token lookup optimization (if not already existing)
CREATE INDEX IF NOT EXISTS idx_org_invitations_expires 
ON organization_invitations USING btree (expires_at) 
WHER<PERSON> accepted_at IS NULL;

-- Sync metadata optimization for recent syncs
CREATE INDEX IF NOT EXISTS idx_sync_metadata_recent 
ON sync_metadata USING btree (last_sync DESC) 
WHERE last_sync > NOW() - INTERVAL '7 days';

-- Composite index for color filtering by organization and source
CREATE INDEX IF NOT EXISTS idx_colors_org_source_active 
ON colors USING btree (organization_id, source_id) 
WHERE organization_id IS NOT NULL;

-- Composite index for product filtering by organization and name
CREATE INDEX IF NOT EXISTS idx_products_org_name_search 
ON products USING btree (organization_id, LOWER(name)) 
WHERE organization_id IS NOT NULL;

-- Analyze tables to update statistics
ANALYZE colors;
ANALYZE products; 
ANALYZE product_colors;
ANALYZE organizations;
ANALYZE organization_members;
ANALYZE organization_invitations;
ANALYZE sync_metadata;