#!/bin/bash
# Quick script to clear local organization data and allow fresh sync

# Get the database path based on OS
if [[ "$OSTYPE" == "darwin"* ]]; then
    DB_PATH="$HOME/Library/Application Support/chroma-sync/chromasync.db"
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    DB_PATH="$APPDATA/chroma-sync/chromasync.db"
else
    DB_PATH="$HOME/.config/chroma-sync/chromasync.db"
fi

echo "Clearing duplicate organizations from local database..."

# Use sqlite3 to clear organization data
sqlite3 "$DB_PATH" <<EOF
-- Delete all local organizations to allow fresh sync
DELETE FROM organization_members;
DELETE FROM organizations;
VACUUM;
EOF

echo "Local organization data cleared. Restart ChromaSync to sync fresh data from Supabase."
