/**
 * @file ColorSwatch.tsx
 * @description Single color swatch component displaying a color entry
 * Now supports gradient display with improved design and modal integration
 */

import React, { useState, useCallback, memo } from 'react';
import { createPortal } from 'react-dom';
import { ColorEntry } from '../../../shared/types/color.types';
import { useTokens } from '../../hooks/useTokens';
import { formatHex, parseCMYK, formatCMYKForDisplay } from '../../../shared/utils/color';
import { useColorStore } from '../../store/color.store';
import { useFeatureFlags } from '../../context/FeatureFlagContext';
import GradientDetailsModal from '../GradientDetailsModal';

// Type declaration for window.api to address TypeScript errors
declare global {
  interface Window {
    api: {
      openProductDatasheet: (product: string) => Promise<boolean>;
    };
  }
}

// Helper to darken a hex color
function darkenColor(hex: string | undefined, amount: number = 0.7): string {
  // Default to black if hex is undefined or invalid
  if (!hex) {return '#000000';}
  
  // Remove the # if present
  hex = hex.replace('#', '');
  
  // Ensure hex is a valid 6-character hex code
  if (!/^[0-9A-Fa-f]{6}$/.test(hex)) {
    return '#000000';
  }
  
  // Convert to RGB
  let r = parseInt(hex.substring(0, 2), 16);
  let g = parseInt(hex.substring(2, 4), 16);
  let b = parseInt(hex.substring(4, 6), 16);
  
  // Darken by multiplying each component by the amount
  r = Math.floor(r * amount);
  g = Math.floor(g * amount);
  b = Math.floor(b * amount);
  
  // Convert back to hex
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
}

// Get the first color from a gradient
function getFirstGradientColor(gradientCSS: string): string {
  const colorMatches = gradientCSS.match(/#[0-9a-f]{3,6}/gi);
  return colorMatches && colorMatches.length > 0 ? colorMatches[0] : '#000000';
}

// Group header component to display at the top of the swatch
interface ProductGroupHeaderProps {
  count: number;
  onClick: (e: React.MouseEvent<HTMLDivElement>) => void;
  isExpanded: boolean;
  backgroundColor?: string;
}

const ProductGroupHeader: React.FC<ProductGroupHeaderProps> = memo(({ count, onClick, isExpanded }) => (
  <div 
    className="absolute top-2 left-2 z-10 py-1 px-2.5 rounded-full cursor-pointer flex items-center space-x-1.5 shadow-sm bg-ui-background-secondary border border-ui-border-light"
    onClick={onClick}
    aria-expanded={isExpanded}
    aria-label={`${count} ${count === 1 ? 'product' : 'products'} using this color. Click to ${isExpanded ? 'hide' : 'show'} list`}
    role="button"
    tabIndex={0}
  >
    <span className="text-ui-foreground-primary text-xs font-medium">{count} {count === 1 ? 'product' : 'products'}</span>
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      className={`h-3 w-3 text-ui-foreground-primary transition-transform duration-200 ${isExpanded ? 'transform rotate-180' : ''}`} 
      fill="none" 
      viewBox="0 0 24 24" 
      stroke="currentColor"
      aria-hidden="true"
    >
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
    </svg>
  </div>
));

interface ColorSwatchProps {
  entry: ColorEntry;
}

const ColorSwatch = ({ entry }: ColorSwatchProps) => {
  const tokens = useTokens();
  const { useNewTokenSystem } = useFeatureFlags?.() || { useNewTokenSystem: false };
  const { colors } = useColorStore();
  // const { openSelectionDatasheets } = useSelectionStore(); // DISABLED - use products instead
  const [copiedField, setCopiedField] = useState<string | null>(null);
  const [showProductList, setShowProductList] = useState(false);
  const [showGradientDetails, setShowGradientDetails] = useState(false);

  // Find all entries with the same name (case-insensitive comparison)
  // Add null checks to handle missing name values
  const productsWithSameName = colors.filter(color => {
    // Skip items without name
    if (!color.name || !entry.name) {return false;}
    // Skip empty product names
    if (!color.product || color.product.trim() === '') {return false;}
    // Only include product names that match this name
    return color.name.toLowerCase() === entry.name.toLowerCase();
  });
  
  // Get unique products by filtering and using a Set
  const validProducts = productsWithSameName
    .map(color => color.product?.trim())
    .filter(product => product && product !== '');
  
  // Use Set to eliminate duplicates
  const uniqueProducts = new Set(validProducts);
  const uniqueProductsCount = uniqueProducts.size;
  
  // For debugging
  // console.log(`Name: ${entry.name}, Count: ${uniqueProductsCount}, Products:`, Array.from(uniqueProducts));

  // Helper function to clean code for display
  const getCleanCode = (code: string): string => {
    // Remove the unique identifier suffix (e.g., "939-MB2UE4Z6-8CAA" -> "939")
    const parts = code.split('-');
    return parts[0];
  };

  // Function to copy values to clipboard
  const copyToClipboard = useCallback((value: string, field: string) => {
    navigator.clipboard.writeText(value)
      .then(() => {
        setCopiedField(field);
        // Clear the copied indicator after 1.5 seconds
        setTimeout(() => setCopiedField(null), 1500);
      })
      .catch(err => {
        console.error('Could not copy to clipboard: ', err);
      });
  }, []);

  // Function to open product datasheet
  const handleProductClick = useCallback(async (product: string, e: React.MouseEvent) => {
    if (!product) {return;}
    
    e.stopPropagation(); // Prevent dropdown from closing
    
    try {
      // Open product datasheet using the API
      console.log(`Opening datasheet for product: "${product}"`);
      const opened = await window.api.openProductDatasheet(product);
      
      if (!opened) {
        console.warn(`No datasheets found for product "${product}"`);
      }
    } catch (error) {
      console.error(`Error opening datasheet for "${product}":`, error);
    }
  }, []);

  // Determine if this is a gradient
  const hasGradient = !!entry.gradient;


  // Handle swatch click
  const handleSwatchClick = useCallback((e: React.MouseEvent) => {
    if (hasGradient) {
      e.stopPropagation();
      setShowGradientDetails(true);
    } else if (entry.hex) {
      copyToClipboard(formatHex(entry.hex), 'hex');
    } else {
      copyToClipboard('#000000', 'hex');
    }
  }, [hasGradient, entry, copyToClipboard]);

  // Handle keyboard events for accessibility
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleSwatchClick(e as unknown as React.MouseEvent);
    }
  }, [handleSwatchClick]);

  return (
    <>
      <div 
        className="group relative overflow-hidden rounded-xl shadow-sm hover:shadow-md transition-all duration-200 border border-ui-border-light bg-ui-background-primary transform hover:-translate-y-1"
        onMouseLeave={() => setShowProductList(false)}
        data-testid="color-swatch"
        onClick={handleSwatchClick}
        onKeyDown={handleKeyDown}
        role="button"
        tabIndex={0}
        aria-label={hasGradient 
          ? `${entry.name || 'Unknown name'}, gradient. Click to view details` 
          : `${entry.name || 'Unknown name'}, Code ${entry.code || 'N/A'}, Hex ${formatHex(entry.hex || '#000000')}, CMYK ${formatCMYKForDisplay(parseCMYK(entry.cmyk || 'C:0 M:0 Y:0 K:0'))}`}
        style={useNewTokenSystem ? {
          transition: `all ${tokens.transitions.duration[200]} ${tokens.transitions.easing.inOut}`,
          borderRadius: tokens.borderRadius.xl
        } : undefined}
      >
        {/* Color preview - more responsive height */}
        <div 
          className="flex-grow h-24 relative p-3 flex items-end"
          style={{ 
            background: hasGradient ? (entry.gradient?.gradientCSS || 'linear-gradient(#000, #fff)') : (entry.hex || '#000000'),
            boxShadow: "inset 0 0 0 1px rgba(0,0,0,0.05)" // Add subtle inner shadow to ensure visibility
          }}
          data-testid={hasGradient ? "gradient-swatch" : "color-swatch-preview"}
        >
          {/* Product group indicator - always display regardless of product count */}
          <ProductGroupHeader 
            count={uniqueProductsCount} 
            onClick={(e) => {
              e.stopPropagation();
              setShowProductList(!showProductList);
            }}
            isExpanded={showProductList}
          />
        </div>
        
        {/* Product list - shown when products indicator is clicked */}
        {showProductList && (
          <div 
            className="absolute left-0 top-0 right-0 bg-ui-background-primary z-20 max-h-[70%] overflow-y-auto rounded-lg shadow-lg"
            style={useNewTokenSystem ? {
              boxShadow: tokens.shadows.lg
            } : undefined}
            onClick={(e) => e.stopPropagation()}
            role="dialog"
            aria-label="Products using this color"
          >
            <div className="p-4 border-b border-ui-border-light sticky top-0 bg-ui-background-primary z-10">
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-medium">
                  {uniqueProductsCount} {uniqueProductsCount === 1 ? 'Product' : 'Products'} with "{entry.name || 'Unknown name'}"
                </h3>
                <button 
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-zinc-700"
                  onClick={() => setShowProductList(false)}
                  aria-label="Close product list"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
            <div className="p-3">
              {productsWithSameName.map((color, index) => (
                <div 
                  key={color.id || index}
                  className="p-3 hover:bg-ui-background-secondary dark:hover:bg-ui-background-tertiary rounded-lg cursor-pointer mb-1 last:mb-0 transition-colors"
                  onClick={(e) => handleProductClick(color.product, e)}
                  role="button"
                  tabIndex={0}
                  aria-label={`View product datasheet for ${color.product} ${color.name}`}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleProductClick(color.product, e as unknown as React.MouseEvent);
                    }
                  }}
                >
                  <div className="text-sm font-medium text-gray-800 dark:text-gray-200">{color.product}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">{color.name}</div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Color info - improved with always visible hex and code */}
        <div className="p-3 bg-ui-background-primary border-t border-ui-border-light">
          <div className="flex flex-col">
            {/* Top row - Name information */}
            <div className="mb-2">
              <h3 className="text-sm font-semibold text-ui-foreground-primary truncate">
                {entry.name || 'Unknown name'}
              </h3>
            </div>
            
            {/* Bottom row - color information aligned to the right */}
            <div className="flex flex-col items-end w-full">
              {/* For solid colors, show Pantone */}
              {!hasGradient && (
                <div className="group/code relative w-full text-right">
                  <span 
                    className="text-xs font-medium text-right text-ui-foreground-secondary cursor-pointer hover:text-red-600 transition-colors block"
                    onClick={(e) => {
                      e.stopPropagation();
                      copyToClipboard(entry.code || 'N/A', 'code');
                    }}
                    role="button"
                    tabIndex={0}
                    aria-label={`Copy code ${entry.code || 'N/A'}`}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        copyToClipboard(entry.code || 'N/A', 'code');
                      }
                    }}
                    title={entry.code || 'N/A'}
                  >
                    {entry.code ? getCleanCode(entry.code) : 'N/A'}
                  </span>
                  {copiedField === 'code' ? (
                    <span className="text-red-600 text-xs ml-1 absolute right-0 -top-5 whitespace-nowrap bg-ui-background-secondary px-1.5 py-0.5 rounded shadow-sm border border-ui-border-light">
                      Copied!
                    </span>
                  ) : (
                    <span className="absolute right-0 -top-5 opacity-0 group-hover/code:opacity-100 transition-opacity duration-200 text-xs whitespace-nowrap bg-ui-background-secondary px-1.5 py-0.5 rounded shadow-sm border border-ui-border-light">
                      Click to copy
                    </span>
                  )}
                </div>
              )}
              
              {/* For all colors, show hex or "Gradient CSS" */}
              <div className="group/hex relative mt-1 w-full text-right">
                <span 
                  className={`text-xs ${hasGradient ? 'text-red-600 font-medium' : 'text-ui-foreground-tertiary font-mono'} cursor-pointer hover:text-red-700 transition-colors block`}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (hasGradient) {
                      setShowGradientDetails(true);
                    } else {
                      copyToClipboard(formatHex(entry.hex), 'hex');
                    }
                  }}
                  role="button"
                  tabIndex={0}
                  aria-label={hasGradient ? `Click to view gradient details` : `Copy hex code ${entry.hex}`}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      if (hasGradient) {
                        setShowGradientDetails(true);
                      } else if (entry.hex) {
                        copyToClipboard(formatHex(entry.hex), 'hex');
                      } else {
                        copyToClipboard('#000000', 'hex');
                      }
                    }
                  }}
                  title={hasGradient ? 'View gradient details' : (entry.hex ? formatHex(entry.hex) : '#000000')}
                >
                  {hasGradient ? 'Gradient' : (entry.hex ? formatHex(entry.hex) : '#000000')}
                </span>
                {!hasGradient && copiedField === 'hex' && (
                  <span className="text-red-600 text-xs ml-1 absolute right-0 top-5 whitespace-nowrap bg-ui-background-secondary px-1.5 py-0.5 rounded shadow-sm border border-ui-border-light">
                    Copied!
                  </span>
                )}
                {!hasGradient && (
                  <span className="absolute right-0 top-5 opacity-0 group-hover/hex:opacity-100 transition-opacity duration-200 text-xs whitespace-nowrap bg-ui-background-secondary px-1.5 py-0.5 rounded shadow-sm border border-ui-border-light">
                    Click to copy
                  </span>
                )}
              </div>
              
              {/* For solid colors, show CMYK */}
              {!hasGradient && (
                <div className="group/cmyk relative mt-1 w-full text-right">
                  <span 
                    className="text-2xs font-mono text-ui-foreground-tertiary cursor-pointer hover:text-ui-foreground-primary transition-colors whitespace-nowrap overflow-visible block"
                    onClick={(e) => {
                      e.stopPropagation();
                      copyToClipboard(formatCMYKForDisplay(parseCMYK(entry.cmyk || 'C:0 M:0 Y:0 K:0')), 'cmyk');
                    }}
                    role="button"
                    tabIndex={0}
                    aria-label={`Copy CMYK values ${formatCMYKForDisplay(parseCMYK(entry.cmyk || 'C:0 M:0 Y:0 K:0'))}`}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        copyToClipboard(formatCMYKForDisplay(parseCMYK(entry.cmyk || 'C:0 M:0 Y:0 K:0')), 'cmyk');
                      }
                    }}
                    title={formatCMYKForDisplay(parseCMYK(entry.cmyk || 'C:0 M:0 Y:0 K:0'))}
                    style={{ fontSize: '9px', letterSpacing: '-0.01em' }}
                  >
                    {formatCMYKForDisplay(parseCMYK(entry.cmyk || 'C:0 M:0 Y:0 K:0'))}
                  </span>
                  {copiedField === 'cmyk' ? (
                    <span className="text-red-600 text-xs ml-1 absolute right-0 top-5 whitespace-nowrap bg-ui-background-secondary px-1.5 py-0.5 rounded shadow-sm border border-ui-border-light">
                      Copied!
                    </span>
                  ) : (
                    <span className="absolute right-0 top-5 opacity-0 group-hover/cmyk:opacity-100 transition-opacity duration-200 text-xs whitespace-nowrap bg-ui-background-secondary px-1.5 py-0.5 rounded shadow-sm border border-ui-border-light">
                      Click to copy
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Use portal for modal to ensure it's not constrained by parent container */}
      {hasGradient && entry.gradient && showGradientDetails && document.body && createPortal(
        <GradientDetailsModal 
          isOpen={showGradientDetails}
          onClose={() => setShowGradientDetails(false)}
          gradient={{
            gradientCSS: entry.gradient.gradientCSS,
            gradientStops: entry.gradient.gradientStops,
            type: entry.gradient.gradientCSS.includes('linear') ? 'linear' : 'radial'
          }}
          product={entry.product}
        />,
        document.body
      )}
    </>
  );
}

// Export as memoized component for performance
export default memo(ColorSwatch);