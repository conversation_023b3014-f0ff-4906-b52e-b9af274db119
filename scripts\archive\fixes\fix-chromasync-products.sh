#!/bin/bash
# Quick fix script to sync products and restart ChromaSync

echo "ChromaSync Product Sync Fix"
echo "=========================="

# Path to the local database
DB_PATH="/Users/<USER>/Library/Application Support/chroma-sync/chromasync.db"

# Check if database exists
if [ ! -f "$DB_PATH" ]; then
    echo "Error: ChromaSync database not found at $DB_PATH"
    exit 1
fi

echo "1. Syncing products to local database..."
sqlite3 "$DB_PATH" < fix-product-sync.sql

echo "2. Products synced successfully!"
echo ""
echo "3. To complete the fix:"
echo "   - Close ChromaSync if it's running"
echo "   - Restart ChromaSync"
echo "   - The products should now appear with their color counts"
echo ""
echo "Note: The product-color relationships will be synced automatically"
echo "when you restart the app with the fixed sync service."
echo ""
echo "Summary of changes:"
echo "- Fixed sync service bug that prevented products from syncing"
echo "- Manually inserted 21 products into local database"
echo "- Products will now show in the UI with proper color counts"
