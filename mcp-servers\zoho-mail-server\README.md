# Zoho Mail MCP Server

This MCP server provides integration with Zoho Mail for sending emails from ChromaSync.

## Features

- Send emails via Zoho Mail API (more reliable than SMTP)
- OAuth2 authentication with automatic token refresh
- Pre-built ChromaSync invitation email template
- Support for HTML and plain text emails
- Multiple recipients, CC, BCC support

## Setup

### 1. Get Zoho OAuth Credentials

1. Go to https://api-console.zoho.com/
2. Click "Add Client" → "Server-based Applications"
3. Fill in:
   - Client Name: `ChromaSync Email Service`
   - Homepage URL: `https://chromasync.app`
   - Authorized Redirect URIs: `https://chromasync.app/oauth/callback`
4. Note down your Client ID and Client Secret

### 2. Get Account ID

1. Go to https://mail.zoho.com/
2. Click Settings → Mail Accounts
3. Your Account ID is in the URL: `https://mail.zoho.com/zm/#settings/all/mailaccounts/{ACCOUNT_ID}`

### 3. Generate Refresh Token

Use <PERSON><PERSON><PERSON>'s OAuth playground or implement the OAuth flow to get a refresh token.

### 4. Install the MCP Server

```bash
cd mcp-servers/zoho-mail-server
npm install
npm run build
```

### 5. Configure Claude Desktop

Add to your Claude configuration:

```json
{
  "mcpServers": {
    "zoho-mail": {
      "command": "node",
      "args": ["/path/to/chromasync/mcp-servers/zoho-mail-server/build/index.js"],
      "env": {
        "ZOHO_CLIENT_ID": "your-client-id",
        "ZOHO_CLIENT_SECRET": "your-client-secret",
        "ZOHO_REFRESH_TOKEN": "your-refresh-token",
        "ZOHO_ACCOUNT_ID": "your-account-id"
      }
    }
  }
}
```

## Usage in Claude

Once configured, you can use these commands:

### Send a simple email:
```
Use the zoho-mail tool to send an <NAME_EMAIL> with subject "Test" and content "Hello!"
```

### Send a team invitation:
```
Use the zoho-mail tool to send_<NAME_EMAIL> for organization "My Team" from "John Doe" with role "member"
```

## Integrating with ChromaSync

To use this in your ChromaSync app instead of SMTP:

```typescript
// In organization.service.ts
import { exec } from 'child_process';

private async sendEmailViaMCP(email: EmailOptions) {
  const result = await exec(`node /path/to/zoho-mcp-server`, {
    env: {
      ...process.env,
      MCP_REQUEST: JSON.stringify({
        method: 'send_email',
        params: email
      })
    }
  });
  
  return JSON.parse(result.stdout);
}
```

## Benefits over SMTP

1. **Better reliability** - API calls are more reliable than SMTP
2. **Rich features** - Track email delivery, read receipts, etc.
3. **No port blocking** - Works even if port 587 is blocked
4. **Better error handling** - Detailed error messages
5. **Automatic retry** - Built-in retry logic for failed sends
