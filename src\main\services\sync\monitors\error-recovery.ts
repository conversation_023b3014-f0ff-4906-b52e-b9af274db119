/**
 * @file error-recovery.ts
 * @description Error handling and recovery system for sync operations
 * 
 * This module provides comprehensive error handling, automatic recovery,
 * and error analysis capabilities for the sync system.
 */

import { 
  SyncError, 
  ErrorRecoveryItem, 
  ErrorSeverity, 
  ErrorCategory,
  SyncEvent<PERSON>and<PERSON> 
} from '../core/sync-types';
import { getSyncConfig } from '../core/sync-config';

// ============================================================================
// ERROR RECOVERY SYSTEM
// ============================================================================

/**
 * Advanced error recovery and handling system
 */
export class ErrorRecoveryManager {
  private errorHistory: SyncError[] = [];
  private recoveryQueue = new Map<string, ErrorRecoveryItem>();
  private eventHandlers = new Map<string, SyncEventHandler[]>();
  
  private readonly MAX_ERROR_HISTORY = 100;
  private readonly RECOVERY_INTERVAL_MS = 30000; // 30 seconds
  private readonly MAX_RECOVERY_ATTEMPTS = 5;
  
  private recoveryInterval: ReturnType<typeof setInterval> | null = null;
  private isRecoveryRunning = false;

  constructor() {
    this.startRecoveryProcessor();
  }

  /**
   * Track and analyze an error
   */
  trackError(error: any, operation: string, context?: Record<string, any>): SyncError {
    const syncError: SyncError = {
      id: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      operation,
      category: this.categorizeError(error),
      severity: this.assessErrorSeverity(error, operation),
      message: this.createUserFriendlyMessage(error, operation),
      originalError: error,
      context,
      recoverable: this.isRecoverableError(error, operation)
    };

    // Add to error history
    this.errorHistory.push(syncError);
    
    // Maintain history size
    if (this.errorHistory.length > this.MAX_ERROR_HISTORY) {
      this.errorHistory.shift();
    }

    // Log error for debugging
    console.error(`[ErrorRecovery] ${syncError.severity.toUpperCase()} error in ${operation}:`, error);

    // Emit error event
    this.emitEvent('error-tracked', syncError);

    // Check if error pattern indicates a systemic issue
    this.analyzeErrorPatterns();

    return syncError;
  }

  /**
   * Queue an operation for automatic recovery
   */
  queueForRecovery(operation: string, data: any, maxAttempts?: number): void {
    const key = `${operation}-${Date.now()}`;
    
    const recoveryItem: ErrorRecoveryItem = {
      operation,
      data,
      attempts: 0,
      lastAttempt: Date.now(),
      maxAttempts: maxAttempts || this.MAX_RECOVERY_ATTEMPTS,
      nextRetry: Date.now() + this.calculateRecoveryDelay(0)
    };

    this.recoveryQueue.set(key, recoveryItem);
    
    console.log(`[ErrorRecovery] Queued ${operation} for recovery (queue size: ${this.recoveryQueue.size})`);
    
    // Emit recovery queued event
    this.emitEvent('recovery-queued', { operation, queueSize: this.recoveryQueue.size });

    // Start recovery processor if not running
    if (!this.recoveryInterval) {
      this.startRecoveryProcessor();
    }
  }

  /**
   * Get error statistics and analysis
   */
  getErrorAnalysis(): {
    totalErrors: number;
    errorsByCategory: Record<ErrorCategory, number>;
    errorsBySeverity: Record<ErrorSeverity, number>;
    recentErrorRate: number;
    topErrors: Array<{ operation: string; count: number; lastOccurrence: number }>;
    recoveryQueueSize: number;
    recommendations: string[];
  } {
    const errorsByCategory: Record<ErrorCategory, number> = {
      network: 0,
      auth: 0,
      database: 0,
      validation: 0,
      conflict: 0,
      timeout: 0
    };

    const errorsBySeverity: Record<ErrorSeverity, number> = {
      low: 0,
      medium: 0,
      high: 0,
      critical: 0
    };

    // Count errors by category and severity
    for (const error of this.errorHistory) {
      errorsByCategory[error.category]++;
      errorsBySeverity[error.severity]++;
    }

    // Calculate recent error rate (last hour)
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    const recentErrors = this.errorHistory.filter(error => error.timestamp > oneHourAgo);
    const recentErrorRate = recentErrors.length;

    // Find top error operations
    const operationCounts = new Map<string, { count: number; lastOccurrence: number }>();
    
    for (const error of this.errorHistory) {
      const existing = operationCounts.get(error.operation);
      if (existing) {
        existing.count++;
        existing.lastOccurrence = Math.max(existing.lastOccurrence, error.timestamp);
      } else {
        operationCounts.set(error.operation, { count: 1, lastOccurrence: error.timestamp });
      }
    }

    const topErrors = Array.from(operationCounts.entries())
      .map(([operation, data]) => ({ operation, ...data }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Generate recommendations
    const recommendations = this.generateErrorRecommendations(errorsByCategory, errorsBySeverity);

    return {
      totalErrors: this.errorHistory.length,
      errorsByCategory,
      errorsBySeverity,
      recentErrorRate,
      topErrors,
      recoveryQueueSize: this.recoveryQueue.size,
      recommendations
    };
  }

  /**
   * Get current recovery queue status
   */
  getRecoveryStatus(): {
    queueSize: number;
    isProcessing: boolean;
    operations: Array<{ 
      operation: string; 
      attempts: number; 
      maxAttempts: number; 
      nextRetry: number;
    }>;
  } {
    return {
      queueSize: this.recoveryQueue.size,
      isProcessing: this.isRecoveryRunning,
      operations: Array.from(this.recoveryQueue.values()).map(item => ({
        operation: item.operation,
        attempts: item.attempts,
        maxAttempts: item.maxAttempts,
        nextRetry: item.nextRetry
      }))
    };
  }

  /**
   * Manually trigger recovery for all queued operations
   */
  async forceRecovery(): Promise<void> {
    console.log('[ErrorRecovery] Forcing immediate recovery for all queued operations');
    await this.processRecoveryQueue();
  }

  /**
   * Clear the recovery queue
   */
  clearRecoveryQueue(): void {
    const size = this.recoveryQueue.size;
    this.recoveryQueue.clear();
    console.log(`[ErrorRecovery] Cleared ${size} items from recovery queue`);
    
    this.emitEvent('recovery-queue-cleared', { clearedItems: size });
  }

  /**
   * Subscribe to error recovery events
   */
  on(event: string, handler: SyncEventHandler): () => void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    
    this.eventHandlers.get(event)!.push(handler);

    // Return unsubscribe function
    return () => {
      const handlers = this.eventHandlers.get(event);
      if (handlers) {
        const index = handlers.indexOf(handler);
        if (index > -1) {
          handlers.splice(index, 1);
        }
      }
    };
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.recoveryInterval) {
      clearInterval(this.recoveryInterval);
      this.recoveryInterval = null;
    }
    
    this.isRecoveryRunning = false;
    this.eventHandlers.clear();
    console.log('[ErrorRecovery] Error recovery manager destroyed');
  }

  // ============================================================================
  // PRIVATE METHODS
  // ============================================================================

  /**
   * Categorize error based on its characteristics
   */
  private categorizeError(error: any): ErrorCategory {
    const message = error.message?.toLowerCase() || '';
    
    if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
      return 'network';
    }
    
    if (message.includes('auth') || message.includes('permission') || message.includes('unauthorized')) {
      return 'auth';
    }
    
    if (message.includes('database') || message.includes('sql') || message.includes('constraint')) {
      return 'database';
    }
    
    if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
      return 'validation';
    }
    
    if (message.includes('conflict') || message.includes('duplicate')) {
      return 'conflict';
    }
    
    if (message.includes('timeout') || message.includes('timed out')) {
      return 'timeout';
    }
    
    return 'database'; // Default category
  }

  /**
   * Assess error severity
   */
  private assessErrorSeverity(error: any, operation: string): ErrorSeverity {
    const message = error.message?.toLowerCase() || '';
    
    // Critical errors that affect core functionality
    if (message.includes('organization') || operation.includes('initialization')) {
      return 'critical';
    }
    
    // High severity for auth and database errors
    if (message.includes('auth') || message.includes('database')) {
      return 'high';
    }
    
    // Medium severity for network and validation errors
    if (message.includes('network') || message.includes('validation')) {
      return 'medium';
    }
    
    // Low severity for minor issues
    return 'low';
  }

  /**
   * Create user-friendly error message
   */
  private createUserFriendlyMessage(error: any, operation: string): string {
    const message = error.message?.toLowerCase() || '';
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'Network connection issue - please check your internet connection and try again';
    }
    
    if (message.includes('auth') || message.includes('permission')) {
      return 'Authentication error - please sign in again to continue syncing';
    }
    
    if (message.includes('limit') || message.includes('quota')) {
      return 'Sync limit reached - upgrade your plan to continue syncing';
    }
    
    if (message.includes('organization')) {
      return 'Organization not found - please check your organization access';
    }
    
    if (message.includes('timeout')) {
      return 'Sync is taking longer than expected - please try again in a moment';
    }
    
    if (message.includes('connection')) {
      return 'Connection lost - automatically retrying in the background';
    }
    
    return `Unable to sync ${operation} - please try again later`;
  }

  /**
   * Check if error is recoverable
   */
  private isRecoverableError(error: any, operation: string): boolean {
    const message = error.message?.toLowerCase() || '';
    
    // Non-recoverable errors
    if (message.includes('auth') || message.includes('permission')) {
      return false; // Auth errors need manual intervention
    }
    
    if (message.includes('limit') || message.includes('quota')) {
      return false; // Rate limit errors need time or upgrade
    }
    
    if (message.includes('organization') && operation.includes('initialization')) {
      return false; // Org errors in initialization need manual intervention
    }
    
    // Most other errors are recoverable
    return true;
  }

  /**
   * Start the recovery processor
   */
  private startRecoveryProcessor(): void {
    if (this.recoveryInterval) {
      return; // Already running
    }
    
    console.log('[ErrorRecovery] Starting recovery processor');
    
    this.recoveryInterval = setInterval(() => {
      this.processRecoveryQueue().catch(error => {
        console.error('[ErrorRecovery] Error in recovery processor:', error);
      });
    }, this.RECOVERY_INTERVAL_MS);
  }

  /**
   * Process the recovery queue
   */
  private async processRecoveryQueue(): Promise<void> {
    if (this.recoveryQueue.size === 0) {
      this.stopRecoveryProcessor();
      return;
    }

    if (this.isRecoveryRunning) {
      return; // Already processing
    }

    this.isRecoveryRunning = true;
    
    try {
      const now = Date.now();
      const itemsToRetry: Array<[string, ErrorRecoveryItem]> = [];
      const itemsToRemove: string[] = [];

      // Find items ready for retry
      for (const [key, item] of this.recoveryQueue.entries()) {
        if (item.attempts >= item.maxAttempts) {
          // Max attempts reached - remove from queue
          itemsToRemove.push(key);
          console.log(`[ErrorRecovery] Giving up on ${item.operation} after ${item.attempts} attempts`);
          
          this.emitEvent('recovery-abandoned', { operation: item.operation, attempts: item.attempts });
        } else if (now >= item.nextRetry) {
          // Ready for retry
          itemsToRetry.push([key, item]);
        }
      }

      // Remove exhausted items
      itemsToRemove.forEach(key => this.recoveryQueue.delete(key));

      // Process retries
      for (const [key, item] of itemsToRetry) {
        try {
          console.log(`[ErrorRecovery] Attempting recovery for ${item.operation} (attempt ${item.attempts + 1}/${item.maxAttempts})`);
          
          await this.retryOperation(item.operation, item.data);
          
          // Success - remove from queue
          this.recoveryQueue.delete(key);
          console.log(`[ErrorRecovery] Successfully recovered ${item.operation}`);
          
          this.emitEvent('recovery-success', { operation: item.operation, attempts: item.attempts + 1 });

        } catch (error) {
          // Update attempt count and next retry time
          item.attempts++;
          item.lastAttempt = now;
          item.nextRetry = now + this.calculateRecoveryDelay(item.attempts);
          
          console.warn(`[ErrorRecovery] Recovery attempt ${item.attempts} failed for ${item.operation}:`, error);
          
          this.emitEvent('recovery-failed', { 
            operation: item.operation, 
            attempts: item.attempts, 
            error: error.message 
          });
        }
      }

      console.log(`[ErrorRecovery] Recovery cycle complete. Queue size: ${this.recoveryQueue.size}`);

    } finally {
      this.isRecoveryRunning = false;
    }
  }

  /**
   * Stop the recovery processor
   */
  private stopRecoveryProcessor(): void {
    if (this.recoveryInterval) {
      clearInterval(this.recoveryInterval);
      this.recoveryInterval = null;
      console.log('[ErrorRecovery] Stopped recovery processor');
    }
  }

  /**
   * Calculate exponential backoff delay for recovery attempts
   */
  private calculateRecoveryDelay(attempt: number): number {
    const config = getSyncConfig();
    return config.calculateRetryDelay(attempt);
  }

  /**
   * Retry a failed operation (placeholder - would be implemented by specific strategies)
   */
  private async retryOperation(operation: string, data: any): Promise<void> {
    // This is a placeholder - in the actual implementation, this would
    // delegate to the appropriate sync strategy or operation handler
    console.log(`[ErrorRecovery] Retrying operation: ${operation}`);
    
    // For now, simulate a retry that might succeed or fail
    if (Math.random() > 0.3) { // 70% success rate for simulation
      return; // Success
    } else {
      throw new Error(`Retry failed for ${operation}`);
    }
  }

  /**
   * Analyze error patterns for systemic issues
   */
  private analyzeErrorPatterns(): void {
    // Check for error spikes in the last 10 minutes
    const tenMinutesAgo = Date.now() - (10 * 60 * 1000);
    const recentErrors = this.errorHistory.filter(error => error.timestamp > tenMinutesAgo);
    
    if (recentErrors.length > 10) {
      console.warn(`[ErrorRecovery] Error spike detected: ${recentErrors.length} errors in last 10 minutes`);
      this.emitEvent('error-spike', { count: recentErrors.length, timeWindow: '10 minutes' });
    }
  }

  /**
   * Generate error-based recommendations
   */
  private generateErrorRecommendations(
    byCategory: Record<ErrorCategory, number>,
    bySeverity: Record<ErrorSeverity, number>
  ): string[] {
    const recommendations: string[] = [];
    
    if (byCategory.network > 5) {
      recommendations.push('High network error rate - check internet connectivity');
    }
    
    if (byCategory.auth > 2) {
      recommendations.push('Authentication issues detected - verify user credentials');
    }
    
    if (bySeverity.critical > 0) {
      recommendations.push('Critical errors detected - immediate attention required');
    }
    
    if (byCategory.timeout > 3) {
      recommendations.push('Multiple timeout errors - consider reducing batch sizes');
    }
    
    return recommendations;
  }

  /**
   * Emit event to all registered handlers
   */
  private emitEvent(event: string, data: any): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`[ErrorRecovery] Error in event handler for ${event}:`, error);
        }
      });
    }
  }
}
