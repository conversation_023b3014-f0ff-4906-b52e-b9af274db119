#!/usr/bin/env node

/**
 * Test script to verify multi-tenant features
 */

const sqlite3 = require('better-sqlite3');
const path = require('path');
const os = require('os');

// Get database path
const dbPath = path.join(
  os.homedir(),
  'Library',
  'Application Support',
  'chromasync',
  'chromasync.db'
);

console.log('Database path:', dbPath);

try {
  const db = new sqlite3(dbPath);
  
  // Check if users table exists
  const tables = db.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' 
    ORDER BY name
  `).all();
  
  console.log('\nTables in database:');
  tables.forEach(table => console.log(`  - ${table.name}`));
  
  // Check if organization tables exist
  const hasOrgTables = tables.some(t => t.name === 'organizations');
  const hasUserTable = tables.some(t => t.name === 'users');
  const hasInvitationsTable = tables.some(t => t.name === 'organization_invitations');
  
  console.log('\nMulti-tenant tables status:');
  console.log(`  - organizations: ${hasOrgTables ? '✅' : '❌'}`);
  console.log(`  - organization_members: ${tables.some(t => t.name === 'organization_members') ? '✅' : '❌'}`);
  console.log(`  - users: ${hasUserTable ? '✅' : '❌'}`);
  console.log(`  - organization_invitations: ${hasInvitationsTable ? '✅' : '❌'}`);
  
  // Count records
  if (hasOrgTables) {
    const orgCount = db.prepare('SELECT COUNT(*) as count FROM organizations').get();
    console.log(`\nOrganization count: ${orgCount.count}`);
    
    const orgs = db.prepare('SELECT * FROM organizations LIMIT 5').all();
    if (orgs.length > 0) {
      console.log('\nSample organizations:');
      orgs.forEach(org => {
        console.log(`  - ${org.name} (${org.plan})`);
      });
    }
  }
  
  if (hasUserTable) {
    const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get();
    console.log(`\nUser count: ${userCount.count}`);
  }
  
  // Check migrations
  const migrations = db.prepare('SELECT * FROM schema_migrations ORDER BY version').all();
  console.log('\nApplied migrations:');
  migrations.forEach(m => {
    console.log(`  - ${m.version}: ${m.name}`);
  });
  
  db.close();
  
} catch (error) {
  console.error('Error:', error.message);
  process.exit(1);
}
