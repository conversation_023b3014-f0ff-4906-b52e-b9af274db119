import { app } from 'electron';
import fs from 'fs/promises';
import path from 'path';
import { SharedFolderConfig, SharedFolderFile } from '../shared/types/shared-folder';

export class SharedFolderManager {
  private folderPath: string;
  private config: SharedFolderConfig;

  constructor(config: SharedFolderConfig) {
    this.config = config;
    this.folderPath = this.resolvePath();
    console.log(`[SharedFolderManager] Resolved path: ${this.folderPath}`);
  }

  private resolvePath(): string {
    // Use the basePath directly and append the specific folderName
    // Ensure basePath is valid before joining
    if (!this.config.basePath || typeof this.config.basePath !== 'string') {
      console.error('[SharedFolderManager] Invalid or missing basePath in configuration.');
      // Fallback to a default path in userData to prevent crashing, though this might not be desired behavior
      const fallbackPath = path.join(app.getPath('userData'), 'pantone_tracker_shared_fallback');
      console.warn(`[SharedFolderManager] Falling back to path: ${fallbackPath}`);
      return fallbackPath; 
    }
    // Construct the full path by joining the basePath and the folderName
    return path.join(this.config.basePath, this.config.folderName);
  }

  async ensureExists(): Promise<boolean> {
    try {
      await fs.mkdir(this.folderPath, { recursive: true });
      return true;
    } catch (error) {
      console.error('Failed to create shared folder:', error);
      return false;
    }
  }

  getPath(): string {
    return this.folderPath;
  }

  async readFile(fileName: string): Promise<string> {
    try {
      const filePath = path.join(this.folderPath, fileName);
      return await fs.readFile(filePath, 'utf-8');
    } catch (error) {
      console.error(`Failed to read file ${fileName}:`, error);
      throw error;
    }
  }

  async writeFile(fileName: string, content: string): Promise<boolean> {
    try {
      const filePath = path.join(this.folderPath, fileName);
      await fs.writeFile(filePath, content, 'utf-8');
      return true;
    } catch (error) {
      console.error(`Failed to write file ${fileName}:`, error);
      return false;
    }
  }

  async listFiles(): Promise<SharedFolderFile[]> {
    try {
      const files = await fs.readdir(this.folderPath, { withFileTypes: true });
      
      const fileDetails = await Promise.all(
        files.map(async (file) => {
          const filePath = path.join(this.folderPath, file.name);
          let stats;
          
          try {
            stats = await fs.stat(filePath);
          } catch (error) {
            console.error(`Failed to get stats for ${file.name}:`, error);
            stats = { size: 0, mtime: new Date() };
          }
          
          return {
            name: file.name,
            path: filePath,
            isDirectory: file.isDirectory(),
            size: stats.size,
            modified: stats.mtime
          };
        })
      );
      
      return fileDetails;
    } catch (error) {
      console.error('Failed to list files:', error);
      return [];
    }
  }
} 