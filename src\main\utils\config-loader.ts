/**
 * @file config-loader.ts
 * @description Load configuration from environment or build config
 */

import { app } from 'electron';
import path from 'path';
import fs from 'fs';

interface AppConfig {
  ZOHO_CLIENT_ID?: string;
  ZOHO_CLIENT_SECRET?: string;
  ZOHO_ACCOUNT_ID?: string;
  ZOHO_REFRESH_TOKEN?: string;
  ZOHO_REGION?: string;
  ZOHO_SUPPORT_ALIAS?: string;
  SUPABASE_URL?: string;
  SUPABASE_ANON_KEY?: string;
}

let cachedConfig: AppConfig | null = null;

export function loadAppConfig(): AppConfig {
  if (cachedConfig) return cachedConfig;

  // First try environment variables (development mode)
  if (process.env.ZOHO_CLIENT_ID) {
    console.log('[Config] Using environment variables');
    cachedConfig = {
      ZOHO_CLIENT_ID: process.env.ZOHO_CLIENT_ID,
      ZOHO_CLIENT_SECRET: process.env.ZOHO_CLIENT_SECRET,
      ZOHO_ACCOUNT_ID: process.env.ZOHO_ACCOUNT_ID,
      ZOHO_REFRESH_TOKEN: process.env.ZOHO_REFRESH_TOKEN,
      ZOHO_REGION: process.env.ZOHO_REGION,
      ZOHO_SUPPORT_ALIAS: process.env.ZOHO_SUPPORT_ALIAS,
      SUPABASE_URL: process.env.SUPABASE_URL,
      SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY
    };
    return cachedConfig;
  }

  // Try loading from build config (production mode)
  try {
    const configPath = app.isPackaged 
      ? path.join(process.resourcesPath, 'app-config.json')
      : path.join(__dirname, '../../out/app-config.json');
    
    if (fs.existsSync(configPath)) {
      console.log('[Config] Loading from build config:', configPath);
      const configData = fs.readFileSync(configPath, 'utf-8');
      cachedConfig = JSON.parse(configData);
      return cachedConfig;
    }
  } catch (error) {
    console.error('[Config] Failed to load build config:', error);
  }

  console.warn('[Config] No configuration found - email service will not work');
  return {};
}

// Helper to get config value
export function getConfig(key: keyof AppConfig): string | undefined {
  const config = loadAppConfig();
  return config[key];
}
