/**
 * @file organization.service.ts
 * @description Service for managing organizations and team members
 */

import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import { 
  Organization, 
  OrganizationMember
} from '../../../shared/types/organization.types';
import { getSupabaseClient } from '../../services/supabase-client';
import nodemailer from 'nodemailer';
import type Mail from 'nodemailer/lib/mailer';

export class OrganizationService {
  private emailTransporter: Mail | null = null;
  
  constructor(private db: Database.Database) {
    this.initializeEmailTransporter();
  }

  /**
   * Initialize Zoho Mail transporter for sending <NAME_EMAIL> alias
   */
  private initializeEmailTransporter() {
    try {
      // Use Zoho Mail EU server for sending emails with professional configuration
      this.emailTransporter = nodemailer.createTransport({
        host: 'smtp.zoho.eu', // EU server - change to smtp.zoho.com for US accounts
        port: 587,
        secure: false, // true for 465, false for other ports
        auth: {
          user: process.env.ZOHO_EMAIL || '<EMAIL>', // Main account for auth
          pass: process.env.ZOHO_PASSWORD || ''
        },
        // Professional SMTP settings
        pool: true,
        maxConnections: 5,
        rateDelta: 20000, // 20 seconds between batches
        rateLimit: 5, // Max 5 emails per batch
        // Security settings
        requireTLS: true,
        tls: {
          ciphers: 'SSLv3'
        }
      });
      
      console.log('[Organization] Email transporter <NAME_EMAIL> alias');
    } catch (error) {
      console.error('[Organization] Failed to initialize email transporter:', error);
    }
  }

  /**
   * Create a new organization
   */
  async createOrganization(
    name: string, 
    ownerId: string
  ): Promise<Organization> {
    // Check for duplicate organization name for this user
    const existingOrg = this.db.prepare(`
      SELECT o.name 
      FROM organizations o
      JOIN organization_members om ON o.id = om.organization_id
      WHERE om.user_id = ? AND LOWER(o.name) = LOWER(?)
    `).get(ownerId, name);

    if (existingOrg) {
      throw new Error(`You already have an organization named "${name}"`);
    }

    const orgId = this.db.prepare(`
      SELECT COALESCE(MAX(id), 0) + 1 as next_id FROM organizations
    `).get().next_id;

    const externalId = uuidv4();
    const slug = this.generateSlug(name);

    this.db.transaction(() => {
      // Create organization
      this.db.prepare(`
        INSERT INTO organizations (id, external_id, name, slug, plan, settings)
        VALUES (?, ?, ?, ?, 'free', '{}')
      `).run(orgId, externalId, name, slug);

      // Add owner as first member
      this.db.prepare(`
        INSERT INTO organization_members (organization_id, user_id, role)
        VALUES (?, ?, 'owner')
      `).run(orgId, ownerId);
    })();

    const org = this.db.prepare(`
      SELECT * FROM organizations WHERE id = ?
    `).get(orgId);

    return this.mapToOrganization(org);
  }

  /**
   * Get organizations for a user
   */
  async getOrganizationsForUser(userId: string): Promise<Organization[]> {
    const rows = this.db.prepare(`
      SELECT o.*, om.role as user_role, COUNT(om2.user_id) as member_count
      FROM organizations o
      JOIN organization_members om ON o.id = om.organization_id
      LEFT JOIN organization_members om2 ON o.id = om2.organization_id
      WHERE om.user_id = ?
      GROUP BY o.id
    `).all(userId);

    return rows.map(row => ({
      ...this.mapToOrganization(row),
      userRole: row.user_role // Add the user's role to the organization object
    }));
  }

  /**
   * Get organization by ID
   */
  async getOrganization(orgId: string): Promise<Organization | null> {
    const row = this.db.prepare(`
      SELECT o.*, COUNT(om.user_id) as member_count
      FROM organizations o
      LEFT JOIN organization_members om ON o.id = om.organization_id
      WHERE o.external_id = ?
      GROUP BY o.id
    `).get(orgId);

    return row ? this.mapToOrganization(row) : null;
  }

  /**
   * Update organization
   */
  async updateOrganization(
    orgId: string,
    updates: Partial<Organization>
  ): Promise<Organization | null> {
    const allowedUpdates = ['name', 'plan', 'settings'];
    const updateFields = Object.keys(updates)
      .filter(key => allowedUpdates.includes(key))
      .map(key => `${key} = ?`);

    if (updateFields.length === 0) {
      return this.getOrganization(orgId);
    }

    const values = updateFields.map(field => {
      const key = field.split(' = ')[0];
      return key === 'settings' 
        ? JSON.stringify(updates[key]) 
        : updates[key];
    });

    this.db.prepare(`
      UPDATE organizations
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE external_id = ?
    `).run(...values, orgId);

    return this.getOrganization(orgId);
  }

  /**
   * Delete organization
   */
  async deleteOrganization(orgId: string, userId: string): Promise<boolean> {
    try {
      // Check if user has permission (must be owner)
      const userRole = await this.getUserRole(orgId, userId);
      if (userRole !== 'owner') {
        throw new Error('Only organization owners can delete organizations');
      }

      // Check if organization has any colors or products
      const hasData = this.db.prepare(`
        SELECT 
          (SELECT COUNT(*) FROM colors WHERE organization_id = (SELECT id FROM organizations WHERE external_id = ?)) +
          (SELECT COUNT(*) FROM products WHERE organization_id = (SELECT id FROM organizations WHERE external_id = ?)) as total
      `).get(orgId, orgId);

      if (hasData && hasData.total > 0) {
        throw new Error('Cannot delete organization with existing colors or products. Please delete all data first.');
      }

      // Get the internal organization ID
      const org = this.db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `).get(orgId);

      if (!org) {
        throw new Error('Organization not found');
      }

      // Delete in transaction
      this.db.transaction(() => {
        // Delete memberships
        this.db.prepare(`
          DELETE FROM organization_members WHERE organization_id = ?
        `).run(org.id);

        // Delete organization
        this.db.prepare(`
          DELETE FROM organizations WHERE id = ?
        `).run(org.id);
      })();

      // Also delete from Supabase
      try {
        const supabase = getSupabaseClient();
        await supabase
          .from('organizations')
          .delete()
          .eq('id', orgId);
      } catch (error) {
        console.error('Failed to delete organization from Supabase:', error);
        // Continue even if Supabase delete fails
      }

      return true;
    } catch (error) {
      console.error('Error deleting organization:', error);
      throw error;
    }
  }

  /**
   * Get organization members
   */
  async getMembers(orgId: string): Promise<OrganizationMember[]> {
    const rows = this.db.prepare(`
      SELECT 
        om.*
      FROM organization_members om
      JOIN organizations o ON om.organization_id = o.id
      WHERE o.external_id = ?
      ORDER BY om.joined_at DESC
    `).all(orgId);

    // Get the current user ID from Supabase
    let currentUserId: string | null = null;
    try {
      const supabase = getSupabaseClient();
      const { data: { user } } = await supabase.auth.getUser();
      currentUserId = user?.id || null;
    } catch (error) {
      console.error('Failed to get current user:', error);
    }

    return rows.map(row => ({
      ...this.mapToMember(row),
      isCurrentUser: row.user_id === currentUserId
    }));
  }

  /**
   * Add member to organization
   */
  async addMember(
    orgId: string,
    userId: string,
    role: 'admin' | 'member' = 'member',
    invitedBy?: string
  ): Promise<OrganizationMember> {
    const org = this.db.prepare(`
      SELECT id FROM organizations WHERE external_id = ?
    `).get(orgId);

    if (!org) {
      throw new Error('Organization not found');
    }

    // Check if user is already a member
    const existing = this.db.prepare(`
      SELECT 1 FROM organization_members 
      WHERE organization_id = ? AND user_id = ?
    `).get(org.id, userId);

    if (existing) {
      throw new Error('User is already a member');
    }

    this.db.prepare(`
      INSERT INTO organization_members (organization_id, user_id, role, invited_by)
      VALUES (?, ?, ?, ?)
    `).run(org.id, userId, role, invitedBy);

    return this.getMember(orgId, userId);
  }

  /**
   * Update member role
   */
  async updateMemberRole(
    orgId: string,
    userId: string,
    role: 'admin' | 'member'
  ): Promise<OrganizationMember | null> {
    const org = this.db.prepare(`
      SELECT id FROM organizations WHERE external_id = ?
    `).get(orgId);

    if (!org) {
      return null;
    }

    // Can't change owner role
    const member = this.db.prepare(`
      SELECT role FROM organization_members
      WHERE organization_id = ? AND user_id = ?
    `).get(org.id, userId);

    if (!member || member.role === 'owner') {
      return null;
    }

    this.db.prepare(`
      UPDATE organization_members
      SET role = ?
      WHERE organization_id = ? AND user_id = ?
    `).run(role, org.id, userId);

    return this.getMember(orgId, userId);
  }

  /**
   * Remove member from organization
   */
  async removeMember(orgId: string, userId: string): Promise<boolean> {
    const org = this.db.prepare(`
      SELECT id FROM organizations WHERE external_id = ?
    `).get(orgId);

    if (!org) {
      return false;
    }

    // Can't remove owner
    const member = this.db.prepare(`
      SELECT role FROM organization_members
      WHERE organization_id = ? AND user_id = ?
    `).get(org.id, userId);

    if (!member || member.role === 'owner') {
      return false;
    }

    const result = this.db.prepare(`
      DELETE FROM organization_members
      WHERE organization_id = ? AND user_id = ?
    `).run(org.id, userId);

    return result.changes > 0;
  }

  /**
   * Check if user is member of organization
   */
  async isMember(orgId: string, userId: string): Promise<boolean> {
    const result = this.db.prepare(`
      SELECT 1 FROM organization_members om
      JOIN organizations o ON om.organization_id = o.id
      WHERE o.external_id = ? AND om.user_id = ?
    `).get(orgId, userId);

    return !!result;
  }

  /**
   * Get member details
   */
  async getMember(orgId: string, userId: string): Promise<OrganizationMember | null> {
    const row = this.db.prepare(`
      SELECT 
        om.*,
        u.email as user_email,
        u.name as user_name
      FROM organization_members om
      JOIN organizations o ON om.organization_id = o.id
      LEFT JOIN users u ON om.user_id = u.id
      WHERE o.external_id = ? AND om.user_id = ?
    `).get(orgId, userId);

    return row ? this.mapToMember(row) : null;
  }

  /**
   * Get user's role in organization
   */
  async getUserRole(orgId: string, userId: string): Promise<string | null> {
    const result = this.db.prepare(`
      SELECT om.role 
      FROM organization_members om
      JOIN organizations o ON om.organization_id = o.id
      WHERE o.external_id = ? AND om.user_id = ?
    `).get(orgId, userId);

    return result?.role || null;
  }

  /**
   * Create an invitation to join an organization
   */
  async inviteMember(
    orgId: string,
    email: string,
    role: 'admin' | 'member',
    invitedById: string
  ): Promise<{ success: boolean; invitation?: any; error?: string }> {
    try {
      const org = this.db.prepare(`
        SELECT id, name FROM organizations WHERE external_id = ?
      `).get(orgId);

      console.log('[Organization] DEBUG - Organization lookup result:');
      console.log('  orgId (external_id):', orgId);
      console.log('  org found:', org);

      if (!org) {
        return { success: false, error: 'Organization not found' };
      }

      // Check if user has permission to invite (must be owner or admin)
      const inviterRole = await this.getUserRole(orgId, invitedById);
      if (!inviterRole || (inviterRole !== 'owner' && inviterRole !== 'admin')) {
        return { success: false, error: 'Insufficient permissions to invite members' };
      }

      // Check if email is already a member (skip this check since users table may not exist)
      // This will be caught when the user actually tries to join if they're already a member

      // Check if invitation already exists (including expired ones due to UNIQUE constraint)
      const existingInvitation = this.db.prepare(`
        SELECT id, email, accepted_at, expires_at FROM organization_invitations 
        WHERE organization_id = ? AND email = ?
      `).get(org.id, email);

      console.log('[Organization] DEBUG - Existing invitation check:');
      console.log('  existingInvitation:', existingInvitation);

      if (existingInvitation) {
        if (existingInvitation.accepted_at) {
          return { success: false, error: 'User has already accepted an invitation to this organization' };
        } else {
          // Delete the old invitation to allow a new one (in case it expired)
          console.log('[Organization] DEBUG - Deleting existing invitation to allow new one');
          this.db.prepare(`
            DELETE FROM organization_invitations 
            WHERE organization_id = ? AND email = ?
          `).run(org.id, email);
        }
      }

      // Create invitation
      const externalId = uuidv4();
      const token = uuidv4();
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7); // 7 days expiry

      // Debug logging before insert
      console.log('[Organization] DEBUG - About to insert invitation with values:');
      console.log('  externalId:', externalId);
      console.log('  org.id:', org.id, '(type:', typeof org.id, ')');
      console.log('  org.name:', org.name);
      console.log('  email:', email);
      console.log('  role:', role);
      console.log('  invitedById:', invitedById);
      console.log('  token:', token.substring(0, 8) + '...');
      console.log('  expiresAt:', expiresAt.toISOString());

      // First check if the organization_invitations table exists and get its schema
      const tableExists = this.db.prepare(`
        SELECT sql FROM sqlite_master 
        WHERE type='table' AND name='organization_invitations'
      `).get();
      
      console.log('[Organization] DEBUG - Table check:');
      console.log('  organization_invitations table exists:', !!tableExists);
      
      if (!tableExists) {
        throw new Error('organization_invitations table does not exist - migration may have failed');
      }
      
      console.log('[Organization] DEBUG - Table schema:');
      console.log('  SQL:', tableExists.sql);
      
      // Also check what foreign key constraints exist
      const foreignKeys = this.db.prepare(`
        PRAGMA foreign_key_list(organization_invitations)
      `).all();
      
      console.log('[Organization] DEBUG - Foreign key constraints:');
      console.log('  constraints:', foreignKeys);

      // Ensure the inviter exists in the users table (required for FK constraint)
      // First check if users table exists and get its schema
      const usersTableSchema = this.db.prepare(`
        SELECT sql FROM sqlite_master 
        WHERE type='table' AND name='users'
      `).get();
      
      console.log('[Organization] DEBUG - Users table check:');
      console.log('  users table exists:', !!usersTableSchema);
      console.log('  users table schema:', usersTableSchema?.sql || 'None');
      
      // Check if users table exists and what columns it has
      if (usersTableSchema) {
        try {
          const usersTableInfo = this.db.prepare('PRAGMA table_info(users)').all();
          console.log('[Organization] DEBUG - Users table columns:', usersTableInfo.map(col => col.name));
        } catch (pragmaError) {
          console.log('[Organization] DEBUG - Could not get table info:', pragmaError.message);
        }
      }
      
      let inviterExists = false;
      if (usersTableSchema) {
        try {
          inviterExists = !!this.db.prepare(`
            SELECT id FROM users WHERE id = ?
          `).get(invitedById);
        } catch (selectError) {
          console.log('[Organization] DEBUG - Error checking inviter:', selectError.message);
        }
      }
      
      console.log('[Organization] DEBUG - Inviter check:');
      console.log('  invitedById:', invitedById);
      console.log('  inviterExists:', inviterExists);
      
      if (!inviterExists && usersTableSchema) {
        console.log('[Organization] DEBUG - Creating user record for inviter');
        // Create a minimal user record for the inviter to satisfy FK constraint
        const userEmail = `user-${invitedById.substring(0, 8)}@local.app`;
        const userName = `User ${invitedById.substring(0, 8)}`;
        
        try {
          // Check what columns actually exist in the users table
          const tableInfo = this.db.prepare('PRAGMA table_info(users)').all();
          const columnNames = tableInfo.map(col => col.name);
          
          if (columnNames.includes('name')) {
            // Table has name column - use the expected schema
            this.db.prepare(`
              INSERT INTO users (id, email, name, created_at, updated_at)
              VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `).run(invitedById, userEmail, userName);
          } else {
            // Table doesn't have name column - use minimal schema
            const columns = ['id', 'email'];
            const values = [invitedById, userEmail];
            
            if (columnNames.includes('created_at')) {
              columns.push('created_at');
              values.push('CURRENT_TIMESTAMP');
            }
            if (columnNames.includes('updated_at')) {
              columns.push('updated_at'); 
              values.push('CURRENT_TIMESTAMP');
            }
            
            const placeholders = values.map(v => v === 'CURRENT_TIMESTAMP' ? 'CURRENT_TIMESTAMP' : '?');
            const actualValues = values.filter(v => v !== 'CURRENT_TIMESTAMP');
            
            this.db.prepare(`
              INSERT INTO users (${columns.join(', ')})
              VALUES (${placeholders.join(', ')})
            `).run(...actualValues);
          }
          
          console.log('[Organization] DEBUG - Created user record for inviter');
        } catch (insertError) {
          console.error('[Organization] DEBUG - Failed to create user record:', insertError.message);
          throw insertError;
        }
      }

      try {
        this.db.prepare(`
          INSERT INTO organization_invitations 
          (external_id, organization_id, email, role, invited_by, token, expires_at)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `).run(externalId, org.id, email, role, invitedById, token, expiresAt.toISOString());
      } catch (insertError) {
        console.error('[Organization] DEBUG - Insert error details:', {
          code: insertError.code,
          message: insertError.message,
          values: { externalId, orgId: org.id, email, role, invitedById, tokenPrefix: token.substring(0, 8) }
        });
        throw insertError;
      }

      // Send email notification directly (bypassing Edge Function)
      try {
        // Get inviter details (simplified to avoid users table dependency)
        const inviter = this.db.prepare(`
          SELECT om.*
          FROM organization_members om
          WHERE om.user_id = ? AND om.organization_id = ?
        `).get(invitedById, org.id);

        // Send email directly via Brevo
        const emailSent = await this.sendInvitationEmailDirect(email, {
          organizationName: org.name,
          inviterName: inviter ? `Team member (${inviter.user_id.substring(0, 8)})` : 'A team member',
          role: role,
          token: token,
          expiresAt: expiresAt
        });

        if (!emailSent) {
          console.error('[Organization] Failed to send invitation email to', email);
          // Don't fail the invitation creation if email fails
        } else {
          console.log('[Organization] Invitation email sent successfully to', email);
        }
      } catch (emailError) {
        console.error('[Organization] Email sending error:', emailError);
        // Continue anyway - invitation is created
      }

      return { 
        success: true, 
        invitation: {
          id: externalId,
          email,
          role,
          token,
          expiresAt: expiresAt.toISOString()
        }
      };
    } catch (error) {
      console.error('[Organization] Error inviting member:', error);
      return { success: false, error: 'Failed to create invitation' };
    }
  }

  /**
   * Accept an invitation to join an organization
   */
  async acceptInvitation(
    token: string,
    userId: string
  ): Promise<{ success: boolean; organization?: Organization; error?: string }> {
    try {
      // Find the invitation
      const invitation = this.db.prepare(`
        SELECT * FROM organization_invitations 
        WHERE token = ? AND accepted_at IS NULL
      `).get(token);

      if (!invitation) {
        return { success: false, error: 'Invalid or expired invitation' };
      }

      // Check if invitation has expired
      if (new Date(invitation.expires_at) < new Date()) {
        return { success: false, error: 'This invitation has expired' };
      }

      // Check if user email matches invitation email
      const user = this.db.prepare(`
        SELECT email FROM users WHERE id = ?
      `).get(userId);

      if (!user || user.email !== invitation.email) {
        return { success: false, error: 'This invitation is for a different email address' };
      }

      // Add user to organization
      this.db.transaction(() => {
        // Add member
        this.db.prepare(`
          INSERT INTO organization_members (organization_id, user_id, role, invited_by)
          VALUES (?, ?, ?, ?)
        `).run(invitation.organization_id, userId, invitation.role, invitation.invited_by);

        // Mark invitation as accepted
        this.db.prepare(`
          UPDATE organization_invitations 
          SET accepted_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).run(invitation.id);
      })();

      // Get the organization
      const org = this.db.prepare(`
        SELECT * FROM organizations WHERE id = ?
      `).get(invitation.organization_id);

      return { 
        success: true, 
        organization: org ? this.mapToOrganization(org) : undefined
      };
    } catch (error) {
      console.error('[Organization] Error accepting invitation:', error);
      return { success: false, error: 'Failed to accept invitation' };
    }
  }

  /**
   * Get pending invitations for an organization
   */
  async getPendingInvitations(orgId: string): Promise<any[]> {
    const org = this.db.prepare(`
      SELECT id FROM organizations WHERE external_id = ?
    `).get(orgId);

    if (!org) {
      return [];
    }

    const invitations = this.db.prepare(`
      SELECT 
        oi.*
      FROM organization_invitations oi
      WHERE oi.organization_id = ? 
      AND oi.accepted_at IS NULL 
      AND oi.expires_at > CURRENT_TIMESTAMP
      ORDER BY oi.created_at DESC
    `).all(org.id);

    return invitations.map(inv => ({
      id: inv.external_id,
      email: inv.email,
      role: inv.role,
      invitedBy: {
        id: inv.invited_by,
        name: `User ${inv.invited_by.substring(0, 8)}` // Fallback since users table may not exist
      },
      expiresAt: inv.expires_at,
      createdAt: inv.created_at
    }));
  }

  /**
   * Revoke an invitation
   */
  async revokeInvitation(orgId: string, invitationId: string): Promise<boolean> {
    const org = this.db.prepare(`
      SELECT id FROM organizations WHERE external_id = ?
    `).get(orgId);

    if (!org) {
      return false;
    }

    const result = this.db.prepare(`
      DELETE FROM organization_invitations 
      WHERE organization_id = ? AND external_id = ? AND accepted_at IS NULL
    `).run(org.id, invitationId);

    return result.changes > 0;
  }

  /**
   * Generate URL-safe slug from name
   */
  private generateSlug(name: string): string {
    const baseSlug = name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');

    // Check for uniqueness
    let slug = baseSlug;
    let counter = 1;
    
    while (this.db.prepare('SELECT 1 FROM organizations WHERE slug = ?').get(slug)) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    return slug;
  }

  /**
   * Map database row to Organization
   */
  private mapToOrganization(row: any): Organization {
    return {
      id: row.external_id,
      external_id: row.external_id,
      name: row.name,
      slug: row.slug,
      plan: row.plan,
      settings: typeof row.settings === 'string' 
        ? JSON.parse(row.settings) 
        : row.settings || {},
      created_at: row.created_at,
      updated_at: row.updated_at,
      memberCount: row.member_count || 1
    };
  }

  /**
   * Map database row to OrganizationMember
   */
  private mapToMember(row: any): OrganizationMember {
    return {
      organization_id: row.organization_id,
      user_id: row.user_id,
      role: row.role,
      joined_at: row.joined_at,
      invited_by: row.invited_by,
      user: {
        id: row.user_id,
        email: row.user_email || `user-${row.user_id.substring(0, 8)}`, // Fallback email
        name: row.user_name || 'User'
      }
    };
  }

  /**
   * Sync organization members from Supabase to local database
   */
  async syncMembersFromSupabase(orgId: string): Promise<void> {
    try {
      const supabase = getSupabaseClient();
      
      // Get organization internal ID
      const org = this.db.prepare(`
        SELECT id FROM organizations WHERE external_id = ?
      `).get(orgId);
      
      if (!org) {
        console.error('Organization not found:', orgId);
        return;
      }
      
      // Fetch members from Supabase
      const { data: members, error } = await supabase
        .from('organization_members')
        .select(`
          user_id,
          role,
          joined_at,
          invited_by,
          user:profiles (
            id,
            email,
            full_name
          )
        `)
        .eq('organization_id', orgId);

      if (error) {
        console.error('Failed to fetch members from Supabase:', error);
        return;
      }

      if (!members || members.length === 0) {
        return;
      }

      // Sync each member to local database
      for (const member of members) {
        // Ensure user exists in local database
        if (member.user) {
          const existingUser = this.db.prepare(`
            SELECT id FROM users WHERE id = ?
          `).get(member.user.id);

          if (!existingUser) {
            this.db.prepare(`
              INSERT INTO users (id, email, name)
              VALUES (?, ?, ?)
            `).run(
              member.user.id,
              member.user.email || '',
              member.user.full_name || member.user.email?.split('@')[0] || 'User'
            );
          } else {
            // Update user info
            this.db.prepare(`
              UPDATE users 
              SET email = ?, name = ?, updated_at = CURRENT_TIMESTAMP
              WHERE id = ?
            `).run(
              member.user.email || '',
              member.user.full_name || member.user.email?.split('@')[0] || 'User',
              member.user.id
            );
          }
        }

        // Check if membership exists locally
        const existingMembership = this.db.prepare(`
          SELECT 1 FROM organization_members 
          WHERE organization_id = ? AND user_id = ?
        `).get(org.id, member.user_id);

        if (!existingMembership) {
          this.db.prepare(`
            INSERT INTO organization_members (organization_id, user_id, role, joined_at, invited_by)
            VALUES (?, ?, ?, ?, ?)
          `).run(org.id, member.user_id, member.role, member.joined_at, member.invited_by);
        } else {
          // Update role if changed
          this.db.prepare(`
            UPDATE organization_members 
            SET role = ?
            WHERE organization_id = ? AND user_id = ?
          `).run(member.role, org.id, member.user_id);
        }
      }
    } catch (error) {
      console.error('Error syncing members from Supabase:', error);
    }
  }

  /**
   * Sync organizations from Supabase to local database
   */
  async syncOrganizationsFromSupabase(userId: string): Promise<Organization[]> {
    try {
      const supabase = getSupabaseClient();
      
      // Fetch organizations and memberships from Supabase
      const { data: memberships, error } = await supabase
        .from('organization_members')
        .select(`
          role,
          organization:organizations (
            id,
            name,
            slug,
            plan,
            settings,
            created_at,
            updated_at
          )
        `)
        .eq('user_id', userId);

      if (error) {
        console.error('Failed to fetch organizations from Supabase:', error);
        return [];
      }

      if (!memberships || memberships.length === 0) {
        return [];
      }

      // Sync each organization to local database
      const organizations: Organization[] = [];
      const seenNames = new Set<string>(); // Track organization names to prevent duplicates
      
      for (const membership of memberships) {
        if (!membership.organization) {continue;}
        
        const org = membership.organization as any;
        
        // Skip if we've already seen this organization name
        const normalizedName = org.name.toLowerCase();
        if (seenNames.has(normalizedName)) {
          console.warn(`Skipping duplicate organization: ${org.name} (${org.id})`);
          continue;
        }
        seenNames.add(normalizedName);
        
        // Check if organization exists locally
        const existingOrg = this.db.prepare(`
          SELECT id FROM organizations WHERE external_id = ?
        `).get(org.id);

        let localOrgId: number;

        if (!existingOrg) {
          // Create organization locally
          const result = this.db.prepare(`
            INSERT INTO organizations (external_id, name, slug, plan, settings, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
          `).run(
            org.id,
            org.name,
            org.slug,
            org.plan || 'free',
            JSON.stringify(org.settings || {}),
            org.created_at,
            org.updated_at
          );
          
          localOrgId = result.lastInsertRowid as number;
        } else {
          localOrgId = existingOrg.id;
          
          // Update organization
          this.db.prepare(`
            UPDATE organizations 
            SET name = ?, slug = ?, plan = ?, settings = ?, updated_at = ?
            WHERE id = ?
          `).run(
            org.name,
            org.slug,
            org.plan || 'free',
            JSON.stringify(org.settings || {}),
            org.updated_at,
            localOrgId
          );
        }

        // Sync membership
        const existingMembership = this.db.prepare(`
          SELECT 1 FROM organization_members 
          WHERE organization_id = ? AND user_id = ?
        `).get(localOrgId, userId);

        if (!existingMembership) {
          this.db.prepare(`
            INSERT INTO organization_members (organization_id, user_id, role, joined_at)
            VALUES (?, ?, ?, ?)
          `).run(localOrgId, userId, membership.role, new Date().toISOString());
        } else {
          // Update role if changed
          this.db.prepare(`
            UPDATE organization_members 
            SET role = ?
            WHERE organization_id = ? AND user_id = ?
          `).run(membership.role, localOrgId, userId);
        }

        // Add to return list
        const fullOrg = await this.getOrganization(org.id);
        if (fullOrg) {
          organizations.push(fullOrg);
        }
      }

      return organizations;
    } catch (error) {
      console.error('Error syncing organizations from Supabase:', error);
      return [];
    }
  }

  /**
   * Send invitation email using Zoho Mail <NAME_EMAIL> alias
   * <NAME_EMAIL> but <NAME_EMAIL>
   */
  private async sendInvitationEmailDirect(
    to: string,
    invitation: {
      organizationName: string;
      inviterName: string;
      role: string;
      token: string;
      expiresAt: Date;
    }
  ): Promise<boolean> {
    try {
      if (!this.emailTransporter) {
        console.error('[Organization] Email transporter not initialized');
        return false;
      }

      // Check if we have Zoho <NAME_EMAIL>
      const zohoPassword = process.env.ZOHO_PASSWORD;
      if (!zohoPassword) {
        console.error('[Organization] Zoho password not <NAME_EMAIL>');
        console.error('[Organization] Please set ZOHO_PASSWORD in .env (use app-specific password)');
        console.error('[Organization] Generate app password at: https://accounts.zoho.com/home#security/app_passwords');
        return false;
      }
      
      const invitationUrl = `chromasync://invite/${invitation.token}`;
      
      // Create email content
      const textContent = `🎨 ChromaSync Team Invitation

Hi there!

${invitation.inviterName} has invited you to join ${invitation.organizationName} on ChromaSync as a ${invitation.role}.

To accept this invitation, copy and paste this link into ChromaSync:
${invitationUrl}

⏰ This invitation expires on ${invitation.expiresAt.toLocaleDateString()}.

Don't have ChromaSync? Download it from https://chromasync.app

Questions? Reply to this email or contact <NAME_EMAIL>

Best regards,
ChromaSync Support Team

© 2024 ChromaSync. All rights reserved.`;

      const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ChromaSync Team Invitation</title>
  <style>
    body { 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
      line-height: 1.6; 
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f8fafc;
    }
    .container { 
      max-width: 600px;
      margin: 0 auto;
      background: #ffffff; 
      border-radius: 12px; 
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 40px 30px;
      text-align: center;
    }
    .logo {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    .content {
      padding: 40px 30px;
    }
    .invitation-card {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-left: 4px solid #3b82f6;
      border-radius: 8px;
      padding: 24px;
      margin: 24px 0;
    }
    .button { 
      display: inline-block; 
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: white !important; 
      padding: 16px 32px; 
      text-decoration: none; 
      border-radius: 8px; 
      margin: 24px 0;
      font-weight: 600;
      text-align: center;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
    }
    .button:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
    }
    .features {
      display: flex;
      justify-content: space-around;
      margin: 30px 0;
      flex-wrap: wrap;
    }
    .feature {
      text-align: center;
      flex: 1;
      min-width: 150px;
      padding: 16px;
      margin: 8px;
      background: #f1f5f9;
      border-radius: 8px;
    }
    .feature-emoji {
      font-size: 24px;
      margin-bottom: 8px;
    }
    .footer { 
      background: #f1f5f9;
      padding: 30px;
      text-align: center;
      color: #64748b;
    }
    .link-text {
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      padding: 12px 16px;
      border-radius: 6px;
      word-break: break-all;
      display: block;
      margin: 16px 0;
      font-size: 14px;
    }
    .brand-badge {
      display: inline-block;
      background: #1f2937;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 600;
      margin-bottom: 16px;
    }
    @media only screen and (max-width: 600px) {
      .container { margin: 10px; }
      .content { padding: 20px; }
      .features { flex-direction: column; }
      .feature { margin: 4px 0; }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">🎨 ChromaSync</div>
      <h1 style="margin: 0; font-size: 28px;">You're Invited!</h1>
      <p style="margin: 8px 0 0 0; opacity: 0.9;">Professional color management made simple</p>
    </div>
    
    <div class="content">
      <div class="brand-badge">TEAM INVITATION</div>
      
      <p>Hi there! 👋</p>
      
      <p><strong>${invitation.inviterName}</strong> has invited you to join <strong>${invitation.organizationName}</strong> on ChromaSync as a <strong>${invitation.role}</strong>.</p>
      
      <div class="invitation-card">
        <h3 style="margin-top: 0; color: #1e293b;">📋 Invitation Details</h3>
        <p style="margin: 8px 0;"><strong>Organization:</strong> ${invitation.organizationName}</p>
        <p style="margin: 8px 0;"><strong>Your Role:</strong> ${invitation.role.charAt(0).toUpperCase() + invitation.role.slice(1)}</p>
        <p style="margin: 8px 0;"><strong>Invited by:</strong> ${invitation.inviterName}</p>
        <p style="margin: 8px 0;"><strong>Valid until:</strong> ${invitation.expiresAt.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</p>
      </div>
      
      <div style="text-align: center;">
        <a href="${invitationUrl}" class="button">🚀 Accept Invitation & Join Team</a>
      </div>
      
      <div class="features">
        <div class="feature">
          <div class="feature-emoji">🎯</div>
          <strong>Precise Colors</strong><br>
          <small>CMYK, RGB, LAB spaces</small>
        </div>
        <div class="feature">
          <div class="feature-emoji">👥</div>
          <strong>Team Sync</strong><br>
          <small>Real-time collaboration</small>
        </div>
        <div class="feature">
          <div class="feature-emoji">📱</div>
          <strong>Cross-Platform</strong><br>
          <small>Works everywhere</small>
        </div>
      </div>
      
      <p><strong>New to ChromaSync?</strong><br>
      Download the app from <a href="https://chromasync.app" style="color: #3b82f6; text-decoration: none;">chromasync.app</a></p>
      
      <p><em>Having trouble with the button? Copy and paste this invitation code:</em></p>
      <div class="link-text">${invitationUrl}</div>
    </div>
    
    <div class="footer">
      <p style="margin-bottom: 20px;"><strong>⏰ This invitation expires on ${invitation.expiresAt.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}.</strong></p>
      <p>Questions? We're here to help!</p>
      <p><a href="mailto:<EMAIL>" style="color: #3b82f6; text-decoration: none;"><EMAIL></a></p>
      <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 24px 0;">
      <p><strong>ChromaSync Support Team</strong><br>
      <small style="color: #64748b;">© 2024 ChromaSync. All rights reserved.</small></p>
    </div>
  </div>
</body>
</html>`;

      // Send <NAME_EMAIL> alias
      const info = await this.emailTransporter.sendMail({
        from: {
          name: 'ChromaSync Support Team',
          address: process.env.ZOHO_SUPPORT_ALIAS || '<EMAIL>'
        },
        to: to,
        subject: `🎨 ${invitation.inviterName} invited you to join ${invitation.organizationName}`,
        text: textContent,
        html: htmlContent,
        // Professional email headers
        headers: {
          'Message-ID': `<invite-${invitation.token}-${Date.now()}@chromasync.app>`,
          'X-Entity-Ref-ID': `invitation-${invitation.token}`,
          'X-ChromaSync-Type': 'team-invitation',
          'X-Mailer': 'ChromaSync v1.0',
          'X-Priority': '3',
          'Reply-To': process.env.ZOHO_SUPPORT_ALIAS || '<EMAIL>',
          'Return-Path': process.env.ZOHO_SUPPORT_ALIAS || '<EMAIL>'
        }
      });

      console.log('[Organization] Email sent <NAME_EMAIL>:', info.messageId);
      console.log('[Organization] Recipient:', to);
      console.log('[Organization] Subject:', `🎨 ${invitation.inviterName} invited you to join ${invitation.organizationName}`);
      console.log('[Organization] Preview URL:', nodemailer.getTestMessageUrl(info));
      return true;
    } catch (error) {
      console.error('[Organization] Failed to send <NAME_EMAIL>:', error);
      
      // Enhanced error logging for troubleshooting
      if (error.code === 'EAUTH') {
        console.error('[Organization] Authentication failed - check ZOHO_PASSWORD (use app-specific password)');
      } else if (error.code === 'EENVELOPE') {
        console.error('[Organization] Invalid email address format');
      } else if (error.code === 'ENOTFOUND') {
        console.error('[Organization] SMTP server not found - check internet connection');
      } else {
        console.error('[Organization] Email error details:', {
          code: error.code,
          command: error.command,
          response: error.response
        });
      }
      
      return false;
    }
  }
}
