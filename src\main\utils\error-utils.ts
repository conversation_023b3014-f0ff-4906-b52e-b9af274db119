/**
 * @file error-utils.ts
 * @description Utility functions for handling errors consistently.
 */

/**
 * Type for handling unknown errors that might have a message property.
 */
type ErrorWithMessage = {
  message: string;
};

/**
 * Type guard to check if an unknown error object has a string message property.
 * @param error - The error object to check.
 * @returns True if the error is an object with a string message property, false otherwise.
 */
function isErrorWithMessage(error: unknown): error is ErrorWithMessage {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as Record<string, unknown>).message === 'string'
  );
}

/**
 * Helper function to safely extract an error message from an unknown error type.
 * Provides a fallback message if extraction fails.
 * @param error - The error object (of unknown type).
 * @param fallbackMessage - Optional fallback message if extraction fails (default: 'An unknown error occurred').
 * @returns A string representing the error message.
 */
export function getErrorMessage(error: unknown, fallbackMessage = 'An unknown error occurred'): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  if (isErrorWithMessage(error)) {
    return error.message;
  }
  // Attempt to stringify if it's an object, otherwise use fallback
  if (typeof error === 'object' && error !== null) {
    try {
      return JSON.stringify(error);
    } catch { /* Fall through to fallback */ }
  }
  return fallbackMessage;
}
