/**
 * @file HarmonySelector.tsx
 * @description Component for selecting color harmony type and options with an interactive color wheel
 */

import React, { useState, useEffect, useRef } from 'react';
import { useTokens } from '../../hooks/useTokens';
import { useColorComparisonStore } from '../../store/colorComparison.store';
import { HarmonyType } from '../../../shared/types/colorComparison.types';
import { ChevronDown } from 'lucide-react';
import HarmonyDisplay from './HarmonyDisplay';

interface HarmonySelectorProps {
  disabled?: boolean;
}

const harmonyOptions: { value: HarmonyType; label: string; description: string }[] = [
  { value: 'complementary', label: 'Complementary', description: 'Colors opposite each other on the color wheel' },
  { value: 'analogous', label: 'Analogous', description: 'Colors adjacent to each other on the color wheel' },
  { value: 'triadic', label: 'Triadic', description: 'Three colors evenly spaced around the color wheel' },
  { value: 'tetradic', label: 'Tetradic', description: 'Four colors arranged in two complementary pairs' },
  { value: 'splitComplementary', label: 'Split Complementary', description: 'A base color and two colors adjacent to its complement' },
  { value: 'monochromatic', label: 'Monochromatic', description: 'Different shades, tones and tints of a single color' },
  { value: 'shades', label: 'Shades', description: 'Variations of a base color mixed with black' },
  { value: 'tints', label: 'Tints', description: 'Variations of a base color mixed with white' },
  { value: 'compound', label: 'Compound', description: 'Base color with analogous and complementary colors' },
];

const ColorWheel: React.FC<{
  baseColor: string | null;
  harmonyType: HarmonyType;
  angle: number;
  count: number;
  onClick?: (position: {x: number, y: number}) => void;
  onColorChange?: (newColor: string) => void;
}> = ({ baseColor, harmonyType, angle, count, onClick, onColorChange }) => {
  const tokens = useTokens();
  
  // Helper functions for HSL conversion (since they're not in utils)
  const hexToHsl = (hex: string) => {
    // Remove the # if it exists
    hex = hex.replace(/^#/, '');
    
    // Parse the r, g, b values
    const r = parseInt(hex.substring(0, 2), 16) / 255;
    const g = parseInt(hex.substring(2, 4), 16) / 255;
    const b = parseInt(hex.substring(4, 6), 16) / 255;
    
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h = 0, s = 0;
    const l = (max + min) / 2;
    
    if (max !== min) {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      
      h /= 6;
    }
    
    return { h: h * 360, s: s * 100, l: l * 100 };
  };
  
  const hslToHex = ({ h, s, l }: { h: number, s: number, l: number }) => {
    h /= 360;
    s /= 100;
    l /= 100;
    
    let r, g, b;
    
    if (s === 0) {
      r = g = b = l; // achromatic
    } else {
      const hue2rgb = (p: number, q: number, t: number) => {
        if (t < 0) {t += 1;}
        if (t > 1) {t -= 1;}
        if (t < 1/6) {return p + (q - p) * 6 * t;}
        if (t < 1/2) {return q;}
        if (t < 2/3) {return p + (q - p) * (2/3 - t) * 6;}
        return p;
      };
      
      const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
      const p = 2 * l - q;
      
      r = hue2rgb(p, q, h + 1/3);
      g = hue2rgb(p, q, h);
      b = hue2rgb(p, q, h - 1/3);
    }
    
    const toHex = (x: number) => {
      const hex = Math.round(x * 255).toString(16);
      return hex.length === 1 ? '0' + hex : hex;
    };
    
    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  };
  
  // Calculate harmony colors based on the base color and harmony type
  const calculateHarmonyPositions = () => {
    if (!baseColor) {return [];}
    
    const positions = [];
    const centerX = 100;
    const centerY = 100;
    const radius = 80;
    
    // Convert hex to HSL to get the hue
    const { h } = hexToHsl(baseColor);
    const baseHue = h;
    
    // Add base color position
    positions.push({
      type: 'base',
      position: {
        x: centerX + radius * Math.cos((baseHue - 90) * (Math.PI / 180)),
        y: centerY + radius * Math.sin((baseHue - 90) * (Math.PI / 180))
      },
      tooltip: 'Base Color'
    });
    
    // Add harmony positions based on the selected harmony type
    switch (harmonyType) {
      case 'complementary':
        positions.push({
          type: 'harmony',
          position: {
            x: centerX + radius * Math.cos((baseHue + 180 - 90) * (Math.PI / 180)),
            y: centerY + radius * Math.sin((baseHue + 180 - 90) * (Math.PI / 180))
          },
          tooltip: 'Complementary Color (180°)'
        });
        break;
      
      case 'analogous': {
        // For analogous colors, we want 'count' total points including base
        // Distribute the remaining points evenly on both sides
        const pointsPerSide = Math.floor((count - 1) / 2);
        const remainder = (count - 1) % 2;
        
        // Points on the positive side
        for (let i = 1; i <= pointsPerSide + remainder; i++) {
          positions.push({
            type: 'harmony',
            position: {
              x: centerX + radius * Math.cos((baseHue + angle * i - 90) * (Math.PI / 180)),
              y: centerY + radius * Math.sin((baseHue + angle * i - 90) * (Math.PI / 180))
            },
            tooltip: `Analogous Color (+${angle * i}°)`
          });
        }
        
        // Points on the negative side
        for (let i = 1; i <= pointsPerSide; i++) {
          positions.push({
            type: 'harmony',
            position: {
              x: centerX + radius * Math.cos((baseHue - angle * i - 90) * (Math.PI / 180)),
              y: centerY + radius * Math.sin((baseHue - angle * i - 90) * (Math.PI / 180))
            },
            tooltip: `Analogous Color (-${angle * i}°)`
          });
        }
        break;
      }
      
      case 'triadic':
        positions.push({
          type: 'harmony',
          position: {
            x: centerX + radius * Math.cos((baseHue + 120 - 90) * (Math.PI / 180)),
            y: centerY + radius * Math.sin((baseHue + 120 - 90) * (Math.PI / 180))
          },
          tooltip: 'Triadic Color (+120°)'
        });
        positions.push({
          type: 'harmony',
          position: {
            x: centerX + radius * Math.cos((baseHue + 240 - 90) * (Math.PI / 180)),
            y: centerY + radius * Math.sin((baseHue + 240 - 90) * (Math.PI / 180))
          },
          tooltip: 'Triadic Color (+240°)'
        });
        break;
      
      case 'tetradic':
        positions.push({
          type: 'harmony',
          position: {
            x: centerX + radius * Math.cos((baseHue + 90 - 90) * (Math.PI / 180)),
            y: centerY + radius * Math.sin((baseHue + 90 - 90) * (Math.PI / 180))
          },
          tooltip: 'Tetradic Color (+90°)'
        });
        positions.push({
          type: 'harmony',
          position: {
            x: centerX + radius * Math.cos((baseHue + 180 - 90) * (Math.PI / 180)),
            y: centerY + radius * Math.sin((baseHue + 180 - 90) * (Math.PI / 180))
          },
          tooltip: 'Tetradic Color (+180°)'
        });
        positions.push({
          type: 'harmony',
          position: {
            x: centerX + radius * Math.cos((baseHue + 270 - 90) * (Math.PI / 180)),
            y: centerY + radius * Math.sin((baseHue + 270 - 90) * (Math.PI / 180))
          },
          tooltip: 'Tetradic Color (+270°)'
        });
        break;
      
      case 'splitComplementary':
        positions.push({
          type: 'harmony',
          position: {
            x: centerX + radius * Math.cos((baseHue + 180 - angle - 90) * (Math.PI / 180)),
            y: centerY + radius * Math.sin((baseHue + 180 - angle - 90) * (Math.PI / 180))
          },
          tooltip: `Split Complementary (${180 - angle}°)`
        });
        positions.push({
          type: 'harmony',
          position: {
            x: centerX + radius * Math.cos((baseHue + 180 + angle - 90) * (Math.PI / 180)),
            y: centerY + radius * Math.sin((baseHue + 180 + angle - 90) * (Math.PI / 180))
          },
          tooltip: `Split Complementary (${180 + angle}°)`
        });
        break;
      
      case 'monochromatic':
      case 'shades':
      case 'tints':
        // For monochromatic, we'll show markers at varying distances from center 
        // to represent different saturations/lightnesses
        for (let i = 1; i <= count; i++) {
          const scaledRadius = radius * (0.4 + i * 0.15);
          const pos = {
            x: centerX + radius * Math.cos((baseHue - 90) * (Math.PI / 180)),
            y: centerY + radius * Math.sin((baseHue - 90) * (Math.PI / 180))
          };
          // Adjust the position to be closer/further from center
          const newX = centerX + (pos.x - centerX) * (scaledRadius / radius);
          const newY = centerY + (pos.y - centerY) * (scaledRadius / radius);
          
          const tooltipText = harmonyType === 'monochromatic' 
            ? `Value Variation ${i}` 
            : harmonyType === 'shades' 
              ? `Shade ${i} (darker)` 
              : `Tint ${i} (lighter)`;
          
          positions.push({
            type: 'harmony',
            position: {x: newX, y: newY},
            tooltip: tooltipText
          });
        }
        break;
      
      case 'compound':
        // Analogous + complement
        positions.push({
          type: 'harmony',
          position: {
            x: centerX + radius * Math.cos((baseHue + angle - 90) * (Math.PI / 180)),
            y: centerY + radius * Math.sin((baseHue + angle - 90) * (Math.PI / 180))
          },
          tooltip: `Compound: Analogous (+${angle}°)`
        });
        positions.push({
          type: 'harmony',
          position: {
            x: centerX + radius * Math.cos((baseHue - angle - 90) * (Math.PI / 180)),
            y: centerY + radius * Math.sin((baseHue - angle - 90) * (Math.PI / 180))
          },
          tooltip: `Compound: Analogous (-${angle}°)`
        });
        positions.push({
          type: 'harmony',
          position: {
            x: centerX + radius * Math.cos((baseHue + 180 - 90) * (Math.PI / 180)),
            y: centerY + radius * Math.sin((baseHue + 180 - 90) * (Math.PI / 180))
          },
          tooltip: 'Compound: Complementary (180°)'
        });
        break;
    }
    
    return positions;
  };
  
  const [isDragging, setIsDragging] = useState(false);
  const svgRef = useRef<SVGSVGElement>(null);
  
  // Calculate position to hue
  const positionToHue = (x: number, y: number) => {
    const centerX = 100;
    const centerY = 100;
    
    // Calculate angle in radians
    const angleRad = Math.atan2(y - centerY, x - centerX);
    
    // Convert to degrees, adjust for color wheel orientation
    let angleDeg = (angleRad * 180 / Math.PI + 90) % 360;
    if (angleDeg < 0) {angleDeg += 360;}
    
    return angleDeg;
  };
  
  // Calculate hue to hex color
  const hueToHex = (hue: number) => {
    if (!baseColor) {return '#000000';}
    
    const hsl = hexToHsl(baseColor);
    return hslToHex({ h: hue, s: hsl.s, l: hsl.l });
  };
  
  // Handle mouse down on base color circle
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!baseColor || !onColorChange) {return;}
    e.preventDefault();
    setIsDragging(true);
  };
  
  // Handle mouse move during drag
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !baseColor || !onColorChange || !svgRef.current) {return;}
    
    const svg = svgRef.current;
    const rect = svg.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 200;
    const y = ((e.clientY - rect.top) / rect.height) * 200;
    
    // Calculate new hue based on position
    const newHue = positionToHue(x, y);
    
    // Convert hue to hex color
    const newColor = hueToHex(newHue);
    
    // Update color
    onColorChange(newColor);
  };
  
  // Add event listeners for mouse up outside the component
  useEffect(() => {
    if (isDragging) {
      const handleGlobalMouseUp = () => {
        setIsDragging(false);
      };
      
      window.addEventListener('mouseup', handleGlobalMouseUp);
      return () => {
        window.removeEventListener('mouseup', handleGlobalMouseUp);
      };
    }
  }, [isDragging]);
  
  const harmonyPositions = calculateHarmonyPositions();
  const [activeTooltip, setActiveTooltip] = useState<number | null>(null);
  
  // Handle clicks on the color wheel
  const handleClick = (e: React.MouseEvent<SVGSVGElement>) => {
    if (!onClick) {return;}
    
    const svg = e.currentTarget;
    const rect = svg.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // Convert to percentage values
    const xPercent = (x / rect.width) * 200;
    const yPercent = (y / rect.height) * 200;
    
    onClick({x: xPercent, y: yPercent});
  };
  
  // Generate hue segments for the wheel with saturation variation
  const generateHueSegments = () => {
    const segments = [];
    const centerX = 100;
    const centerY = 100;
    const radius = 80;
    
    // Create saturation rings (from inner to outer, increasing saturation)
    const saturationRings = 4;
    const ringThickness = radius / saturationRings;
    
    for (let ring = 0; ring < saturationRings; ring++) {
      const innerRadius = ring * ringThickness;
      const outerRadius = (ring + 1) * ringThickness;
      const saturation = 25 + ring * 25; // 25% -> 100% saturation from inner to outer
      
      // Create 12 segments (30° each) for a smooth color wheel
      for (let i = 0; i < 12; i++) {
        const startAngle = i * 30;
        const endAngle = (i + 1) * 30;
        const startRad = (startAngle - 90) * (Math.PI / 180);
        const endRad = (endAngle - 90) * (Math.PI / 180);
        
        const x1 = centerX + innerRadius * Math.cos(startRad);
        const y1 = centerY + innerRadius * Math.sin(startRad);
        const x2 = centerX + outerRadius * Math.cos(startRad);
        const y2 = centerY + outerRadius * Math.sin(startRad);
        const x3 = centerX + outerRadius * Math.cos(endRad);
        const y3 = centerY + outerRadius * Math.sin(endRad);
        const x4 = centerX + innerRadius * Math.cos(endRad);
        const y4 = centerY + innerRadius * Math.sin(endRad);
        
        // Calculate the hue for this segment
        const hue = i * 30;
        
        // Create the path for this segment
        const path = `
          M ${x1} ${y1}
          L ${x2} ${y2}
          A ${outerRadius} ${outerRadius} 0 0 1 ${x3} ${y3}
          L ${x4} ${y4}
          A ${innerRadius} ${innerRadius} 0 0 0 ${x1} ${y1}
          Z
        `;
        
        segments.push(
          <path
            key={`${ring}-${i}`}
            d={path}
            fill={`hsl(${hue}, ${saturation}%, 50%)`}
            stroke="none"
          />
        );
      }
    }
    
    return segments;
  };

  // Generate connector lines between harmony positions
  const generateConnectorLines = () => {
    if (!baseColor || harmonyPositions.length <= 1) {return null;}
    
    const basePosition = harmonyPositions.find(pos => pos.type === 'base')?.position;
    if (!basePosition) {return null;}
    
    // Draw lines from base to each harmony color
    return harmonyPositions
      .filter(pos => pos.type === 'harmony')
      .map((pos, index) => (
        <line
          key={`connector-${index}`}
          x1={basePosition.x}
          y1={basePosition.y}
          x2={pos.position.x}
          y2={pos.position.y}
          stroke="rgba(255,255,255,0.3)"
          strokeWidth="1"
          strokeDasharray="3,3"
        />
      ));
  };
  
  return (
    <div className="color-wheel-container">
      <svg
        ref={svgRef}
        width="200"
        height="200"
        viewBox="0 0 200 200"
        className="color-wheel"
        onClick={handleClick}
        onMouseMove={handleMouseMove}
        style={{
          borderRadius: tokens.borderRadius.full,
          boxShadow: tokens.shadows.md,
          overflow: 'visible',
          cursor: isDragging ? 'grabbing' : 'default'
        }}
      >
        {/* New color wheel implementation with saturation variation */}
        <g>
          {generateHueSegments()}
        </g>
        
        {/* Connector lines between harmony positions */}
        {generateConnectorLines()}
        
        {/* Center white circle */}
        <circle cx="100" cy="100" r="20" fill="white" stroke="#ccc" strokeWidth="1" />
        
        {/* Harmony positions with tooltips */}
        {harmonyPositions.map((pos, i) => (
          <g key={i}>
            <circle 
              cx={pos.position.x}
              cy={pos.position.y}
              r={pos.type === 'base' ? 10 : 7}
              fill={pos.type === 'base' ? (baseColor || '#000') : 'rgba(255,255,255,0.7)'}
              stroke={pos.type === 'base' ? tokens.colors.ui.background.primary : tokens.colors.ui.border.medium}
              strokeWidth={2}
              onMouseEnter={() => setActiveTooltip(i)}
              onMouseLeave={() => setActiveTooltip(null)}
              onMouseDown={pos.type === 'base' ? handleMouseDown : undefined}
              style={{
                transition: `cx ${tokens.transitions.duration[300]} ${tokens.transitions.easing.apple}, cy ${tokens.transitions.duration[300]} ${tokens.transitions.easing.apple}`,
                cursor: pos.type === 'base' ? (isDragging ? 'grabbing' : 'grab') : 'default'
              }}
            />
            {activeTooltip === i && (
              <g>
                <rect 
                  x={pos.position.x + 15}
                  y={pos.position.y - 10}
                  width={pos.tooltip?.length * 6 + 10}
                  height="20"
                  rx="4"
                  ry="4"
                  fill="rgba(0,0,0,0.7)"
                />
                <text 
                  x={pos.position.x + 20}
                  y={pos.position.y + 5}
                  fontSize="12"
                  fill="white"
                >
                  {pos.tooltip}
                </text>
              </g>
            )}
          </g>
        ))}
      </svg>
    </div>
  );
};

const HarmonySelector: React.FC<HarmonySelectorProps> = ({ disabled = false }) => {
  const tokens = useTokens();
  const {
    selectedHarmonyType,
    harmonyOptions: options,
    setHarmonyType,
    setHarmonyOptions,
    generateHarmonies,
    comparisonColors,
    activeColorIndex,
  } = useColorComparisonStore();
  
  const [count, setCount] = useState(options.count || 5);
  const [angle, setAngle] = useState(options.angle || 30);
  const [activeColor, setActiveColor] = useState<string | null>(null);
  const [_showTooltip, setShowTooltip] = useState<string | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  
  // Get the current harmony option for display
  const currentHarmonyOption = harmonyOptions.find(option => option.value === selectedHarmonyType);
  
  // Check if we should show count or angle options based on harmony type
  const showCountOption = ['analogous', 'monochromatic', 'shades', 'tints'].includes(selectedHarmonyType);
  const showAngleOption = ['analogous', 'splitComplementary', 'compound'].includes(selectedHarmonyType);
  
  // Check if a color is selected for harmony generation
  const isColorSelected = activeColorIndex !== null && activeColor !== null;
  
  // Update active color when active index changes
  useEffect(() => {
    if (activeColorIndex !== null && comparisonColors[activeColorIndex]) {
      setActiveColor(comparisonColors[activeColorIndex].hex);
    } else {
      setActiveColor(null);
    }
  }, [activeColorIndex, comparisonColors]);
  
  // Auto-generate harmonies when settings change
  useEffect(() => {
    if (activeColorIndex !== null && comparisonColors.length > 0) {
      generateHarmonies();
    }
  }, [selectedHarmonyType, count, angle, activeColorIndex, comparisonColors.length]);
  
  const handleHarmonyChange = (type: HarmonyType) => {
    setHarmonyType(type);
    setIsDropdownOpen(false);
  };
  
  const handleCountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newCount = Number(e.target.value);
    setCount(newCount);
    setHarmonyOptions({ ...options, count: newCount });
  };
  
  const handleAngleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newAngle = Number(e.target.value);
    setAngle(newAngle);
    setHarmonyOptions({ ...options, angle: newAngle });
  };
  // @ts-ignore - Intentionally unused
  
  const _handleRefreshHarmonies = () => {
    // Generate harmonies using current settings
    generateHarmonies();
    
    // Add tooltip to guide users
    setShowTooltip('refresh');
    setTimeout(() => setShowTooltip(null), 2000);
  };
  
  const handleBaseColorChange = (newColor: string) => {
    if (activeColorIndex !== null) {
      // Create a new array with the updated color
      const updatedColors = [...comparisonColors];
      updatedColors[activeColorIndex] = {
        ...updatedColors[activeColorIndex],
        hex: newColor
      };
      
      // Update the store directly
      useColorComparisonStore.setState({
        comparisonColors: updatedColors
      });
      
      // Regenerate harmonies with the new base color
      setTimeout(() => generateHarmonies(), 50);
    }
  };
  
  return (
    <div className="mb-[var(--spacing-4)] bg-ui-background-secondary dark:bg-zinc-800 p-[var(--spacing-4)] rounded-[var(--radius-lg)] border border-ui-border dark:border-zinc-700">
      <div className="flex flex-row items-center justify-between mb-[var(--spacing-4)]">
        <h3 className="text-[var(--fontSize-lg)] font-medium text-ui-text-primary dark:text-white">Colour Harmony Generator</h3>
        <div className="w-64 flex items-center">
          <div className="relative w-full">
            <button
              onClick={() => !disabled && setIsDropdownOpen(!isDropdownOpen)}
              disabled={disabled}
              className="w-full flex items-center justify-between px-[var(--spacing-3)] py-[var(--spacing-2)] border border-ui-border dark:border-zinc-700 rounded-[var(--radius-md)] bg-ui-background-primary dark:bg-zinc-700 text-ui-text-primary dark:text-white disabled:opacity-50 disabled:cursor-not-allowed"
              style={{
                transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
              }}
            >
              <span>{currentHarmonyOption?.label || 'Select Harmony Type'}</span>
              <ChevronDown size={16} className="text-ui-text-secondary dark:text-gray-400" />
            </button>
            {isDropdownOpen && (
              <div className="absolute z-10 mt-[var(--spacing-1)] w-full rounded-[var(--radius-md)] border border-ui-border dark:border-zinc-700 bg-ui-background-primary dark:bg-zinc-800 shadow-[var(--shadow-lg)]">
                <div className="py-[var(--spacing-1)] max-h-48 overflow-y-auto">
                  {harmonyOptions.map(option => (
                    <div
                      key={option.value}
                      className="px-[var(--spacing-3)] py-[var(--spacing-2)] flex flex-col cursor-pointer hover:bg-ui-background-hover dark:hover:bg-zinc-700 text-ui-text-primary dark:text-white"
                      onClick={() => {
                        handleHarmonyChange(option.value);
                        setIsDropdownOpen(false);
                      }}
                    >
                      <div className="font-medium">{option.label}</div>
                      <div className="text-xs text-ui-text-secondary dark:text-gray-400">{option.description}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-12 gap-[var(--spacing-4)]">
        {/* Left Side: Controls */}
        <div className="col-span-1 md:col-span-5">
          <div className="flex flex-col items-center">
            {/* Color Wheel */}
            <ColorWheel
              baseColor={activeColor}
              harmonyType={selectedHarmonyType}
              angle={angle}
              count={count}
              onClick={!disabled ? undefined : undefined}
              onColorChange={!disabled && activeColorIndex !== null ? handleBaseColorChange : undefined}
            />

            {/* Sliders section */}
            <div className="w-full">
              {showCountOption && (
                <div>
                  <label className="block text-ui-text-secondary font-medium text-sm mb-[var(--spacing-1)]">
                    Color Count: {count}
                  </label>
                  <input
                    type="range"
                    disabled={disabled || !activeColor}
                    min={3}
                    max={9}
                    value={count}
                    onChange={handleCountChange}
                    className="w-full h-2 bg-ui-background-tertiary rounded-full appearance-none disabled:opacity-50 disabled:cursor-not-allowed"
                    style={{
                      accentColor: tokens.colors.brand.primary,
                      transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                    }}
                  />
                </div>
              )}

              {showAngleOption && (
                <div>
                  <label className="block text-ui-text-secondary font-medium text-sm mb-[var(--spacing-1)]">
                    Angle: {angle}°
                  </label>
                  <input
                    type="range"
                    disabled={disabled || !activeColor}
                    min={10}
                    max={90}
                    value={angle}
                    onChange={handleAngleChange}
                    className="w-full h-2 bg-ui-background-tertiary rounded-full appearance-none disabled:opacity-50 disabled:cursor-not-allowed"
                    style={{
                      accentColor: tokens.colors.brand.primary,
                      transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                    }}
                  />
                </div>
              )}

              {/* Base color info */}
              <div className="text-ui-text-secondary text-sm mt-[var(--spacing-3)]">
                {isColorSelected ? (
                  <span>Base color: <span className="font-mono">{activeColorIndex !== null && comparisonColors[activeColorIndex]?.code}</span> - <span className="font-mono">{activeColor}</span></span>
                ) : (
                  <span>Select a color from the comparison panel to set as base color</span>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Right Side: Harmony Display */}
        <div className="col-span-1 md:col-span-7">
          <HarmonyDisplay />
        </div>
      </div>
    </div>
  );
};

export default HarmonySelector; 