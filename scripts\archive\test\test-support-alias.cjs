/**
 * Test Zoho support@ alias
 */

require('dotenv').config();
const nodemailer = require('nodemailer');

console.log('🔍 Testing <EMAIL> alias\n');

async function testAlias() {
  const transporter = nodemailer.createTransport({
    host: 'smtp.zoho.eu',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: 'SKYiXmiehXjk'
    }
  });

  try {
    console.log('1️⃣ Testing connection...');
    await transporter.verify();
    console.log('✅ Connected to Zoho EU server\n');

    console.log('2️⃣ Testing support@ alias...');
    const info = await transporter.sendMail({
      from: {
        name: 'ChromaSync Support',
        address: '<EMAIL>'
      },
      to: '<EMAIL>',
      subject: '🎨 ChromaSync Invitation Test',
      text: 'This is a test <NAME_EMAIL> alias.',
      html: `
        <div style="font-family: Arial, sans-serif; padding: 20px;">
          <h2>🎨 ChromaSync Support Alias Test</h2>
          <p>This email was sent from <strong><EMAIL></strong></p>
          <p>If you received this, the alias is working correctly!</p>
          <hr>
          <p><small>ChromaSync Support Team</small></p>
        </div>
      `
    });

    console.log('✅ Email sent successfully!');
    console.log('   Message ID:', info.messageId);
    console.log('   From:', info.envelope.from);
    console.log('   To:', info.envelope.to);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.code === 'EENVELOPE') {
      console.error('\n⚠️  The <EMAIL> alias may not be configured');
      console.error('   Please verify in Zoho Mail settings that support@ is an alias');
    }
  }
}

testAlias();