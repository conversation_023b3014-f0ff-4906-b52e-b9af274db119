const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Database path - check multiple locations
function findDatabase() {
  const possiblePaths = [
    path.join(
      process.env.HOME || process.env.USERPROFILE,
      'Library',
      'Application Support',
      'chroma-sync',
      'chromasync.db'
    ),
    path.join(
      process.env.HOME || process.env.USERPROFILE,
      'Library',
      'Application Support',
      'ChromaSync',
      'chromasync.db'
    ),
    './chromasync.db',
    path.join(process.cwd(), 'chromasync.db')
  ];
  
  for (const dbPath of possiblePaths) {
    if (fs.existsSync(dbPath)) {
      console.log('Found database at:', dbPath);
      return dbPath;
    }
  }
  
  throw new Error('Database not found');
}

function analyzeDatabaseData() {
  try {
    const dbPath = findDatabase();
    const db = new Database(dbPath, { readonly: true });
    
    // Get all product counts from database
    const productCountsQuery = `
      SELECT 
        p.name as product_name,
        COUNT(DISTINCT pc.color_id) as color_count,
        COUNT(DISTINCT c.name) as unique_flavors
      FROM products p
      LEFT JOIN product_colors pc ON p.id = pc.product_id
      LEFT JOIN colors c ON pc.color_id = c.id
      GROUP BY p.id, p.name
      ORDER BY p.name
    `;
    
    const productCounts = db.prepare(productCountsQuery).all();
    
    // Get total counts
    const totalQuery = `
      SELECT 
        COUNT(DISTINCT p.id) as total_products,
        COUNT(DISTINCT pc.color_id) as total_unique_colors,
        COUNT(*) as total_product_color_mappings
      FROM products p
      LEFT JOIN product_colors pc ON p.id = pc.product_id
    `;
    
    const totals = db.prepare(totalQuery).get();
    
    // Check for duplicate product names
    const duplicateProductsQuery = `
      SELECT name, COUNT(*) as count
      FROM products
      GROUP BY name
      HAVING COUNT(*) > 1
    `;
    
    const duplicateProducts = db.prepare(duplicateProductsQuery).all();
    
    // Check for orphaned colors (colors not assigned to any product)
    const orphanedColorsQuery = `
      SELECT COUNT(*) as orphaned_count
      FROM colors c
      WHERE NOT EXISTS (
        SELECT 1 FROM product_colors pc WHERE pc.color_id = c.id
      )
    `;
    
    const orphanedColors = db.prepare(orphanedColorsQuery).get();
    
    console.log('=== DATABASE ANALYSIS ===\n');
    console.log('Total unique products:', totals.total_products);
    console.log('Total unique colors:', totals.total_unique_colors);
    console.log('Total product-color mappings:', totals.total_product_color_mappings);
    console.log('Orphaned colors (no product):', orphanedColors.orphaned_count);
    
    if (duplicateProducts.length > 0) {
      console.log('\n⚠️  Duplicate product names found:');
      duplicateProducts.forEach(dup => {
        console.log(`  - "${dup.name}" appears ${dup.count} times`);
      });
    }
    
    console.log('\nProduct Color Counts:');
    console.log('Product Name                                    | Colors | Unique Flavors');
    console.log('-----------------------------------------------|--------|---------------');
    
    let totalColors = 0;
    const dbProductCounts = {};
    
    productCounts.forEach(row => {
      totalColors += row.color_count;
      dbProductCounts[row.product_name] = row.color_count;
      console.log(`${row.product_name.padEnd(46)} | ${row.color_count.toString().padStart(6)} | ${row.unique_flavors.toString().padStart(14)}`);
    });
    
    console.log('-----------------------------------------------|--------|---------------');
    console.log(`${'TOTAL'.padEnd(46)} | ${totalColors.toString().padStart(6)} |`);
    
    // Save summary for comparison
    const summary = {
      totalProducts: totals.total_products,
      totalUniqueColors: totals.total_unique_colors,
      totalMappings: totals.total_product_color_mappings,
      orphanedColors: orphanedColors.orphaned_count,
      productCounts: dbProductCounts,
      duplicateProducts: duplicateProducts,
      timestamp: new Date().toISOString()
    };
    
    fs.writeFileSync(
      path.join(__dirname, 'database-summary.json'),
      JSON.stringify(summary, null, 2)
    );
    
    console.log('\nSummary saved to database-summary.json');
    
    db.close();
    
  } catch (error) {
    console.error('Error analyzing database:', error);
  }
}

analyzeDatabaseData();