/**
 * @file FormInput.tsx
 * @description Reusable form input component with validation
 */

import React, { ReactNode } from 'react';
import { useTokens } from '../hooks/useTokens';

interface FormInputProps {
  id: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  label?: string;
  required?: boolean;
  placeholder?: string;
  isValid?: boolean;
  errorMessage?: string;
  prefix?: ReactNode;
  suffix?: ReactNode;
  type?: string;
  helpText?: string;
  inputClassName?: string;
  hideLabel?: boolean;
}

export default function FormInput({
  id,
  name,
  value,
  onChange,
  label,
  required = false,
  placeholder = '',
  isValid = true,
  errorMessage = '',
  prefix,
  suffix,
  type = 'text',
  helpText,
  inputClassName = '',
  hideLabel = false
}: FormInputProps) {
  const tokens = useTokens();
  
  // Always use the specified placeholder, never show validation messages as placeholders
  const activePlaceholder = placeholder;
  
  // Only show error icons when there's actual user input and it's invalid
  const showErrorIcon = !isValid && value !== '';

  // Define the token-based styling classes
  const labelClasses = "text-[var(--font-size-sm)] font-[var(--font-weight-medium)] text-[var(--color-ui-foreground-secondary)] flex items-center";
  const inputClasses = `
    w-full px-[var(--spacing-3)] py-[var(--spacing-2)]
    border border-[var(--color-ui-border-light)] 
    rounded-[var(--radius-md)]
    text-[var(--color-ui-foreground-primary)]
    bg-[var(--color-ui-background-primary)]
    focus:outline-none focus:ring-1 focus:ring-brand-primary focus:border-transparent
    ${showErrorIcon ? 'border-feedback-error focus:ring-feedback-error' : ''}
    ${prefix ? 'rounded-l-none' : ''}
    ${suffix ? 'rounded-r-none' : ''}
    ${inputClassName}
  `;
  
  return (
    <div className="space-y-[var(--spacing-1)]">
      {!hideLabel && label && (
        <label htmlFor={id} className={labelClasses}>
          {label}
          {required && <span className="ml-[var(--spacing-1)] text-feedback-error">*</span>}
          {helpText && <span className="ml-[var(--spacing-2)] text-[var(--font-size-xs)] text-[var(--color-ui-foreground-tertiary)]">{helpText}</span>}
        </label>
      )}
      
      <div className="relative flex">
        {prefix && (
          <div className="inline-flex items-center px-[var(--spacing-3)] py-[var(--spacing-2)] border border-r-0 border-[var(--color-ui-border-light)] bg-[var(--color-ui-background-secondary)] rounded-l-[var(--radius-md)] text-[var(--color-ui-foreground-tertiary)]">
            {prefix}
          </div>
        )}
        
        <input
          id={id}
          name={name}
          type={type}
          value={value}
          onChange={onChange}
          placeholder={activePlaceholder}
          data-testid={`input-${id}`}
          className={inputClasses}
          style={{
            transitionTimingFunction: tokens.transitions.easing.apple,
            transitionDuration: tokens.transitions.duration[200],
          }}
        />
        
        {suffix && (
          <div className="inline-flex items-center px-[var(--spacing-3)] py-[var(--spacing-2)] border border-l-0 border-[var(--color-ui-border-light)] bg-[var(--color-ui-background-secondary)] rounded-r-[var(--radius-md)] text-[var(--color-ui-foreground-tertiary)]">
            {suffix}
          </div>
        )}
        
        {showErrorIcon && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-[var(--spacing-3)]">
            <svg 
              className="h-4 w-4 text-feedback-error opacity-70" 
              xmlns="http://www.w3.org/2000/svg" 
              viewBox="0 0 20 20" 
              fill="currentColor" 
              aria-hidden="true"
            >
              <path 
                fillRule="evenodd" 
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" 
                clipRule="evenodd" 
              />
            </svg>
          </div>
        )}
      </div>
      
      {showErrorIcon && errorMessage && (
        <p className="mt-[var(--spacing-1)] text-[var(--font-size-sm)] text-feedback-error opacity-90" data-testid={`error-${id}`}>
          {errorMessage}
        </p>
      )}
    </div>
  );
} 