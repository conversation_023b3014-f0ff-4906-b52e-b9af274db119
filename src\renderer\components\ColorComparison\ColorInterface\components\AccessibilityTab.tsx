/**
 * Accessibility Tab Component
 * Displays color accessibility information and simulations
 */

import React, { memo, useState, useMemo } from 'react';
import { Eye, AlertCircle, Zap } from 'lucide-react';
import type { AccessibilityTabProps } from '../types';
import { 
  simulateColorBlindness, 
  getColorBlindnessDescription,
  getColorBlindnessPrevalence,
  type ColorBlindnessType 
} from '../../../../../shared/utils/color/colorBlindness';
import { evaluateAPCAContrast } from '../../../../../shared/utils/color/analysis';
import { hexToRgb } from '../../../../../shared/utils/color/conversion';

const COLOR_BLINDNESS_TYPES: Array<{
  id: ColorBlindnessType;
  label: string;
  shortLabel: string;
}> = [
  { id: 'protanopia', label: 'Protanopia (Red-Blind)', shortLabel: 'Red-Blind' },
  { id: 'deuteranopia', label: 'Deuteranopia (Green-Blind)', shortLabel: 'Green-Blind' },
  { id: 'tritanopia', label: 'Tritanopia (Blue-Blind)', shortLabel: 'Blue-Blind' },
  { id: 'achromatopsia', label: 'Achromatopsia (No Color)', shortLabel: 'No Color' }
];

export const AccessibilityTab = memo<AccessibilityTabProps>(({ 
  selectedColor, 
  contrastResults 
}) => {
  const [selectedSimulation, setSelectedSimulation] = useState<ColorBlindnessType>('protanopia');
  const [selectedBackground, setSelectedBackground] = useState<'white' | 'black' | 'gray'>('white');

  // Calculate simulated colors
  const simulatedColors = useMemo(() => {
    if (!selectedColor) {
      return {} as Record<ColorBlindnessType, string>;
    }
    
    const result: Record<ColorBlindnessType, string> = {} as any;
    
    // Main types
    COLOR_BLINDNESS_TYPES.forEach(type => {
      result[type.id] = simulateColorBlindness(selectedColor.hex, type.id);
    });
    
    // Anomaly types
    result['protanomaly'] = simulateColorBlindness(selectedColor.hex, 'protanomaly');
    result['deuteranomaly'] = simulateColorBlindness(selectedColor.hex, 'deuteranomaly');
    result['tritanomaly'] = simulateColorBlindness(selectedColor.hex, 'tritanomaly');
    
    return result;
  }, [selectedColor?.hex]);

  if (!selectedColor || !contrastResults) {
    return (
      <div className="p-4 text-center text-ui-text-muted">
        <Eye className="mx-auto mb-2 h-8 w-8 opacity-50" />
        <p>Select a color to view accessibility information</p>
      </div>
    );
  }

  const getWCAGBadge = (level: string) => {
    const colors = {
      'AAA': 'text-green-600 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800',
      'AA': 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800',
      'AA Large': 'text-orange-600 bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800',
      'Fail': 'text-red-600 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
    };
    
    return colors[level as keyof typeof colors] || colors.Fail;
  };

  const prevalence = getColorBlindnessPrevalence(selectedSimulation);

  // Get current background and contrast based on selection
  const getCurrentBackground = () => {
    switch (selectedBackground) {
      case 'white': return { color: '#FFFFFF', result: contrastResults.white };
      case 'black': return { color: '#000000', result: contrastResults.black };
      case 'gray': return { color: '#808080', result: contrastResults.gray };
    }
  };

  const currentBg = getCurrentBackground();

  // Calculate APCA results for all backgrounds
  const apcaResults = useMemo(() => {
    if (!selectedColor) {
      return null;
    }
    
    const textRgb = hexToRgb(selectedColor.hex);
    if (!textRgb) {
      return null;
    }

    return {
      white: evaluateAPCAContrast(textRgb, { r: 255, g: 255, b: 255 }),
      black: evaluateAPCAContrast(textRgb, { r: 0, g: 0, b: 0 }),
      gray: evaluateAPCAContrast(textRgb, { r: 128, g: 128, b: 128 })
    };
  }, [selectedColor]);

  const currentAPCA = apcaResults && apcaResults[selectedBackground];

  const getAPCABadge = (level: string) => {
    const colors = {
      'Gold': 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800',
      'Silver': 'text-gray-600 bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800',
      'Bronze': 'text-orange-600 bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800',
      'Fail': 'text-red-600 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
    };
    
    return colors[level as keyof typeof colors] || colors.Fail;
  };

  return (
    <div className="p-3 h-full flex flex-col">
      {/* Combined WCAG & Color Blindness Section */}
      <div className="flex gap-3 h-full">
        {/* Left Side - WCAG Contrast Analysis */}
        <div className="flex-1 bg-ui-background-tertiary dark:bg-zinc-800 rounded-lg p-3 flex flex-col">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Contrast Analysis
            </h4>
            
            {/* Background Toggle */}
            <div className="flex items-center gap-1 bg-ui-background dark:bg-zinc-900 rounded-md p-0.5">
              <button
                onClick={() => setSelectedBackground('white')}
                className={`px-2 py-1 text-xs font-medium rounded transition-all ${
                  selectedBackground === 'white'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-ui-text-muted hover:text-ui-text-primary'
                }`}
              >
                White
              </button>
              <button
                onClick={() => setSelectedBackground('black')}
                className={`px-2 py-1 text-xs font-medium rounded transition-all ${
                  selectedBackground === 'black'
                    ? 'bg-gray-900 text-white shadow-sm'
                    : 'text-ui-text-muted hover:text-ui-text-primary'
                }`}
              >
                Black
              </button>
              <button
                onClick={() => setSelectedBackground('gray')}
                className={`px-2 py-1 text-xs font-medium rounded transition-all ${
                  selectedBackground === 'gray'
                    ? 'bg-gray-500 text-white shadow-sm'
                    : 'text-ui-text-muted hover:text-ui-text-primary'
                }`}
              >
                Gray
              </button>
            </div>
          </div>
          
          {/* Large Preview */}
          <div className="flex-1 rounded-lg border border-ui-border overflow-hidden">
            <div className="h-full p-6" style={{ backgroundColor: currentBg.color }}>
              <div className="h-full flex flex-col justify-center">
                <h2
                  className="text-2xl font-bold mb-3"
                  style={{ color: selectedColor.hex }}
                >
                  Sample Heading
                </h2>
                <p className="text-base leading-relaxed mb-4" style={{ color: selectedColor.hex }}>
                  The quick brown fox jumps over the lazy dog. This sample text demonstrates 
                  readability at different contrast levels.
                </p>
                <p className="text-sm leading-relaxed" style={{ color: selectedColor.hex }}>
                  WCAG guidelines recommend a minimum contrast ratio of 4.5:1 for normal text 
                  and 3:1 for large text. This helps ensure content is accessible to users 
                  with moderately low vision.
                </p>
              </div>
            </div>
          </div>
          
          {/* Contrast Info */}
          <div className="mt-3 space-y-3">
            {/* WCAG 2.1 */}
            <div className="bg-ui-background dark:bg-zinc-900 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="text-sm font-medium">WCAG 2.1</div>
                <span className={`text-xs px-3 py-1 rounded-full font-medium border ${getWCAGBadge(currentBg.result.level)}`}>
                  {currentBg.result.level}
                </span>
              </div>
              <div className="text-sm">
                <span className="text-ui-text-muted">Contrast Ratio: </span>
                <span className="font-mono font-semibold">{currentBg.result.ratio.toFixed(2)}:1</span>
              </div>
            </div>

            {/* APCA (WCAG 3.0) */}
            {currentAPCA && (
              <div className="bg-ui-background dark:bg-zinc-900 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="text-sm font-medium flex items-center gap-1">
                    <Zap className="h-3 w-3" />
                    APCA (WCAG 3.0)
                  </div>
                  <span className={`text-xs px-3 py-1 rounded-full font-medium border ${getAPCABadge(currentAPCA.level)}`}>
                    {currentAPCA.level}
                  </span>
                </div>
                <div className="text-sm mb-2">
                  <span className="text-ui-text-muted">Perceptual Contrast: </span>
                  <span className="font-mono font-semibold">{currentAPCA.contrast.toFixed(0)}%</span>
                </div>
                <div className="text-xs text-ui-text-muted">
                  {currentAPCA.description}
                </div>
                {(currentAPCA.minFontSize || currentAPCA.minFontWeight) && (
                  <div className="text-xs mt-1 text-orange-600">
                    Recommend: 
                    {currentAPCA.minFontSize && ` ${currentAPCA.minFontSize}px+`}
                    {currentAPCA.minFontWeight && ` ${currentAPCA.minFontWeight}+ weight`}
                  </div>
                )}
              </div>
            )}

            {/* Quick Stats */}
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div className="text-center bg-ui-background dark:bg-zinc-900 rounded p-2">
                <div className={`font-medium ${contrastResults.white.passes ? 'text-green-600' : 'text-red-600'}`}>
                  {contrastResults.white.ratio.toFixed(1)}:1
                </div>
                <div className="text-ui-text-muted">vs White</div>
                {apcaResults && (
                  <div className="text-xs text-ui-text-muted mt-1">
                    {apcaResults.white.contrast.toFixed(0)}%
                  </div>
                )}
              </div>
              <div className="text-center bg-ui-background dark:bg-zinc-900 rounded p-2">
                <div className={`font-medium ${contrastResults.black.passes ? 'text-green-600' : 'text-red-600'}`}>
                  {contrastResults.black.ratio.toFixed(1)}:1
                </div>
                <div className="text-ui-text-muted">vs Black</div>
                {apcaResults && (
                  <div className="text-xs text-ui-text-muted mt-1">
                    {apcaResults.black.contrast.toFixed(0)}%
                  </div>
                )}
              </div>
              <div className="text-center bg-ui-background dark:bg-zinc-900 rounded p-2">
                <div className={`font-medium ${contrastResults.gray.passes ? 'text-green-600' : 'text-red-600'}`}>
                  {contrastResults.gray.ratio.toFixed(1)}:1
                </div>
                <div className="text-ui-text-muted">vs Gray</div>
                {apcaResults && (
                  <div className="text-xs text-ui-text-muted mt-1">
                    {apcaResults.gray.contrast.toFixed(0)}%
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Right Side - Color Blindness Simulator */}
        <div className="flex-1 bg-ui-background-tertiary dark:bg-zinc-800 rounded-lg p-3 flex flex-col">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              Color Blindness Simulator
            </h4>
          </div>

          {/* Simulation Type Grid */}
          <div className="mb-2">
            <div className="grid grid-cols-2 gap-2">
              {COLOR_BLINDNESS_TYPES.map(type => (
                <button
                  key={type.id}
                  onClick={() => setSelectedSimulation(type.id)}
                  className={`p-2 rounded-lg border transition-all text-left ${
                    selectedSimulation === type.id 
                      ? 'border-brand-primary bg-brand-primary/10 text-brand-primary' 
                      : 'border-ui-border hover:border-ui-border-hover hover:bg-ui-background-secondary'
                  }`}
                  title={type.label}
                >
                  <div className="flex items-center gap-2">
                    <div
                      className="w-4 h-4 rounded border border-ui-border flex-shrink-0"
                      style={{
                        backgroundColor: simulatedColors[type.id] || selectedColor.hex
                      }}
                    />
                    <div className="min-w-0">
                      <div className="text-xs font-medium truncate">{type.shortLabel}</div>
                      <div className="text-xs text-ui-text-muted truncate">
                        {type.id === 'protanopia' && 'Red-blind'}
                        {type.id === 'deuteranopia' && 'Green-blind'}
                        {type.id === 'tritanopia' && 'Blue-blind'}
                        {type.id === 'achromatopsia' && 'No color'}
                      </div>
                    </div>
                  </div>
                </button>
              ))}
              
              {/* Additional anomaly types */}
              <button
                onClick={() => setSelectedSimulation('protanomaly')}
                className={`p-2 rounded-lg border transition-all text-left ${
                  selectedSimulation === 'protanomaly'
                    ? 'border-brand-primary bg-brand-primary/10 text-brand-primary' 
                    : 'border-ui-border hover:border-ui-border-hover hover:bg-ui-background-secondary'
                }`}
                title="Protanomaly (Red-Weak)"
              >
                <div className="flex items-center gap-2">
                  <div
                    className="w-4 h-4 rounded border border-ui-border flex-shrink-0"
                    style={{
                      backgroundColor: simulatedColors['protanomaly'] || selectedColor.hex
                    }}
                  />
                  <div className="min-w-0">
                    <div className="text-xs font-medium truncate">Red-Weak</div>
                    <div className="text-xs text-ui-text-muted truncate">Protanomaly</div>
                  </div>
                </div>
              </button>
              
              <button
                onClick={() => setSelectedSimulation('deuteranomaly')}
                className={`p-2 rounded-lg border transition-all text-left ${
                  selectedSimulation === 'deuteranomaly'
                    ? 'border-brand-primary bg-brand-primary/10 text-brand-primary' 
                    : 'border-ui-border hover:border-ui-border-hover hover:bg-ui-background-secondary'
                }`}
                title="Deuteranomaly (Green-Weak)"
              >
                <div className="flex items-center gap-2">
                  <div
                    className="w-4 h-4 rounded border border-ui-border flex-shrink-0"
                    style={{
                      backgroundColor: simulatedColors['deuteranomaly'] || selectedColor.hex
                    }}
                  />
                  <div className="min-w-0">
                    <div className="text-xs font-medium truncate">Green-Weak</div>
                    <div className="text-xs text-ui-text-muted truncate">Deuteranomaly</div>
                  </div>
                </div>
              </button>
            </div>
          </div>

          {/* Main Simulation Display */}
          <div className="flex-1 flex flex-col">
            {/* Color Comparison */}
            <div className="flex gap-2 mb-2">
              {/* Original */}
              <div className="flex-1">
                <div className="text-xs text-ui-text-muted mb-1">Original</div>
                <div
                  className="h-20 rounded-lg border border-ui-border shadow-sm"
                  style={{ backgroundColor: selectedColor.hex }}
                />
                <div className="text-xs mt-1 font-mono text-center">{selectedColor.hex}</div>
              </div>
              
              {/* Simulated */}
              <div className="flex-1">
                <div className="text-xs text-ui-text-muted mb-1">As Seen By</div>
                <div
                  className="h-20 rounded-lg border border-ui-border shadow-sm"
                  style={{ backgroundColor: simulatedColors[selectedSimulation] || selectedColor.hex }}
                />
                <div className="text-xs mt-1 font-mono text-center">
                  {simulatedColors[selectedSimulation] || selectedColor.hex}
                </div>
              </div>
            </div>

            {/* Preview on Background */}
            <div className="flex-1 rounded-lg border border-ui-border overflow-hidden mb-2">
              <div className="h-full p-4" style={{ backgroundColor: currentBg.color }}>
                <div className="grid grid-cols-2 gap-3 h-full">
                  <div>
                    <h5 className="text-xs font-medium mb-2" style={{ color: selectedColor.hex }}>
                      Normal Vision
                    </h5>
                    <p className="text-xs leading-relaxed" style={{ color: selectedColor.hex }}>
                      This is how the color appears to people with normal color vision.
                    </p>
                  </div>
                  <div>
                    <h5 className="text-xs font-medium mb-2" style={{ color: simulatedColors[selectedSimulation] || selectedColor.hex }}>
                      {selectedSimulation.charAt(0).toUpperCase() + selectedSimulation.slice(1)}
                    </h5>
                    <p className="text-xs leading-relaxed" style={{ color: simulatedColors[selectedSimulation] || selectedColor.hex }}>
                      This is the simulated appearance for color blind users.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Prevalence Info */}
            <div className="bg-ui-background dark:bg-zinc-900 rounded-lg p-2 text-xs">
              <div className="flex items-center justify-between mb-1">
                <span className="font-medium">{getColorBlindnessDescription(selectedSimulation).split(' - ')[0]}</span>
                <span className="text-ui-text-muted">
                  {prevalence.male.toFixed(1)}% ♂ / {prevalence.female.toFixed(2)}% ♀
                </span>
              </div>
              <p className="text-ui-text-muted text-xs">
                {getColorBlindnessDescription(selectedSimulation).split(' - ')[1]}
              </p>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
});

AccessibilityTab.displayName = 'AccessibilityTab';