/**
 * @file test-data.ipc.ts
 * @description IPC handlers for creating test data
 */

import { ipcMain } from 'electron';
import { ProductService } from '../db/services/product.service';
import { ColorService } from '../db/services/color.service';
import { TestDataChannels } from '../../shared/constants/channels';
import { getCurrentOrganizationId } from './organization.ipc';

export function registerTestDataHandlers(
  productService: ProductService,
  colorService: ColorService
): void {
  console.log('[TestDataIPC] Registering test data IPC handlers...');

  // Create test product with flat and gradient colors
  ipcMain.removeHandler(TestDataChannels.CREATE_TEST_PRODUCT);
  ipcMain.handle(TestDataChannels.CREATE_TEST_PRODUCT, async () => {
    try {
      console.log('[TestDataIPC] Creating test product with colors...');
      
      // Get current organization ID
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      
      // 1. Create test product
      const productName = `Test Product ${new Date().toISOString().slice(0, 10)}`;
      const product = productService.create({
        name: productName,
        metadata: {
          description: 'Test product with flat and gradient colors',
          testData: true,
          created: new Date().toISOString()
        }
      }, organizationId);

      console.log('[TestDataIPC] Created product:', product);

      // 2. Create flat color
      const flatColor = colorService.add({
        product: product.name,
        organizationId,
        name: 'Test Blue',
        code: `TEST-FLAT-${Date.now()}`,
        hex: '#2563EB',
        cmyk: '85,60,0,8',
        notes: 'Test flat color',
        isLibrary: false
      }, organizationId);

      console.log('[TestDataIPC] Created flat color:', flatColor);

      // 3. Create gradient color
      const gradientColor = colorService.add({
        product: product.name,
        organizationId,
        name: 'Test Gradient (Blue to Purple)',
        code: `TEST-GRADIENT-${Date.now()}`,
        hex: '#2563EB',
        cmyk: '85,60,0,8',
        notes: 'Test gradient color',
        isLibrary: false,
        gradient: {
          gradientStops: [
            { color: '#2563EB', position: 0 },
            { color: '#7C3AED', position: 0.5 },
            { color: '#DC2626', position: 1 }
          ],
          gradientCSS: 'linear-gradient(45deg, #2563EB 0%, #7C3AED 50%, #DC2626 100%)'
        }
      }, organizationId);

      console.log('[TestDataIPC] Created gradient color:', gradientColor);

      // 4. Associate colors with product
      productService.addColorToProduct(product.id, flatColor.id, organizationId);
      productService.addColorToProduct(product.id, gradientColor.id, organizationId);
      
      console.log('[TestDataIPC] Associated colors with product');

      // 5. Get the complete product with colors to return
      const productWithColors = productService.getProductWithColors(product.id, organizationId);

      return {
        success: true,
        product: productWithColors,
        message: `Created test product "${productName}" with flat and gradient colors`
      };

    } catch (error) {
      console.error('[TestDataIPC] Error creating test product:', error);
      throw error;
    }
  });

  // Remove all test data
  ipcMain.removeHandler(TestDataChannels.REMOVE_TEST_DATA);
  ipcMain.handle(TestDataChannels.REMOVE_TEST_DATA, async () => {
    try {
      console.log('[TestDataIPC] Removing all test data...');
      
      // Get current organization ID
      const organizationId = getCurrentOrganizationId();
      if (!organizationId) {
        throw new Error('No organization selected');
      }
      
      // Get all products with test prefix
      const allProducts = productService.getAll(organizationId);
      const testProducts = allProducts.filter(p => 
        p.name.startsWith('Test Product')
      );

      let removedCount = 0;
      
      for (const product of testProducts) {
        // Get product colors before deletion
        const productColors = productService.getProductColors(product.id, organizationId);
        
        // Remove the product (this should cascade and remove associations)
        productService.delete(product.id, organizationId);
        
        // Remove the associated test colors
        for (const color of productColors) {
          if (color.code?.startsWith('TEST-')) {
            colorService.delete(color.id, organizationId);
          }
        }
        
        removedCount++;
      }

      return {
        success: true,
        removedCount,
        message: `Removed ${removedCount} test products and their associated colors`
      };

    } catch (error) {
      console.error('[TestDataIPC] Error removing test data:', error);
      throw error;
    }
  });

  console.log('[TestDataIPC] Test data IPC handlers registered.');
}