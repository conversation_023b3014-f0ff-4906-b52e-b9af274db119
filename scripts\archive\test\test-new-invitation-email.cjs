#!/usr/bin/env node

// Test the new invitation email template
const axios = require('axios');
const path = require('path');
const fs = require('fs');
const os = require('os');
require('dotenv').config();

// Get token file location
const appName = 'ChromaSync';
const userData = process.platform === 'darwin' 
  ? path.join(os.homedir(), 'Library', 'Application Support', appName)
  : process.platform === 'win32'
  ? path.join(process.env.APPDATA, appName)
  : path.join(os.homedir(), '.config', appName);

const tokenFile = path.join(userData, 'zoho-tokens.json');

console.log('🧪 Testing New Invitation Email Template...\n');

async function testNewInvitationEmail() {
  try {
    // Load saved tokens
    if (!fs.existsSync(tokenFile)) {
      console.error('❌ No token file found. Run refresh-zoho-token.cjs first.');
      process.exit(1);
    }
    
    const tokens = JSON.parse(fs.readFileSync(tokenFile, 'utf-8'));
    
    // Check if token is still valid
    if (Date.now() >= tokens.expiresAt) {
      console.error('❌ Token has expired. Run refresh-zoho-token.cjs first.');
      process.exit(1);
    }
    
    console.log('✅ Using existing access token\n');
    
    // Create test invitation data with a sample token
    const testInvitation = {
      organizationName: 'IVG E-Liquids',
      inviterName: 'Michael',
      role: 'member',
      token: 'f6f37b50-93ab-48db-9b95-31fb0e706fe9',
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
    };
    
    // Build the new email template with manual instructions
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ChromaSync Team Invitation</title>
  <style>
    body { 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
      line-height: 1.6; 
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f8fafc;
    }
    .container { 
      max-width: 600px;
      margin: 0 auto;
      background: #ffffff; 
      border-radius: 12px; 
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 40px 30px;
      text-align: center;
    }
    .logo {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    .content {
      padding: 40px 30px;
    }
    .invitation-card {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      border-left: 4px solid #3b82f6;
      border-radius: 8px;
      padding: 24px;
      margin: 24px 0;
    }
    .instructions {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 20px;
      margin: 24px 0;
    }
    .instructions h3 {
      margin-top: 0;
      color: #1f2937;
    }
    .instructions ol {
      margin: 10px 0;
      padding-left: 20px;
    }
    .instructions li {
      margin: 8px 0;
    }
    .token-display {
      background: #1f2937;
      color: #10b981;
      font-family: 'Monaco', 'Courier New', monospace;
      padding: 12px 16px;
      border-radius: 6px;
      margin: 12px 0;
      word-break: break-all;
      text-align: center;
      font-size: 14px;
    }
    .footer {
      padding: 30px;
      background: #f8fafc;
      text-align: center;
      font-size: 14px;
      color: #64748b;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo">🎨 ChromaSync</div>
      <p>Professional Color Management</p>
    </div>
    
    <div class="content">
      <h2>You're invited to join ${testInvitation.organizationName}!</h2>
      
      <div class="invitation-card">
        <p><strong>${testInvitation.inviterName}</strong> has invited you to join their ChromaSync team as a <strong>${testInvitation.role}</strong>.</p>
        <p>ChromaSync helps teams organize and sync their color palettes across projects.</p>
      </div>
      
      <div class="instructions">
        <h3>📋 How to Accept Your Invitation:</h3>
        <ol>
          <li><strong>Open ChromaSync</strong> on your computer</li>
          <li>Sign in with your Google account</li>
          <li>Go to <strong>Settings → Team</strong></li>
          <li>Click <strong>"Join Organization"</strong></li>
          <li>Enter this invitation code:</li>
        </ol>
        
        <div class="token-display">
          ${testInvitation.token}
        </div>
        
        <p style="text-align: center; margin-top: 20px;">
          <em>💡 Tip: You can copy the code above and paste it into ChromaSync</em>
        </p>
      </div>
    </div>
    
    <div class="footer">
      <p><strong>⏰ This invitation expires on ${testInvitation.expiresAt.toLocaleDateString('en-US', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      })}.</strong></p>
      <p>Don't have ChromaSync yet? Download it at <a href="https://chromasync.app" style="color: #3b82f6;">chromasync.app</a></p>
      <p>Questions? Contact us at <a href="mailto:<EMAIL>" style="color: #3b82f6;"><EMAIL></a></p>
      <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 24px 0;">
      <p>© 2024 ChromaSync. All rights reserved.</p>
    </div>
  </div>
</body>
</html>`;
    
    // Send email using Zoho API
    const emailData = {
      fromAddress: process.env.ZOHO_SUPPORT_ALIAS || '<EMAIL>',
      toAddress: '<EMAIL>',
      subject: `🎨 ${testInvitation.inviterName} invited you to join ${testInvitation.organizationName}`,
      content: htmlContent,
      mailFormat: 'html'
    };
    
    console.log('📧 Sending new invitation email to:', emailData.toAddress);
    console.log('   With invitation code:', testInvitation.token);
    
    const zohoApiDomain = process.env.ZOHO_REGION === 'EU' ? 'mail.zoho.eu' : 'mail.zoho.com';
    const accountId = process.env.ZOHO_ACCOUNT_ID;
    
    const response = await axios.post(
      `https://${zohoApiDomain}/api/accounts/${accountId}/messages`,
      emailData,
      {
        headers: {
          'Authorization': `Zoho-oauthtoken ${tokens.accessToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 15000
      }
    );
    
    console.log('\n✅ New invitation email sent successfully!');
    console.log(`   Message ID: ${response.data.data?.messageId || 'N/A'}`);
    console.log('\n📋 Next Steps:');
    console.log('1. Check <NAME_EMAIL>');
    console.log('2. Follow the manual instructions in the email');
    console.log('3. Open ChromaSync → Settings → Team → Join Organization');
    console.log('4. Paste the invitation code');
    
  } catch (error) {
    console.error('❌ Failed to send invitation email:', error.response?.data || error.message);
    process.exit(1);
  }
}

// Run the test
testNewInvitationEmail();
