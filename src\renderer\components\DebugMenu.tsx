import React, { useState } from 'react';

const DebugMenu: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  // repairProductColors method no longer exists in optimized schema

  const toggleVisibility = () => {
    setIsVisible(prev => !prev);
  };

  const handleRepairProductColors = async () => {
    // This functionality is no longer needed with the optimized schema
    setMessage('Product color repair is not needed with the optimized database schema.');
  };

  const handleCreateTestProduct = async () => {
    try {
      setMessage('Creating test product with colors... Please wait.');
      const result = await window.testDataAPI.createTestProduct();
      setMessage(result.message || 'Test product created successfully!');
    } catch (error) {
      setMessage('Error: ' + (error instanceof Error ? error.message : String(error)));
    }
  };

  const handleRemoveTestData = async () => {
    if (window.confirm('Are you sure you want to remove all test data? This cannot be undone.')) {
      try {
        setMessage('Removing test data... Please wait.');
        const result = await window.testDataAPI.removeTestData();
        setMessage(result.message || 'Test data removed successfully!');
      } catch (error) {
        setMessage('Error: ' + (error instanceof Error ? error.message : String(error)));
      }
    }
  };

  // Style for the debug button
  const buttonStyle: React.CSSProperties = {
    position: 'fixed',
    bottom: '10px',
    right: '10px',
    width: '24px',
    height: '24px',
    borderRadius: '50%',
    backgroundColor: '#555',
    color: '#fff',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    zIndex: 9999,
    fontSize: '12px',
    border: 'none',
    outline: 'none'
  };

  // Style for the debug panel
  const panelStyle: React.CSSProperties = {
    position: 'fixed',
    bottom: '40px',
    right: '10px',
    width: '300px',
    backgroundColor: '#fff',
    border: '1px solid #ccc',
    borderRadius: '4px',
    padding: '10px',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
    zIndex: 9998
  };

  // Style for the debug panel buttons
  const panelButtonStyle: React.CSSProperties = {
    backgroundColor: '#f0f0f0',
    border: '1px solid #ccc',
    borderRadius: '4px',
    padding: '5px 10px',
    margin: '5px 0',
    cursor: 'pointer',
    width: '100%',
    textAlign: 'left'
  };

  // Style for the message box
  const messageStyle: React.CSSProperties = {
    marginTop: '10px',
    padding: '5px',
    border: '1px solid #ccc',
    borderRadius: '4px',
    backgroundColor: '#f9f9f9',
    maxHeight: '100px',
    overflowY: 'auto',
    fontSize: '12px'
  };

  return (
    <>
      <button style={buttonStyle} onClick={toggleVisibility} title="Debug Menu">
        D
      </button>
      {isVisible && (
        <div style={panelStyle}>
          <h4 style={{ margin: '0 0 10px 0', fontSize: '14px' }}>Debug Tools</h4>
          <button 
            style={panelButtonStyle} 
            onClick={handleRepairProductColors}
            title="Repair product colors associations in the database"
          >
            Repair Product Colors
          </button>
          <button 
            style={panelButtonStyle} 
            onClick={handleCreateTestProduct}
            title="Create a test product with flat and gradient colors"
          >
            Create Test Product
          </button>
          <button 
            style={panelButtonStyle} 
            onClick={handleRemoveTestData}
            title="Remove all test products and colors from the database"
          >
            Remove Test Data
          </button>
          {message && (
            <div style={messageStyle}>
              {message}
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default DebugMenu;
