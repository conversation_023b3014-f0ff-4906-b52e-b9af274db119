/* eslint-disable @typescript-eslint/no-explicit-any */
// Define the structure of the API exposed by the preload script
export interface ISetupAPI {
  getInitialConfigStatus: () => Promise<{ configured: boolean }>;
  selectSharedFolder: () => Promise<string | null>;
  saveStorageConfig: (config: { mode: 'standalone' | 'collaboration' | 'server-sync', path?: string }) => Promise<{ success: boolean; error?: string }>;
  sendSetupComplete: () => void;
  onShowSetupModal: (callback: () => void) => () => void; // Returns a cleanup function
}

export interface ISyncAPI {
  getState: () => Promise<unknown>;
  getConfig: () => Promise<unknown>;
  updateConfig: (config: any) => Promise<unknown>;
  getAuthState: () => Promise<unknown>;
  login: (email: string, password: string) => Promise<unknown>;
  signup: (email: string, password: string) => Promise<unknown>;
  logout: () => Promise<unknown>;
  syncData: () => Promise<unknown>;
  testConnection: () => Promise<unknown>;
  subscribe: () => Promise<unknown>;
  unsubscribe: () => Promise<unknown>;
  resolveConflicts: (resolutions: any[]) => Promise<unknown>;
  onStatusUpdate: (callback: (data: unknown) => void) => void;
  onDataChanged: (callback: (data: unknown) => void) => void;
  onConflicts: (callback: (data: unknown) => void) => void;
  hasUnsyncedLocalChanges: () => Promise<boolean>;
}

declare global {
  interface Window {
    setupAPI: ISetupAPI;
    syncAPI: ISyncAPI;
    // Add other preload APIs here if necessary, e.g.:
    // electronAPI: IElectronAPI;
    // colorAPI: IColorAPI;
    // ...etc
  }
}
