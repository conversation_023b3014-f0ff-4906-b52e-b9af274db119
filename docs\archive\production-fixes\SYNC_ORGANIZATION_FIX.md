# Sync Organization Fix

## Problem
The sync is failing with `FOREIGN KEY constraint failed` errors because colors are trying to reference organization ID `4047153f-7be8-490b-9cb2-a1e3ed04b92b` which doesn't exist in the local SQLite database.

## Root Cause
The sync process is attempting to sync colors before ensuring the organization exists locally. The foreign key constraint on the `colors` table prevents insertion of colors that reference non-existent organizations.

## Quick Fix (for immediate use)

Run this SQL in your local database to create the organization:

```sql
-- Insert the IVG organization into local database
INSERT OR IGNORE INTO organizations (
  id,
  external_id,
  name,
  slug,
  plan,
  settings,
  created_at,
  updated_at
) VALUES (
  1,  -- Local ID
  '4047153f-7be8-490b-9cb2-a1e3ed04b92b',  -- Supabase ID
  'IVG',  -- Organization name
  'ivg',  -- URL slug
  'free',  -- Plan type
  '{}',  -- Settings JSON
  datetime('now'),
  datetime('now')
);

-- Add yourself as a member (replace with your actual user ID)
INSERT OR IGNORE INTO organization_members (
  organization_id,
  user_id,
  role,
  joined_at
) VALUES (
  1,  -- Local org ID
  'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',  -- Your user ID from the logs
  'owner',
  datetime('now')
);

-- If you have a users table, ensure your user exists
INSERT OR IGNORE INTO users (
  id,
  email,
  name,
  created_at,
  updated_at
) VALUES (
  'bdcfcd21-ddb3-4386-bfb3-e6d5906babbf',
  '<EMAIL>',  -- Replace with your actual email
  'Your Name',  -- Replace with your name
  datetime('now'),
  datetime('now')
);
```

## Permanent Fix (needs to be implemented)

The sync process should:
1. First sync organizations from Supabase
2. Then sync organization members
3. Only then sync colors and products

This requires modifying the sync initialization to call `organizationService.syncOrganizationsFromSupabase()` before syncing colors.

## Steps to Apply Quick Fix

1. Open the SQLite database (located at your app's data directory)
2. Run the SQL commands above
3. Restart the application
4. The colors should now load properly

## Verification

After applying the fix, you should see:
- 451 colors in the table
- 21 products
- Organization "IVG" selected in the header