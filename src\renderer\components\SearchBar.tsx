/**
 * @file SearchBar.tsx
 * @description Apple-inspired search input for filtering color entries
 */

import { useTokens } from '../hooks/useTokens';

interface SearchBarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  viewMode?: 'table' | 'swatches' | 'codes' | 'products';
}

export default function SearchBar({ searchQuery, onSearchChange, viewMode = 'table' }: SearchBarProps) {
  const tokens = useTokens();

  // Determine placeholder based on viewMode
  const getPlaceholder = () => {
    switch (viewMode) {
      case 'products':
        return 'Search products...';
      case 'codes':
        return 'Search color codes...';
      case 'swatches':
        return 'Search colors...';
      default:
        return 'Search colors or use product:name...';
    }
  };

  return (
    <div className="relative w-full">
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <svg
          className="h-5 w-5 text-ui-foreground-tertiary dark:text-gray-400"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>
      <input
        type="search"
        role="searchbox"
        aria-label={`Search ${viewMode} entries`}
        placeholder={getPlaceholder()}
        className="w-full pl-10 pr-4 py-2 bg-ui-background-tertiary dark:bg-zinc-700 border-none rounded-lg text-ui-foreground-primary dark:text-white placeholder-ui-foreground-tertiary dark:placeholder-gray-400 focus:ring-2 focus:ring-brand-secondary focus:bg-white dark:focus:bg-zinc-800 transition-all duration-200 text-sm"
        value={searchQuery}
        onChange={(e) => onSearchChange(e.target.value)}
        data-testid="search-input"
        style={{
          transition: `all ${tokens.transitions.duration[200]} ${tokens.transitions.easing.apple}`,
        }}
      />
      {searchQuery && (
        <button
          onClick={() => onSearchChange('')}
          className="absolute inset-y-0 right-0 pr-3 flex items-center text-ui-foreground-tertiary dark:text-gray-400 hover:text-ui-foreground-primary dark:hover:text-white"
          aria-label="Clear search"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        </button>
      )}
    </div>
  );
}
