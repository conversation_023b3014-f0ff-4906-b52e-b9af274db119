/**
 * @file ProductsPanel.tsx
 * @description Component for managing and displaying products with their colors
 */

import React, { useState, useEffect } from 'react';
import { useProductStore } from '../../store/product.store';
import { useColorStore } from '../../store/color.store';
import ProductView from './ProductView';
import { Plus, Folder, Trash2, RefreshCw } from 'lucide-react';
import { useTokens } from '../../hooks/useTokens';
import ProductCreateModal from './ProductCreateModal';
import Modal from '../ui/Modal';
import { ProductCardSkeleton } from '../ui/Skeleton';

const ProductsPanel: React.FC = () => {
  const tokens = useTokens();
  const {
    products,
    activeProductId,
    setActiveProductId,
    deleteProduct,
    fetchProductsWithColors,
    isLoading
  } = useProductStore();

  const { searchQuery } = useColorStore();

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncMessage, setSyncMessage] = useState<string | null>(null);

  // Fetch products on component mount
  useEffect(() => {
    console.log('[ProductsPanel] Component mounted, checking window.ipc...');
    // Check if window.ipc is available before fetching
    if (typeof window !== 'undefined' && window.ipc) {
      console.log('[ProductsPanel] window.ipc is available, fetching products...');
      fetchProductsWithColors();
    } else {
      console.log('[ProductsPanel] window.ipc not available yet, waiting...');
      // Try again after a short delay if window.ipc is not ready
      const timer = setTimeout(() => {
        if (typeof window !== 'undefined' && window.ipc) {
          console.log('[ProductsPanel] window.ipc now available, fetching products...');
          fetchProductsWithColors();
        } else {
          console.warn('[ProductsPanel] window.ipc still not available for fetching products');
        }
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [fetchProductsWithColors]);

  // Function to refresh products
  const handleSyncProducts = async () => {
    try {
      setIsSyncing(true);
      setSyncMessage(null);

      // Refresh products
      console.log('Refreshing products...');
      await fetchProductsWithColors();

      // Set success message
      setSyncMessage('Products refreshed successfully');

      // Auto-hide the message after 3 seconds
      setTimeout(() => {
        setSyncMessage(null);
      }, 3000);
    } catch (error) {
      console.error('Error refreshing products:', error);
      setSyncMessage('Error refreshing products');
    } finally {
      setIsSyncing(false);
    }
  };

  // Handle product selection
  const handleSelectProduct = (id: string) => {
    setActiveProductId(id);
  };

  // Handle product deletion
  const handleDeleteProduct = async () => {
    if (!deleteConfirmId) {return;}

    try {
      await deleteProduct(deleteConfirmId);
      setDeleteConfirmId(null);

      // If the deleted product was active, clear the active selection
      if (deleteConfirmId === activeProductId) {
        setActiveProductId(null);
      }
    } catch (err) {
      console.error('Failed to delete product:', err);
    }
  };

  // Filter products based on search query from the main search bar
  const filteredProducts = searchQuery
    ? products.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (product.description && product.description.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    : products;

  return (
    <div className="h-full flex flex-col bg-ui-background-primary dark:bg-zinc-900 border-r border-ui-border-light dark:border-zinc-700">
      {/* Split view with sidebar and content */}
      <div className="flex flex-1 h-full overflow-hidden">
        {/* Products sidebar */}
        <div className="w-56 border-r border-ui-border-light dark:border-zinc-700 flex flex-col bg-ui-background-secondary dark:bg-zinc-800">
          {/* Premium header with actions */}
          <div className="flex items-center justify-between px-3 py-2.5 border-b border-ui-border-light dark:border-zinc-700">
            <h2 className="text-xs font-medium uppercase tracking-wide text-ui-foreground-secondary dark:text-gray-400">Products</h2>
            <div className="flex gap-1">
              <button
                onClick={handleSyncProducts}
                className={`p-1 text-ui-muted hover:text-ui-foreground-primary dark:text-gray-500 dark:hover:text-white rounded transition-colors ${isSyncing ? 'animate-spin' : ''}`}
                aria-label="Refresh products"
                title="Refresh products"
                disabled={isSyncing}
                style={{
                  transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                }}
              >
                <RefreshCw size={14} />
              </button>
              <button
                onClick={() => setShowCreateModal(true)}
                className="p-1 text-ui-muted hover:text-ui-foreground-primary dark:text-gray-500 dark:hover:text-white rounded transition-colors"
                aria-label="Create product"
                title="Create new product"
                style={{
                  transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                }}
              >
                <Plus size={14} />
              </button>
            </div>
          </div>


          {/* Status message */}
          {syncMessage && (
            <div className="px-3 py-1.5 text-xs text-center bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100 border-b border-ui-border-light dark:border-zinc-700">
              {syncMessage}
            </div>
          )}

          <div className="flex-1 overflow-y-auto py-2 products-scroll dark:products-scroll-dark">
            {isLoading ? (
              <div className="px-4 space-y-3">
                {Array.from({ length: 4 }).map((_, i) => (
                  <ProductCardSkeleton key={i} />
                ))}
              </div>
            ) : filteredProducts.length === 0 ? (
              <div className="px-4 py-6 text-center">
                <Folder className="mx-auto h-8 w-8 text-ui-muted dark:text-gray-500 mb-2" />
                <p className="text-ui-muted dark:text-gray-400 text-xs">
                  {searchQuery ? 'No products match your search' : 'No products yet'}
                </p>
                {!searchQuery && (
                  <button
                    onClick={() => setShowCreateModal(true)}
                    className="mt-3 text-xs text-brand-primary hover:text-brand-primary-dark transition-colors"
                    style={{
                      transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                    }}
                  >
                    Create your first product
                  </button>
                )}
              </div>
            ) : (
              <ul className="space-y-0.5 px-2">
                {filteredProducts.map(product => {
                  // Generate a color for the product based on its name (just as a visual indicator)
                  const colorHash = product.name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % 360;
                  const dotColor = `hsl(${colorHash}, 70%, 60%)`;

                  return (
                    <li key={product.id}>
                      <div
                        className={`group flex items-center px-2.5 py-1.5 rounded-md cursor-pointer transition-all ${
                          activeProductId === product.id
                            ? 'product-item-active dark:bg-zinc-700 dark:text-white'
                            : 'text-ui-foreground-secondary dark:text-gray-300 hover:bg-ui-background-tertiary/50 dark:hover:bg-zinc-700/50 border-l-2 border-transparent pl-2'
                        }`}
                        onClick={() => handleSelectProduct(product.id)}
                        style={{
                          transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                        }}
                      >
                        <div className="flex items-center flex-1 min-w-0">
                          <div
                            className="product-dot mr-2 flex-shrink-0"
                            style={{ backgroundColor: dotColor }}
                          />
                          <div className="truncate text-sm">{product.name}</div>
                        </div>

                        <div className="flex items-center ml-1">
                          <span className="product-counter dark:bg-zinc-600 dark:text-gray-300">
                            {product.colors?.length || 0}
                          </span>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setDeleteConfirmId(product.id);
                            }}
                            className="p-1 ml-1 text-ui-muted dark:text-gray-400 hover:text-red-500 opacity-0 group-hover:opacity-100 transition-opacity"
                            aria-label={`Delete ${product.name}`}
                            title={`Delete ${product.name}`}
                            style={{
                              transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                            }}
                          >
                            <Trash2 size={14} />
                          </button>
                        </div>
                      </div>
                    </li>
                  );
                })}
              </ul>
            )}
          </div>
        </div>

        {/* Product view */}
        <div className="flex-1 overflow-hidden">
          <ProductView activeProductId={activeProductId} />
        </div>
      </div>

      {/* Create product modal */}
      <ProductCreateModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
      />

      {/* Delete confirmation modal */}
      {deleteConfirmId && (
        <Modal
          isOpen={true}
          onClose={() => setDeleteConfirmId(null)}
          title="Delete Product?"
        >
          <div className="p-5">
            <div className="text-ui-foreground-secondary dark:text-gray-300 text-sm mb-5">
              Are you sure you want to delete this product? This action cannot be undone.
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setDeleteConfirmId(null)}
                className="px-3.5 py-1.5 text-xs font-medium text-ui-foreground-primary bg-ui-background-tertiary rounded-md hover:bg-ui-background-tertiary-hover transition-colors"
                style={{
                  transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteProduct}
                className="px-3.5 py-1.5 text-xs font-medium bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
                style={{
                  transition: `all ${tokens.transitions.duration[150]} ${tokens.transitions.easing.apple}`
                }}
              >
                Delete
              </button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default ProductsPanel;