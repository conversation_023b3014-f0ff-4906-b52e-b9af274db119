/**
 * Database Connection Management
 * Handles connection pooling, initialization, and resource management
 */

import { app } from 'electron';
import path from 'path';
import fs from 'fs';

// We'll load Database dynamically to avoid bundler issues
let Database: any;

/**
 * Database connection pool for better concurrent access
 * Manages multiple connections to prevent blocking and improve performance
 */
export class DatabasePool {
  private static instance: DatabasePool;
  private connections: any[] = [];
  private readonly maxConnections = 5;
  private busyConnections = new Set<any>();
  private dbPath: string;
  
  private constructor() {
    this.dbPath = this.getDatabasePath();
  }
  
  /**
   * Get singleton instance of database pool
   */
  static getInstance(): DatabasePool {
    if (!DatabasePool.instance) {
      DatabasePool.instance = new DatabasePool();
    }
    return DatabasePool.instance;
  }
  
  /**
   * Get database path
   */
  private getDatabasePath(): string {
    const userDataPath = app.getPath('userData');
    return path.join(userDataPath, 'chromasync.db');
  }
  
  /**
   * Get available connection from pool or create new one
   */
  async getConnection(): Promise<any> {
    // Return available connection
    const availableConnection = this.connections.find(conn => !this.busyConnections.has(conn));
    if (availableConnection) {
      this.busyConnections.add(availableConnection);
      return availableConnection;
    }
    
    // Create new connection if under limit
    if (this.connections.length < this.maxConnections) {
      const newConnection = this.createConnection();
      this.connections.push(newConnection);
      this.busyConnections.add(newConnection);
      return newConnection;
    }
    
    // Wait for connection to become available (simplified approach)
    return new Promise((resolve) => {
      const checkForConnection = () => {
        const conn = this.connections.find(c => !this.busyConnections.has(c));
        if (conn) {
          this.busyConnections.add(conn);
          resolve(conn);
        } else {
          setTimeout(checkForConnection, 10);
        }
      };
      checkForConnection();
    });
  }
  
  /**
   * Release connection back to pool
   */
  releaseConnection(connection: any): void {
    this.busyConnections.delete(connection);
  }
  
  /**
   * Create new database connection with optimal settings
   */
  private createConnection(): any {
    if (!Database) {
      Database = this.loadBetterSqlite3();
    }
    
    // Ensure directory exists
    const dirPath = path.dirname(this.dbPath);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
    }
    
    const connection = new Database(this.dbPath, { 
      verbose: process.env.NODE_ENV === 'development' ? console.log : undefined,
      fileMustExist: false
    });
    
    // Apply optimal PRAGMA settings
    connection.exec(`
      PRAGMA foreign_keys = ON;
      PRAGMA journal_mode = WAL;
      PRAGMA synchronous = NORMAL;
      PRAGMA temp_store = MEMORY;
      PRAGMA mmap_size = 30000000000;
      PRAGMA cache_size = -64000;
      PRAGMA wal_autocheckpoint = 1000;
    `);
    
    return connection;
  }
  
  /**
   * Load better-sqlite3 module for Electron
   */
  private loadBetterSqlite3(): any {
    // Use window.require in Electron which bypasses webpack
    const electronRequire = (globalThis as any).require || require;
    return electronRequire('better-sqlite3');
  }
  
  /**
   * Close all connections in pool
   */
  closeAll(): void {
    this.connections.forEach(conn => {
      try {
        conn.close();
      } catch (error) {
        console.warn('[DatabasePool] Error closing database connection:', error);
      }
    });
    this.connections = [];
    this.busyConnections.clear();
  }
  
  /**
   * Get pool statistics for monitoring
   */
  getStats(): {
    totalConnections: number;
    busyConnections: number;
    availableConnections: number;
    maxConnections: number;
  } {
    return {
      totalConnections: this.connections.length,
      busyConnections: this.busyConnections.size,
      availableConnections: this.connections.length - this.busyConnections.size,
      maxConnections: this.maxConnections
    };
  }
}

/**
 * Get database connection from pool
 */
export async function getPooledConnection(): Promise<any> {
  const pool = DatabasePool.getInstance();
  return await pool.getConnection();
}

/**
 * Release database connection back to pool
 */
export function releasePooledConnection(connection: any): void {
  const pool = DatabasePool.getInstance();
  pool.releaseConnection(connection);
}

/**
 * Execute a query with automatic connection pooling
 */
export async function executeWithPool<T>(operation: (db: any) => T): Promise<T> {
  const connection = await getPooledConnection();
  try {
    return operation(connection);
  } finally {
    releasePooledConnection(connection);
  }
}

/**
 * Get database path utility function
 */
export function getDatabasePath(): string {
  const userDataPath = app.getPath('userData');
  return path.join(userDataPath, 'chromasync.db');
}
