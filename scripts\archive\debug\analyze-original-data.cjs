const fs = require('fs');
const path = require('path');

// Read and parse the original JSON data
function analyzeOriginalData() {
  try {
    const jsonPath = '/Users/<USER>/Library/CloudStorage/OneDrive-SharedLibraries-ACMEVAPELTD/Creative - Documents/2. TEAM/Michael/Colour Store/Archive/Pantone tracker dev v3/load.data.json';
    const jsonData = JSON.parse(fs.readFileSync(jsonPath, 'utf8'));
    
    // Count colors by product
    const productCounts = {};
    const productFlavors = {};
    const seenColors = new Set();
    const duplicateColors = [];
    
    jsonData.forEach(item => {
      const productName = item.name;
      
      // Count colors per product
      if (!productCounts[productName]) {
        productCounts[productName] = 0;
        productFlavors[productName] = new Set();
      }
      productCounts[productName]++;
      
      // Track flavors
      if (item.flavor) {
        productFlavors[productName].add(item.flavor);
      }
      
      // Check for duplicate entries
      const colorKey = `${productName}-${item.pantoneCode}-${item.flavor}`;
      if (seenColors.has(colorKey)) {
        duplicateColors.push({
          product: productName,
          pantone: item.pantoneCode,
          flavor: item.flavor,
          hex: item.colorHex
        });
      }
      seenColors.add(colorKey);
    });
    
    // Sort products by name
    const sortedProducts = Object.keys(productCounts).sort();
    
    console.log('=== ORIGINAL DATA ANALYSIS ===\n');
    console.log('Total entries:', jsonData.length);
    console.log('Total unique products:', sortedProducts.length);
    console.log('\nProduct Color Counts:');
    console.log('Product Name                                    | Colors | Unique Flavors');
    console.log('-----------------------------------------------|--------|---------------');
    
    let totalColors = 0;
    sortedProducts.forEach(product => {
      const count = productCounts[product];
      const flavorCount = productFlavors[product].size;
      totalColors += count;
      console.log(`${product.padEnd(46)} | ${count.toString().padStart(6)} | ${flavorCount.toString().padStart(14)}`);
    });
    
    console.log('-----------------------------------------------|--------|---------------');
    console.log(`${'TOTAL'.padEnd(46)} | ${totalColors.toString().padStart(6)} |`);
    
    if (duplicateColors.length > 0) {
      console.log('\n⚠️  Duplicate entries found:', duplicateColors.length);
      console.log('\nFirst 10 duplicates:');
      duplicateColors.slice(0, 10).forEach(dup => {
        console.log(`  - ${dup.product}: ${dup.pantone} (${dup.flavor})`);
      });
    }
    
    // Save summary to file for later comparison
    const summary = {
      totalEntries: jsonData.length,
      productCounts: productCounts,
      duplicatesFound: duplicateColors.length,
      timestamp: new Date().toISOString()
    };
    
    fs.writeFileSync(
      path.join(__dirname, 'original-data-summary.json'),
      JSON.stringify(summary, null, 2)
    );
    
    console.log('\nSummary saved to original-data-summary.json');
    
  } catch (error) {
    console.error('Error analyzing original data:', error);
  }
}

analyzeOriginalData();