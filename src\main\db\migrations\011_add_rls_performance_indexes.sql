-- Migration 011: Add RLS Performance Indexes
-- Based on Supabase best practices for RLS performance optimization
-- Reference: https://supabase.com/docs/guides/database/postgres/row-level-security

-- Add index on colors.organization_id for RLS performance
-- This is crucial for RLS policies that filter by organization_id
CREATE INDEX IF NOT EXISTS idx_colors_organization_id 
ON colors USING btree (organization_id);

-- Add index on products.organization_id for RLS performance  
-- This is crucial for RLS policies that filter by organization_id
CREATE INDEX IF NOT EXISTS idx_products_organization_id 
ON products USING btree (organization_id);

-- Add index on product_colors.organization_id for RLS performance
-- Note: This assumes organization_id column exists from migration 010
CREATE INDEX IF NOT EXISTS idx_product_colors_organization_id 
ON product_colors USING btree (organization_id);

-- Add composite index on colors for common query patterns
-- Optimizes queries that filter by both organization_id and deleted_at
CREATE INDEX IF NOT EXISTS idx_colors_org_active 
ON colors USING btree (organization_id, deleted_at) 
WHERE deleted_at IS NULL;

-- Add composite index on products for common query patterns  
-- Optimizes queries that filter by both organization_id and is_active
CREATE INDEX IF NOT EXISTS idx_products_org_active 
ON products USING btree (organization_id, is_active) 
WHERE is_active = true;

-- Add index on colors.user_id for RLS performance
-- Many RLS policies use user_id for filtering
CREATE INDEX IF NOT EXISTS idx_colors_user_id 
ON colors USING btree (user_id);

-- Add index on products.user_id for RLS performance
-- Many RLS policies use user_id for filtering  
CREATE INDEX IF NOT EXISTS idx_products_user_id 
ON products USING btree (user_id);

-- Add index on colors.external_id for sync performance
-- Used heavily in sync operations for upserts
CREATE INDEX IF NOT EXISTS idx_colors_external_id 
ON colors USING btree (external_id);

-- Add index on products.external_id for sync performance
-- Used heavily in sync operations for upserts
CREATE INDEX IF NOT EXISTS idx_products_external_id 
ON products USING btree (external_id);