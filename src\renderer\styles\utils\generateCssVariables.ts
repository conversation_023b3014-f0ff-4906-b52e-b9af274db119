/**
 * @file generateCssVariables.ts
 * @description Utility to generate CSS variables from design tokens
 */

import tokens from '../tokens';

/**
 * Generates CSS variables from the design token system
 * @returns {string} The CSS variables as a string
 */
export function generateCssVariables(): string {
  let cssVariables = ':root {\n';
  
  // Generate color variables
  Object.entries(tokens.colors).forEach(([category, values]) => {
    Object.entries(values).forEach(([name, value]) => {
      if (typeof value === 'string') {
        cssVariables += `  --color-${category}-${name}: ${value};\n`;
      } else if (typeof value === 'object') {
        Object.entries(value).forEach(([subName, subValue]) => {
          cssVariables += `  --color-${category}-${name}-${subName}: ${subValue};\n`;
        });
      }
    });
  });
  
  // Generate typography variables
  // Font families
  Object.entries(tokens.typography.fontFamily).forEach(([name, value]) => {
    cssVariables += `  --font-family-${name}: ${Array.isArray(value) ? value.join(', ') : value};\n`;
  });
  
  // Font sizes
  Object.entries(tokens.typography.fontSize).forEach(([name, value]) => {
    cssVariables += `  --font-size-${name}: ${value};\n`;
  });
  
  // Font weights
  Object.entries(tokens.typography.fontWeight).forEach(([name, value]) => {
    cssVariables += `  --font-weight-${name}: ${value};\n`;
  });
  
  // Line heights
  Object.entries(tokens.typography.lineHeight).forEach(([name, value]) => {
    cssVariables += `  --line-height-${name}: ${value};\n`;
  });
  
  // Generate spacing variables
  Object.entries(tokens.spacing).forEach(([name, value]) => {
    cssVariables += `  --spacing-${name.replace('.', '\\.')}: ${value};\n`;
  });
  
  // Generate border radius variables
  Object.entries(tokens.borderRadius).forEach(([name, value]) => {
    cssVariables += `  --radius-${name === 'DEFAULT' ? 'default' : name}: ${value};\n`;
  });
  
  // Generate shadow variables
  Object.entries(tokens.shadows).forEach(([name, value]) => {
    cssVariables += `  --shadow-${name === 'DEFAULT' ? 'default' : name}: ${value};\n`;
  });
  
  // Generate transition duration variables
  Object.entries(tokens.transitions.duration).forEach(([name, value]) => {
    cssVariables += `  --transition-duration-${name}: ${value};\n`;
  });
  
  // Generate transition easing variables
  Object.entries(tokens.transitions.easing).forEach(([name, value]) => {
    cssVariables += `  --transition-easing-${name}: ${value};\n`;
  });
  
  // Generate z-index variables
  Object.entries(tokens.zIndex).forEach(([name, value]) => {
    cssVariables += `  --z-${name}: ${value};\n`;
  });
  
  // Generate breakpoint variables
  Object.entries(tokens.breakpoints).forEach(([name, value]) => {
    cssVariables += `  --breakpoint-${name}: ${value};\n`;
  });
  
  cssVariables += '}\n\n';
  
  // Add dark mode variables
  cssVariables += '.dark {\n';
  // Dark mode variable overrides
  cssVariables += '  /* UI Background colors - Dark mode */\n';
  cssVariables += '  --color-ui-background-primary: #1C1C1E;\n';
  cssVariables += '  --color-ui-background-secondary: #2C2C2E;\n';
  cssVariables += '  --color-ui-background-tertiary: #3A3A3C;\n';
  
  cssVariables += '  /* UI Foreground colors - Dark mode */\n';
  cssVariables += '  --color-ui-foreground-primary: #FFFFFF;\n';
  cssVariables += '  --color-ui-foreground-secondary: #EBEBF0;\n';
  cssVariables += '  --color-ui-foreground-tertiary: #8E8E93;\n';
  cssVariables += '  --color-ui-foreground-inverse: #1D1D1F;\n';
  
  cssVariables += '  /* UI Border colors - Dark mode */\n';
  cssVariables += '  --color-ui-border-light: #38383A;\n';
  cssVariables += '  --color-ui-border-medium: #48484A;\n';
  cssVariables += '  --color-ui-border-dark: #636366;\n';
  
  cssVariables += '  /* Shadows - Dark mode */\n';
  cssVariables += '  --shadow-default: 0 2px 5px rgba(0, 0, 0, 0.2);\n';
  cssVariables += '  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.2);\n';
  cssVariables += '  --shadow-md: 0 4px 10px rgba(0, 0, 0, 0.25), 0 2px 4px rgba(0, 0, 0, 0.2);\n';
  cssVariables += '  --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.3), 0 2px 6px rgba(0, 0, 0, 0.2);\n';
  cssVariables += '  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.3), 0 3px 10px rgba(0, 0, 0, 0.2);\n';
  cssVariables += '}\n';
  
  return cssVariables;
} 