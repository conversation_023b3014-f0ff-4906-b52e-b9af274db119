{"name": "@chromasync/zoho-mail-mcp-server", "version": "1.0.0", "description": "MCP server for Zoho Mail integration", "main": "build/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node build/index.js", "setup": "tsx setup-oauth.ts"}, "keywords": ["mcp", "zoho", "email", "chromasync"], "author": "ChromaSync", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "axios": "^1.6.0", "dotenv": "^16.0.3"}, "devDependencies": {"@types/node": "^20.0.0", "tsx": "^4.6.0", "typescript": "^5.0.0"}}