/**
 * @file product.store.ts
 * @description Store for managing products using the optimized schema
 */

import { create } from 'zustand';
import { ColorEntry } from '../../shared/types/color.types';

// Product interface matching the optimized schema
export interface Product {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ProductWithColors extends Product {
  colors: ColorEntry[];
}

// Product store interface
export interface ProductState {
  // State
  products: ProductWithColors[];
  activeProductId: string | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchProducts: () => Promise<void>;
  fetchProductsWithColors: () => Promise<void>;
  addColorToProduct: (productId: string, colorId: string) => Promise<boolean>;
  addLibraryColorToProduct: (productId: string, libraryColor: any, customName?: string) => Promise<boolean>;
  removeColorFromProduct: (productId: string, colorId: string) => Promise<boolean>;
  createProduct: (name: string, metadata?: any) => Promise<Product | null>;
  deleteProduct: (productId: string) => Promise<boolean>;
  setActiveProductId: (id: string | null) => void;
}

// Create the product store
export const useProductStore = create<ProductState>((set, get) => ({
  // Initial state
  products: [],
  activeProductId: null,
  isLoading: false,
  error: null,

  // Fetch all products
  fetchProducts: async () => {
    set({ isLoading: true, error: null });
    try {
      const products = await window.ipc.invoke('product:getAll') as ProductWithColors[];
      set({ 
        products: products || [],
        isLoading: false 
      });
    } catch (error) {
      console.error('Error fetching products:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch products',
        isLoading: false 
      });
    }
  },

  // Fetch all products with their colors
  fetchProductsWithColors: async () => {
    console.log('[ProductStore] fetchProductsWithColors called');
    set({ isLoading: true, error: null });
    try {
      // Check if window.ipc is available
      if (typeof window === 'undefined' || !window.ipc) {
        console.warn('[ProductStore] window.ipc not available for fetching products');
        set({ isLoading: false });
        return;
      }
      
      console.log('[ProductStore] Calling window.ipc.invoke(product:getAllWithColors)...');
      const products = await window.ipc.invoke('product:getAllWithColors') as ProductWithColors[];
      console.log('[ProductStore] Received products:', products);
      set({ 
        products: products || [],
        isLoading: false 
      });
      console.log('[ProductStore] State updated with products');
    } catch (error) {
      console.error('[ProductStore] Error fetching products with colors:', error);
      set({ 
        error: error instanceof Error ? error.message : 'Failed to fetch products',
        isLoading: false 
      });
    }
  },

  // Add color to product
  addColorToProduct: async (productId: string, colorId: string): Promise<boolean> => {
    try {
      // Check if window.ipc is available
      if (typeof window === 'undefined' || !window.ipc) {
        console.warn('window.ipc not available for adding color to product');
        return false;
      }
      
      const success = await window.ipc.invoke('product:addColor', productId, colorId);
      if (success) {
        // Refresh products to get updated data
        await get().fetchProductsWithColors();
      }
      return !!success;
    } catch (error) {
      console.error('Error adding color to product:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to add color' });
      return false;
    }
  },

  // Add library color to product (creates a new color entry)
  addLibraryColorToProduct: async (productId: string, libraryColor: any, customName?: string): Promise<boolean> => {
    try {
      // Check if window.ipc is available
      if (typeof window === 'undefined' || !window.ipc) {
        console.warn('window.ipc not available for adding library color to product');
        return false;
      }
      
      const createdColor = await window.ipc.invoke('product:addLibraryColor', productId, libraryColor, customName);
      if (createdColor) {
        // Refresh products to get updated data
        await get().fetchProductsWithColors();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error adding library color to product:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to add library color' });
      return false;
    }
  },

  // Remove color from product
  removeColorFromProduct: async (productId: string, colorId: string): Promise<boolean> => {
    try {
      // Check if window.ipc is available
      if (typeof window === 'undefined' || !window.ipc) {
        console.warn('window.ipc not available for removing color from product');
        return false;
      }
      
      const success = await window.ipc.invoke('product:removeColor', productId, colorId);
      if (success) {
        // Refresh products to get updated data
        await get().fetchProductsWithColors();
      }
      return !!success;
    } catch (error) {
      console.error('Error removing color from product:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to remove color' });
      return false;
    }
  },

  // Create new product
  createProduct: async (name: string, metadata?: any): Promise<Product | null> => {
    try {
      // Check if window.ipc is available
      if (typeof window === 'undefined' || !window.ipc) {
        console.warn('window.ipc not available for creating product');
        return null;
      }
      
      const product = await window.ipc.invoke('product:add', { name, metadata });
      if (product) {
        // Refresh products to include the new one
        await get().fetchProductsWithColors();
      }
      return product as Product;
    } catch (error) {
      console.error('Error creating product:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to create product' });
      return null;
    }
  },

  // Delete product
  deleteProduct: async (productId: string): Promise<boolean> => {
    try {
      // Check if window.ipc is available
      if (typeof window === 'undefined' || !window.ipc) {
        console.warn('window.ipc not available for deleting product');
        return false;
      }
      
      const success = await window.ipc.invoke('product:delete', productId);
      if (success) {
        // Refresh products
        await get().fetchProductsWithColors();
      }
      return !!success;
    } catch (error) {
      console.error('Error deleting product:', error);
      set({ error: error instanceof Error ? error.message : 'Failed to delete product' });
      return false;
    }
  },

  // Set active product ID
  setActiveProductId: (id: string | null) => {
    set({ activeProductId: id });
  }
}));