/**
 * Test OAuth flow to debug the hanging issue
 * Run with: npx tsx test-oauth-flow.ts
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import crypto from 'crypto';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL || '';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing environment variables');
  process.exit(1);
}

async function testOAuthFlow() {
  console.log('🔍 Testing OAuth Flow...\n');

  // Create client with PKCE
  const supabase = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      flowType: 'pkce',
      persistSession: false,
      detectSessionInUrl: false
    }
  });

  // Generate state for testing
  const state = crypto.randomBytes(32).toString('base64url');
  console.log('🔑 Generated state:', state);

  try {
    // Test local redirect
    console.log('\n📋 Testing Local Redirect (localhost:3000):');
    const { data: localData, error: localError } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: 'http://localhost:3000/auth/callback',
        skipBrowserRedirect: true,
        queryParams: {
          state: state,
          access_type: 'offline',
          prompt: 'consent'
        }
      }
    });

    if (localError) {
      console.error('❌ Local redirect error:', localError);
    } else {
      console.log('✅ Local redirect URL generated');
      console.log('🔗 URL:', localData.url);
      
      // Check if it's PKCE
      const url = new URL(localData.url);
      console.log('📊 Flow Analysis:');
      console.log('   Response Type:', url.searchParams.get('response_type') || 'not set');
      console.log('   Code Challenge:', url.searchParams.get('code_challenge') ? '✅ Present' : '❌ Missing');
      console.log('   State:', url.searchParams.get('state') ? '✅ Present' : '❌ Missing');
    }

    // Test protocol redirect
    console.log('\n📋 Testing Protocol Redirect (chromasync://):');
    const { data: protocolData, error: protocolError } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: 'chromasync://auth/callback',
        skipBrowserRedirect: true,
        queryParams: {
          state: state,
          access_type: 'offline',
          prompt: 'consent'
        }
      }
    });

    if (protocolError) {
      console.error('❌ Protocol redirect error:', protocolError);
    } else {
      console.log('✅ Protocol redirect URL generated');
      console.log('🔗 URL:', protocolData.url);
    }

    // Test web redirect
    console.log('\n📋 Testing Web Redirect (auth.chromasync.app):');
    const { data: webData, error: webError } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: 'https://auth.chromasync.app/auth/callback',
        skipBrowserRedirect: true,
        queryParams: {
          state: state,
          access_type: 'offline',
          prompt: 'consent'
        }
      }
    });

    if (webError) {
      console.error('❌ Web redirect error:', webError);
    } else {
      console.log('✅ Web redirect URL generated');
      console.log('🔗 URL:', webData.url);
    }

    console.log('\n🎯 Summary:');
    console.log('- All three redirect types should work');
    console.log('- PKCE should be automatically enabled with flowType: "pkce"');
    console.log('- The issue is likely in the callback handling, not URL generation');
    
    console.log('\n🔧 Next Steps:');
    console.log('1. Test one of the URLs above manually in a browser');
    console.log('2. Check if the callback reaches the app');
    console.log('3. Verify protocol handler registration');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

testOAuthFlow();