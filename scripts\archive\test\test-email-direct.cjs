require('dotenv').config();
const nodemailer = require('nodemailer');

console.log('🔍 Testing Zoho Mail Configuration...\n');

// Check environment variables
console.log('Environment Variables:');
console.log('  ZOHO_EMAIL:', process.env.ZOHO_EMAIL || 'NOT SET');
console.log('  ZOHO_PASSWORD:', process.env.ZOHO_PASSWORD ? '***' + process.env.ZOHO_PASSWORD.slice(-4) : 'NOT SET');
console.log('  ZOHO_SUPPORT_ALIAS:', process.env.ZOHO_SUPPORT_ALIAS || 'NOT SET');
console.log('');

if (!process.env.ZOHO_PASSWORD) {
  console.error('❌ ZOHO_PASSWORD not found in environment variables!');
  console.error('   Make sure .env file is in the project root');
  process.exit(1);
}

async function testEmail() {
  try {
    // Create transporter with Zoho configuration
    const transporter = nodemailer.createTransport({
      host: 'smtp.zoho.com',
      port: 587,
      secure: false,
      auth: {
        user: process.env.ZOHO_EMAIL || '<EMAIL>',
        pass: process.env.ZOHO_PASSWORD
      },
      tls: {
        rejectUnauthorized: true
      }
    });

    console.log('📧 Verifying SMTP connection...');
    
    // Verify connection
    await transporter.verify();
    console.log('✅ SMTP connection verified successfully!\n');

    // Send test email
    console.log('📤 Sending test email...');
    const info = await transporter.sendMail({
      from: {
        name: 'ChromaSync Support',
        address: process.env.ZOHO_SUPPORT_ALIAS || '<EMAIL>'
      },
      to: '<EMAIL>',
      subject: '🧪 ChromaSync Email Test',
      text: 'This is a test email from ChromaSync to <NAME_EMAIL> alias is working correctly.',
      html: `
        <div style="font-family: Arial, sans-serif; padding: 20px;">
          <h2>🧪 ChromaSync Email Test</h2>
          <p>This is a test email to verify that:</p>
          <ul>
            <li>✅ Zoho Mail SMTP is configured correctly</li>
            <li>✅ The <EMAIL> alias is working</li>
            <li>✅ Emails can be sent from the ChromaSync app</li>
          </ul>
          <p>If you received this email, the configuration is working!</p>
          <hr>
          <p><small>Sent from ChromaSync Support Team</small></p>
        </div>
      `
    });

    console.log('✅ Email sent successfully!');
    console.log('   Message ID:', info.messageId);
    console.log('   Response:', info.response);
    
  } catch (error) {
    console.error('\n❌ Error:', error.message);
    
    if (error.code === 'EAUTH') {
      console.error('\n🔐 Authentication failed!');
      console.error('   - Check that ZOHO_PASSWORD contains a valid app-specific password');
      console.error('   - Generate one at: https://accounts.zoho.com/home#security/app_passwords');
      console.error('   - Make sure 2FA is enabled on your Zoho account');
    } else if (error.code === 'EENVELOPE') {
      console.error('\n📮 Invalid sender address!');
      console.error('   - <NAME_EMAIL> is configured as an alias');
      console.error('   - Check alias settings in Zoho Mail');
    }
  }
}

testEmail();