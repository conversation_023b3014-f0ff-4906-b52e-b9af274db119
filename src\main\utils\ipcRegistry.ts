/**
 * @file ipcRegistry.ts
 * @description Utility to track registered IPC handlers and prevent duplicate registrations
 */

// Track registered IPC channels to prevent duplicate registrations
const registeredChannels = new Set<string>();

/**
 * Safely register an IPC handler, preventing duplicate registrations
 * @param channel The IPC channel name
 * @returns Boolean indicating if the channel is available for registration
 */
export function canRegisterHandler(channel: string): boolean {
  if (registeredChannels.has(channel)) {
    console.log(`IPC handler for '${channel}' already registered. Skipping duplicate registration.`);
    return false;
  }
  
  registeredChannels.add(channel);
  return true;
}

/**
 * Reset the registry (mainly for testing purposes)
 */
export function resetRegistry(): void {
  registeredChannels.clear();
}

/**
 * Check if a handler is already registered
 * @param channel The IPC channel name
 * @returns Boolean indicating if the channel is already registered
 */
export function isHandlerRegistered(channel: string): boolean {
  return registeredChannels.has(channel);
}

/**
 * Get all registered channels (for debugging)
 * @returns Array of registered channel names
 */
export function getRegisteredChannels(): string[] {
  return Array.from(registeredChannels);
}
