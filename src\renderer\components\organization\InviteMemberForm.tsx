/**
 * @file InviteMemberForm.tsx
 * @description Form component for inviting new team members
 */

import React, { useState } from 'react';
import { Mail, Loader2, Send } from 'lucide-react';

interface InviteMemberFormProps {
  onInvite: (email: string, role: 'member' | 'admin') => Promise<{ success: boolean; error?: string }>;
  disabled?: boolean;
}

export const InviteMemberForm: React.FC<InviteMemberFormProps> = ({ onInvite, disabled = false }) => {
  const [email, setEmail] = useState('');
  const [role, setRole] = useState<'member' | 'admin'>('member');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const validateEmail = (email: string) => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(false);

    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setIsLoading(true);

    try {
      const result = await onInvite(email, role);
      if (result.success) {
        setSuccess(true);
        setEmail('');
        setRole('member');
        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(false), 3000);
      } else {
        setError(result.error || 'Failed to send invitation');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send invitation');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="invite-email" className="block text-sm font-medium text-gray-700 mb-2">
          Email Address
        </label>
        <div className="relative">
          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            id="invite-email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={disabled || isLoading}
            required
          />
        </div>
      </div>

      <div>
        <label htmlFor="invite-role" className="block text-sm font-medium text-gray-700 mb-2">
          Role
        </label>
        <select
          id="invite-role"
          value={role}
          onChange={(e) => setRole(e.target.value as 'member' | 'admin')}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          disabled={disabled || isLoading}
        >
          <option value="member">Member - Can view and edit colors</option>
          <option value="admin">Admin - Can manage team and colors</option>
        </select>
      </div>

      {error && (
        <div className="p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg text-sm">
          {error}
        </div>
      )}

      {success && (
        <div className="p-3 bg-green-50 border border-green-200 text-green-700 rounded-lg text-sm">
          <div className="flex items-start space-x-2">
            <div className="flex-shrink-0 mt-0.5">
              ✅
            </div>
            <div>
              <p className="font-medium mb-1">Invitation sent successfully!</p>
              <p className="text-xs text-green-600">
                The invitation email includes a manual code that always works, even if email links fail.
              </p>
            </div>
          </div>
        </div>
      )}

      <button
        type="submit"
        disabled={disabled || isLoading || !email.trim()}
        className={`w-full py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center ${
          disabled || isLoading || !email.trim()
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
            : 'bg-blue-500 text-white hover:bg-blue-600'
        }`}
      >
        {isLoading ? (
          <>
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            Sending Invitation...
          </>
        ) : (
          <>
            <Send className="w-4 h-4 mr-2" />
            Send Invitation
          </>
        )}
      </button>
      
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="text-sm font-medium text-blue-900 mb-2">📧 How Invitations Work:</h4>
        <ul className="text-xs text-blue-800 space-y-1">
          <li>• Invited users receive an email with a unique invitation code</li>
          <li>• They can join by entering the code in Settings → Team → Join Organization</li>
          <li>• If email links don't work, the manual code always works</li>
          <li>• Failed invitations are automatically retried in the background</li>
        </ul>
      </div>
    </form>
  );
};
