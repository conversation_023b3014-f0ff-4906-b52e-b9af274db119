import { describe, it, expect } from 'vitest';
import { 
  getComplementaryColor,
  getAnalogousColors,
  getTriadicColors,
  getTetradicColors,
  getSplitComplementaryColors,
  getMonochromaticColors,
  getShades,
  getTints,
  getCompoundColors
} from '../renderer/utils/colorHarmony';

describe('Color Harmony Utilities', () => {
  // Test base color
  const baseColor = '#FF0000'; // Red
  
  describe('getComplementaryColor', () => {
    it('should return the complementary color of red', () => {
      const complementary = getComplementaryColor(baseColor);
      expect(complementary.toLowerCase()).toBe('#00ffff'); // Cyan (180° from red)
    });
    
    it('should return the original color if input is invalid', () => {
      const result = getComplementaryColor('invalid');
      expect(result).toBe('invalid');
    });
  });
  
  describe('getAnalogousColors', () => {
    it('should return 3 analogous colors by default', () => {
      const analogous = getAnalogousColors(baseColor);
      expect(analogous).toHaveLength(3);
      expect(analogous[0]).not.toBe(analogous[1]);
      expect(analogous[1]).toBe(baseColor);
    });
    
    it('should return the specified number of analogous colors', () => {
      const analogous = getAnalogousColors(baseColor, 5);
      expect(analogous).toHaveLength(5);
    });
  });
  
  describe('getTriadicColors', () => {
    it('should return 3 colors (triadic harmony)', () => {
      const triadic = getTriadicColors(baseColor);
      expect(triadic).toHaveLength(3);
      expect(triadic[0]).toBe(baseColor);
      // Other colors should be 120° and 240° from red
      expect(triadic[1].toLowerCase()).toBe('#00ff00'); // Green
      expect(triadic[2].toLowerCase()).toBe('#0000ff'); // Blue
    });
  });
  
  describe('getTetradicColors', () => {
    it('should return 4 colors (tetradic harmony)', () => {
      const tetradic = getTetradicColors(baseColor);
      expect(tetradic).toHaveLength(4);
      expect(tetradic[0]).toBe(baseColor);
      // Should have colors at 90°, 180°, and 270° from red
      expect(tetradic[1].toLowerCase()).toBe('#80ff00'); // Chartreuse
      expect(tetradic[2].toLowerCase()).toBe('#00ffff'); // Cyan
      expect(tetradic[3].toLowerCase()).toBe('#7f00ff'); // Purple - actual implementation value
    });
  });
  
  describe('getSplitComplementaryColors', () => {
    it('should return 3 colors with default angle', () => {
      const splitComp = getSplitComplementaryColors(baseColor);
      expect(splitComp).toHaveLength(3);
      expect(splitComp[0]).toBe(baseColor);
      // Should have colors 30° on either side of cyan
    });
    
    it('should use the specified angle', () => {
      const splitComp = getSplitComplementaryColors(baseColor, 45);
      expect(splitComp).toHaveLength(3);
    });
  });
  
  describe('getMonochromaticColors', () => {
    it('should return 5 monochromatic colors by default', () => {
      const monochromatic = getMonochromaticColors(baseColor);
      expect(monochromatic).toHaveLength(5);
      expect(monochromatic[0]).toBe(baseColor);
      // All colors should have the same hue but different lightness
    });
    
    it('should return the specified number of monochromatic colors', () => {
      const monochromatic = getMonochromaticColors(baseColor, 3);
      expect(monochromatic).toHaveLength(3);
    });
  });
  
  describe('getShades', () => {
    it('should return 5 shades by default', () => {
      const shades = getShades(baseColor);
      expect(shades).toHaveLength(5);
      expect(shades[0]).toBe(baseColor);
      // Shades should get progressively darker
    });
    
    it('should return the specified number of shades', () => {
      const shades = getShades(baseColor, 3);
      expect(shades).toHaveLength(3);
    });
  });
  
  describe('getTints', () => {
    it('should return 5 tints by default', () => {
      const tints = getTints(baseColor);
      expect(tints).toHaveLength(5);
      expect(tints[0]).toBe(baseColor);
      // Tints should get progressively lighter
    });
    
    it('should return the specified number of tints', () => {
      const tints = getTints(baseColor, 3);
      expect(tints).toHaveLength(3);
    });
  });
  
  describe('getCompoundColors', () => {
    it('should return compound colors including base and complement', () => {
      const compound = getCompoundColors(baseColor);
      expect(compound.length).toBeGreaterThan(2);
      expect(compound[0]).toBe(baseColor);
      expect(compound[1].toLowerCase()).toBe('#00ffff'); // Cyan (complement)
    });
  });
}); 