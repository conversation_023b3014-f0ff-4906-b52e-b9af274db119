import { describe, test, beforeAll, afterAll, expect } from 'vitest';
import { chromium, Page, Browser } from 'playwright';
import RTLTester from './utils/rtl-support';

describe('RTL support tests', () => {
  let browser: Browser;
  let page: Page;
  let rtlTester: RTLTester;
  
  beforeAll(async () => {
    browser = await chromium.launch();
    const context = await browser.newContext();
    page = await context.newPage();
    rtlTester = new RTLTester();
    
    // Navigate to app
    await page.goto('http://localhost:5173');
    
    // Verify the app is loaded
    await page.waitForSelector('[data-testid="app-container"]');
  });
  
  afterAll(async () => {
    await browser.close();
    
    // Generate and save RTL support report
    const report = rtlTester.generateReport();
    console.log(report);
  });
  
  test('Header component should support RTL', async () => {
    const result = await rtlTester.testComponent(
      page,
      'Header',
      'header',
      [
        // Test header title alignment
        () => rtlTester.checkElementAlignment(page, 'header .title', 'right'),
        
        // Test header buttons container direction
        () => rtlTester.checkFlexDirection(page, 'header .header-controls'),
        
        // Test header icons direction flip
        () => rtlTester.checkIconFlip(page, 'header .header-controls .back-icon')
      ],
      true
    );
    
    expect(result.passed).toBe(true);
  });
  
  test('SearchBar component should support RTL', async () => {
    const result = await rtlTester.testComponent(
      page,
      'SearchBar',
      '[data-testid="search-bar"]',
      [
        // Test search input alignment
        () => rtlTester.checkElementAlignment(page, '[data-testid="search-input"]', 'right'),
        
        // Test search icon placement
        () => page.evaluate(() => {
          const searchIcon = document.querySelector('[data-testid="search-icon"]');
          if (!searchIcon) {return false;}
          
          const style = window.getComputedStyle(searchIcon as Element);
          return style.left !== undefined && style.left !== 'auto';
        })
      ],
      true
    );
    
    expect(result.passed).toBe(true);
  });
  
  test('ColorTable component should support RTL', async () => {
    // Navigate to table view if not already there
    await page.click('[data-testid="table-view-tab"]');
    
    const result = await rtlTester.testComponent(
      page,
      'ColorTable',
      '[data-testid="color-table"]',
      [
        // Test table alignment
        () => rtlTester.checkElementAlignment(page, '[data-testid="color-table"] th:first-child', 'right'),
        
        // Test table headers
        () => page.evaluate(() => {
          const tableHeaders = document.querySelectorAll('[data-testid="color-table"] th');
          if (tableHeaders.length === 0) {return false;}
          
          // Check if first header is on the right side in RTL
          const firstHeaderRect = tableHeaders[0].getBoundingClientRect();
          const lastHeaderRect = tableHeaders[tableHeaders.length - 1].getBoundingClientRect();
          
          return firstHeaderRect.right > lastHeaderRect.right;
        })
      ],
      true
    );
    
    expect(result.passed).toBe(true);
  });
  
  test('ColorSwatches component should support RTL', async () => {
    // Navigate to swatches view
    await page.click('[data-testid="swatch-view-tab"]');
    
    const result = await rtlTester.testComponent(
      page,
      'ColorSwatches',
      '[data-testid="color-swatches"]',
      [
        // Test flex container direction
        () => page.evaluate(() => {
          const swatchesContainer = document.querySelector('[data-testid="color-swatches"]');
          if (!swatchesContainer) {return false;}
          
          const style = window.getComputedStyle(swatchesContainer as Element);
          return style.flexDirection === 'row-reverse' || style.flexWrap === 'wrap-reverse';
        }),
        
        // Test individual swatch alignment
        () => rtlTester.checkElementAlignment(
          page, 
          '[data-testid="color-swatch"] .swatch-name', 
          'right'
        )
      ],
      true
    );
    
    expect(result.passed).toBe(true);
  });
  
  test('ColorForm component should support RTL', async () => {
    // Open the color form
    await page.click('[data-testid="add-color-button"]');
    await page.waitForSelector('[data-testid="color-form"]');
    
    const result = await rtlTester.testComponent(
      page,
      'ColorForm',
      '[data-testid="color-form"]',
      [
        // Test form label alignment
        () => rtlTester.checkElementAlignment(page, '[data-testid="color-form"] label', 'right'),
        
        // Test form input text alignment
        () => rtlTester.checkElementAlignment(page, '[data-testid="color-form"] input', 'right'),
        
        // Test form button container direction
        () => rtlTester.checkFlexDirection(page, '[data-testid="color-form"] .form-buttons')
      ],
      true
    );
    
    expect(result.passed).toBe(true);
  });
}); 