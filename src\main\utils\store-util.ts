/**
 * @file store-util.ts
 * @description Utility for creating safe store instances
 */

import { SimpleStore } from './simple-store';

/**
 * Create a safe store instance
 * @param options Store options
 * @returns Safe store instance
 */
export function createSafeStore<T extends Record<string, any>>(options: {
  name: string;
  defaults?: T;
  encryptionKey?: string;
}): SimpleStore<T> {
  return new SimpleStore<T>(options);
}
