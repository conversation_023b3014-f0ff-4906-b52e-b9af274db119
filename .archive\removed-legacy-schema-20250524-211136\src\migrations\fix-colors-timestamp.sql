-- Migration script to fix colors table schema
-- This will add missing columns and set default timestamps

-- Add created_at and updated_at columns if they don't exist
ALTER TABLE colors ADD COLUMN created_at TEXT DEFAULT NULL;
ALTER TABLE colors ADD COLUMN updated_at TEXT DEFAULT NULL;

-- Set timestamps for all records missing them
UPDATE colors SET created_at = strftime('%Y-%m-%dT%H:%M:%SZ', 'now') WHERE created_at IS NULL;
UPDATE colors SET updated_at = strftime('%Y-%m-%dT%H:%M:%SZ', 'now') WHERE updated_at IS NULL;