-- Sample data for ChromaSync - 10 Products with 5 Colors each
-- This script uses the optimized schema structure

-- First, ensure we have color sources
INSERT OR IGNORE INTO color_sources (id, code, name, is_system) VALUES
(1, 'user', 'User Created', 0),
(2, 'pantone', 'PANTONE®', 1),
(3, 'ral', 'RAL', 1),
(4, 'ncs', 'NCS', 1);

-- Insert sample products
INSERT INTO products (external_id, name, sku, metadata) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Summer Collection 2024', 'SUMM-2024', json_object('description', 'Vibrant summer fashion line', 'category', 'Fashion')),
('550e8400-e29b-41d4-a716-446655440002', 'Urban Office Furniture', 'URB-OFF-001', json_object('description', 'Modern office furniture collection', 'category', 'Furniture')),
('550e8400-e29b-41d4-a716-446655440003', 'Kids Room Essentials', 'KIDS-ESS-001', json_object('description', 'Colorful and safe furniture for children', 'category', 'Kids')),
('550e8400-e29b-41d4-a716-446655440004', 'Automotive Paint Series', 'AUTO-PAINT-001', json_object('description', 'Professional automotive paint collection', 'category', 'Automotive')),
('550e8400-e29b-41d4-a716-446655440005', 'Garden Furniture Set', 'GARD-FURN-001', json_object('description', 'Weather-resistant outdoor furniture', 'category', 'Outdoor')),
('550e8400-e29b-41d4-a716-446655440006', 'Tech Accessories Line', 'TECH-ACC-001', json_object('description', 'Modern tech accessories with style', 'category', 'Technology')),
('550e8400-e29b-41d4-a716-446655440007', 'Bathroom Fixtures', 'BATH-FIX-001', json_object('description', 'Premium bathroom fixtures and fittings', 'category', 'Bathroom')),
('550e8400-e29b-41d4-a716-446655440008', 'Kitchen Cabinet Series', 'KITCH-CAB-001', json_object('description', 'Contemporary kitchen cabinet designs', 'category', 'Kitchen')),
('550e8400-e29b-41d4-a716-446655440009', 'Sports Equipment Range', 'SPORT-EQ-001', json_object('description', 'Professional sports equipment', 'category', 'Sports')),
('550e8400-e29b-41d4-a716-446655440010', 'Living Room Textiles', 'LIV-TEX-001', json_object('description', 'Luxury textiles for modern living', 'category', 'Home Textiles'));

-- Insert sample colors (50 total - 5 per product)
-- Colors for Summer Collection 2024
INSERT INTO colors (external_id, source_id, code, display_name, hex, is_metallic, search_terms) VALUES
('color-001', 2, 'PANTONE 17-1463', 'Tangerine Tango', '#DD4124', 0, 'orange red vibrant summer'),
('color-002', 2, 'PANTONE 14-4318', 'Aqua Sky', '#7BC4C4', 0, 'blue aqua turquoise summer'),
('color-003', 2, 'PANTONE 13-0647', 'Illuminating Yellow', '#F5DF4D', 0, 'yellow bright sunny summer'),
('color-004', 2, 'PANTONE 18-3838', 'Ultra Violet', '#5F4B8B', 0, 'purple violet deep summer'),
('color-005', 2, 'PANTONE 15-5519', 'Turquoise', '#45B8AC', 0, 'turquoise green blue summer');

-- Colors for Urban Office Furniture
INSERT INTO colors (external_id, source_id, code, display_name, hex, is_metallic, search_terms) VALUES
('color-006', 3, 'RAL 7016', 'Anthracite Grey', '#293133', 0, 'grey dark office professional'),
('color-007', 3, 'RAL 9003', 'Signal White', '#F4F4F4', 0, 'white clean office modern'),
('color-008', 3, 'RAL 5024', 'Pastel Blue', '#5D9B9B', 0, 'blue pastel office calm'),
('color-009', 3, 'RAL 7035', 'Light Grey', '#D7D7D7', 0, 'grey light office neutral'),
('color-010', 1, 'CUSTOM-001', 'Executive Brown', '#3E2723', 0, 'brown dark wood executive');

-- Colors for Kids Room Essentials
INSERT INTO colors (external_id, source_id, code, display_name, hex, is_metallic, search_terms) VALUES
('color-011', 2, 'PANTONE 13-4411', 'Baby Blue', '#B5D3E7', 0, 'blue baby kids soft'),
('color-012', 2, 'PANTONE 12-0752', 'Buttercup', '#FCE883', 0, 'yellow kids happy bright'),
('color-013', 2, 'PANTONE 14-4318', 'Mint Green', '#98D982', 0, 'green mint kids fresh'),
('color-014', 2, 'PANTONE 14-2311', 'Pink Lavender', '#E8B5CE', 0, 'pink kids soft pastel'),
('color-015', 2, 'PANTONE 16-1441', 'Peach', '#FFBE98', 0, 'peach orange kids warm');

-- Colors for Automotive Paint Series
INSERT INTO colors (external_id, source_id, code, display_name, hex, is_metallic, search_terms) VALUES
('color-016', 3, 'RAL 3020', 'Traffic Red', '#CC0605', 0, 'red automotive racing sport'),
('color-017', 3, 'RAL 9005', 'Jet Black', '#0A0A0A', 0, 'black automotive classic'),
('color-018', 1, 'METAL-001', 'Metallic Silver', '#C0C0C0', 1, 'silver metallic automotive luxury'),
('color-019', 3, 'RAL 5002', 'Ultramarine Blue', '#1E3A8A', 1, 'blue metallic automotive sport'),
('color-020', 1, 'PEARL-001', 'Pearl White', '#F8F8FF', 1, 'white pearl automotive premium');

-- Colors for Garden Furniture Set
INSERT INTO colors (external_id, source_id, code, display_name, hex, is_metallic, search_terms) VALUES
('color-021', 3, 'RAL 6005', 'Moss Green', '#0F4336', 0, 'green moss garden outdoor'),
('color-022', 3, 'RAL 8017', 'Chocolate Brown', '#45322E', 0, 'brown chocolate garden wood'),
('color-023', 3, 'RAL 7032', 'Pebble Grey', '#B8B799', 0, 'grey pebble garden stone'),
('color-024', 3, 'RAL 1015', 'Light Ivory', '#E6D690', 0, 'ivory cream garden elegant'),
('color-025', 2, 'PANTONE 19-0509', 'Forest Night', '#1B3B36', 0, 'green dark forest garden');

-- Colors for Tech Accessories Line
INSERT INTO colors (external_id, source_id, code, display_name, hex, is_metallic, search_terms) VALUES
('color-026', 1, 'TECH-001', 'Space Grey', '#4A4A4A', 1, 'grey space tech modern metallic'),
('color-027', 1, 'TECH-002', 'Rose Gold', '#E0BFB8', 1, 'pink rose gold tech premium metallic'),
('color-028', 2, 'PANTONE 2735', 'Electric Blue', '#3F00FF', 0, 'blue electric tech gaming'),
('color-029', 1, 'TECH-003', 'Midnight Black', '#000000', 0, 'black midnight tech sleek'),
('color-030', 1, 'TECH-004', 'Arctic White', '#FFFFFF', 0, 'white arctic tech clean minimal');

-- Colors for Bathroom Fixtures
INSERT INTO colors (external_id, source_id, code, display_name, hex, is_metallic, search_terms) VALUES
('color-031', 3, 'RAL 9010', 'Pure White', '#FFFFFF', 0, 'white pure bathroom clean'),
('color-032', 1, 'CHROME-001', 'Chrome', '#D4D4D4', 1, 'chrome silver metallic bathroom'),
('color-033', 1, 'BRASS-001', 'Brushed Brass', '#B8860B', 1, 'brass gold metallic bathroom vintage'),
('color-034', 3, 'RAL 7001', 'Silver Grey', '#8D948D', 0, 'grey silver bathroom modern'),
('color-035', 1, 'COPPER-001', 'Aged Copper', '#B87333', 1, 'copper metallic bathroom luxury');

-- Colors for Kitchen Cabinet Series
INSERT INTO colors (external_id, source_id, code, display_name, hex, is_metallic, search_terms) VALUES
('color-036', 3, 'RAL 9016', 'Traffic White', '#F6F6F6', 0, 'white kitchen cabinet clean modern'),
('color-037', 4, 'NCS S 7500-N', 'Charcoal', '#3A3A3A', 0, 'charcoal dark kitchen contemporary'),
('color-038', 3, 'RAL 7047', 'Telegraph Grey', '#D0D0D0', 0, 'grey kitchen cabinet neutral'),
('color-039', 1, 'WOOD-001', 'Natural Oak', '#BA8C63', 0, 'oak wood natural kitchen warm'),
('color-040', 4, 'NCS S 4010-B30G', 'Sage Green', '#87A96B', 0, 'green sage kitchen trendy calm');

-- Colors for Sports Equipment Range
INSERT INTO colors (external_id, source_id, code, display_name, hex, is_metallic, search_terms) VALUES
('color-041', 2, 'PANTONE 18-1664', 'Fiery Red', '#FF4040', 0, 'red fiery sports energy dynamic'),
('color-042', 2, 'PANTONE 2728', 'Royal Blue', '#004CFF', 0, 'blue royal sports team professional'),
('color-043', 2, 'PANTONE 375', 'Lime Green', '#A6CE39', 0, 'green lime sports fresh energy'),
('color-044', 3, 'RAL 2004', 'Pure Orange', '#F44611', 0, 'orange sports vibrant energy'),
('color-045', 1, 'NEON-001', 'Neon Yellow', '#FFFF00', 0, 'yellow neon sports visibility safety');

-- Colors for Living Room Textiles
INSERT INTO colors (external_id, source_id, code, display_name, hex, is_metallic, search_terms) VALUES
('color-046', 2, 'PANTONE 18-1441', 'Marsala', '#955251', 0, 'marsala wine red textiles luxury'),
('color-047', 2, 'PANTONE 14-4107', 'Harbor Mist', '#B4B8B0', 0, 'grey mist textiles neutral soft'),
('color-048', 2, 'PANTONE 19-4052', 'Classic Blue', '#0F4C81', 0, 'blue classic textiles timeless'),
('color-049', 2, 'PANTONE 13-0919', 'Warm Sand', '#C7B299', 0, 'sand beige textiles warm cozy'),
('color-050', 2, 'PANTONE 18-5338', 'Emerald', '#009473', 0, 'green emerald textiles luxury rich');

-- Add color space data for each color (CMYK values)
-- This is a simplified example - in reality, you'd calculate accurate CMYK values
INSERT INTO color_cmyk (color_id, c, m, y, k) 
SELECT id, 
  CASE 
    WHEN hex LIKE '%FF%' THEN 0
    WHEN hex LIKE '%00%' THEN 100
    ELSE 50
  END as c,
  CASE 
    WHEN hex LIKE '%FF%' THEN 0
    WHEN hex LIKE '%00%' THEN 100
    ELSE 50
  END as m,
  CASE 
    WHEN hex LIKE '%FF%' THEN 0
    WHEN hex LIKE '%00%' THEN 100
    ELSE 50
  END as y,
  CASE 
    WHEN hex = '#000000' THEN 100
    WHEN hex = '#FFFFFF' THEN 0
    ELSE 20
  END as k
FROM colors;

-- Add RGB values (extracted from hex)
INSERT INTO color_rgb (color_id, r, g, b)
SELECT id,
  CAST(strftime('%d', CAST(('0x' || substr(hex, 2, 2)) AS INTEGER)) AS INTEGER) as r,
  CAST(strftime('%d', CAST(('0x' || substr(hex, 4, 2)) AS INTEGER)) AS INTEGER) as g,
  CAST(strftime('%d', CAST(('0x' || substr(hex, 6, 2)) AS INTEGER)) AS INTEGER) as b
FROM colors;

-- Associate colors with products (5 colors per product)
-- Summer Collection 2024
INSERT INTO product_colors (product_id, color_id, display_order, usage_type) 
SELECT p.id, c.id, 
  CASE c.external_id
    WHEN 'color-001' THEN 1
    WHEN 'color-002' THEN 2
    WHEN 'color-003' THEN 3
    WHEN 'color-004' THEN 4
    WHEN 'color-005' THEN 5
  END as display_order,
  CASE c.external_id
    WHEN 'color-001' THEN 'primary'
    ELSE 'standard'
  END as usage_type
FROM products p, colors c
WHERE p.external_id = '550e8400-e29b-41d4-a716-446655440001'
AND c.external_id IN ('color-001', 'color-002', 'color-003', 'color-004', 'color-005');

-- Urban Office Furniture
INSERT INTO product_colors (product_id, color_id, display_order, usage_type) 
SELECT p.id, c.id, 
  CASE c.external_id
    WHEN 'color-006' THEN 1
    WHEN 'color-007' THEN 2
    WHEN 'color-008' THEN 3
    WHEN 'color-009' THEN 4
    WHEN 'color-010' THEN 5
  END as display_order,
  'standard' as usage_type
FROM products p, colors c
WHERE p.external_id = '550e8400-e29b-41d4-a716-446655440002'
AND c.external_id IN ('color-006', 'color-007', 'color-008', 'color-009', 'color-010');

-- Kids Room Essentials
INSERT INTO product_colors (product_id, color_id, display_order, usage_type) 
SELECT p.id, c.id, 
  (CAST(substr(c.external_id, -3) AS INTEGER) - 10) as display_order,
  'standard' as usage_type
FROM products p, colors c
WHERE p.external_id = '550e8400-e29b-41d4-a716-446655440003'
AND c.external_id IN ('color-011', 'color-012', 'color-013', 'color-014', 'color-015');

-- Automotive Paint Series
INSERT INTO product_colors (product_id, color_id, display_order, usage_type) 
SELECT p.id, c.id, 
  (CAST(substr(c.external_id, -3) AS INTEGER) - 15) as display_order,
  CASE c.external_id
    WHEN 'color-018' THEN 'primary'
    ELSE 'standard'
  END as usage_type
FROM products p, colors c
WHERE p.external_id = '550e8400-e29b-41d4-a716-446655440004'
AND c.external_id IN ('color-016', 'color-017', 'color-018', 'color-019', 'color-020');

-- Garden Furniture Set
INSERT INTO product_colors (product_id, color_id, display_order, usage_type) 
SELECT p.id, c.id, 
  (CAST(substr(c.external_id, -3) AS INTEGER) - 20) as display_order,
  'standard' as usage_type
FROM products p, colors c
WHERE p.external_id = '550e8400-e29b-41d4-a716-446655440005'
AND c.external_id IN ('color-021', 'color-022', 'color-023', 'color-024', 'color-025');

-- Tech Accessories Line
INSERT INTO product_colors (product_id, color_id, display_order, usage_type) 
SELECT p.id, c.id, 
  (CAST(substr(c.external_id, -3) AS INTEGER) - 25) as display_order,
  CASE c.external_id
    WHEN 'color-026' THEN 'primary'
    ELSE 'standard'
  END as usage_type
FROM products p, colors c
WHERE p.external_id = '550e8400-e29b-41d4-a716-446655440006'
AND c.external_id IN ('color-026', 'color-027', 'color-028', 'color-029', 'color-030');

-- Bathroom Fixtures
INSERT INTO product_colors (product_id, color_id, display_order, usage_type) 
SELECT p.id, c.id, 
  (CAST(substr(c.external_id, -3) AS INTEGER) - 30) as display_order,
  'standard' as usage_type
FROM products p, colors c
WHERE p.external_id = '550e8400-e29b-41d4-a716-446655440007'
AND c.external_id IN ('color-031', 'color-032', 'color-033', 'color-034', 'color-035');

-- Kitchen Cabinet Series
INSERT INTO product_colors (product_id, color_id, display_order, usage_type) 
SELECT p.id, c.id, 
  (CAST(substr(c.external_id, -3) AS INTEGER) - 35) as display_order,
  'standard' as usage_type
FROM products p, colors c
WHERE p.external_id = '550e8400-e29b-41d4-a716-446655440008'
AND c.external_id IN ('color-036', 'color-037', 'color-038', 'color-039', 'color-040');

-- Sports Equipment Range
INSERT INTO product_colors (product_id, color_id, display_order, usage_type) 
SELECT p.id, c.id, 
  (CAST(substr(c.external_id, -3) AS INTEGER) - 40) as display_order,
  'standard' as usage_type
FROM products p, colors c
WHERE p.external_id = '550e8400-e29b-41d4-a716-446655440009'
AND c.external_id IN ('color-041', 'color-042', 'color-043', 'color-044', 'color-045');

-- Living Room Textiles
INSERT INTO product_colors (product_id, color_id, display_order, usage_type) 
SELECT p.id, c.id, 
  (CAST(substr(c.external_id, -3) AS INTEGER) - 45) as display_order,
  'standard' as usage_type
FROM products p, colors c
WHERE p.external_id = '550e8400-e29b-41d4-a716-446655440010'
AND c.external_id IN ('color-046', 'color-047', 'color-048', 'color-049', 'color-050');

-- Add some sample gradients
INSERT INTO colors (external_id, source_id, code, display_name, hex, is_gradient, search_terms) VALUES
('gradient-001', 1, 'GRAD-SUNSET', 'Sunset Gradient', '#FF6B6B', 1, 'gradient sunset orange red yellow'),
('gradient-002', 1, 'GRAD-OCEAN', 'Ocean Gradient', '#4ECDC4', 1, 'gradient ocean blue turquoise');

-- Add gradient stops
INSERT INTO gradient_stops (gradient_id, stop_index, position, hex) 
SELECT c.id, 0, 0.0, '#FF6B6B' FROM colors c WHERE c.external_id = 'gradient-001'
UNION ALL
SELECT c.id, 1, 0.5, '#FFE66D' FROM colors c WHERE c.external_id = 'gradient-001'
UNION ALL
SELECT c.id, 2, 1.0, '#4ECDC4' FROM colors c WHERE c.external_id = 'gradient-001';

INSERT INTO gradient_stops (gradient_id, stop_index, position, hex) 
SELECT c.id, 0, 0.0, '#1A535C' FROM colors c WHERE c.external_id = 'gradient-002'
UNION ALL
SELECT c.id, 1, 0.33, '#4ECDC4' FROM colors c WHERE c.external_id = 'gradient-002'
UNION ALL
SELECT c.id, 2, 0.66, '#95E1D3' FROM colors c WHERE c.external_id = 'gradient-002'
UNION ALL
SELECT c.id, 3, 1.0, '#F7FFF7' FROM colors c WHERE c.external_id = 'gradient-002';

-- Summary of data created:
-- 10 Products with varied categories
-- 50 Colors (5 per product) from different color systems (PANTONE, RAL, NCS, Custom)
-- 2 Gradient colors with stops
-- Color associations with proper display order and usage types
-- Basic CMYK and RGB values for all colors

SELECT 'Data import complete!' as message;
SELECT COUNT(*) as product_count FROM products WHERE is_active = 1;
SELECT COUNT(*) as color_count FROM colors WHERE is_active = 1;
SELECT COUNT(*) as association_count FROM product_colors;
