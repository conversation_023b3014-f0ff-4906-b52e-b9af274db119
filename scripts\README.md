# Scripts Directory

This directory contains various scripts for ChromaSync development, testing, and deployment.

## Directory Structure

### Active Scripts

- **build/** - Build and packaging scripts
  - `build-config.cjs` - Build configuration generator
  - `build-installer.bat` - Windows installer build script
  - `build-new.bat` - Alternative Windows build script
  - `create-launcher.bat` - Windows launcher creation

- **migrations/** - Database migration scripts
  - SQL files numbered sequentially for version control
  - Run automatically by the migration runner

- **Active utilities:**
  - `init-database.cjs` - Initialize fresh database
  - `clear-local-db.ts` - Clear local database
  - `import-chromasync-data.ts` - Import data utility
  - `notarize.js` - macOS notarization script
  - `obfuscate.js` - Code obfuscation utility
  - Others as needed for production use

### Archive Directory

The `archive/` directory contains scripts that are:
- No longer actively used in production
- Historical fixes and one-time migrations
- Debug utilities for troubleshooting
- Test scripts for various components

#### archive/debug/
Debug and analysis scripts for troubleshooting:
- Database state analysis
- Schema checking
- Data comparison tools
- Debug utilities for specific features

#### archive/fixes/
One-time fixes and patches:
- SQL fixes for specific issues
- Data cleanup scripts
- Schema migration fixes
- RLS (Row Level Security) fixes

#### archive/migration/
Historical migration and data transfer scripts:
- Legacy data migration tools
- Database reset utilities
- Sample data generators
- User migration scripts

#### archive/test/
Test scripts for various services:
- Email service tests (Zoho, SMTP)
- OAuth flow tests
- Invitation system tests
- API integration tests

#### archive/utilities/
Miscellaneous utility scripts:
- Token refresh tools
- Database lookup utilities
- Setup helpers
- Manual process scripts

## Usage Guidelines

1. **Active scripts** should be maintained and documented
2. **Archive scripts** are kept for reference but may not work with current codebase
3. Always test scripts in development before running in production
4. Check script dependencies and required environment variables
5. For database operations, ensure proper backups exist

## Important Scripts

### Database Management
```bash
# Initialize a fresh database
node scripts/init-database.cjs

# Clear local database (development)
npm run clear-db
```

### Build & Deployment
```bash
# Generate build configuration
node scripts/build-config.cjs

# Build installer (Windows)
./scripts/build/build-installer.bat

# Notarize macOS build
node scripts/notarize.js
```

### Migrations
Database migrations run automatically on app startup. To add a new migration:
1. Create a new SQL file in `migrations/` with the next sequential number
2. Follow the naming pattern: `XXX_description.sql`
3. Test the migration thoroughly before committing

## Environment Requirements

Many scripts require environment variables. See `.env.example` for required variables.

Key variables often needed:
- `SUPABASE_URL` - Supabase project URL
- `SUPABASE_ANON_KEY` - Supabase anonymous key
- `ZOHO_*` - Zoho email service credentials
- Database connection settings