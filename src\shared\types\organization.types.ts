/**
 * @file organization.types.ts
 * @description Shared organization type definitions
 */

export interface Organization {
  id: string;
  external_id: string;
  name: string;
  slug: string;
  plan: 'free' | 'team' | 'enterprise';
  settings: OrganizationSettings;
  created_at: string;
  updated_at: string;
  // Runtime properties
  memberCount?: number;
  userRole?: 'owner' | 'admin' | 'member'; // Current user's role in this organization
}

export interface OrganizationMember {
  organization_id: string;
  user_id: string;
  role: 'owner' | 'admin' | 'member';
  joined_at: string;
  invited_by?: string;
  // Runtime properties
  user?: {
    id: string;
    email: string;
    name?: string;
  };
  isCurrentUser?: boolean;
}

export interface OrganizationSettings {
  allowedDomains?: string[];
  colorNamePattern?: string;
  requireApproval?: boolean;
  defaultColorProperties?: Record<string, any>;
}

export interface CreateOrganizationRequest {
  name: string;
}

export interface InviteMemberRequest {
  email: string;
  role: 'admin' | 'member';
}

export interface UpdateMemberRoleRequest {
  userId: string;
  role: 'admin' | 'member';
}

export interface OrganizationInvite {
  id: string;
  organization_id: string;
  email: string;
  role: 'admin' | 'member';
  token: string;
  invited_by: string;
  expires_at: string;
  accepted_at?: string;
  created_at: string;
}
