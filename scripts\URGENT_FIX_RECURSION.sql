-- IMMEDIATE FIX for ChromaSync Infinite Recursion Error
-- Run this entire script in Supabase SQL Editor

-- 1. First disable <PERSON><PERSON> to fix policies
ALTER TABLE organization_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE organizations DISABLE ROW LEVEL SECURITY;

-- 2. Drop ALL existing problematic policies
DO $$ 
BEGIN
    -- Drop organization_members policies
    IF EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'organization_members') THEN
        EXECUTE (
            SELECT string_agg('DROP POLICY IF EXISTS "' || policyname || '" ON organization_members;', E'\n')
            FROM pg_policies 
            WHERE tablename = 'organization_members'
        );
    END IF;
    
    -- Drop organizations policies
    IF EXISTS (SELECT 1 FROM pg_policies WHERE tablename = 'organizations') THEN
        EXECUTE (
            SELECT string_agg('DROP POLICY IF EXISTS "' || policyname || '" ON organizations;', E'\n')
            FROM pg_policies 
            WHERE tablename = 'organizations'
        );
    END IF;
END $$;

-- 3. Re-enable RLS
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- 4. Create SIMPLE non-recursive policies for organizations
CREATE POLICY "org_select" ON organizations
    FOR SELECT TO authenticated
    USING (
        id IN (
            SELECT organization_id FROM organization_members WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "org_update" ON organizations
    FOR UPDATE TO authenticated
    USING (
        id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND role = 'owner'
        )
    );

-- 5. Create SIMPLE policies for organization_members
-- Key: Split the policies to avoid self-reference
CREATE POLICY "members_own_select" ON organization_members
    FOR SELECT TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "members_org_select" ON organization_members
    FOR SELECT TO authenticated
    USING (
        organization_id IN (
            SELECT DISTINCT om2.organization_id 
            FROM organization_members om2
            WHERE om2.user_id = auth.uid()
        )
    );

CREATE POLICY "members_admin_all" ON organization_members
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM organization_members existing
            WHERE existing.organization_id = organization_members.organization_id
            AND existing.user_id = auth.uid()
            AND existing.role IN ('owner', 'admin')
        )
    );

-- 6. Test that it works
SELECT 'Testing organization query...' as status;
SELECT id, name, slug FROM organizations LIMIT 1;

SELECT 'Testing organization_members query...' as status;
SELECT organization_id, user_id, role FROM organization_members LIMIT 1;

SELECT 'Fix completed successfully!' as status;
