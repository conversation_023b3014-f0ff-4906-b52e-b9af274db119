#!/bin/bash

# Pantone Color Tracker Installation Script

# Print colored output
print_green() {
  echo -e "\033[0;32m$1\033[0m"
}

print_blue() {
  echo -e "\033[0;34m$1\033[0m"
}

print_red() {
  echo -e "\033[0;31m$1\033[0m"
}

# Check if Node.js is installed
print_blue "Checking Node.js installation..."
if ! command -v node &> /dev/null; then
  print_red "Node.js is not installed. Please install Node.js 16 or later."
  exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2)
MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1)

if [ $MAJOR_VERSION -lt 16 ]; then
  print_red "Node.js version must be 16 or later. Found version: $NODE_VERSION"
  exit 1
fi

print_green "Node.js version $NODE_VERSION detected."

# Install dependencies
print_blue "Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
  print_red "Failed to install dependencies."
  exit 1
fi

print_green "Dependencies installed successfully."

# Create necessary directories
print_blue "Setting up directory structure..."
mkdir -p src/renderer/components/ColorTable
mkdir -p src/renderer/components/ColorSwatches
mkdir -p src/renderer/store
mkdir -p src/renderer/utils
mkdir -p src/main/db/services
mkdir -p src/main/ipc
mkdir -p src/shared/types

print_green "Directory structure created."

# Build the application
print_blue "Building the application..."
npm run build

if [ $? -ne 0 ]; then
  print_red "Failed to build the application."
  exit 1
fi

print_green "Application built successfully."

print_blue "Installation complete! You can now run the application with 'npm run dev'." 